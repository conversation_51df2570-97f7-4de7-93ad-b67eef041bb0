/* Newsletter Modern Styling - Based on Tailwind Design System */

:root {
  /* Tailwind-inspired color palette */
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.6231 0.1880 259.8145);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9670 0.0029 264.5419);
  --secondary-foreground: oklch(0.4461 0.0263 256.8018);
  --muted: oklch(0.9846 0.0017 247.8389);
  --muted-foreground: oklch(0.5510 0.0234 264.3637);
  --accent: oklch(0.9514 0.0250 236.8242);
  --accent-foreground: oklch(0.3791 0.1378 265.5222);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9276 0.0058 264.5313);
  --input: oklch(0.9276 0.0058 264.5313);
  --ring: oklch(0.6231 0.1880 259.8145);
  --chart-1: oklch(0.6231 0.1880 259.8145);
  --chart-2: oklch(0.5461 0.2152 262.8809);
  --chart-3: oklch(0.4882 0.2172 264.3763);
  --chart-4: oklch(0.4244 0.1809 265.6377);
  --chart-5: oklch(0.3791 0.1378 265.5222);
  --sidebar: oklch(0.9846 0.0017 247.8389);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.6231 0.1880 259.8145);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9514 0.0250 236.8242);
  --sidebar-accent-foreground: oklch(0.3791 0.1378 265.5222);
  --sidebar-border: oklch(0.9276 0.0058 264.5313);
  --sidebar-ring: oklch(0.6231 0.1880 259.8145);
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: oklch(0.2046 0 0);
  --foreground: oklch(0.9219 0 0);
  --card: oklch(0.2686 0 0);
  --card-foreground: oklch(0.9219 0 0);
  --popover: oklch(0.2686 0 0);
  --popover-foreground: oklch(0.9219 0 0);
  --primary: oklch(0.6231 0.1880 259.8145);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2686 0 0);
  --secondary-foreground: oklch(0.9219 0 0);
  --muted: oklch(0.2686 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.3791 0.1378 265.5222);
  --accent-foreground: oklch(0.8823 0.0571 254.1284);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3715 0 0);
  --input: oklch(0.3715 0 0);
  --ring: oklch(0.6231 0.1880 259.8145);
  --chart-1: oklch(0.7137 0.1434 254.6240);
  --chart-2: oklch(0.6231 0.1880 259.8145);
  --chart-3: oklch(0.5461 0.2152 262.8809);
  --chart-4: oklch(0.4882 0.2172 264.3763);
  --chart-5: oklch(0.4244 0.1809 265.6377);
  --sidebar: oklch(0.2046 0 0);
  --sidebar-foreground: oklch(0.9219 0 0);
  --sidebar-primary: oklch(0.6231 0.1880 259.8145);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.3791 0.1378 265.5222);
  --sidebar-accent-foreground: oklch(0.8823 0.0571 254.1284);
  --sidebar-border: oklch(0.3715 0 0);
  --sidebar-ring: oklch(0.6231 0.1880 259.8145);
}

/* Newsletter specific styling */
.newsletter-container {
  background-color: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
}

.newsletter-card {
  background-color: var(--card);
  color: var(--card-foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
}

.newsletter-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.newsletter-card-header {
  background-color: var(--muted);
  color: var(--muted-foreground);
  border-bottom: 1px solid var(--border);
  padding: 1rem 1.5rem;
  border-radius: var(--radius) var(--radius) 0 0;
  font-weight: 600;
}

.newsletter-card-body {
  padding: 1.5rem;
}

.newsletter-btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: 1px solid var(--primary);
  border-radius: var(--radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.newsletter-btn-primary:hover {
  background-color: var(--primary);
  filter: brightness(1.1);
  color: var(--primary-foreground);
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.newsletter-btn-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.newsletter-btn-secondary:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.newsletter-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.newsletter-stat-card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem;
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
}

.newsletter-stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.newsletter-stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  display: block;
  margin-bottom: 0.5rem;
}

.newsletter-stat-label {
  color: var(--muted-foreground);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.newsletter-quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.newsletter-action-tile {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem 1rem;
  text-align: center;
  text-decoration: none;
  color: var(--foreground);
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.newsletter-action-tile:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.newsletter-action-icon {
  font-size: 1.5rem;
  color: var(--primary);
}

.newsletter-action-tile:hover .newsletter-action-icon {
  color: var(--accent-foreground);
}

.newsletter-table {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.newsletter-table th {
  background-color: var(--muted);
  color: var(--muted-foreground);
  font-weight: 600;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
}

.newsletter-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
}

.newsletter-table tr:last-child td {
  border-bottom: none;
}

.newsletter-table tr:hover {
  background-color: var(--accent);
}

.newsletter-breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-bottom: 1.5rem;
}

.newsletter-breadcrumb-item {
  color: var(--muted-foreground);
}

.newsletter-breadcrumb-item.active {
  color: var(--foreground);
  font-weight: 500;
}

.newsletter-breadcrumb-item a {
  color: var(--primary);
  text-decoration: none;
}

.newsletter-breadcrumb-item a:hover {
  text-decoration: underline;
}

.newsletter-form-control {
  background-color: var(--background);
  border: 1px solid var(--input);
  border-radius: var(--radius);
  padding: 0.5rem 0.75rem;
  color: var(--foreground);
  transition: all 0.2s ease-in-out;
}

.newsletter-form-control:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring);
}

.newsletter-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: calc(var(--radius) - 2px);
}

.newsletter-badge-success {
  background-color: var(--chart-2);
  color: var(--primary-foreground);
}

.newsletter-badge-warning {
  background-color: var(--chart-3);
  color: var(--primary-foreground);
}

.newsletter-badge-danger {
  background-color: var(--destructive);
  color: var(--destructive-foreground);
}

.newsletter-badge-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
}

/* Additional utility classes */
.newsletter-muted-text {
  color: var(--muted-foreground);
}

.newsletter-text-primary {
  color: var(--primary);
}

.newsletter-text-success {
  color: var(--chart-2);
}

.newsletter-text-warning {
  color: var(--chart-3);
}

.newsletter-text-danger {
  color: var(--destructive);
}

.newsletter-bg-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.newsletter-bg-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
}

.newsletter-bg-muted {
  background-color: var(--muted);
  color: var(--muted-foreground);
}

/* List group overrides for newsletter */
.newsletter-list-group {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
}

.newsletter-list-group-item {
  background-color: transparent;
  border-color: var(--border);
  color: var(--foreground);
}

.newsletter-list-group-item:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

/* Alert styles */
.newsletter-alert {
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem;
  margin-bottom: 1rem;
}

.newsletter-alert-info {
  background-color: var(--accent);
  color: var(--accent-foreground);
  border-color: var(--primary);
}

.newsletter-alert-success {
  background-color: var(--chart-2);
  color: var(--primary-foreground);
  border-color: var(--chart-2);
}

.newsletter-alert-warning {
  background-color: var(--chart-3);
  color: var(--primary-foreground);
  border-color: var(--chart-3);
}

.newsletter-alert-danger {
  background-color: var(--destructive);
  color: var(--destructive-foreground);
  border-color: var(--destructive);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .newsletter-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .newsletter-quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }

  .newsletter-card-body {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .newsletter-stats-grid {
    grid-template-columns: 1fr;
  }

  .newsletter-quick-actions {
    grid-template-columns: 1fr;
  }
}
