<!-- front/dashboard/partner_dashboard.html -->

{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load crispy_forms_filters %}

{% block title %}{{ block.super }} - {% trans "Profile" %}{% endblock %}

{% block extra_content_1 %}

<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name active">{% trans "Partner Panel" %}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Affiliate Link -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h2 class="db-section-title">{% trans "Your Affiliate Link" %}</h2>   
                <hr>
                <div class="mb-4 ms-4 me-4">
                    <p>{{ affiliate_link }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h2 class="db-section-title">{% trans "Your Commissions" %}</h2>   
                <hr>
                <div class="mb-4 ms-4 me-4">
                    <p><strong>{% trans "Current Commission Rate:" %}</strong> {{ current_commission_rate }}%</p>
                    <p><strong>{% trans "Total Commission:" %}</strong> {{ total_commission }}</p>
                </div>
            </div>
        </div>

        <!-- Referred Users -->
        <div class="col-lg-12 col-md-12 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h2 class="db-section-title">{% trans "Your Clients" %}</h2> 
                <hr>
                <div class="mb-4 ms-4 me-4">
                    <ul>
                        {% for user in referred_users %}
                            <li>{{ user.user.username }} - {% trans "Registered" %}: {{ user.user.date_joined }}</li>
                        {% empty %}
                            <li>{% trans "No referred users." %}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>

       

        <div class="col-lg-12 col-md-12 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h3 class="db-section-title">{% trans "Withdrawal Cycles and History" %}</h3>   
                <hr>
                <table class="table table-striped my-custom-table">
                    <thead>
                        <tr>
                            <th>{% trans "Cycle Start" %}</th>
                            <th>{% trans "Cycle End" %}</th>
                            <th>{% trans "Total Commission in Cycle" %}</th>
                            <th>{% trans "Withdrawal Status" %}</th>
                            <th>{% trans "Withdrawal Amount" %}</th>
                            <th>{% trans "Processed Date" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for data in cycle_data %}
                            <tr>
                                <td>{{ data.cycle.start_date|date:"Y-m-d" }}</td>
                                <td>{{ data.cycle.end_date|date:"Y-m-d" }}</td>
                                <td>{{ data.total_commission|floatformat:2 }}</td>
                                <td>{{ data.withdrawal_status }}</td>
                                <td>{{ data.withdrawal_amount|floatformat:2 }}</td>
                                <td>
                                    {% if data.processed_at %}
                                        {{ data.processed_at|date:"Y-m-d H:i" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6">{% trans "No withdrawal cycles available." %}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="col-lg-12 col-md-12 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h3 class="db-section-title">{% trans "Commission Details:" %}</h3>   
                <hr>
                    <table class="my-custom-table table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Purchased Products" %}</th>
                                <th>{% trans "Net Order Value" %}</th>
                                <th>{% trans "Gross Order Value" %}</th>
                                <th>{% trans "Commission Rate (%)" %}</th>
                                <th>{% trans "Commission Amount" %}</th>
                                <th>{% trans "Description" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for commission in commissions %}
                            <tr>
                                <td>{{ commission.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    {% for item in commission.related_order.items.all %}
                                        {{ item.product.description }} ({{ item.product.value }}){% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                </td>
                                <td>{{ commission.related_order.price }}</td>
                                <td>{{ commission.related_order.price_incl_tax }}</td>
                                <td>{{ commission.commission_rate }}%</td>
                                <td>{{ commission.amount }}</td>
                                <td>{% trans "Purchased by" %} {{ commission.related_event.user.username }}</td>
                            </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="7">{% trans "No commissions available." %}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
