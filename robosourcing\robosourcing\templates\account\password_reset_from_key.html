{% extends "account/base.html" %}
{% load crispy_forms_filters %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block head_title %}{% trans "Change Password" %}{% endblock %}

{% block title %}
    {{ block.super }} - {% trans "Change Password" %}
{% endblock title %}

{% block content %}
<div class="container">
    <div class="row justify-content-center _text-light">
        <div class="col-md-8 col-lg-6 col-xxl-5">
            <h1>{% if token_fail %}{% trans "Bad Token" %}{% else %}{% trans "Change Password" %}{% endif %}</h1>

            {% if token_fail %}
                {% url 'account_reset_password' as passwd_reset_url %}
                <p>{% blocktrans %}The password reset link was invalid, possibly because it has already been used.  Please request a <a href="{{ passwd_reset_url }}">new password reset</a>.{% endblocktrans %}</p>
            {% else %}
                <form method="POST" action="{{ action_url }}">
                {% csrf_token %}
                {{ form|crispy }}
                <input class="btn btn-primary" type="submit" name="action" value="{% trans 'Change password' %}"/>
                </form>
                <p class="text-center text-muted"><i class="fa-solid fa-keyboard fa-10x"></i></p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
