{% extends "socialaccount/base.html" %}
{% load i18n %}

{% block head_title %}{% trans "Sign In" %}{% endblock %}

{% block title %}
    {{ block.super }} - {% trans "Sign In" %}
{% endblock title %}

{% block content %}
<div class="container">
    <div class="row justify-content-center _text-light">
        <section class="col-md-8 col-lg-6 col-xxl-5">
        {% if process == "connect" %}
            <h1>{% blocktrans with provider.name as provider %}Connect {{ provider }}{% endblocktrans %}</h1>
            <p>{% blocktrans with provider.name as provider %}You are about to connect a new third party account from {{ provider }}.{% endblocktrans %}</p>
        {% else %}
            <h1>{% blocktrans with provider.name as provider %}Sign In Via {{ provider }}{% endblocktrans %}</h1>
            <p>{% blocktrans with provider.name as provider %}You are about to sign in using a third party account from {{ provider }}.{% endblocktrans %}</p>
        {% endif %}
          <form method="post">
            {% csrf_token %}
            <button class="btn btn-primary" type="submit">{% trans "Continue" %}</button>
          </form>
        </section>
    </div>
</div>
{% endblock %}
