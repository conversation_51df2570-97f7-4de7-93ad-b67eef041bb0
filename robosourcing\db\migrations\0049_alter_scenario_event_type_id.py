# Generated by Django 4.2.13 on 2025-05-29 20:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0048_taggroup_alter_tag_options_remove_tag_image_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='scenario_event',
            name='type_id',
            field=models.IntegerField(choices=[(0, 'Sent to moderation'), (1, 'Assigned to moderator'), (2, '<PERSON><PERSON><PERSON> in moderation'), (3, '<PERSON><PERSON><PERSON> rejected'), (4, '<PERSON><PERSON><PERSON> accepted'), (5, '<PERSON><PERSON><PERSON> added to favorites'), (6, 'Scenario file dawnloaded'), (7, '<PERSON><PERSON><PERSON> suspended'), (8, '<PERSON><PERSON><PERSON> restored'), (9, '<PERSON><PERSON><PERSON> canceled'), (10, 'Return Scenario to pool'), (11, '<PERSON><PERSON><PERSON> removed from favorites'), (12, '<PERSON><PERSON><PERSON> completed'), (13, '<PERSON><PERSON><PERSON> assigned to robot')], default=0),
        ),
    ]
