from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import List

from django.db.models import Q, Value, <PERSON><PERSON>anField
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid
from django.http import JsonResponse
from django.shortcuts import redirect, get_object_or_404
from django.contrib import messages
from django.core.mail import send_mail
from django.contrib.auth.models import User, Group
from django.core.exceptions import ObjectDoesNotExist
from uuid import UUID
import logging
from django.db import transaction

from .utils.exceptions import robo_exception_handler
from scenarios.serializers import ScenarioSerializer


from . import forms

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status

from db.serializers import ElementSerializer
from api.serializers import *
from db.models import FavoriteScenario, Robot, Credit, Hid, Pid, Scenario_Category, UserStorage, Element, \
    Scenario_Event, Sc<PERSON>rio_Event_Message, Scenario, ScenarioTag, Tag, DownloadPlatform, DownloadItem, DownloadEvent, \
    Arch_Robot, StartScenario
from profiles.models import UserConnectKey, RobotResetKey, UserProfile

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

# geoip2 reader
import geoip2.database
from robosourcing.settings import GEOIP2_CITY_DB_PATH

User = get_user_model()


@api_view(['GET'])
# @permission_classes([IsAuthenticated])
def get_elements(request, *args, **kwargs):
    instance = Element.objects.all()
    data = {}
    if instance:
        data = ElementSerializer(list(instance)).data
    return Response(data)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['get'],
                     query_serializer=RobotActionAviableSerializer,
                     operation_summary='Checks if the execution of the scenario is possible',
                     operation_id=None,
                     operation_description='Checks if the execution of the scenario is possible',
                     responses={status.HTTP_200_OK: DetailedResponseSerializer,
                                '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@robo_exception_handler
@permission_classes([IsAuthenticated])
def robot_action_availble(request, *args, **kwargs):
    serializer = RobotInfoSerializer(data=request.query_params)
    serializer.is_valid(raise_exception=True)

    if not Robot.objects.filter(rid=serializer.validated_data['rid']).exists():
        response = DetailedResponseSerializer(detail='Robot does not exists!')
        return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
    else:
        robot = Robot.objects.get(rid=serializer.validated_data['rid'])

    if not Scenario.objects.filter(pk=serializer.validated_data['sid']).exists():
        response = DetailedResponseSerializer(detail='Scenario does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    else:
        scenario = Scenario.objects.get(pk=serializer.validated_data['sid'])

    credits = Credit.objects.get(user=robot.owner)

    if credits.credits - scenario.price >= 0:
        restricted_statuses = [Scenario.ScenarioStatus.SUSPENDED, Scenario.ScenarioStatus.CANCELED]
        if scenario.status in restricted_statuses:
            detailed_info = DetailedResponseSerializer(detail=f'The scenario is {scenario.status}!')
            return Response(detailed_info.data, status=status.HTTP_202_ACCEPTED)
        else:
            detailed_info = DetailedResponseSerializer(detail='The scenario is feasible.')
            return Response(detailed_info.data, status=status.HTTP_200_OK)
    else:
        detailed_info = DetailedResponseSerializer(detail='Not enough credits to perform the scenario!')
        return Response(detailed_info.data, status=status.HTTP_403_FORBIDDEN)


LOGGER = logging.getLogger('django')

@swagger_auto_schema(tags=["TESTS", "BotieApp"], methods=['post'],
                     request_body=UploadScenarioSerializer,
                     operation_summary='Przesłanie scenariusza do BotieWeb',
                     operation_id=None,
                     operation_description='Przesłanie scenariusza',
                     responses={status.HTTP_200_OK: ScenarioSerializer,
                                '>=400': DetailedResponseSerializer},)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic()
@robo_exception_handler
def robot_action_create_scenario(request, *args, **kwargs):
    LOGGER.info(f'Execute create scenario request:\n{request}')
    serializer = UploadScenarioSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    LOGGER.info(f'Serializer data:\n{serializer}')

    upload_mode = serializer.validated_data.pop('upload_mode', None)

    if upload_mode != Scenario.ScenarioUploadMode.NEW:
        response = DetailedResponseSerializer(detail='Wrong upload mode!')
        return Response(response.data, status=status.HTTP_403_FORBIDDEN)
    
    # remove Many2Many relations from serializer to baypass "Direct assignment to the forward side of a many-to-many set is prohibited." exception
    t = serializer.validated_data.pop('tags', None)
    c = serializer.validated_data.pop('categories', None)
    a = serializer.validated_data.pop('apps', None)
    h = serializer.validated_data.pop('keyword_hashes', None)
    scenario = serializer.save(owner=request.user,
                               previous_id = None)
    scenario.root_id = scenario
    scenario.branch_id = scenario
    scenario.save()
    if scenario.available in (Scenario.AvailabilityStatus.PRIVATE, Scenario.AvailabilityStatus.ACCOUNT):
        scenario._increment_scenario_version()
        scenario.save()

    if t is not None:
        print(t)
        tags = TagSerializer(data=t, many=True)
        tags.is_valid(raise_exception=True)
        for tag in tags.data:
            tag_obj, created = Tag.objects.get_or_create(**tag)
            scenario.tags.add(tag_obj)
    if c is not None:
        categories = CategorySerializer(data=c, many=True)
        categories.is_valid(raise_exception=True)
        for cat in categories.data:
            cat_obj, created = Category.objects.get_or_create(**cat)
            scenario.categories.add(cat_obj)
    if a is not None:
        apps = ProgramSerializer(data=a, many=True)
        apps.is_valid(raise_exception=True)
        for app in apps.data:
            app_obj, created = Program.objects.get_or_create(**app)
            scenario.apps.add(app_obj)
    if h is not None:
        hashes = KeywordHashSerializer(data=h, many=True)
        hashes.is_valid(raise_exception=True)
        for hash in hashes.data:
            print(hash)
            hash_obj, created = KeywordsHash.objects.get_or_create(**hash)
            scenario.keywords_hashes.add(hash_obj)

    # Utwórz wpis w UserStorage
    try:
        robot_id = serializer.validated_data.get('rid')
        robot = Robot.objects.get(rid=robot_id) if robot_id else None
        UserStorage.objects.create(user=request.user, sid=scenario, rid=robot)
        response = ScenarioSerializer(scenario).data
        # response = UploadScenarioResponseSerializer(detail='Scenario uploaded!', sid=scenario.sid)
        return Response(response, status=status.HTTP_200_OK)
    except Robot.DoesNotExist:
        response = DetailedResponseSerializer(detail='No robot found with the provided ID!')
        return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        LOGGER.error(f"Error creating UserStorage entry: {str(e)}")
        response = DetailedResponseSerializer(detail=str(e))
        return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
    


@swagger_auto_schema(tags=["Robots", "BotieApp", "TESTS"], methods=['post'],
                     request_body=UploadScenarioSerializer,
                     operation_summary='Przesłanie powiązanego scenariusza do BotieWeb',
                     operation_id=None,
                     operation_description='Przesłanie powiązanego scenariusza',
                     responses={status.HTTP_200_OK: ScenarioSerializer,
                                '>=400': DetailedResponseSerializer},)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic()
@robo_exception_handler
def robot_action_create_related_scenario(request, sid, *args, **kwargs):
    LOGGER.info(f'Execute create releated scenario request:\n{request}\nReleated SID: {sid}')
    serializer = UploadScenarioSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    LOGGER.info(f'Serializer data:\n{serializer.validated_data}')

    upload_mode = serializer.validated_data.pop('upload_mode', None)

    if serializer.validated_data.get('upload_mode') == Scenario.ScenarioUploadMode.NEW:
        response = DetailedResponseSerializer(detail='Wrong upload mode!')
        return Response(response.data, status=status.HTTP_403_FORBIDDEN)
    
    # remove Many2Many relations from serializer to baypass "Direct assignment to the forward side of a many-to-many set is prohibited." exception
    t = serializer.validated_data.pop('tags', None)
    c = serializer.validated_data.pop('categories', None)
    a = serializer.validated_data.pop('apps', None)
    h = serializer.validated_data.pop('keyword_hashes', None)

    related_scenario = Scenario.objects.get(pk=sid)
    new_root_id = related_scenario.get_root() 
    if upload_mode == Scenario.ScenarioUploadMode.BRANCH:
        scenario = serializer.save(owner = request.user,
                                   root_id = new_root_id,
                                   previous_id = related_scenario)
        scenario.branch_id = scenario
        scenario.save()
    elif upload_mode == Scenario.ScenarioUploadMode.VERSION:
        scenario = serializer.save(owner = request.user,
                                   root_id = new_root_id,
                                   previous_id = related_scenario)
        branch_scenario = Scenario.objects.get(pk=related_scenario.branch_id.sid)
        scenario.branch_id = branch_scenario
        scenario.save()
    else:
        response = DetailedResponseSerializer(detail='Wrong upload mode!')
        return Response(response.data, status=status.HTTP_403_FORBIDDEN)
    
    if scenario.available in (Scenario.AvailabilityStatus.PRIVATE, Scenario.AvailabilityStatus.ACCOUNT):
        scenario._increment_scenario_version()
        scenario.save()
    
    if t is not None:
        print(t)
        tags = TagSerializer(data=t, many=True)
        tags.is_valid(raise_exception=True)
        for tag in tags.data:
            tag_obj, created = Tag.objects.get_or_create(**tag)
            scenario.tags.add(tag_obj)
    if c is not None:
        categories = CategorySerializer(data=c, many=True)
        categories.is_valid(raise_exception=True)
        for cat in categories.data:
            cat_obj, created = Category.objects.get_or_create(**cat)
            scenario.categories.add(cat_obj)
    if a is not None:
        apps = ProgramSerializer(data=a, many=True)
        apps.is_valid(raise_exception=True)
        for app in apps.data:
            app_obj, created = Program.objects.get_or_create(**app)
            scenario.apps.add(app_obj)
    if h is not None:
        hashes = KeywordHashSerializer(data=h, many=True)
        hashes.is_valid(raise_exception=True)
        for hash in hashes.data:
            print(hash)
            hash_obj, created = KeywordsHash.objects.get_or_create(**hash)
            scenario.keywords_hashes.add(hash_obj)

    # Utwórz wpis w UserStorage
    try:
        robot_id = serializer.validated_data.get('rid')
        robot = Robot.objects.get(rid=robot_id) if robot_id else None
        UserStorage.objects.create(user=request.user, sid=scenario, rid=robot)
        response = ScenarioSerializer(scenario).data
        # response = UploadScenarioResponseSerializer(detail='Scenario uploaded!', sid=scenario.sid)
        return Response(response, status=status.HTTP_200_OK)
    except Robot.DoesNotExist:
        response = DetailedResponseSerializer(detail='No robot found with the provided ID!')
        return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        LOGGER.error(f"Error creating UserStorage entry: {str(e)}")
        response = DetailedResponseSerializer(detail=str(e))
        return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
    

@swagger_auto_schema(tags=["TESTS", "BotieApp"], methods=['put'],
                     request_body=UploadScenarioSerializer,
                     operation_summary='Aktualizacja scenariusza w BotieWeb',
                     operation_id=None,
                     operation_description='Aktualizacja scenariusza',
                     responses={status.HTTP_200_OK: UploadScenarioResponseSerializer,
                                '>=400': DetailedResponseSerializer},)
@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@transaction.atomic()
@robo_exception_handler
def robot_action_update_scenario(request, sid, *args, **kwargs):
    scenario = Scenario.objects.filter(pk=sid)
    if not scenario.exists():
        response = DetailedResponseSerializer(detail='Scenario not exists!!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    org_scenario = Scenario.objects.get(pk=sid)
    serializer = UploadScenarioSerializer(org_scenario, data=request.data, partial=True)
    serializer.is_valid(raise_exception=True)
    # remove Many2Many relations from serializer to baypass "Direct assignment to the forward side of a many-to-many set is prohibited." exception
    t = serializer.validated_data.pop('tags', None)
    c = serializer.validated_data.pop('categories', None)
    a = serializer.validated_data.pop('apps', None)
    h = serializer.validated_data.pop('keyword_hashes', None)
    scenario = serializer.save()
    scenario.tags.clear()
    scenario.categories.clear()
    scenario.apps.clear()
    scenario.keywords_hashes.clear()
    
    if t is not None:
        tags = TagSerializer(data=t, many=True)
        tags.is_valid(raise_exception=True)
        for tag in tags.data:
            tag_obj, created = Tag.objects.get_or_create(**tag)
            scenario.tags.add(tag_obj)
    if c is not None:
        categories = CategorySerializer(data=c, many=True)
        categories.is_valid(raise_exception=True)
        for cat in categories.data:
            cat_obj, created = Category.objects.get_or_create(**cat)
            scenario.categories.add(cat_obj)
    if a is not None:
        apps = ProgramSerializer(data=a, many=True)
        apps.is_valid(raise_exception=True)
        for app in apps.data:
            app_obj, created = Program.objects.get_or_create(**app)
            scenario.apps.add(app_obj)
    if h is not None:
        hashes = KeywordHashSerializer(data=h, many=True)
        hashes.is_valid(raise_exception=True)
        for hash in hashes.data:
            hash_obj, created = KeywordsHash.objects.get_or_create(**hash)
            scenario.keywords_hashes.add(hash_obj)

    # Utwórz wpis w UserStorage
    try:
        robot_id = serializer.validated_data.get('rid')
        robot = Robot.objects.get(rid=robot_id) if robot_id else None
        UserStorage.objects.create(user=request.user, sid=scenario, rid=robot)
        response = UploadScenarioResponseSerializer(detail='Scenario uploaded!', sid=scenario.sid)
        return Response(response.data, status=status.HTTP_200_OK)
    except Robot.DoesNotExist:
        response = DetailedResponseSerializer(detail='No robot found with the provided ID!')
        return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        exception_text = str(e)
        LOGGER.error(f"Error creating UserStorage entry: {exception_text}")
        response = DetailedResponseSerializer(detail=exception_text)
        return Response(response.data, status=status.HTTP_400_BAD_REQUEST)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['get'],
                     responses={status.HTTP_200_OK: ScenarioSerializer, 
                                '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@robo_exception_handler
def scenario_detail(request, sid, *args, **kwargs):
    scenario = Scenario.objects.filter(pk=sid).first()
    if scenario.exists():
        scenario_obj = ScenarioSerializer(scenario.get())
        return Response(scenario_obj.data, status=status.HTTP_200_OK)
    else:
        response = DetailedResponseSerializer(detail='Scenario not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                     request_body=LockCreditsSerializer,
                     operation_summary='Zablokowanie kredytów',
                     operation_id=None,
                     operation_description='Zablokowanie kredytów',
                     responses={status.HTTP_200_OK: LockCreditsResponseSerializer,
                                '>=400': DetailedResponseSerializer},)
@api_view(['POST'])
@robo_exception_handler
@permission_classes([IsAuthenticated])
def robot_action_lock_credits(request, *args, **kwargs):
    serializer = LockCreditsSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    if not Robot.objects.filter(rid=serializer.validated_data['rid']).exists():
        response = DetailedResponseSerializer(detail='Robot does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    else:
        robot = Robot.objects.get(rid=serializer.validated_data['rid'])
        if request.user != robot.owner:
            response = DetailedResponseSerializer(detail='You can request only for your own robots!')
            return Response(response.data, status=status.HTTP_406_NOT_ACCEPTABLE)

    if not serializer.validated_data['private_scenario']:
        if not Scenario.objects.filter(pk=serializer.validated_data['sid']).exists():
            response = DetailedResponseSerializer(detail='Scenario does not exists!')
            return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    
    lock_successful, locked_credits, cached_credits = robot.lock_credits(credits_to_lock=serializer.validated_data['credits_to_lock'])
    if lock_successful:
        response = LockCreditsResponseSerializer(detail='OK', locked_credits=locked_credits, cached_credits=cached_credits)
        return Response(response.data, status=status.HTTP_200_OK)
    else:
        response = DetailedResponseSerializer(detail='Lock credits error!')
        return Response(response.data, status=status.HTTP_403_FORBIDDEN)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                     request_body=CreditsPaymentSerializer,
                     operation_summary='Rozliczenie kredytów',
                     operation_id=None,
                     operation_description='Rozlicza wykonanie scenariusza na podstawie realnie zużytych kredytów.',
                     responses={status.HTTP_200_OK: LockCreditsResponseSerializer,
                                '>=400': DetailedResponseSerializer},)
@api_view(['POST'])
@robo_exception_handler
@permission_classes([IsAuthenticated])
@transaction.atomic()
def robot_action_payment(request, *args, **kwargs):
    serializer = CreditsPaymentSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    request_locked_credits = serializer.validated_data['locked_credits']
    request_used_credits = serializer.validated_data['used_credits']
    offline_payment = serializer.validated_data['offline_payment']
    private_scenario = serializer.validated_data['private_scenario']

    scenario = Scenario.objects.filter(pk=serializer.validated_data['sid']).first()
    
    if not Robot.objects.filter(rid=serializer.validated_data['rid']).exists():
        response = DetailedResponseSerializer(detail='Robot does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    else:
        robot = Robot.objects.get(rid=serializer.validated_data['rid'])
        if request.user != robot.owner:
            response = DetailedResponseSerializer(detail='You can request only for your own robots!')
            return Response(response.data, status=status.HTTP_406_NOT_ACCEPTABLE)
    
    if not offline_payment:
        if not private_scenario and not scenario:
            response = DetailedResponseSerializer(detail='Scenario does not exists!')
            return Response(response.data, status=status.HTTP_404_NOT_FOUND)
        if not robot.locked:
            response = DetailedResponseSerializer(detail='Scenario has not been initiated!')
            return Response(response.data, status=status.HTTP_409_CONFLICT)
        consume_successful, info,  locked_credits, cached_credits = robot.consume_credits(used_credits=request_used_credits)
    else:
        consume_successful, info, locked_credits, cached_credits = robot.consume_offline_credits(used_credits=request_used_credits)
    if consume_successful:
        if scenario:
            scenario.register_scenario_event(event_type=Scenario_Event.EventType.SCEN_COMPLETED,
                                             rid=serializer.validated_data['rid'],
                                             uid=request.user.id)
        response = LockCreditsResponseSerializer(detail=info, locked_credits=locked_credits, cached_credits=cached_credits)
        return Response(response.data, status=status.HTTP_200_OK)
    else:
        response = DetailedResponseSerializer(detail=info)
        return Response(response.data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def post_elements(request, *args, **kwargs):
    serializer = ElementSerializer(data=request.data, many=True)
    if serializer.is_valid(raise_exception=True):
        return Response(serializer.data)
    else:
        return Response(serializer.errors) 
    instance = Element.objects.all()
    data = {}
    if instance:
        data = ElementSerializer(list(instance)).data
    return Response(data)

@swagger_auto_schema(tags=["Robots"], methods=['post'],
                     operation_summary='Utworzenie nowego robota',
                     operation_id=None,
                     operation_description='Tworzenie nowego robota',
                     responses={201: 'HTTP_201_CREATED',
                                '>=400': 'error details',},)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_robot(request):
    if request.method == 'POST':
        form = forms.CreateRobotForm(request.POST)         
        if form.is_valid():
            robot = Robot.objects.create(owner=request.user, 
                                         name=form.cleaned_data['robot_name'], 
                                         description=form.cleaned_data['robot_description'])
            content = {
                'robot_uuid': robot.rid,
                'user': robot.owner.username,
            }
            messages.success(request, f'Robot ID: {robot.rid} created!')
            return redirect('robots')
    else:
        content = {'status': 'HTTP_405_METHOD_NOT_ALLOWED'}
        return JsonResponse(content, status=status.HTTP_405_METHOD_NOT_ALLOWED)

@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                     request_body=CreateAnonymousRobotRequestSerializer,
                     operation_summary='Utworzenie anonimowego robota',
                     operation_id=None,
                     operation_description='Tworzenie anonimowego robota',
                     responses={200: openapi.Response('response description', RobotSerializer(fields=('rid',))),
                                '>=400': 'error details',},)
@api_view(['POST'])
@robo_exception_handler
def create_anonymous_robot(request):
    if request.method == 'POST':
        serializer = CreateAnonymousRobotRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        hid_obj, hid_new = Hid.objects.get_or_create(hid=serializer.validated_data['hid'])
        pid_obj, pid_new = Pid.objects.get_or_create(pid=serializer.validated_data['pid'])
        print(hid_obj.hid)
        print(pid_obj.pid)
        robot = Robot.objects.create(hid=hid_obj, pid=pid_obj)
        response = RobotSerializer(instance=robot, fields=('rid',))
        print(response.data)
        return Response(response.data)


@api_view(['POST'])
def add_favorite_scenario(request):
    if request.method == 'POST':
        form = forms.AddScenarioToFavoritesForm(request.POST)
        if form.is_valid():
            scenario_uuid = form.cleaned_data['scenario']
            robot_ids = request.POST.getlist('robots[]')
            remove_robot_ids = request.POST.getlist('remove[]')
            user = request.user

            # Log received data for debugging
            print(f"Scenario UUID: {scenario_uuid}")
            print(f"Robot IDs to add: {robot_ids}")
            print(f"Robot IDs to remove: {remove_robot_ids}")

            # Pobierz instancję scenariusza korzystając z UUID
            scenario_instance = Scenario.objects.get(pk=scenario_uuid)

            # Dodawanie powiązań
            for robot_id in robot_ids:
                if robot_id not in remove_robot_ids:
                    # Sprawdź, czy już istnieje rekord UserStorage dla danego użytkownika, scenariusza i robota
                    if not UserStorage.objects.filter(user=user, sid=scenario_instance, rid_id=robot_id).exists():
                        # Utwórz nowy rekord UserStorage z instancją scenariusza
                        UserStorage.objects.create(user=user, sid=scenario_instance, rid_id=robot_id)

            # Usuwanie powiązań
            for robot_id in remove_robot_ids:
                UserStorage.objects.filter(user=user, sid=scenario_instance, rid_id=robot_id).delete()

            for robot_id in robot_ids:
                scenario_instance.register_scenario_event(event_type=Scenario_Event.EventType.ADD_TO_FAV,
                                                          rid=robot_id,
                                                          uid=request.user.id)

            return JsonResponse({'status': 'OK'}, status=200)
        else:
            print(form.errors)
            return JsonResponse({'status': 'BAD_REQUEST', 'errors': form.errors}, status=400)
    else:
        return JsonResponse({'status': 'METHOD_NOT_ALLOWED'}, status=405)


@swagger_auto_schema(tags=["Robots"], methods=['get'],
                     responses={200: openapi.Response('response description', ConnectKeyResponseSerializer),
                                '>=400': 'error details',},
                     operation_description='Pobieranie klucza do połączenia robota',)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_connect_key(request, *args, **kwargs):
    def _create_connect_key(request):
        return UserConnectKey.objects.create(user_id=request.user, create_time=datetime.now(tz=timezone.utc), connect_key=uuid.uuid4(), duration_time=timedelta(minutes=1))
    def _recreate_connect_key(ck):
        print(f'recreating connect_key...')
        ck.connect_key      = uuid.uuid4()
        ck.create_time      = datetime.now(tz=timezone.utc)
        ck.duration_time    = timedelta(minutes=1)
        ck.save()

    if request.method == 'GET':
        connect_key = UserConnectKey.objects.filter(user_id=request.user).first()
        if not connect_key:
            connect_key = _create_connect_key(request)
        if not connect_key.is_valid():
            _recreate_connect_key(connect_key)

        content = {
            'connect_key': connect_key.connect_key,
            'create_time': connect_key.create_time,
            'duration_time': str(connect_key.duration_time),
            }
        return JsonResponse(content, status=status.HTTP_200_OK)
    else:
        content = {'status': 'HTTP_405_METHOD_NOT_ALLOWED'}
        return JsonResponse(content, status=status.HTTP_405_METHOD_NOT_ALLOWED)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                     request_body=RobotConnectSerializer,
                     operation_summary='Przypisanie anonimowego robota do użytkownika',
                     operation_id=None,
                     operation_description='Przypisanie anonimowego robota do użytkownika',
                     responses={status.HTTP_200_OK: "This endpoint returns only HTTP status code."},)
@api_view(['POST'])
@robo_exception_handler
# @permission_classes([IsAuthenticated])
def connect_robot(request, *args, **kwargs):
    if request.method == 'POST':
        serializer = RobotConnectSerializer(data=request.data, many=False)
        serializer.is_valid(raise_exception=True)
        connect_key = UserConnectKey.objects.filter(connect_key=serializer.validated_data['connect_key']).first()
        if not connect_key:
            content = {'status': 'connect_key does not exist'}
            return JsonResponse(content, status=status.HTTP_404_NOT_FOUND)
        if not connect_key.is_valid():
            content = {'status': 'connect_key is invalid'}
            return JsonResponse(content, status=status.HTTP_406_NOT_ACCEPTABLE)
        robot   = Robot.objects.filter(rid=serializer.validated_data['rid']).first()
        if robot:
            robot.owner = connect_key.user_id
            robot.name = serializer.validated_data['name']
            robot.save()
            content = {'status': 'HTTP_200_OK'}
            return JsonResponse(content, status=status.HTTP_200_OK)
        else:
            content = {'status': 'HTTP_500_INTERNAL_SERVER_ERROR'}
            return JsonResponse(content, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    else:
        content = {'status': 'HTTP_405_METHOD_NOT_ALLOWED'}
        return JsonResponse(content, status=status.HTTP_405_METHOD_NOT_ALLOWED)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['delete'],
                     request_body=RobotDeleteSerializer,
                     operation_summary='Usunięcie robota',
                     operation_id=None,
                     operation_description='Usunięcie robota',
                     responses={status.HTTP_200_OK: "This endpoint returns only HTTP status code."},)
@api_view(['DELETE'])
@robo_exception_handler
# @permission_classes([IsAuthenticated])
def delete_robot(request, *args, **kwargs):
    serializer = RobotDeleteSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    robot   = Robot.objects.filter(rid=serializer.validated_data['rid']).first()
    if robot:
        robot.delete()
        detailed_info = DetailedResponseSerializer(detail='Robot deleted.')
        return Response(detailed_info.data, status=status.HTTP_200_OK)
    else:
        response = DetailedResponseSerializer(detail='Robot does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    

@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                    request_body=RobotUpdateSerializer,
                    operation_summary='Aktualizacja robota',
                    operation_id=None,
                    operation_description='Aktualizacja robota',
                    responses={status.HTTP_200_OK: "This endpoint returns only HTTP status code."},)
@api_view(['POST'])
@robo_exception_handler
# @permission_classes([IsAuthenticated])
def update_robot(request, *args, **kwargs):
    serializer = RobotUpdateSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    robot   = Robot.objects.filter(rid=serializer.validated_data['rid']).first()
    if robot:
        robot.name = serializer.validated_data['name']
        robot.save()
        detailed_info = DetailedResponseSerializer(detail='Robot updated.')
        return Response(detailed_info.data, status=status.HTTP_200_OK)
    else:
        response = DetailedResponseSerializer(detail='Robot does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    

@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                     request_body=RobotDeleteSerializer,
                     operation_summary='Rozłączenie robota',
                     operation_id=None,
                     operation_description='Rozłączenie robota',
                     responses={200: openapi.Response('response description', RobotSerializer),
                                '>=400': openapi.Response('response description', DetailedResponseSerializer),},)
@api_view(['POST'])
@robo_exception_handler
# @permission_classes([IsAuthenticated])
def disconnect_robot(request, *args, **kwargs):
    serializer = RobotDeleteSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    robot   = Robot.objects.filter(rid=serializer.validated_data['rid']).first()
    if robot:
        hid_obj, hid_new = Hid.objects.get_or_create(hid=robot.hid.hid)
        pid_obj, pid_new = Pid.objects.get_or_create(pid=robot.pid.pid)
        robot.delete()
        new_robot = Robot.objects.create(hid=hid_obj, pid=pid_obj)
        response = RobotSerializer(instance=new_robot, fields=('rid',))
        print(response.data)
        return Response(response.data, status=status.HTTP_200_OK)        
    else:
        response = DetailedResponseSerializer(detail='Robot does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    

@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['get'],
                     query_serializer=RobotDeleteSerializer,
                     operation_summary='Informacje o robocie',
                     operation_id=None,
                     operation_description='Informacje o robocie',
                     responses={200: openapi.Response('response description', RobotInfoResponseSerializer),
                                '>=400': openapi.Response('response description', DetailedResponseSerializer),},)

@api_view(['GET'])
@robo_exception_handler
# @permission_classes([IsAuthenticated])
def info_robot(request, *args, **kwargs):
    serializer = RobotInfoSerializer(data=request.query_params)
    serializer.is_valid(raise_exception=True)
    rid = serializer.validated_data['rid']
    
    # Sprawdzenie, czy robot jest zarchiwizowany
    if Arch_Robot.objects.filter(rid=rid).exists():
        response = DetailedResponseSerializer(detail='Robot has been archived!')
        return Response(response.data, status=status.HTTP_409_CONFLICT)
    
    # Pobranie robota lub zwrócenie 404
    robot = get_object_or_404(Robot, rid=rid)
    
    user = robot.owner
    if not user:
        response = DetailedResponseSerializer(detail='Robot is not connected to the profile!')
        return Response(response.data, status=status.HTTP_424_FAILED_DEPENDENCY)
    
    # Pobranie UserProfile lub zwrócenie błędu
    try:
        profile = UserProfile.objects.get(user=user)
    except UserProfile.DoesNotExist:
        response = DetailedResponseSerializer(detail='Robot is not connected to the profile!')
        return Response(response.data, status=status.HTTP_424_FAILED_DEPENDENCY)
    
    #FIXME: user może należeć w przyszłości do wielu grup!!!
    group = user.groups.first()
    user_role = group.name if group else None
    
    # Pobranie lub utworzenie Credit
    credits, created = Credit.objects.get_or_create(user=user)
    
    response = RobotInfoResponseSerializer(
        uid=user.id, 
        user_name=user.username,
        user_role=user_role,
        robot_credits=robot.cached_credits,
        profile_credits=credits.credits,
        locked_credits=credits.locked_credits,
    )
    
    return Response(response.data, status=status.HTTP_200_OK)

    

@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                    request_body=RidSerializer,
                    operation_summary='Request resetu robota',
                    operation_id=None,
                    operation_description='Request resetu robota',
                    responses={status.HTTP_200_OK: "This endpoint returns only HTTP status code."},)
@api_view(['POST'])
@robo_exception_handler
# @permission_classes([IsAuthenticated])
def request_reset_key(request, *args, **kwargs):
    def _create_reset_key(robot):
        return RobotResetKey.objects.update_or_create(rid=robot,
                                            create_time=datetime.now(tz=timezone.utc),
                                            duration_time=timedelta(minutes=15))
    
    serializer = RidSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    robot   = Robot.objects.get(rid=serializer.validated_data['rid'])
    user    = User.objects.get(id=robot.owner_id)
    reset_key = _create_reset_key(robot)

    # result = send_mail("Botie Reset Key",
    #                    f'Your reset key: {reset_key[0].reset_key}',
    #                    "<EMAIL>",
    #                    [f'{user.email}'],
    #                    fail_silently=False,)
    #
    # if result == 1:
    #     return Response(status=status.HTTP_200_OK)
    # else:
    #     return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # send email notification
    from db.mailer import Mailer
    mailer = Mailer()
    mail_result = mailer.password_reset_key(user, reset_key[0].reset_key)

    if mail_result:
        return Response(status=status.HTTP_200_OK)
    else:
        return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                    request_body=ResetVerificationSerializer,
                    operation_summary='Weryfikacja tokena resetującego',
                    operation_id=None,
                    operation_description='Weryfikacja tokena resetującego',
                    responses={status.HTTP_200_OK: "This endpoint returns only HTTP status code."},)
@api_view(['POST'])
@robo_exception_handler
# @permission_classes([IsAuthenticated])
def check_reset_key(request, *args, **kwargs):
    serializer = ResetVerificationSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    reset_key = RobotResetKey.objects.get(reset_key=serializer.validated_data['reset_key'])
    if reset_key.is_valid():
        return Response(status=status.HTTP_200_OK) 
    else:
        return Response(status=status.HTTP_403_FORBIDDEN)


@swagger_auto_schema(tags=["Scenarios", "BotieApp"], methods=['post'],
                    request_body=RegisterScenarioEventSerializer,
                    operation_summary='Rejestracja zdarzenia',
                    operation_id=None,
                    operation_description='Rejestracja zdarzenia',
                    responses={status.HTTP_200_OK: "This endpoint returns only HTTP status code."},)
@api_view(['POST'])
@robo_exception_handler
# @permission_classes([IsAuthenticated])
@transaction.atomic()
def register_scenario_event(request):
    LOGGER.info(f'Request data: {request.data}')
    serializer = RegisterScenarioEventSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)    
    LOGGER.info(f'erializer data: {serializer.validated_data}')

    try:
        scenario = Scenario.objects.get(sid=serializer.validated_data['sid'])
        registration_succesfull, registration_message = scenario.register_scenario_event(event_type=serializer.validated_data['event_type_id'], 
                                                                                         message=serializer.validated_data.get('message', None),
                                                                                         rid=serializer.validated_data.get('rid', None),
                                                                                         uid=serializer.validated_data.get('uid', None))

        if registration_succesfull:
            LOGGER.info(f'Executing response_message OK...')
            return Response({"message": registration_message}, status=status.HTTP_200_OK)
        else:
            LOGGER.error(f'Executing response_message ERROR...')
            return Response({"error": "Invalid event type for status update."}, status=status.HTTP_400_BAD_REQUEST)
    except Scenario.DoesNotExist as e:
        LOGGER.exception(e)
        return Response({"error": "Scenario not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        LOGGER.info(f'Unexpected error: {str(e)}')
        LOGGER.exception(e)
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        
#API for DOWNLOADS
@api_view(['GET'])
@robo_exception_handler
def get_download_platforms(request, *args, **kwargs):
    platforms = DownloadPlatform.objects.filter(active=True)
    serializer = DownloadPlatformSerializer(platforms, many=True)
    return Response(serializer.data)

@api_view(['GET'])
@robo_exception_handler
def get_latest_version(request, platform_id, *args, **kwargs):
    latest_version = DownloadItem.objects.filter(platforms__id=platform_id, is_active=True).latest('release_date')
    return Response({'id': latest_version.id})

@api_view(['GET'])
@robo_exception_handler
def get_last_ver_info(request, platform_id, *args, **kwargs):
        
    if not DownloadItem.objects.filter(platform_id=platform_id, is_default=True).exists():
        response = DetailedResponseSerializer(detail='Object does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    
    last_ver = DownloadItem.objects.filter(platform_id=platform_id, is_default=True).first()
    serializer = DownloadItemSerializer(last_ver)
    return Response(serializer.data, status=status.HTTP_200_OK)

@swagger_auto_schema(tags=["BotieApp"], methods=['post'],
                    request_body=MyScenariosSerializer,
                    operation_summary='Pobranie informacji o scenariuszach startowych i przypisanych do robota',
                    operation_id=None,
                    operation_description='Pobranie informacji o scenariuszach startowych (przypisanych odgórnie do wszystkich robotów) i przypisanych do robota przez użytkownika',
                    responses={200: openapi.Response('response description', MyScenariosDetailedResponseSerializer(many=True)),
                                '>=400': openapi.Response('response description', DetailedResponseSerializer),},)
@api_view(['POST'])
#@robo_exception_handler
@permission_classes([IsAuthenticated])
def get_my_scenarios(request, *args, **kwargs):    

    # get scenarios assigned to robot
    scenarios = None
    if request.data['rid']:

        serializer = MyScenariosSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        if not Robot.objects.filter(rid=serializer.validated_data['rid']).exists():
            response = DetailedResponseSerializer(detail='Robot does not exists!')
            return Response(response.data, status=status.HTTP_404_NOT_FOUND)
        else:
            robot = Robot.objects.get(rid=serializer.validated_data['rid'])
            if request.user != robot.owner:
                response = DetailedResponseSerializer(detail='You can request only for your own robots!')
                return Response(response.data, status=status.HTTP_406_NOT_ACCEPTABLE)

        scenarios = Scenario.objects.filter(pk__in=UserStorage.objects.filter(rid=serializer.validated_data['rid']).values('sid'))

    # get start scenarios
    current_datetime = timezone.now()
    start_scenarios = Scenario.objects.filter(Q(startscenario__isnull=False) & Q(startscenario__is_active=True) & (
                Q(startscenario__start_date__lte=current_datetime) | Q(startscenario__start_date__isnull=True)) & (
                                                          Q(startscenario__end_date__gte=current_datetime) | Q(
                                                            startscenario__end_date__isnull=True)))

    # combine with origin annotation
    start_scenarios = start_scenarios.annotate(is_robot_assigned=Value(False, output_field=BooleanField()))
    if scenarios:
        scenarios = scenarios.annotate(is_robot_assigned=Value(True, output_field=BooleanField()))
        combined_scenarios = start_scenarios.union(scenarios)
    else:
        combined_scenarios = start_scenarios

    # remove duplicates with is_robot_assigned=True as prefered
    unique_scenarios = {}
    for scenario in combined_scenarios:
        # Prefer scenarios with is_robot_assigned=True
        if scenario.sid not in unique_scenarios or scenario.is_robot_assigned:
            unique_scenarios[scenario.sid] = scenario
    combined_scenarios = list(unique_scenarios.values())

    if not combined_scenarios:
        response = DetailedResponseSerializer(detail='Scenario does not exists!')
        return Response(response.data, status=status.HTTP_204_NO_CONTENT)
    else:
        response = MyScenariosDetailedResponseSerializer(combined_scenarios, many=True)
        return Response(response.data, status=status.HTTP_200_OK)

@api_view(['GET'])
@robo_exception_handler
def get_download_versions(request, platform_id, *args, **kwargs):
    versions = DownloadItem.objects.filter(platforms__id=platform_id, is_active=True)
    data = [{'id': v.id, 'release_version': v.release_version, 'release_date': v.release_date, 'short_description': v.short_description} for v in versions]
    return Response(data)

@api_view(['GET'])
@robo_exception_handler
def get_download_version_info(request, version_id, *args, **kwargs):
    version = DownloadItem.objects.get(id=version_id)
    serializer = DownloadVersionInfoSerializer(version)
    return Response(serializer.data)

def record_download_event(version_id, ip_address, request):
    """
    Record a download event in the database.

    :param version_id: The ID of the downloaded version.
    :param request: The HTTP request object.
    :return: The newly created DownloadEvent instance.
    """
    try:
        # Retrieve the DownloadItem instance corresponding to the provided version_id
        download_item = DownloadItem.objects.get(id=version_id)

        # Extract geolocation data
        region = determine_region_from_ip(ip_address)

        # Create and save the download event
        download_event = DownloadEvent.objects.create(
            version=download_item,
            ip_address=ip_address,
            region=region
        )

        return download_event

    except ObjectDoesNotExist:
        # Handle the case where the DownloadItem does not exist
        raise ValueError(f"DownloadItem with id {version_id} does not exist.")

def determine_region_from_ip(ip_address):
    """
    Determine the geographic region from an IP address.
    Placeholder function - implement according to your requirements.

    :param ip_address: The IP address as a string.
    :return: Region as a string.
    """
    try:
        reader = geoip2.database.Reader(GEOIP2_CITY_DB_PATH)
    except FileNotFoundError:
        reader = None
    if reader:
        try:
            obj = reader.city(ip_address)
            cont = obj.continent.names['en']
            country = obj.country.names['en']
            subdiv1 = ''
            if obj.subdivisions:
                subdiv1 = obj.subdivisions[0].names['en']
            long = obj.location.longitude
            lat = obj.location.latitude
            if long > 0:
                long = f'{abs(long)}E'
            else:
                long = f'{abs(long)}W'
            if lat > 0:
                lat = f'{abs(lat)}N'
            else:
                lat = f'{abs(lat)}S'
            return f'{cont} {country} {subdiv1} {long} {lat}'
        except geoip2.errors.AddressNotFoundError:
            pass
    return ''

@api_view(['POST'])
@robo_exception_handler
def register_download_event(request, *args, **kwargs):
    version_id = request.data.get('version_id')
    # ip_address = request.data.get('ip_address')
    ip_address = request.META.get('HTTP_X_FORWARDED_FOR')
    if ip_address:
        ip_address = ip_address.split(',')[0]
    else:
        ip_address = request.META.get('REMOTE_ADDR')
    download_event = record_download_event(version_id, ip_address, request)
    serializer = RegisterDownloadEventSerializer(download_event)
    return Response(serializer.data)

@api_view(['POST'])
@robo_exception_handler
def add_download_item(request, *args, **kwargs):
    serializer = AddDownloadItemSerializer(data=request.data)
    if serializer.is_valid(raise_exception=True):
        serializer.save()
        return Response(serializer.data)
    return Response(serializer.errors)

@api_view(['PUT'])
@robo_exception_handler
def update_download_item(request, version_id, *args, **kwargs):
    try:
        download_item = DownloadItem.objects.get(id=version_id)
    except DownloadItem.DoesNotExist:
        return Response({'detail': 'Download item not found.'}, status=status.HTTP_404_NOT_FOUND)

    serializer = UpdateDownloadItemSerializer(download_item, data=request.data, partial=True)
    if serializer.is_valid(raise_exception=True):
        serializer.save()
        return Response(serializer.data)
    return Response(serializer.errors)


@swagger_auto_schema(tags=['api','BitieApp'], methods=['get'],
                     responses={status.HTTP_200_OK: ScenarioSerializer, '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def get_scenario_detail(request, sid, *args, **kwargs):    
    scenario = Scenario.objects.get(pk=sid)
    serialized_scenario = ScenarioSerializer(instance=scenario)    
    return Response(serialized_scenario.data, status=status.HTTP_200_OK)


@swagger_auto_schema(tags=['api','BitieApp'], methods=['patch'],
                     request_body=ChangeDescriptionScenarioSerializer,
                     responses={status.HTTP_200_OK: ScenarioSerializer, '>=400': DetailedResponseSerializer},)
@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def change_description(request, sid, *args, **kwargs):    
    scenario = Scenario.objects.get(pk=sid)
    serializer = ChangeDescriptionScenarioSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    scenario.name = serializer.validated_data['name']
    scenario.description = serializer.validated_data['description']
    scenario.save()
    serialized_scenario = ScenarioSerializer(instance=scenario)    
    return Response(serialized_scenario.data, status=status.HTTP_200_OK)


@swagger_auto_schema(tags=["TESTS", "BotieApp"], methods=['get'],
                     query_serializer=ScenarioCheckUpdateSerializer,
                     operation_summary='Sprawdzenie najnowszych wersji scenariuszy',
                     operation_id=None,
                     operation_description='Sprawdzenie najnowszych wersji scenariuszy',
                     responses={status.HTTP_200_OK: ScenarioLatestVersionsSerializer(many=True), '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def check_scenarios_updates(request, *args, **kwargs):
    class LatestVersion(object):
        def __init__(self, origin_sid, latest_version, latest_sid):
            self.origin_sid = origin_sid
            self.latest_version = latest_version
            self.latest_sid = latest_sid
    sids = []
    response_data = []
    querry_sids = request.GET.getlist('sids')
    for s in querry_sids:    #in case '...?keywords_hashes=123,345...' instead of proper '...?keywords_hashes=123&keywords_hashes=345...'
        s_list = s.split(',')
        sids.extend(s_list)

    scenarios = Scenario.objects.filter(sid__in=sids)
    for scenario in scenarios:
        latest_scenario = Scenario.objects.filter(branch_id=scenario.branch_id).order_by('-scenario_version').first()
        latest_version_instance = LatestVersion(origin_sid=scenario.sid, latest_version=latest_scenario.scenario_version, latest_sid=latest_scenario.sid)
        response_data.append(latest_version_instance)
    if not scenarios.exists():
        response = DetailedResponseSerializer(detail='Scenarios does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    else:          
        response = ScenarioLatestVersionsSerializer(response_data, many=True)
        return Response(response.data, status=status.HTTP_200_OK)
    

@swagger_auto_schema(tags=['TESTS', 'BitieApp'], methods=['get'],
                     operation_summary='Historia zmian wersji',
                     operation_id=None,
                     operation_description='Historia zmian wersji',
                     responses={status.HTTP_200_OK: ScenarioChangelogSerializer(many=True), '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def get_scenario_changelog(request, sid, *args, **kwargs):    
    scenario = Scenario.objects.get(pk=sid)
    scenarios = Scenario.objects.filter(branch_id=scenario.branch_id).order_by('scenario_version')
    serialized_scenarios = ScenarioChangelogSerializer(instance=scenarios, many=True)    
    return Response(serialized_scenarios.data, status=status.HTTP_200_OK)