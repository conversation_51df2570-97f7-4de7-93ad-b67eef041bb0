<ul class="pagination justify-content-center">
    <!-- Display links for First and Previous -->
    <li class="page-item">
        <a class="page-link{% if objects.has_previous %}{% else %} disabled{% endif %}"{% if objects.has_previous %} href="?page=1"{% endif %} aria-label="First">
          <span aria-hidden="true"><i class="fa fa-angles-left"></i></span>
        </a>
    </li>
    <li class="page-item">
        <a class="page-link{% if objects.has_previous %}{% else %} disabled{% endif %}"{% if objects.has_previous %} href="?page={{ objects.previous_page_number }}"{% endif %} aria-label="Previous">
          <span aria-hidden="true"><i class="fa fa-angle-left"></i></span>
        </a>
    </li>

    <!-- Display ellipsis -->
    {% if objects.number|add:'-2' > 1 %}
    <li class="page-item"><span class="page-link disabled">&hellip;</span></li>
    {% endif %}

    <!-- Display page numbers -->
    {% for i in objects.paginator.page_range %}
        {% if objects.number == i %}
            <li class="page-item active" aria-current="page">
                <span class="page-link">{{ i }}</span>
            </li>
        {% elif i > objects.number|add:'-3' and i < objects.number|add:'3' %}
            <li class="page-item">
                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
            </li>
    {% endif %}
    {% endfor %}

    <!-- Display ellipsis -->
    {% if objects.paginator.num_pages > objects.number|add:'2' %}
    <li class="page-item"><span class="page-link disabled">&hellip;</span></li>
    {% endif %}

    <!-- Display links for Next and Last -->
    <li class="page-item">
        <a class="page-link{% if objects.has_next %}{% else %} disabled{% endif %}"{% if objects.has_next %} href="?page={{ objects.next_page_number }}"{% endif %} aria-label="Next">
          <span aria-hidden="true"><i class="fa fa-angle-right"></i></span>
        </a>
    </li>
    <li class="page-item">
        <a class="page-link{% if objects.has_next %}{% else %} disabled{% endif %}"{% if objects.has_next %} href="?page={{ objects.paginator.num_pages }}"{% endif %} aria-label="Last">
          <span aria-hidden="true"><i class="fa fa-angles-right"></i></span>
        </a>
    </li>
</ul>