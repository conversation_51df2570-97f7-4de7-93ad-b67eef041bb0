# Generated by Django 4.1.9 on 2024-06-12 11:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('db', '0004_usersubscription_last_renew_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='CreditEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_time', models.DateTimeField(auto_now=True)),
                ('event_type', models.CharField(choices=[('BUY', 'BUY'), ('CHG', 'SUBSCRIPTION CHANGE'), ('TOP', 'TOP-UP'), ('LOC', 'LOCK'), ('USE', 'USAGE')], default='BUY', max_length=3)),
                ('d_package_credits', models.PositiveBigIntegerField(default=0)),
                ('d_subscription_credits', models.PositiveBigIntegerField(default=0)),
                ('package_credits', models.PositiveBigIntegerField(default=0)),
                ('subscription_credits', models.PositiveBigIntegerField(default=0)),
                ('l_package_credits', models.PositiveBigIntegerField(default=0)),
                ('l_subscription_credits', models.PositiveBigIntegerField(default=0)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
