from django.urls import path
from profiles import views


urlpatterns = [
    path('roboty', views.profile_robots, name='roboty'),
    path('block_credits', views.block_credits, name='block_credits'),
    path('kredyty', views.profile_kredyty , name='kredyty'),
    path('organizacja', views.profile_profil , name='organizacja'),
    path('profil', views.profile_profil , name='profil'),
    path('scenario_mod', views.profile_scenario_mod , name='scenario_mod'),
    path('dashboard/', views.partner_dashboard_menu, name='partner_dashboard_menu'),
    path('dashboard/commissions/', views.partner_dashboard_commission, name='partner_dashboard_commission'),
    path('dashboard/withdrawals/', views.partner_dashboard_withdrawal, name='partner_dashboard_withdrawal'),
    path('withdraw/', views.request_withdrawal, name='request_withdrawal'),
    path('robots', views.profile_robots, name='robots'),
    path('credits', views.profile_kredyty, name='credits'),
    # path('sklep', views.profile_profil, name='sklep'),
    # path('organizacja', views.profile_profil, name='organizacja'),
    path('profile', views.profile_profil, name='profile'),
    path('scenario_mod', views.profile_scenario_mod, name='scenario_mod'),
    path('scenarios', views.robot_scenarios_view, name='robot_scenarios_view'),
    path('ajax_public_scenarios', views.ajax_public_scenarios, name='ajax_public_scenarios'),
    path('ajax_favorite_scenarios', views.ajax_favorite_scenarios, name='ajax_favorite_scenarios'),
    path('ajax_private_scenarios', views.ajax_private_scenarios, name='ajax_private_scenarios'),
    path('ajax_robot_scenarios/<uuid:robot_rid>/', views.ajax_robot_scenarios, name='ajax_robot_scenarios'),
    path('add_scenario_to_favorites/', views.add_scenario_to_favorites, name='add_scenario_to_favorites'),
    path('remove_scenario_from_favorites/', views.remove_scenario_from_favorites,
         name='remove_scenario_from_favorites'),
    path('scenarios/<uuid:sid>/', views.scenario_detail, name='scenario_detail'),
    path('download_scenario_botie/<scenario_sid>', views.download_scenario_botie, name='download_scenario_botie'),
    path('scenario_mod_details/<sid>/', views.scenario_mod_details, name='scenario_mod_details'),
    path('change_scenario_status/<sid>/', views.change_scenario_status, name='change_scenario_status'),
    path('scenario/<sid>/detach_tag/<str:tag_name>/', views.detach_tag, name='detach_tag'),
    path('add-tag/<sid>/', views.add_tag_to_scenario, name='add_tag_to_scenario'),
    path('scenario_mod_details/<sid>/change_history/', views.scenario_change_history,
         name='scenario_change_history'),
    path('newsletter_dashboard/', views.newsletter_dashboard, name='profiles_newsletter_dashboard'),
    path('newsletter_subscribers/', views.newsletter_subscribers, name='profiles_newsletter_subscribers'),
    path('newsletter_campaigns/', views.newsletter_campaigns, name='profiles_newsletter_campaigns'),
    path('newsletter_campaigns/create/', views.newsletter_campaign_create, name='profiles_newsletter_campaign_create'),
    path('newsletter_campaigns/<int:campaign_id>/edit/', views.newsletter_campaign_edit, name='profiles_newsletter_campaign_edit'),
    path('newsletter_campaigns/<int:campaign_id>/send/', views.newsletter_campaign_send, name='profiles_newsletter_campaign_send'),
    path('newsletter_campaigns/<int:campaign_id>/cancel/', views.newsletter_campaign_cancel, name='profiles_newsletter_campaign_cancel'),
    path('newsletter_campaigns/<int:campaign_id>/stats/', views.newsletter_campaign_stats, name='profiles_newsletter_campaign_stats'),
    path('newsletter_templates/', views.newsletter_templates, name='profiles_newsletter_templates'),
    path('newsletter_segments/', views.newsletter_segments, name='profiles_newsletter_segments'),
    path('newsletter_analytics/', views.newsletter_analytics, name='profiles_newsletter_analytics'),
]
