# Generated by Django 4.2.13 on 2025-06-05 08:35

from django.db import migrations, models
import meta.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='StaticPageMeta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page', models.CharField(choices=[('home', 'Strona główna'), ('how it works', 'Jak to działa'), ('pricing', 'Cennik'), ('download Botie', 'Pobierz Botie'), ('contact', 'Kontakt')], max_length=50, unique=True, verbose_name='Strona')),
                ('title', models.CharField(max_length=200, verbose_name='Tytuł')),
                ('description', models.TextField(blank=True, verbose_name='Opis')),
                ('keywords', models.CharField(blank=True, max_length=255, verbose_name='Słowa kluczowe')),
                ('image', models.ImageField(blank=True, null=True, upload_to='seo/', verbose_name='Obraz do OpenGraph')),
            ],
            options={
                'verbose_name': 'Metadane statycznej strony',
                'verbose_name_plural': 'Metadane statycznych stron',
            },
            bases=(meta.models.ModelMeta, models.Model),
        ),
    ]
