from django import forms

from db.models import Robot, UserStorage

class CreateRobotForm(forms.Form):
    robot_name = forms.CharField(
        label='Robot Name', 
        max_length=100, 
        empty_value='robot', 
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        )
    robot_description = forms.CharField(
        label='Description', 
        max_length=200, 
        empty_value='my new robot', 
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        )
    
# class AddScenarioToFavoritesForm(forms.ModelForm):
#     class Meta:
#         model = UserStorage
#         fields = ['user', 'robot', 'scenario']
    
class AddScenarioToFavoritesForm(forms.Form):
    # robots = forms.MultipleChoiceField(choices=[(robot.rid, robot.name) for robot in Robot.objects.all()], widget=forms.CheckboxSelectMultiple)
    scenario = forms.UUIDField(
        label='Scenario ID',
        widget=forms.HiddenInput(),
        required=True,
    )
    
