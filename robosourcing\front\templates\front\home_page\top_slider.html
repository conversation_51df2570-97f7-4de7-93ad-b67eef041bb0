{% load i18n %}
<section class="top-slider">
  <div id="carouselExampleDark" class="carousel slide index-carousel" data-bs-ride="carousel">
    <div class="carousel-indicators">
      {% for item in carousel_items %}
        <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="{{ forloop.counter0 }}" {% if forloop.first %}class="active"{% endif %} aria-label="Slide {{ forloop.counter }}"></button>
      {% endfor %}
    </div>
    <div class="carousel-inner">
      {% get_current_language as LANGUAGE_CODE %}
      {% for item in carousel_items %}
        <div class="carousel-item {% if forloop.first %}active{% endif %}">
          
          {% if item.layout == 'c' %}
            {# Layout C – pełnoekranowe tło. Obrazy zapisane w data-light-image i data-dark-image #}
            <div class="slider-bg"
                data-light-image="{% if item.background_image %}{{ item.background_image.url }}{% endif %}"
                data-dark-image="{% if item.background_image_dark %}{{ item.background_image_dark.url }}{% endif %}">
            </div>

            <div class="carousel-caption d-flex flex-column justify-content-center align-items-center h-100"
                style="z-index: 2;">
              
              <div class="my-text-col">
                <h2 class="content-title">
                  {% if LANGUAGE_CODE == 'pl' %}
                    {{ item.title_pl|safe }}
                  {% else %}
                    {{ item.title_en|safe }}
                  {% endif %}
                </h2>
                <p class="lead">
                  {% if LANGUAGE_CODE == 'pl' %}
                    {{ item.description_pl|safe }}
                  {% else %}
                    {{ item.description_en|safe }}
                  {% endif %}
                </p>
                
                {% if item.button_url %}
                  {% if item.button_description_en or item.button_description_pl %}
                    <a class="btn btn-lg" href="{{ item.button_url }}"
                      style="background-color: {{ item.button_color }};">
                      {% if LANGUAGE_CODE == 'pl' %}
                        {{ item.button_description_pl }}
                      {% else %}
                        {{ item.button_description_en }}
                      {% endif %}
                    </a>
                  {% endif %}
                {% endif %}
                
                {% if item.button2_url %}
                  {% if item.button2_description_en or item.button2_description_pl %}
                    <a class="btn btn-lg btn-secondary" href="{{ item.button2_url }}">
                      {% if LANGUAGE_CODE == 'pl' %}
                        {{ item.button2_description_pl }}
                      {% else %}
                        {{ item.button2_description_en }}
                      {% endif %}
                    </a>
                  {% endif %}
                {% endif %}
              </div>
            </div>
          
          {% else %}
            {# Layout A i B – zawartość w kontenerze z kolumnami #}
            <div class="container position-relative h-100">
              {# Tło responsywne – używamy elementu <picture> z kilkoma <source> #}
              <div class="responsive-bg position-absolute w-100 h-100" style="top:0; left:0; z-index: 1;">
                <picture class="responsive-image">
                  {# Mobile #}
                  {% if item.background_image_mobile %}
                    {% if item.background_image_mobile_dark %}
                      <source data-light-srcset="{{ item.background_image_mobile.url }}" data-dark-srcset="{{ item.background_image_mobile_dark.url }}" media="(max-width: 767px)">
                    {% else %}
                      <source srcset="{{ item.background_image_mobile.url }}" media="(max-width: 767px)">
                    {% endif %}
                  {% endif %}
                  {# Tablet #}
                  {% if item.background_image_tablet %}
                    {% if item.background_image_tablet_dark %}
                      <source data-light-srcset="{{ item.background_image_tablet.url }}" data-dark-srcset="{{ item.background_image_tablet_dark.url }}" media="(max-width: 991px)">
                    {% else %}
                      <source srcset="{{ item.background_image_tablet.url }}" media="(max-width: 991px)">
                    {% endif %}
                  {% endif %}
                  {# Desktop #}
                  {% if item.background_image_desktop %}
                    {% if item.background_image_desktop_dark %}
                      <source data-light-srcset="{{ item.background_image_desktop.url }}" data-dark-srcset="{{ item.background_image_desktop_dark.url }}" media="(min-width: 992px)">
                    {% else %}
                      <source srcset="{{ item.background_image_desktop.url }}" media="(min-width: 992px)">
                    {% endif %}
                  {% endif %}
                  <img class="img-fluid w-100 h-100"
                       data-light-src="{% if item.background_image_desktop %}{{ item.background_image_desktop.url }}{% endif %}"
                       data-dark-src="{% if item.background_image_desktop_dark %}{{ item.background_image_desktop_dark.url }}{% endif %}"
                       src="{% if item.background_image_desktop %}{{ item.background_image_desktop.url }}{% endif %}"
                       alt="">
                </picture>
              </div>
              <div class="row h-100 align-items-center position-relative" style="z-index: 2;">
                {% if item.layout == 'a' %}
                  <div class="col-md-5 px-5 layout-img-col">
                    {% if item.graphic_image %}
                      <img src="{{ item.graphic_image.url }}"
                           alt="{% if LANGUAGE_CODE == 'pl' %}{{ item.title_pl }}{% else %}{{ item.title_en }}{% endif %}"
                           class="img-fluid" style="width:80%;">
                    {% endif %}
                  </div>
                  <div class="col-md-7 px-5 py-5">
                    <h2 class="content-title text-start">
                      {% if LANGUAGE_CODE == 'pl' %}
                        {{ item.title_pl|safe }}
                      {% else %}
                        {{ item.title_en|safe }}
                      {% endif %}
                    </h2>
                    <p class="lead text-start">
                      {% if LANGUAGE_CODE == 'pl' %}
                        {{ item.description_pl|safe }}
                      {% else %}
                        {{ item.description_en|safe }}
                      {% endif %}
                    </p>
                    {% if item.button_url %}
                      {% if item.button_description_en or item.button_description_pl %}
                        <a class="btn btn-lg" href="{{ item.button_url }}" style="background-color: {{ item.button_color }};">
                          {% if LANGUAGE_CODE == 'pl' %}
                            {{ item.button_description_pl }}
                          {% else %}
                            {{ item.button_description_en }}
                          {% endif %}
                        </a>
                      {% endif %}
                    {% endif %}
                    {% if item.button2_url %}
                      {% if item.button2_description_en or item.button2_description_pl %}
                        <a class="btn btn-lg btn-secondary" href="{{ item.button2_url }}">
                          {% if LANGUAGE_CODE == 'pl' %}
                            {{ item.button2_description_pl }}
                          {% else %}
                            {{ item.button2_description_en }}
                          {% endif %}
                        </a>
                      {% endif %}
                    {% endif %}
                  </div>
                {% elif item.layout == 'b' %}
                  <div class="col-md-7 px-5">
                    <h2 class="content-title text-start">
                      {% if LANGUAGE_CODE == 'pl' %}
                        {{ item.title_pl|safe }}
                      {% else %}
                        {{ item.title_en|safe }}
                      {% endif %}
                    </h2>
                    <p class="lead text-start">
                      {% if LANGUAGE_CODE == 'pl' %}
                        {{ item.description_pl|safe }}
                      {% else %}
                        {{ item.description_en|safe }}
                      {% endif %}
                    </p>
                    {% if item.button_url %}
                      {% if item.button_description_en or item.button_description_pl %}
                        <a class="btn btn-lg" href="{{ item.button_url }}" style="background-color: {{ item.button_color }};">
                          {% if LANGUAGE_CODE == 'pl' %}
                            {{ item.button_description_pl }}
                          {% else %}
                            {{ item.button_description_en }}
                          {% endif %}
                        </a>
                      {% endif %}
                    {% endif %}
                    {% if item.button2_url %}
                      {% if item.button2_description_en or item.button2_description_pl %}
                        <a class="btn btn-lg btn-secondary" href="{{ item.button2_url }}">
                          {% if LANGUAGE_CODE == 'pl' %}
                            {{ item.button2_description_pl }}
                          {% else %}
                            {{ item.button2_description_en }}
                          {% endif %}
                        </a>
                      {% endif %}
                    {% endif %}
                  </div>
                  <div class="col-md-5 px-5 py-5 image-col layout-img-col">
                    {% if item.graphic_image %}
                      <img src="{{ item.graphic_image.url }}"
                           alt="{% if LANGUAGE_CODE == 'pl' %}{{ item.title_pl }}{% else %}{{ item.title_en }}{% endif %}"
                           class="img-fluid" style="width:80%;">
                    {% endif %}
                  </div>
                {% endif %}
              </div>
            </div>
          {% endif %}
          
          {# Kontrolki slidera – Previous/Next #}
          <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">{% trans "Previous" %}</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">{% trans "Next" %}</span>
          </button>
        </div>
      {% endfor %}
    </div>
  </div>
</section>
<div class="divider"></div>
