from django.utils import timezone

from db.models import Credit, MarketingCampaign, Referral, Visit
from django.utils.deprecation import MiddlewareMixin


class CustomMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):

        # code to be processed before a view processing
        if request.user.is_authenticated:
            # update credits balance (dashboard menu only)
            credits = Credit.objects.filter(user=request.user)
            if credits:
                request.session['credits_balance'] = credits[0].credits

        response = self.get_response(request)

        # code to be processed after a view processing
        # ...

        return response


class ReferralTrackingMiddleware(MiddlewareMixin):
    @staticmethod
    def process_request(request):
        ref_code = request.GET.get('ref')
        if ref_code and not request.COOKIES.get(f'referral_{ref_code}'):
            try:
                campaign = MarketingCampaign.objects.get(code=ref_code)
                # set campaign start_date with first detected referral code
                if not campaign.start_date:
                    campaign.start_date = timezone.now()
                    campaign.save()
                # ip_address = request.data.get('ip_address')
                ip_address = request.META.get('HTTP_X_FORWARDED_FOR')
                if ip_address:
                    ip_address = ip_address.split(',')[0]
                else:
                    ip_address = request.META.get('REMOTE_ADDR')
                # create a new visit
                visit = Visit.objects.create(
                    campaign=campaign,
                    referrer_ip=ip_address
                )
                # save first visited page
                Referral.objects.create(
                    visit=visit,
                    path=request.path
                )
                request.session['visit_id'] = visit.id  # store visit in session
                request.ref_code = ref_code  # add ref code to request to set a cookie in response
            except MarketingCampaign.DoesNotExist:
                pass  # ignore non-existing campaigns

        # if visit ID in session, track further pages
        elif 'visit_id' in request.session:
            try:
                visit = Visit.objects.get(id=request.session['visit_id'])
                # save next visited page
                Referral.objects.create(
                    visit=visit,
                    path=request.path
                )
            except Visit.DoesNotExist:
                pass

    @staticmethod
    def process_response(request, response):
        ref_code = getattr(request, 'ref_code', None)
        if ref_code:
            # set cookie valid for 1 hour
            response.set_cookie(f'referral_{ref_code}', 'visited', max_age=1 * 60 * 60)
        return response
