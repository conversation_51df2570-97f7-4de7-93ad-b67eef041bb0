from datetime import datetime, timed<PERSON>ta
from typing import Tuple
import uuid
# from simple_history.models import HistoricalRecords
from dateutil.relativedelta import relativedelta
import logging

from django.db import models, transaction
from django.core.validators import MinLengthValidator
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
# from django.utils import timezone
# from django.forms import ValidationError
from django.db.models import Q
from django.db.models.functions import Lower
from django.utils.translation import gettext_lazy as _
# from django.db.models.signals import pre_save
# from django.dispatch import receiver
from django.utils import timezone

from profiles.models import UserProfile
from store.models import Order
# from store.models import Product
from django.conf import settings
from django.core.files.storage import default_storage
from django.db.models import Max
import base64
from PIL import Image
# from django.core.mail import send_mail

from django.contrib.contenttypes.fields import GenericRelation
from seo.models import ObjectMeta

LOGGER = logging.getLogger('django')


class CaseInsensitiveQuerySet(models.QuerySet):
    def order_by_ci(self, *fields):
        annotated_fields = [Lower(field) if isinstance(field, str) else field for field in fields]
        return self.order_by(*annotated_fields)


class CaseInsensitiveManager(models.Manager):
    def get_queryset(self):
        return CaseInsensitiveQuerySet(self.model, using=self._db)


# Hashes
class Hash(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    hash = models.CharField(max_length=64, validators=[MinLengthValidator(64)], primary_key=True)  # sha256 is 64chars (4bits/char)
    info = models.TextField(blank=True, null=True)
    date = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['hash']


# Elements
class Element(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    hash = models.ForeignKey(Hash, on_delete=models.CASCADE)
    tree_index = models.TextField(blank=False, null=False)
    hash_txt = models.TextField(blank=False, null=False)
    label = models.TextField(blank=True, null=True)
    typedef_val = models.TextField(blank=True, null=True)
    text = models.TextField(blank=True, null=True)
    date = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['hash', 'tree_index']
        ordering = ['hash', 'tree_index']


# Maps
class Map(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    source = models.ForeignKey(Hash, related_name='%(class)s_source', on_delete=models.CASCADE)
    target = models.ForeignKey(Hash, related_name='%(class)s_target',  on_delete=models.CASCADE)
    info = models.TextField(blank=True, null=True)
    date = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['source', 'target']


# Steps
class Step(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    id = models.ForeignKey(Map, on_delete=models.CASCADE)
    step = models.PositiveIntegerField(primary_key=True)
    job = models.TextField(blank=True, null=True)
    info = models.TextField(blank=True, null=True)
    date = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['id', 'step']

# #HIDs
# class Hid(models.Model):
#     hid         = models.CharField(max_length=64, validators=[MinLengthValidator(64)])  #sha256 is 64chars (4bits/char)
#     user        = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
#     local_user  = models.CharField(max_length=64, validators=[MinLengthValidator(64)])  #sha256 is 64chars (4bits/char)
#     create_time = models.DateTimeField(auto_now_add=True)
#     update_time = models.DateTimeField(auto_now=True)

#     class Meta:
#         ordering = ['hid', 'user']
#         unique_together = ['hid', 'local_user']
#         constraints = [
#                 models.CheckConstraint(
#                     check=Q(hid__isnull=False) | Q(user__isnull=False),
#                     name='hid_not_both_null'
#                 ),
#                 models.CheckConstraint(
#                     check=Q(hid__isnull=False) & Q(local_user__isnull=False),
#                     name='hid_hid_local_user_not_null'
#                 ),
#             ]


# PID
class Pid(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    pid         = models.UUIDField(primary_key=True, editable=False)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)


# HID_2
class Hid(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    hid = models.CharField(primary_key=True, max_length=64, validators=[MinLengthValidator(64)])
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)


# Robots
class AbstractRobot(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    rid             = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, auto_created=True)
    hid             = models.ForeignKey(Hid, on_delete=models.DO_NOTHING, null=True, blank=True)
    pid             = models.ForeignKey(Pid, on_delete=models.DO_NOTHING, null=True, blank=True)
    owner           = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    create_time     = models.DateTimeField(auto_now_add=True)
    last_activ_time = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


class Robot(AbstractRobot):
    class LockReason(models.TextChoices):
        USER = "USR", _("Locked by User")
        ADMIN = "ADM", _("Locked by Admin")
        JOB = "JOB", _("Locked by active job")

    name = models.CharField(max_length=100, null=True, blank=True)
    description = models.CharField(max_length=200, null=True, blank=True)
    locked = models.BooleanField(null=False, default=False)
    lock_reason = models.CharField(null=True, max_length=3, choices=LockReason.choices)
    cached_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    l_cached_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    l_package_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    l_subscription_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    min_buffer = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    # offline_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)

    @property
    def locked_credits(self):
        return self.l_cached_credits + self.l_package_credits + self.l_subscription_credits

    def is_action_possible(self, credits: int):
        credit = self.get_credits_non_update()
        if any(credits <= credit.credits, credits <= (self.cached_credits + credit.credits)):
            return True
        else:
            return False

    def get_owner_credits(self):
        profile = UserProfile.objects.get(user=self.owner)
        if profile.external_payer is None:
            return Credit.objects.filter(user=profile.user)
        else:
            return Credit.objects.filter(user=profile.external_payer)

    def get_credits_for_update(self):
        return self.get_owner_credits().select_for_update().get()

    def get_credits_non_update(self):
        return self.get_owner_credits().get()

    @transaction.atomic
    def consume_credits(self, used_credits: int) -> Tuple[bool, str, int, int]:
        consume_successful = True
        return_to_subscription = 0
        return_to_package = 0
        return_to_cached = 0
        credit = self.get_credits_for_update()
        info = 'Payment completed successfully!'

        if used_credits <= self.l_subscription_credits:
            return_to_subscription = self.l_subscription_credits - used_credits
            return_to_package = self.l_package_credits
            return_to_cached = self.l_cached_credits
        elif used_credits <= (self.l_subscription_credits + self.l_package_credits):
            return_to_package = self.l_package_credits - (used_credits + self.l_subscription_credits)
            return_to_cached = self.l_cached_credits
        elif used_credits <= (self.l_subscription_credits + self.l_package_credits + self.l_cached_credits):
            return_to_cached = self.l_cached_credits - (used_credits + self.l_subscription_credits + self.l_package_credits)
        else:
            info = 'Not enough credits to complete this payment!'
            consume_successful = False

        if consume_successful:
            credit.l_subscription_credits -= self.l_subscription_credits
            credit.subscription_credits += return_to_subscription
            self.l_subscription_credits = 0
            credit.l_package_credits -= self.l_package_credits
            credit.package_credits += return_to_package
            self.l_package_credits = 0
            self.l_cached_credits = 0
            self.cached_credits += return_to_cached
            self.locked = False
            self.lock_reason = None
            credit.save()
            self.save()
            from store.store_utils import StoreUtils
            su = StoreUtils()
            su.store_credit_event(credit.user, 'USE', credit.package_credits, credit.subscription_credits,
                                  credit.l_package_credits, credit.l_subscription_credits,
                                  dpc=return_to_package, dlpc=-self.l_package_credits,
                                  dsc=return_to_subscription, dlsc=-self.l_subscription_credits, robot=self)
        return consume_successful, info, self.locked_credits, self.cached_credits

    @transaction.atomic
    def release_locked_credits(self):
        credit = self.get_credits_for_update()
        self.cached_credits += self.l_cached_credits
        self.l_cached_credits = 0
        credit.package_credits += self.l_package_credits
        self.l_package_credits = 0
        credit.subscription_credits += self.l_subscription_credits
        self.l_subscription_credits = 0
        credit.save()
        self.save()

    @transaction.atomic
    def remove_credits_from_robot(self):
        credit = self.get_credits_for_update()
        credit.package_credits += self.l_package_credits + self.l_cached_credits
        credit.l_package_credits -= self.l_package_credits
        self.l_cached_credits = 0
        self.l_package_credits = 0
        credit.subscription_credits += self.l_subscription_credits
        credit.l_subscription_credits -= self.l_subscription_credits
        self.l_subscription_credits = 0
        credit.save()
        self.save()

    @transaction.atomic
    def consume_offline_credits(self, used_credits: int) -> Tuple[bool, str, int, int]:
        self.release_locked_credits()
        consume_successful = True
        credit = self.get_credits_for_update()

        if used_credits <= credit.subscription_credits:
            credit.subscription_credits -= used_credits
            credit.save()
        elif used_credits <= (credit.subscription_credits + credit.package_credits):
            credit.package_credits -= (used_credits - credit.subscription_credits)
            credit.subscription_credits = 0
            credit.save()
        elif used_credits <= (credit.subscription_credits + credit.package_creditss + self.cached_credits):
            self.cached_credits -= (used_credits - credit.subscription_credits - credit.package_credits)
            credit.subscription_credits = 0
            credit.package_credits = 0
            credit.save()
            self.save()
        else:
            return False, 'Not enough credits to complete this payment!', self.locked_credits, self.cached_credits
        
        if consume_successful:
            # store_credit_event :TODO 
            return consume_successful, 'Offline payment completed successfully!', self.locked_credits, self.cached_credits

    @transaction.atomic
    def lock_credits(self, credits_to_lock: int) -> Tuple[bool, int, int]:
        def _lock_pool_credits():
            credits_from_subscription = 0
            credits_from_packages = 0
            if credits_to_lock <= credit.subscription_credits:
                credit.subscription_credits -= credits_to_lock
                credit.l_subscription_credits += credits_to_lock
                self.l_subscription_credits += credits_to_lock
                from store.store_utils import StoreUtils
                su = StoreUtils()
                su.store_credit_event(credit.user, 'USE', credit.package_credits, credit.subscription_credits,
                                      credit.l_package_credits, credit.l_subscription_credits,
                                      dsc=-credits_to_lock, dlsc=credits_to_lock, robot=self)
            else:
                subscription_credits_left = credit.subscription_credits
                if credit.subscription_credits > 0:
                    credits_from_subscription += credit.subscription_credits
                    credit.subscription_credits = 0
                    credit.l_subscription_credits += credits_from_subscription
                    self.l_subscription_credits += credits_from_subscription
                credits_from_packages += (credits_to_lock - credits_from_subscription)
                credit.package_credits -= credits_from_packages
                credit.l_package_credits += credits_from_packages
                self.l_package_credits += credits_from_packages
                from store.store_utils import StoreUtils
                su = StoreUtils()
                su.store_credit_event(credit.user, 'USE', credit.package_credits, credit.subscription_credits,
                                      credit.l_package_credits, credit.l_subscription_credits,
                                      dpc=-credits_from_packages, dlpc=credits_from_packages,
                                      dsc=-subscription_credits_left, dlsc=credits_from_subscription, robot=self)
            return credits_from_subscription + credits_from_packages

        if self.locked:
            LOGGER.warning(f'Robot is locked!')
            return (False, self.locked_credits, self.cached_credits)
        lock_successful = True
        credit = self.get_credits_for_update()
        if credits_to_lock <= credit.credits:
            _lock_pool_credits()
            self.locked = True
            self.lock_reason = self.LockReason.JOB
            credit.save()
            self.save()
        elif credits_to_lock <= self.cached_credits:
            credits_from_pool = 0
            if credit.credits > 0:
                credits_from_pool = _lock_pool_credits()
            self.cached_credits -= (credits_to_lock - credits_from_pool)
            self.l_cached_credits += (credits_to_lock - credits_from_pool)
            self.locked = True
            self.lock_reason = self.LockReason.JOB
            credit.save()
            self.save()
        else:
            LOGGER.warning(f'Not enough credits!')
            lock_successful = False
        return (lock_successful, self.locked_credits, self.cached_credits)

    @transaction.atomic
    def block_cached_credits(self, credits_to_block: int) -> Tuple[bool, int]:
        if self.locked:
            LOGGER.warning(f'Robot {self.id} is locked and cannot block additional credits.')
            return (False, self.cached_credits)

        credit = self.get_credits_for_update()
        total_available_credits = credit.subscription_credits + credit.package_credits

        if credits_to_block <= total_available_credits:
            self.cached_credits += credits_to_block
            if credits_to_block <= credit.subscription_credits:
                credit.subscription_credits -= credits_to_block
            else:
                remaining = credits_to_block - credit.subscription_credits
                credit.subscription_credits = 0
                credit.package_credits -= remaining
            credit.save()
            self.save()
            LOGGER.info(f"Successfully blocked {credits_to_block} credits to cached.")
            return (True, self.cached_credits)
        else:
            LOGGER.warning(f'Insufficient credits to block: {credits_to_block} requested, {total_available_credits} available.')
            return (False, self.cached_credits)

    @transaction.atomic
    def unblock_cached_credits(self, credits_to_unblock: int) -> Tuple[bool, int]:
        if credits_to_unblock > self.cached_credits:
            LOGGER.warning("Attempt to unblock more credits than available cached credits")
            return (False, self.cached_credits)

        credit = self.get_credits_for_update()
        self.cached_credits -= credits_to_unblock
        credit.package_credits += credits_to_unblock  # Returning credits to package_credits

        self.save()
        credit.save()

        return (True, self.cached_credits)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=Q(hid__isnull=False) | Q(owner__isnull=False),
                name='robot_not_both_null'
            )
        ]
        unique_together = ['hid', 'pid']
        ordering = ['owner', 'last_activ_time']


class Arch_Robot(AbstractRobot):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    arch_time       = models.DateTimeField(auto_now_add=True)
    create_time     = models.DateTimeField(null=False, blank=False)
    last_activ_time = models.DateTimeField(null=False, blank=False)

    class Meta:
        ordering = ['owner', 'hid', 'pid', 'arch_time', 'last_activ_time']


#Credits
class Credit(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    user                    = models.OneToOneField(User, on_delete=models.CASCADE)
    package_credits         = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    subscription_credits    = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    l_package_credits       = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    l_subscription_credits  = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    create_time             = models.DateTimeField(auto_now_add=True)
    # create_time = models.DateTimeField()
    update_time             = models.DateTimeField(auto_now=True)
    # update_time = models.DateTimeField()

    @property
    def credits(self):
        return self.package_credits + self.subscription_credits
    
    @property
    def locked_credits(self):
        return self.l_package_credits + self.l_subscription_credits

    class Meta:
        ordering = ['user', 'update_time', 'create_time']


class CreditEvent(models.Model):
    @property
    def credits(self):
        return self.package_credits + self.subscription_credits

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    class EventType(models.TextChoices):
        BUY = "BUY", _("Purchase")
        CHANGE = "CHG", _("Subscription change")
        TOP_UP = "TOP", _("Credits top-up")
        FREEBIE = "FRE", _("Freebie")
        USE = "USE", _("Credits usage")

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    rid = models.ForeignKey(Robot, on_delete=models.DO_NOTHING, null=True, blank=True)
    event_time = models.DateTimeField(auto_now=True)
    # event_time = models.DateTimeField()
    event_type = models.CharField(choices=EventType.choices, default=EventType.BUY, max_length=3, null=False)
    d_package_credits = models.BigIntegerField(null=False, blank=False, default=0)
    d_subscription_credits = models.BigIntegerField(null=False, blank=False, default=0)
    d_l_package_credits = models.BigIntegerField(null=False, blank=False, default=0)
    d_l_subscription_credits = models.BigIntegerField(null=False, blank=False, default=0)
    package_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    subscription_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    l_package_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    l_subscription_credits = models.PositiveBigIntegerField(null=False, blank=False, default=0)


class Schedule(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    user                = models.OneToOneField(User, on_delete=models.CASCADE, editable=False, primary_key=True)
    active              = models.BooleanField(default=True)
    create_time         = models.DateTimeField(auto_now_add=True)
    # create_time = models.DateTimeField()
    last_update         = models.DateTimeField(auto_now=True)
    # last_update = models.DateTimeField()
    update_counter      = models.PositiveIntegerField(default=0)
    max_update_counter  = models.PositiveIntegerField(default=0)

    class Meta:
        abstract = True

    def processing_possible(self):
        if all(self.active, any(self.max_update_counter == 0, self.max_update_counter >= self.update_counter)):
            return True
        else:
            LOGGER.info(f'Processing of the scheduled task is not possible!')
            return False


def get_default_renew_date():
    """Funkcja pomocnicza do generowania domyślnej daty odnowienia"""
    if settings.SCHEDULER_TEST_MODE:
        if settings.SHORT_PERIOD == 'hour':
            short_delta = relativedelta(hours=1)
        elif settings.SHORT_PERIOD == 'day':
            short_delta = relativedelta(days=1)
    else:
        short_delta = relativedelta(months=1)

    return timezone.now() + short_delta


class UserSubscription(Schedule):

    def one_month_later(self):
        """Metoda instancji dla kompatybilności wstecznej"""
        return get_default_renew_date()

    @transaction.atomic
    def process_subscription(self):
        credits = Credit.objects.select_for_update().get_or_create(user=self.user)
        if credits[0].subscription_credits < self.value:
            subscription_credits_left = credits[0].subscription_credits
            credits[0].subscription_credits = self.value
            # store credit event
            from store.store_utils import StoreUtils
            su = StoreUtils()
            su.store_credit_event(credits[0].user, 'TOP', credits[0].package_credits, credits[0].subscription_credits,
                                  credits[0].l_package_credits, credits[0].l_subscription_credits,
                                  dsc=self.value-subscription_credits_left)
        if not self.last_renew_date or self.last_renew_date != self.renew_date:

            if settings.SCHEDULER_TEST_MODE:
                if settings.SHORT_PERIOD == 'hour':
                    short_delta = relativedelta(hours=1)
                elif settings.SHORT_PERIOD == 'day':
                    short_delta = relativedelta(days=1)
            else:
                short_delta = relativedelta(months=1)

            self.renew_date = self.renew_date + short_delta

        else:

            self.renew_date = None
            self.last_renew_date = None
            # check last subscription event type
            from store.store_utils import StoreUtils
            su = StoreUtils()
            events_query = su.read_subscription_event(self.user)
            # go further in case of unsubscribing
            if events_query:
                event_obj = events_query.get()
                if event_obj.event_type == 'UNS':
                    # update subscription event status
                    event_obj = su.update_subscription_event_status(event_obj.id, 'COMPLETED')
                    # send email notification
                    from db.mailer import Mailer
                    mailer = Mailer()
                    mailer.subscription_event_completed(self.user, event_obj)

        self.update_counter += 1
        credits[0].save()
        self.save()

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    value = models.PositiveIntegerField(null=False)
    renew_date = models.DateTimeField(default=get_default_renew_date)
    payment_method = models.CharField(max_length=20, null=True)  # PBL, CARD_TOKEN, INSTALLMENTS
    payment_details = models.CharField(max_length=50, null=True)
    payment_last_update = models.DateTimeField(auto_now=False, null=True)
    last_renew_date = models.DateTimeField(default=None, null=True)

    class Meta:
        ordering = ['user_id']


class ScheduledFreebies(Schedule):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    value = models.PositiveIntegerField(null=False)

    @transaction.atomic
    def refill_subscription_credits(self):
        if self.processing_possible():
            credits = Credit.objects.select_for_update().get_or_create(user=self.user)
            if credits[0].subscription_credits < self.value:
                subscription_credits_left = credits[0].subscription_credits
                credits[0].subscription_credits = self.value
            self.update_counter += 1
            credits[0].save()
            self.save()
            # store credit event
            from store.store_utils import StoreUtils
            su = StoreUtils()
            su.store_credit_event(credits[0].user, 'FRE', credits[0].package_credits, credits[0].subscription_credits,
                                  credits[0].l_package_credits, credits[0].l_subscription_credits,
                                  dsc=self.value-subscription_credits_left)
    
    @transaction.atomic
    def add_package_credits(self):
        if self.processing_possible():
            credits = Credit.objects.select_for_update().get_or_create(user=self.user)
            credits[0].package_credits += self.value
            self.update_counter += 1
            credits[0].save()
            self.save()
            # store credit event
            from store.store_utils import StoreUtils
            su = StoreUtils()
            su.store_credit_event(credits[0].user, 'FRE', credits[0].package_credits, credits[0].subscription_credits,
                                  credits[0].l_package_credits, credits[0].l_subscription_credits,
                                  dpc=self.value)


##################################
AVAILABILITY_CHOICE = [
    ('PRIVATE', 'PRIVATE'),
    ('ACCOUNT', 'ACCOUNT'),
    ('GROUP',   'GROUP'),
    ('PUBLIC',  'PUBLIC')
]


def validate_image(image):
    # Sprawdzenie formatu pliku
    if not image.name.lower().endswith('.png'):
        raise ValidationError("Obrazek musi być w formacie PNG.")

    # # Upewnij się, że obraz odczytywany jest od początku
    # image.seek(0)
    # img = Image.open(image)
    
    # # Sprawdzenie, czy wymiary mieszczą się między 94 a 98 pikseli
    # if not (94 <= img.width <= 98 and 94 <= img.height <= 98):
    #     raise ValidationError("Obrazek musi mieć wymiary między 94 a 98 pikseli.")


class TagGroup(models.Model):
    """
    Grupa tagów trzymająca wspólną ikonę.
    Kilka Tagów może przynależeć do jednej grupy.
    """
    objects = CaseInsensitiveManager()

    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_("Group name")
    )
    icon = models.ImageField(
        upload_to='tag_groups/',
        validators=[validate_image],
        blank=True,
        null=True,
        verbose_name=_("Group icon")
    )

    class Meta:
        verbose_name = _("Tag group")
        verbose_name_plural = _("Tag groups")
        ordering = ['name']

    def __str__(self):
        return self.name


class Tag(models.Model):
    """
    Pojedynczy Tag, opcjonalnie przypisany do TagGroup.
    ImageField usuwamy — ikonę pobieramy z grupy.
    """
    objects = CaseInsensitiveManager()

    name = models.CharField(
        max_length=50,
        primary_key=True,
        verbose_name=_("Tag name")
    )
    group = models.ForeignKey(
        TagGroup,
        on_delete=models.CASCADE,
        related_name='tags',
        blank=True,
        null=True,
        verbose_name=_("Tag group")
    )

    class Meta:
        verbose_name = _("Tag")
        verbose_name_plural = _("Tags")
        ordering = ['name']

    def __str__(self):
        return self.name

class Category(models.Model):
    name = models.CharField(primary_key=True, max_length=50)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True)

    def clean(self):
        if self.parent and self.parent == self:
            raise ValidationError("Kategoria nie może być rodzicem samej siebie.")

    def __str__(self):
        return f"{self.name} ({self.parent})" if self.parent else self.name

    @classmethod
    def build_category_tree(cls):
        categories = cls.objects.all() 
        category_dict = {cat.name: {"parent": cat.parent.name if cat.parent else None, "children": []} for cat in categories}

        for cat in categories:
            if cat.parent:
                category_dict[cat.parent.name]["children"].append(cat.name)

        return category_dict



class Program(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    name            = models.CharField(max_length=100, null=False, blank=False)
    version_number  = models.CharField(max_length=50, null=False, blank=False)
    build_number    = models.CharField(max_length=50, null=True, blank=False, default=None)
    producer        = models.CharField(max_length=100, null=True, blank=False, default=None)

    class Meta:
        # unique_together = ['name', 'version_number']
        ordering = ['name', '-version_number', '-build_number', 'producer']

    def __str__(self):
        if self.producer:
            return f"{self.name} {self.version_number} ({self.producer})"
        else:
            return f"{self.name} {self.version_number}"


class App(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    name            = models.CharField(max_length=100, null=False, blank=False)
    version_number  = models.CharField(max_length=50, null=False, blank=False)
    build_number    = models.CharField(max_length=50, null=True, blank=False, default=None)
    producer        = models.CharField(max_length=100, null=True, blank=False, default=None)

    class Meta:
        # unique_together = ['name', 'version_number']
        ordering = ['name', '-version_number', '-build_number', 'producer']


class KeywordsHash(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    keywords_hash = models.CharField(blank=False, max_length=32, primary_key=True)   # MD5


class Scenario(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    class ScenarioStatus(models.IntegerChoices):
        TO_MOD      = 0, _("Sent to moderation")
        FOR_MOD     = 1, _("Assigned to moderator")
        IN_MOD      = 2, _("Scenario in moderation")
        REJECTED    = 3, _("Scenario rejected")
        ACCEPTED    = 4, _("Scenario accepted")
        SUSPENDED   = 5, _("Scenario suspended")
        CANCELED    = 6, _("Scenario canceled")
        
    class AvailabilityStatus(models.IntegerChoices):
        PRIVATE = 10, _("Private")
        ACCOUNT = 20, _("Limited to account")
        GROUP   = 50, _("limited to group")
        PUBLIC  = 90, _("Public")
    
    class ScenarioUploadMode(models.IntegerChoices):
        NEW     = 0, _("Fresh scenario")
        BRANCH  = 10, _("Branch related scenario")
        VERSION = 20, _("Version related scenario")
        
    sid                 = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name                = models.CharField(max_length=150, blank=False)
    scenario_version    = models.IntegerField(default=0)
    version_changelog   = models.CharField(max_length=1000, blank=True, null=True)
    description         = models.CharField(max_length=2500)
    author              = models.CharField(max_length=50, blank=False)
    created_at          = models.DateTimeField("Creation date", editable=False, blank=False, auto_now_add=True)
    updated_at          = models.DateTimeField("Last update date", blank=False, auto_now=True)
    accepted            = models.BooleanField(default=False)
    scenario            = models.JSONField()
    owner               = models.ForeignKey(User, related_name='scenarios', on_delete=models.CASCADE, null=True)
    moderator           = models.ForeignKey(
                            User,
                            on_delete=models.SET_NULL,
                            null=True,
                            blank=True,
                            limit_choices_to={'groups__name': 'Moderator'},
                            related_name='assigned_scenarios',
                            verbose_name=_("Assigned moderator")
                        )
    price               = models.PositiveBigIntegerField(null=False, blank=False, default=0)
    status              = models.IntegerField(choices=ScenarioStatus.choices, default=ScenarioStatus.TO_MOD)
    publish_time        = models.DateTimeField("First publication date", null=True, auto_now=False, auto_now_add=False)
    is_fixed_cost       = models.BooleanField(default=False)
    available           = models.IntegerField(choices=AvailabilityStatus.choices, default=AvailabilityStatus.PRIVATE)
    editable            = models.IntegerField(choices=AvailabilityStatus.choices, default=AvailabilityStatus.PRIVATE)
    updatable           = models.IntegerField(choices=AvailabilityStatus.choices, default=AvailabilityStatus.PRIVATE)
    apps                = models.ManyToManyField(Program, blank=True)
    tags                = models.ManyToManyField(Tag, blank=True)
    categories          = models.ManyToManyField(Category, blank=True)
    keywords_hashes     = models.ManyToManyField(KeywordsHash, blank=True)
    root_id             = models.ForeignKey('self', on_delete=models.DO_NOTHING, null=True, blank=True, related_name='all_nodes')
    branch_id           = models.ForeignKey('self', on_delete=models.DO_NOTHING, null=True, blank=True, related_name='branch_nodes')
    previous_id         = models.ForeignKey('self', on_delete=models.DO_NOTHING, null=True, blank=True, related_name='next')
    update_lock         = models.BooleanField(default=False)
    update_lock_by      = models.CharField(null=True, blank=True)
    seo                 = GenericRelation(ObjectMeta)


    def __str__(self):
        return f"{self.name} - {self.author} [{self.AvailabilityStatus(self.available).label}]"
    
    def _increment_scenario_version(self):
        LOGGER.info(f'Version incrementation of scenario {self.sid} started...')
        last_version = self.get_latest_version()
        self.scenario_version = last_version + 1
        LOGGER.info(f'New scenario version for {self.sid} is {self.scenario_version}')
        LOGGER.info(f'...version incrementation of scenario {self.sid} done!')
    
    def set_update_lock(self, lock_by: str=None):
        LOGGER.info(f'Setting update lock of scenario {self.sid} started...')
        self.update_lock = True
        LOGGER.info(f'...setting update_lock flag...')
        self.update_lock_by = lock_by
        LOGGER.info(f'...setting update_lock_by info...')
        LOGGER.info(f'...setting update lock of scenario {self.sid} done!')
    
    def remove_update_lock(self):
        LOGGER.info(f'Removing update lock of scenario {self.sid} started...')
        self.update_lock = False
        LOGGER.info(f'...removing update_lock flag...')
        self.update_lock_by = None
        LOGGER.info(f'...removing update_lock_by info...')
        LOGGER.info(f'...removing update lock of scenario {self.sid} done!')

    def set_status_from_event_type(self, event_type):
        LOGGER.info(f'Seting status of scenario {self.sid} started...')
        status = self._get_status_from_event_type(event_type)
        LOGGER.info(f'Changing status of scenario {self.sid} to {status}...')
        self.status = status
        if status == Scenario.ScenarioStatus.ACCEPTED:
            LOGGER.info(f'Changing publish_time of scenario {self.sid}...')
            self.publish_time = datetime.now()
        LOGGER.info(f'Seting status of scenario {self.sid} done!')
        return self.status
    
    
    def _get_status_from_event_type(self, event_type):
        LOGGER.info(f'Geting status from eventtype {event_type} started...')
        status = self.status
        if event_type == Scenario_Event.EventType.TO_MOD: status = Scenario.ScenarioStatus.TO_MOD
        elif event_type == Scenario_Event.EventType.IN_MOD: status = Scenario.ScenarioStatus.IN_MOD
        elif event_type == Scenario_Event.EventType.FOR_MOD: status = Scenario.ScenarioStatus.FOR_MOD
        elif event_type == Scenario_Event.EventType.REJECTED: status = Scenario.ScenarioStatus.REJECTED
        elif event_type == Scenario_Event.EventType.ACCEPTED: status = Scenario.ScenarioStatus.ACCEPTED
        elif event_type == Scenario_Event.EventType.SUSPENDED: status = Scenario.ScenarioStatus.SUSPENDED
        elif event_type == Scenario_Event.EventType.RESTORED: status = Scenario.ScenarioStatus.IN_MOD
        elif event_type == Scenario_Event.EventType.CANCELED: status = Scenario.ScenarioStatus.CANCELED
        LOGGER.info(f'Output status from eventtype {event_type} is {status}!')
        return status
    
    def get_event_type_from_status(self, scenario_status):
        LOGGER.info(f'Geting event_type from status {scenario_status} started...')
        int_scenario_status = int(scenario_status)
        event_type = None
        if int_scenario_status == Scenario.ScenarioStatus.TO_MOD: event_type = Scenario_Event.EventType.TO_MOD
        elif int_scenario_status == Scenario.ScenarioStatus.FOR_MOD: event_type = Scenario_Event.EventType.FOR_MOD
        elif int_scenario_status == Scenario.ScenarioStatus.IN_MOD: event_type = Scenario_Event.EventType.IN_MOD
        elif int_scenario_status == Scenario.ScenarioStatus.REJECTED: event_type = Scenario_Event.EventType.REJECTED
        elif int_scenario_status == Scenario.ScenarioStatus.ACCEPTED: event_type = Scenario_Event.EventType.ACCEPTED
        elif int_scenario_status == Scenario.ScenarioStatus.SUSPENDED: event_type = Scenario_Event.EventType.SUSPENDED
        elif int_scenario_status == Scenario.ScenarioStatus.CANCELED: event_type = Scenario_Event.EventType.CANCELED
        LOGGER.info(f'Output event_type from status {scenario_status} is {event_type}!')
        return event_type


    def get_root(self):
        if self.root_id is not None:
            return self.root_id
        else:
            return self
        
    def get_latest_version(self):
        LOGGER.info(f'Checking latest version of scenario {self.sid}...')
        latest_version = Scenario.objects.filter(branch_id=self.branch_id).order_by('-scenario_version').first().scenario_version
        LOGGER.info(f'...latest version of scenario {self.sid} is {latest_version}')
        return Scenario.objects.filter(branch_id=self.branch_id).order_by('-scenario_version').first().scenario_version
    
    def register_scenario_event(self, event_type, message="", rid=None, uid=None) -> Tuple[bool, str]:
        LOGGER.info(f'Registering scenario event for {self.sid} started...')
        regitration_successful = False
        registration_message = ""
        try:
            robot = Robot.objects.get(pk=rid) if rid else None
            user = User.objects.get(pk=uid) if uid else None
            scenario_event = Scenario_Event.objects.create(sid=self, type_id=event_type, rid=robot, user_id=user)

            if event_type == Scenario_Event.EventType.TO_MOD:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [set_status]')
                scenario_status = self.set_status_from_event_type(event_type=event_type)
                registration_message = f"Scenario status updated to {scenario_status}."
                regitration_successful = True

            elif event_type == Scenario_Event.EventType.FOR_MOD:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [set_status]')
                scenario_status = self.set_status_from_event_type(event_type=event_type)
                registration_message = f"Scenario status updated to {scenario_status}."
                regitration_successful = True

            elif event_type == Scenario_Event.EventType.IN_MOD:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [set_status]')
                scenario_status = self.set_status_from_event_type(event_type=event_type)
                registration_message = f"Scenario status updated to {scenario_status}."
                regitration_successful = True
            
            elif event_type == Scenario_Event.EventType.REJECTED:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [set_status]')
                scenario_status = self.set_status_from_event_type(event_type=event_type)
                Scenario_Event_Message.objects.create(event_id=scenario_event, message=message)
                # send_mail("Your Botie Scenario has been rejected!", f'Your scenario: {self.name} [ID: {self.sid}] has been rejected! message:{message}', "<EMAIL>", [f'{self.owner.email}'], fail_silently=False)
                registration_message = f"Scenario status updated to {scenario_status}."
                regitration_successful = True

                # send email notification
                from db.mailer import Mailer
                mailer = Mailer()
                mailer.scenario_rejected(self, message)


            elif event_type == Scenario_Event.EventType.ACCEPTED:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                # print(self.description)
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [increment_scenario_version]')
                self._increment_scenario_version()
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [remove_update_loc]')
                self.remove_update_lock()
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [set_status]')
                scenario_status = self.set_status_from_event_type(event_type=event_type)
                registration_message = f"Scenario status updated to {scenario_status}."
                regitration_successful = True

                # send email notification
                from db.mailer import Mailer
                mailer = Mailer()
                mailer.scenario_accepted(self)

            elif event_type == Scenario_Event.EventType.SUSPENDED:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [set_status]')
                scenario_status = self.set_status_from_event_type(event_type=event_type)
                registration_message = f"Scenario status updated to {scenario_status}."
                regitration_successful = True

            elif event_type == Scenario_Event.EventType.CANCELED:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                self.remove_update_lock()
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [set_status]')
                scenario_status = self.set_status_from_event_type(event_type=event_type)
                registration_message = f"Scenario status updated to {scenario_status}."
                regitration_successful = True

            elif event_type == Scenario_Event.EventType.RESTORED:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                LOGGER.debug(f'Executing scenario event type: {event_type} in progress: [set_status]')
                scenario_status = self.set_status_from_event_type(event_type=event_type)
                registration_message = f"Scenario status updated to {scenario_status}."
                regitration_successful = True

            elif event_type == Scenario_Event.EventType.ADD_TO_FAV:
                LOGGER.info(f'Executing scenario event type: {event_type}')            
                registration_message = "Scenario added to favorites."
                regitration_successful = True

            elif event_type == Scenario_Event.EventType.DOWNLOADED:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                registration_message = "Scenario downloaded."
                regitration_successful = True

            elif event_type == Scenario_Event.EventType.SCEN_ASSIGNED:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                registration_message = "Scenario assigned to the robot."
                regitration_successful = True

            elif event_type == Scenario_Event.EventType.SCEN_COMPLETED:
                LOGGER.info(f'Executing scenario event type: {event_type}')
                registration_message = "Scenario completed."
                regitration_successful = True
            
            LOGGER.debug(f'Executing scenario event in progress: [save()]')
            self.save()

            LOGGER.info(f'Creating Scenario_Event_Message...')
            event_msg = f'{registration_message}: [{message}]' if message else registration_message
            Scenario_Event_Message.objects.create(event_id=scenario_event, message=event_msg)
            LOGGER.info(f'...Scenario_Event_Message created!')

        except Robot.DoesNotExist as e:
            LOGGER.exception(e)
            registration_message = "Robot not found."
        except User.DoesNotExist as e:
            LOGGER.exception(e)
            registration_message = "User not found."
        except Exception as e:
            LOGGER.info(f'Unexpected error: {str(e)}')
            LOGGER.exception(e)
            registration_message = "Unexpected error occurred."
        else:
            LOGGER.info(f'...scenario event registered for {self.sid} with id {scenario_event.id}')
        finally:
            return regitration_successful, registration_message

    class Meta:
        ordering = ['sid']


class StartScenario(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    sid = models.ForeignKey(Scenario, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True, help_text="Czy ten scenariusz powinien być publikowany?")
    start_date = models.DateTimeField(blank=True, null=True, help_text="Data i czas rozpoczęcia publikowania scenariusza.")
    end_date = models.DateTimeField(blank=True, null=True, help_text="Data i czas zakończenia publikowania scenariusza.")

    def clean(self):
        if self.sid.available != Scenario.AvailabilityStatus.PUBLIC:
            raise ValidationError("You can't bind StartScenario to a Scenario that is not PUBLIC.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class ScenarioTag(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    sid = models.ForeignKey(Scenario, on_delete=models.CASCADE, null=False, blank=False, unique=False)
    tag = models.ForeignKey(Tag, on_delete=models.CASCADE, null=False, blank=False, unique=False)

    class Meta:
        unique_together = ['sid', 'tag']
        ordering = ['sid', 'tag']


class Scenario_Program(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    sid         = models.ForeignKey(Scenario, on_delete=models.CASCADE, null=False, blank=False, unique=False)
    program     = models.ForeignKey(Program, on_delete=models.CASCADE, null=False, blank=False, unique=False)

    class Meta:
        unique_together = ['sid', 'program']
        ordering = ['sid', 'program']


class Scenario_Category(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    sid         = models.ForeignKey(Scenario, on_delete=models.CASCADE, null=False, blank=False, unique=False)
    category    = models.ForeignKey(Category, on_delete=models.CASCADE, null=False, blank=False, unique=False)

    class Meta:
        unique_together = ['sid', 'category']
        ordering = ['sid', 'category']


class Scenario_Event(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    class EventType(models.IntegerChoices):
        TO_MOD              = 0, _("Sent to moderation")
        FOR_MOD             = 1, _("Assigned to moderator")
        IN_MOD              = 2, _("Scenario in moderation")
        REJECTED            = 3, _("Scenario rejected")
        ACCEPTED            = 4, _("Scenario accepted")
        ADD_TO_FAV          = 5, _("Scenario added to favorites")
        DOWNLOADED          = 6, _("Scenario file dawnloaded")
        SUSPENDED           = 7, _("Scenario suspended")
        RESTORED            = 8, _("Scenario restored")
        CANCELED            = 9, _("Scenario canceled")
        RET_TO_POOL         = 10, _("Return Scenario to pool")
        REM_FROM_FAV        = 11, _("Scenario removed from favorites")
        SCEN_COMPLETED      = 12, _("Scenario completed")
        SCEN_ASSIGNED       = 13, _("Scenario assigned to robot")

    sid         = models.ForeignKey(Scenario, on_delete=models.CASCADE)
    event_time  = models.DateTimeField(auto_now_add=True)
    type_id     = models.IntegerField(choices=EventType.choices, default=EventType.TO_MOD)
    user_id     = models.ForeignKey(User, on_delete=models.DO_NOTHING, null=True, blank=False)
    rid         = models.ForeignKey(Robot, on_delete=models.DO_NOTHING, null=True, blank=False)


# Scenario Event Messages
class Scenario_Event_Message(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    event_id        = models.ForeignKey(Scenario_Event, on_delete=models.CASCADE)
    message         = models.CharField(max_length=2000, null=False, blank=False)


# ScenarioStorage
class UserStorage(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    user        = models.ForeignKey(User, on_delete=models.CASCADE)
    sid         = models.ForeignKey(Scenario, on_delete=models.CASCADE)
    rid         = models.ForeignKey(Robot, on_delete=models.CASCADE, null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['user', 'sid', 'rid', 'update_time', 'create_time']


class FavoriteScenario(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_scenarios')
    scenario = models.ForeignKey(Scenario, on_delete=models.CASCADE, related_name='favorited_by')
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'scenario')
        ordering = ['-added_at']
        verbose_name = 'Favorite Scenario'
        verbose_name_plural = 'Favorite Scenarios'

    def __str__(self):
        return f'{self.user.username} favorites {self.scenario.name}'


# DOWNLOAD
class DownloadRequirement(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    os = models.TextField(default="Windows 7 / 10 / 11")
    processor = models.TextField(default="Intel Core i3 lub nowszy")
    ram = models.TextField(default="2GB lub więcej")
    disk = models.TextField(default="200MB lub więcej")
    graphic = models.TextField(default="Zgodna z DirectX 12 / OpenGL 4.5")

    def __str__(self):
        return f"Requirements - ID: {self.id}"


class DownloadServer(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    name = models.CharField(max_length=255, help_text="Nazwa serwera (np. Serwer Europa Wschodnia)")
    id_based_url = models.URLField(help_text="Adres IP (np. https://***************)")
    domain_name_based_url = models.URLField(help_text="Adres domenowy (np. https://s1.robosourcing.pl)")
    repository_path = models.CharField(max_length=255, help_text="Lokalizacja plików na danym serwerze (np. /instalki)")
    active = models.BooleanField(default=True)
    default = models.BooleanField(default=False)

    def __str__(self):
        return self.name
    

class DownloadPlatform(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    name = models.CharField(max_length=255, help_text="Nazwa platformy (np. Windows XP)")
    active = models.BooleanField(default=True)
    default = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class DownloadPackage(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    name = models.CharField(max_length=10, help_text="MSI, EXE, ZIP, etc.")
    active = models.BooleanField(default=True)
    default = models.BooleanField(default=False)
    platform_id = models.ForeignKey(DownloadPlatform, on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        return self.name


class DownloadItem(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    release_version = models.CharField(max_length=10)
    release_date = models.DateField()
    release_date_time = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    publish_time = models.DateTimeField(null=True, blank=True, help_text="Data i czas początku wyświetlania")
    end_time = models.DateTimeField(null=True, blank=True, help_text="Data i czas zakończenia wyświetlania")
    is_default = models.BooleanField(default=False, help_text="Czy ma być wersją domyślną dla platformy i pakietu?")
    is_active = models.BooleanField(default=True)
    filename = models.TextField()
    short_description = models.TextField(null=True, blank=True)
    full_description = models.TextField(null=True, blank=True)
    requirements = models.ForeignKey(DownloadRequirement, on_delete=models.CASCADE, null=True, blank=True)
    server_id = models.ForeignKey(DownloadServer, on_delete=models.CASCADE, null=True, blank=True)
    package_id = models.ForeignKey(DownloadPackage, on_delete=models.CASCADE, null=True, blank=True)
    platform_id = models.ForeignKey(DownloadPlatform, on_delete=models.CASCADE, null=True, blank=True)
    # platforms = models.ManyToManyField(DownloadPlatforms, blank=True)

    def get_download_url(self):
        if self.server_id:
            base_url = self.server_id.domain_name_based_url
            file_path = f"{self.server_id.repository_path}/{self.filename}"
            return f"{base_url}{file_path}"
        return None

    def __str__(self):
        return self.release_version
    
# @receiver(pre_save, sender=DownloadItem)
# def ensure_single_active_instance(sender, instance, **kwargs):
#     current_time = timezone.now()
#     if instance.is_active:
#         if instance.publish_time and current_time < instance.publish_time:
#             instance.is_active = False
#         elif instance.end_time and current_time > instance.end_time:
#             instance.is_active = False
#
#         active_instances = DownloadItem.objects.filter(is_active=True)
#         if active_instances.exists() and instance != active_instances.first():
#             active_instances.update(is_active=False)


class DownloadEvent(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    version = models.ForeignKey(DownloadItem, on_delete=models.CASCADE, related_name='download_events')
    event_time = models.DateTimeField(auto_now_add=True)
    # event_time = models.DateTimeField()
    ip_address = models.GenericIPAddressField()
    region = models.CharField(max_length=255)

    def __str__(self):
        return f"Download Event - ID: {self.id}"


class EmailNotification(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    class Message(models.IntegerChoices):
        AFTER_REGISTRATION = 0, _("Thank you for registering an account")
        AFTER_LOGIN = 1, _("Welcome to our web service")
        AFTER_PURCHASE = 2, _("Thank you for making your purchases")
        SUBSCRIPTION_EVENT_COMPLETED = 3, _("Your subscription event completed successfully")
        SUBSCRIPTION_EVENT_CANCELED = 4, _("Your subscription event has failed")
        PAYMENT_CARD_EXPIRE_SOON = 5, _("Your payment card expires soon")
        PAYMENT_CARD_EXPIRED = 6, _("Your payment card has expired")
        CONTACT_FORM_SUBMITTED = 7, _("Contact form submitted")
        WITHDRAWAL_REQUEST_SUBMITTED = 8, _("Withdrawal request submitted")
        MONTHLY_WITHDRAWAL_SUMMARY = 9, _("Monthly withdrawal summary")
        PASSWORD_RESET_KEY = 10, _("Password reset key")
        SCENARIO_ACCEPTED = 11, _("Scenario accepted")
        SCENARIO_REJECTED = 12, _("Scenario rejected")
        NEWSLETTER_CONFIRMATION = 13, _("Newsletter confirmation")
        NEWSLETTER_WELCOME = 14, _("Newsletter welcome")

    class Status(models.IntegerChoices):
        FAILED = 0, _("Failed")
        SENT = 1, _("Sent")

    user = models.ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)  # Zmiana: użytkownik opcjonalny
    sending_time = models.DateTimeField(auto_now_add=True)
    message_id = models.IntegerField(choices=Message.choices)
    status_id = models.IntegerField(choices=Status.choices)

    def __str__(self):
        return f"Email notification - ID: {self.id}"


class FileCategory(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    name    = models.CharField(blank=False, max_length=50, primary_key=True)


class FileKeyword(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    keyword = models.CharField(blank=False, max_length=50, primary_key=True)


class File(models.Model):

    # objects = models.Manager()
    objects = CaseInsensitiveManager()

    export_name     = models.CharField(null=False, blank=False, max_length=50)
    file_type       = models.CharField(null=False, blank=False, max_length=5)
    file_hash       = models.CharField(null=False, blank=False, max_length=32)   # MD5
    keywords_hash   = models.ForeignKey(KeywordsHash, on_delete=models.CASCADE, null=True, blank=False)   # MD5
    scenario        = models.ForeignKey(Scenario, on_delete=models.CASCADE, null=False, blank=False)
    keywords        = models.ManyToManyField(FileKeyword, symmetrical=False)
    categories      = models.ManyToManyField(FileCategory, symmetrical=False)

    def file_base64(self):
        file_base64 = ''
        # print('JS-1')
        # print(self.file_hash)
        if default_storage.exists(self.file_hash):
            # print('JS-1')
            file = default_storage.open(self.file_hash, mode='rb')
            file_base64 = base64.b64encode(file.read())
            file.close()
        return file_base64
    
    class Meta:
        unique_together = ['file_hash', 'scenario']


class MarketingCampaign(models.Model):
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=50, unique=True, blank=True, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    start_date = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = uuid.uuid4().hex[:10]  # Generowanie unikalnego kodu
        super().save(*args, **kwargs)

    def get_weekly_visits(self, week_number):
        # determine week start datetime
        if self.start_date:
            week_start = self.start_date
        else:
            week_start = self.created_at
        week_start += timedelta(weeks=week_number - 1)
        # determine week end datetime
        week_end = week_start + timedelta(weeks=1)

        return self.visits.filter(started_at__range=[week_start, week_end]).count()

    def first_week_visits(self):
        return self.get_weekly_visits(1)

    def second_week_visits(self):
        return self.get_weekly_visits(2)

    def third_week_visits(self):
        return self.get_weekly_visits(3)

    def fourth_week_visits(self):
        return self.get_weekly_visits(4)

    def fifth_week_visits(self):
        return self.get_weekly_visits(5)

    def __str__(self):
        return self.name


class Visit(models.Model):
    campaign = models.ForeignKey(MarketingCampaign, on_delete=models.CASCADE, related_name='visits')
    referrer_ip = models.GenericIPAddressField()
    started_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Visit for {self.campaign.name} from {self.referrer_ip} started at {self.started_at}"


class Referral(models.Model):
    visit = models.ForeignKey(Visit, on_delete=models.CASCADE, related_name='referrals')
    path = models.CharField(max_length=255)
    visited_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Referral on {self.path} during visit at {self.visited_at}"


class AffiliateLink(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='affiliate_link')
    code = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def get_link(self):
        return self.code
        #return f"https://botie.pl/accounts/signup/?ref={self.code}"

    def get_current_commission_rate(self):
        now = timezone.now()
        current_rate = self.rate_history.filter(start_date__lte=now, end_date__isnull=True).first()
        return current_rate.commission_rate if current_rate else 0.0
    
    def update_commission_rate(affiliate_link, new_rate):
        now = timezone.now()
        # Zakończ poprzednią stawkę
        current_rate = affiliate_link.rate_history.filter(end_date__isnull=True).first()
        if current_rate:
            current_rate.end_date = now
            current_rate.save()
        # Utwórz nową stawkę
        CommissionRateHistory.objects.create(
            affiliate_link=affiliate_link,
            commission_rate=new_rate,
            start_date=now
        )
        
    def __str__(self):
        return f"Affiliate link for {self.user.username}"


class PartnerCommission(models.Model):
    partner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='commissions')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    commission_rate = models.DecimalField(max_digits=5, decimal_places=2)  
    related_order = models.ForeignKey(Order, on_delete=models.CASCADE)
    related_event = models.ForeignKey(CreditEvent, on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)


class CommissionRateHistory(models.Model):
    affiliate_link = models.ForeignKey(AffiliateLink, on_delete=models.CASCADE, related_name='rate_history')
    commission_rate = models.DecimalField(max_digits=5, decimal_places=2)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(null=True, blank=True)  # Null oznacza, że prowizja jest aktualna

    class Meta:
        ordering = ['-start_date']

    def __str__(self):
        end_date = self.end_date if self.end_date else 'obecnie'
        return f"{self.affiliate_link.user.username} - {self.commission_rate}% od {self.start_date} do {end_date}"


class CommissionChangeEvent(models.Model):
    affiliate_link = models.ForeignKey(AffiliateLink, on_delete=models.CASCADE)
    old_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    new_rate = models.DecimalField(max_digits=5, decimal_places=2)
    changed_at = models.DateTimeField(auto_now_add=True)
    changed_by = models.ForeignKey(User, null=True, on_delete=models.SET_NULL)


class WithdrawalCycle(models.Model):
    partner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='withdrawal_cycles')
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    total_withdrawn = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    class Meta:
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.partner.username} - {self.start_date.date()} to {self.end_date.date()}"


class AffiliateWithdrawalRequest(models.Model):
    class Status(models.TextChoices):
        PENDING = "PENDING", "Oczekuje"
        PAID = "PAID", "Wypłacono"
        CANCELED = "CANCELED", "Anulowano"

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='withdrawal_requests')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=8, choices=Status.choices, default=Status.PENDING)
    requested_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    cycle = models.ForeignKey(WithdrawalCycle, on_delete=models.CASCADE, related_name='withdrawals', null=True, blank=True)

    class Meta:
        unique_together = ('user', 'cycle')

    def mark_as_paid(self):
        self.status = self.Status.PAID
        self.processed_at = timezone.now()
        self.save()

    def mark_as_canceled(self):
        self.status = self.Status.CANCELED
        self.processed_at = timezone.now()
        self.save()

    def __str__(self):
        return f"{self.user.username} - {self.amount} - {self.get_status_display()}"
