# Generated by Django 4.2.13 on 2024-12-05 18:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0032_partnercommission_related_order_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='affiliatelink',
            name='commission_rate',
        ),
        migrations.AddField(
            model_name='partnercommission',
            name='commission_rate',
            field=models.DecimalField(decimal_places=2, default=1, max_digits=5),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='CommissionRateHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('affiliate_link', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rate_history', to='db.affiliatelink')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
    ]
