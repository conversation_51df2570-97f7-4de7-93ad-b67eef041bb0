{% load i18n %}
{% load static %}

<div id="cookieModal" class="modal fade" role="dialog" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content px-3 py-3">
            <div class="modal-header">
                <h5 class="modal-title">{% trans 'We value your privacy' %}</h5>
            </div>
            <div class="modal-body row align-items-center">
                <div class="col-9 col-xxl-10">{% trans 'We use cookies on this website to enhance your user experience. By clicking "I agree", you are giving your consent for us to set cookies.' %}<br>
                    {% trans 'For more information on what data is contained in the cookies, please see our' %} <a href="{% url 'privacy_policy' %}">{% trans "Privacy policy" %}</a>.</div>
                <div class="col-3 col-xxl-2 text-center"><button type="button" class="btn btn-secondary mx-1 my-1" data-dismiss="modal" id="rejectCookies">{% trans 'I decline' %}</button><button type="button" class="btn btn-primary mx-1 my-1" data-dismiss="modal" id="acceptCookies">{% trans 'I agree' %}</button></div>
            </div>
            <div class="modal-footer"></div>
        </div>
    </div>
</div>

<script>
$(document).ready(function(){
    // Cookie consent functions
    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days*24*60*60*1000));
            expires = "; expires=" + date.toUTCString();
        } else {
            expires = "; Max-Age=0";
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i=0;i < ca.length;i++) {
            var c = ca[i];
            while (c.charAt(0)==' ') c = c.substring(1,c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
        }
        return null;
    }

    // Show cookie consent modal if not accepted yet (except privacy policy page)
    if(getCookie('cookies') === null && location.href.indexOf('/privacy-policy/') === -1){
        $('#cookieModal').modal('show');
    }

    // Handle cookie consent choices
    $('#acceptCookies').on('click', function(){
        setCookie('cookies', 'true', 30);
        $('#cookieModal').modal('hide');
    });

    $('#rejectCookies').on('click', function(){
        setCookie('cookies', 'false', 30);
        $('#cookieModal').modal('hide');
    });
});
</script>