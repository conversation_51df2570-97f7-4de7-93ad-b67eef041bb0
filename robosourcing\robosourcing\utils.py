import datetime
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from django.conf import settings


class SingletonMeta(type):
    """
    The Singleton metaclass.
    """

    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]


class TimeManager(metaclass=SingletonMeta):

    @staticmethod
    def accelerated_time():
        base_time = settings.BASE_TIME
        speed_factor = settings.SPEED_FACTOR
        now = datetime.datetime.utcnow().replace(tzinfo=timezone.utc)
        elapsed = (now - base_time).total_seconds()
        accelerated_elapsed = elapsed * speed_factor
        return base_time + datetime.timedelta(seconds=accelerated_elapsed)

    def current_time(self):
        if settings.ACCELERATE_TIME:
            return self.accelerated_time()
        return timezone.now()

    def shifted_time(self, hours=0, days=0, weeks=0, months=0, years=0):
        now = self.current_time()
        if hours or days or weeks or months or years:
            now += relativedelta(hours=hours, days=days, weeks=weeks, months=months, years=years)
        return now
