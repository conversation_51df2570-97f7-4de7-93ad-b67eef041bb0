{% extends 'main.html' %}
{% load i18n %}
{% load static %}
{% load divide %}

{% block head_title %}{% trans "Price list" %}{% endblock %}

{% block title %}
{{ block.super }} - {% trans "Price list" %}
{% endblock title %}

{% block scripts %}
{% endblock %}

{% block navbar_buttons %}
{{ block.super }}
{% endblock %}

{% block content %}
<div class="container mt-3">
    <div class="container section-title fade-in pb-2">
        <h2>{% trans "Price list" %}</h2>
        
    </div>

    <div class="container row mt-5">
        <div class="col-xl-10 mx-auto">
            <div class="row mb-3">

                <div class="col-12 mb-5">
                    <div class="custom_switch row align-items-center">
                        <div class="col text-end"><span class="text-muted">{% trans "subscription agreement" %}</span> <strong>{% trans "monthly" %}</strong></div>
                        <div class="col col-auto">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault" style="transform: scale(1.5); margin-left: -2.3em" onchange="toggleSubBox()">
                            </div>
                        </div>
                        <div class="col"><strong>{% trans "annual" %}</strong> <span class="text-muted">({% trans "one month for free" %})</span></div>
                    </div>
                </div>

            </div>
            <div class="row">

                <div class="col-lg-6 mb-5 mb-lg-0" style="display: none">
                    <div id="pricelist_box_year" class="pricingTable year">
                        <!--<form action="{% url 'make_order' %}" method="post">-->
                        <form action="{% url 'choose_operator' %}" method="post">
                        {% csrf_token %}
                        <input type="hidden" name="mode" value="single">
                        <input type="hidden" name="pricelist_item_id" value="{{ pricelist_year.0.id }}">
                        <div class="dymek dymek-value position-absolute top-0 start-50 translate-middle px-3"></div>
                        <div class="pricingTable-header">
                            <i class="fa fa-calendar-week"></i>
                            <div class="heading-lines">{% trans "annual" %}<span>{% trans "subscription agreement" %}</span></div>
                        </div>
                        <h3 class="amount mt-5"><span>{{ pricelist_year.0.value|floatformat:"0g" }}</span><span
                                class="amount-desc text-muted position-absolute mx-1">{% trans "credits" %}{% trans "/mo" %}</span>
                        </h3>
                        <div class="pricing-content w-100">
                            <div class="btn-group w-75">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="price ms-3"><span>{{ pricelist_year.0.price|divide:12|floatformat:"2g" }} zł</span><span>{% trans "/mo" %}</span></span>
                                </button>
                                <ul class="dropdown-menu w-100">
                                    {% for item in pricelist_year %}
                                    <li class="dropdown-item py-0 w-100" onclick="updateBox('year', this, {{ item.id }})"><span
                                            class="row"><span class="col text-start"><span class="amount-option">{{ item.value|floatformat:"0g" }}</span><span
                                            class="text-muted"> {% trans "credits" %}</span></span><span
                                            class="price-option col-6 text-end" rel="{{ item.price|floatformat:'2g' }}">{{ item.price|divide:12|floatformat:"2g" }} zł</span></span></li>
                                    {% endfor %}
                                </ul>
                            </div>
                            <div class="price-desc text-muted"><span class="annual_price">{{ pricelist_year.0.price|floatformat:"2g" }}</span> zł {% trans "netto per year" %}</div>
                        </div>
                        <div class="pricingTable-signup mt-5">
                            <p class="bottom-info text-muted px-5">{% trans "One purchase and for 12 months your account will receive a refreshed credit limit to be used during the month." %}</p>
                            <div class="btn-group" role="group">
                                {#<a class="btn btn-secondary{% if has_active_subscription or locked_for_changes or not request.user.is_authenticated %} disabled{% endif %}" href="#" onclick="addToBasket('year')"><i class="fa fa-cart-arrow-down"></i></a>#}
                                <input type="submit"
                                   class="btn{% if has_active_subscription or locked_for_changes or not request.user.is_authenticated %} disabled{% endif %}"
                                   value="{% trans 'Buy' %}">
                                {% if not request.user.is_authenticated %}
                                <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'To purchase a credit package or subscription, you must first log in.' %}"><i class="fa fa-info-circle"></i></button>
                                {% elif has_active_subscription %}
                                <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'You currently have an active subscription. We encourage you to buy another one if this one ends. You can also make changes to an existing one from a dashboard at any time.' %}"><i class="fa fa-info-circle"></i></button>
                                {% elif locked_for_changes %}
                                <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'You already bought a subscription and it is about to activate. However your order is still pending, please be patient a little more.' %}"><i class="fa fa-info-circle"></i></button>
                                {% endif %}
                            </div>
                        </div>
                        </form>
                    </div>
                </div>

                <div class="col-lg-6 mb-5 mb-lg-0">
                    <div id="pricelist_box_month" class="pricingTable month">
                        <!--<form action="{% url 'make_order' %}" method="post">-->
                        <form action="{% url 'choose_operator' %}" method="post">
                        {% csrf_token %}
                        <input type="hidden" name="mode" value="single">
                        <input type="hidden" name="pricelist_item_id" value="{{ pricelist_month.0.id }}">
                        <div class="pricingTable-header">
                            <i class="fa fa-calendar-days"></i>
                            <div class="heading-lines">{% trans "monthly" %}<span>{% trans "subscription agreement" %}</span></div>
                        </div>
                        <h3 class="amount mt-5"><span>{{ pricelist_month.0.value|floatformat:"0g" }}</span><span
                                class="amount-desc text-muted position-absolute mx-1">{% trans "credits" %}{% trans "/mo" %}</span>
                        </h3>
                        <div class="pricing-content w-100">
                            <div class="btn-group w-75">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="price ms-3"><span>{{ pricelist_month.0.price|floatformat:"2g" }} zł</span><span>{% trans "/mo" %}</span></span>
                                </button>
                                <ul class="dropdown-menu w-100">
                                    {% for item in pricelist_month %}
                                    <li class="dropdown-item py-0 w-100" onclick="updateBox('month', this, {{ item.id }})"><span
                                            class="row"><span class="col text-start"><span class="amount-option">{{ item.value|floatformat:"0g" }}</span><span
                                            class="text-muted"> {% trans "credits" %}</span></span><span
                                            class="price-option col-6 text-end">{{ item.price|floatformat:"2g" }} zł</span></span></li>
                                    {% endfor %}
                                </ul>
                            </div>
                            <div class="price-desc text-muted">{% trans "netto per month" %}</div>
                        </div>
                        <div class="pricingTable-signup mt-5">
                            <p class="bottom-info text-muted px-5">{% trans "Every month the payment operator will collect the above amount and you will receive a refreshed credit limit in your account to be used during the month." %}</p>
                            <div class="btn-group" role="group">
                                {#<a class="btn btn-secondary{% if has_active_subscription or locked_for_changes or not request.user.is_authenticated %} disabled{% endif %}" href="#" onclick="addToBasket('month')"><i class="fa fa-cart-arrow-down"></i></a>#}
                                <input type="submit"
                                       class="btn{% if has_active_subscription or locked_for_changes or not request.user.is_authenticated %} disabled{% endif %}"
                                       value="{% trans 'Buy' %}">
                                {% if not request.user.is_authenticated %}
                                <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'To purchase a credit package or subscription, you must first log in.' %}"><i class="fa fa-info-circle"></i></button>
                                {% elif has_active_subscription %}
                                <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'You currently have an active subscription. We encourage you to buy another one if this one ends. You can also make changes to an existing one from a dashboard at any time.' %}"><i class="fa fa-info-circle"></i></button>
                                {% elif locked_for_changes %}
                                <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'You already bought a subscription and it is about to activate. However your order is still pending, please be patient a little more.' %}"><i class="fa fa-info-circle"></i></button>
                                {% endif %}
                            </div>
                        </div>
                        </form>
                    </div>
                </div>

                <div class="col-lg-6 mb-5 mb-lg-0">
                    <div id="pricelist_box_package" class="pricingTable package">
                        <!--<form action="{% url 'make_order' %}" method="post">-->
                        <form action="{% url 'choose_operator' %}" method="post">
                        {% csrf_token %}
                        <input type="hidden" name="mode" value="single">
                        <input type="hidden" name="pricelist_item_id" value="{{ pricelist_package.0.id }}">
                        <!--<div class="dymek dymek-lightgray position-absolute top-0 start-50 translate-middle px-3">{% trans "we recommend it for beginners" %}</div>-->
                        <div class="pricingTable-header">
                            <i class="fa fa-box"></i>
                            <div class="heading-lines">{% trans "once" %}<span>{% trans "pay for package" %}</span>
                            </div>
                        </div>
                        <h3 class="amount mt-5"><span>{{ pricelist_package.0.value|floatformat:"0g" }}</span><span
                                class="amount-desc text-muted position-absolute mx-1">{% trans "credits" %}</span></h3>
                        <div class="pricing-content w-100">
                            <div class="btn-group w-75">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="price ms-3"><span>{{ pricelist_package.0.price|floatformat:"2g" }} zł</span></span>
                                </button>
                                <ul class="dropdown-menu w-100">
                                    {% for item in pricelist_package %}
                                    <li class="dropdown-item py-0 w-100" onclick="updateBox('package', this, {{ item.id }})"><span
                                            class="row"><span class="col text-start"><span class="amount-option">{{ item.value|floatformat:"0g" }}</span><span
                                            class="text-muted"> {% trans "credits" %}</span></span><span
                                            class="price-option col-6 text-end">{{ item.price|floatformat:"2g" }} zł</span></span></li>
                                    {% endfor %}
                                </ul>
                            </div>
                            <div class="price-desc text-muted">{% trans "netto per package" %}</div>
                        </div>
                        <div class="pricingTable-signup mt-5">
                            <p class="bottom-info text-muted px-5">{% trans "One-time purchase of credits without a subscription without a time limit until they run out." %}</p>
                            <div class="btn-group" role="group">
                                <a class="btn btn-secondary{% if not request.user.is_authenticated %} disabled{% endif %}" href="#" onclick="addToBasket('package')"><i class="fa fa-cart-arrow-down"></i></a>
                                <input type="submit"
                                   class="btn{% if not request.user.is_authenticated %} disabled{% endif %}"
                                   value="{% trans 'Buy' %}">
                                {% if not request.user.is_authenticated %}
                                <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'To purchase a credit package or subscription, you must first log in.' %}"><i class="fa fa-info-circle"></i></button>
                                {% endif %}
                            </div>
                        </div>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="container row mt-5">
        <div class="accordion accordion-flush pricelist-regulations">
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse01">
                        {% trans 'What is an operation?' %}
                    </button>
                </h2>
                <div id="collapse01" class="accordion-collapse collapse" data-bs-parent=".pricelist-regulations">
                    <div class="accordion-body text-muted">
                        <p>{% blocktrans %}The scenario (program executed by Botie) consists of a different number of operations.{% endblocktrans %}</p>
                        <p>{% blocktrans %}An operation is a single action to be performed i.e. clicking, inserting data into a text box, etc.{% endblocktrans %}</p>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse02">
                        {% trans 'What is the cost of performing operations?' %}
                    </button>
                </h2>
                <div id="collapse02" class="accordion-collapse collapse" data-bs-parent=".pricelist-regulations">
                    <div class="accordion-body text-muted">
                        <p>{% blocktrans %}Individual operations cost 0 (are free), 1 or more credits.{% endblocktrans %}</p>
                        <p>{% blocktrans %}The cost in credits of a single execution of a given scenario is known before the scenario is executed and is communicated to the User.{% endblocktrans %}</p>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse03">
                        {% trans 'How can I get credits?' %}
                    </button>
                </h2>
                <div id="collapse03" class="accordion-collapse collapse" data-bs-parent=".pricelist-regulations">
                    <div class="accordion-body text-muted">
                        <p>{% blocktrans %}The user receives the first credits for downloading and running the Botie application.{% endblocktrans %}</p>
                        <p>{% blocktrans %}The User receives the first limit of free monthly credits for registering an account on the website: www.botie.pl.{% endblocktrans %}</p>
                        <p>{% blocktrans %}When the User has a revolving credit limit, it means that his account balance is replenished to the initial level in accordance with the credit limit.{% endblocktrans %}</p>
                        <p>{% blocktrans %}At any time, the User may purchase an additional package of credits by paying a one-time fee in addition to the existing subscription.{% endblocktrans %}</p>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse04">
                        {% trans 'What is the order in which credits are consumed?' %}
                    </button>
                </h2>
                <div id="collapse04" class="accordion-collapse collapse" data-bs-parent=".pricelist-regulations">
                    <div class="accordion-body text-muted">
                        <p>{% blocktrans %}Botie will first use the credits from the revolving limit, and secondly those one-time purchased (in packages).{% endblocktrans %}</p>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse05">
                        {% trans 'What are the types of subscriptions?' %}
                    </button>
                </h2>
                <div id="collapse05" class="accordion-collapse collapse" data-bs-parent=".pricelist-regulations">
                    <div class="accordion-body text-muted">
                        <p>{% blocktrans %}The subscription can be paid monthly (every month) or annually (12 months in advance).{% endblocktrans %}</p>
                        <p>{% blocktrans %}The monthly subscription can be changed at any time with immediate effect (limit increase only) or for the next month.{% endblocktrans %}</p>
                        <p>{% blocktrans %}The annual subscription can be increased by paying the difference to the higher subscription for the remaining period purchased.{% endblocktrans %}</p>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse06">
                        {% trans 'What happens when I run out of funds?' %}
                    </button>
                </h2>
                <div id="collapse06" class="accordion-collapse collapse" data-bs-parent=".pricelist-regulations">
                    <div class="accordion-body text-muted">
                         <p>{% blocktrans %}If the credits run out while the program is running, Botie will ask the User to make a decision: interrupt, complete the operation (while saving the work results, it will only perform operations costing 0 credits), or wait until the User tops up the account (e.g. with a one-time purchase).{% endblocktrans %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<div class="mt-5 mb-5 divider"></div>
<script>
function toggleSubBox() {
    $('#pricelist_box_month').parent().toggle();
    $('#pricelist_box_year').parent().toggle();
}
function updateBox(boxname, el, id) {
    var amount = $('span.amount-option', el).text();
    var price = $('span.price-option', el).text();
    var annual_price = $('span.price-option', el).attr('rel');
    $('#pricelist_box_'+boxname+' input[name=pricelist_item_id]').val(id);
    $('#pricelist_box_'+boxname+' .price > span:first-child()').text(price);
    $('#pricelist_box_'+boxname+' .amount > span:first-child()').text(amount);
    if (boxname == 'year') $('#pricelist_box_'+boxname+' .annual_price').text(annual_price);
}
function addToBasket(boxname) {
    $('#pricelist_box_'+boxname+' form').attr('action','{% url 'add_to_basket' %}');
    $('#pricelist_box_'+boxname+' form').submit();
}
document.addEventListener("DOMContentLoaded", function () {

    //init popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
      return new bootstrap.Popover(popoverTriggerEl)
    });

    //show first item of accordion
    $(".accordion.pricelist-regulations button:first").trigger("click");

});
</script>
{% endblock %}