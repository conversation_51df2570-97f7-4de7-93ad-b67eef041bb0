import json

from django import forms
from django.forms import Textarea
from django.utils import timezone

from db.models import Scenario

default_scenario = json.loads("""
{
   "general_data": {
      "SID": "1124fd3c-3e14-49f1-a794-0d07e815ef68",
      "version": 0,
      "status": 0,
      "author_name": "Robosourcing",
      "author_rid": "7d5c1131-d38b-4edf-86fd-a279485effdb",
      "author_pid": "3b00b762b2fa4073a2bd6c9f9cba6de7",
      "author_uid": 15,
      "origin_id": null,
      "time_create": "2025-02-26T11:47:43.656358+01:00",
      "time_update": "2025-02-26T11:47:43.656374+01:00",
      "description": "<PERSON><PERSON><PERSON>z alternatywny, coś poszło nie tak. Uruchomienie strony do kontaktu do Botie",
      "programs": [],
      "tags": [],
      "categories": [],
      "available": 90,
      "editable": 10,
      "updatable": 10,
      "keyword_hashes": null,
      "mod_message": null,
      "cost": 2,
      "is_fixed_cost": true,
      "credits_alloc": "ask",
      "downloads": null,
      "uses": null,
      "rating": null,
      "scene_size": [
         100,
         100
      ],
      "structure_version": 1
   },
   "starting_box": {
      "box_type": "StartBox",
      "id": "********************************",
      "displayed_name": "Start",
      "housing": "SceneStart",
      "col": 4,
      "row": 2,
      "next_obj_id": "********************************",
      "start_config": {
         "scenario": {
            "_extraction": {}
         },
         "profile": {
            "_extraction": {},
            "_extraction_ss": {}
         },
         "aliases": {},
         "descriptions": {},
         "clear_logs": false,
         "stop_on_error": true
      },
      "scene_obj_type": "SceneStart"
   },
   "action_boxes": [
      {
         "box_type": "ActionBox",
         "id": "********************************",
         "displayed_name": "Kontakt",
         "housing": "SceneSequence",
         "col": 4.0,
         "row": 5.0,
         "next_obj_id": "********************************",
         "skip": false,
         "actions": [
            {
               "action_type": "ActionMessageBox",
               "id": "********************************",
               "displayed_name": "Brak nazwy",
               "skip": false,
               "config_params": {
                  "amb_message": {
                     "variable": null,
                     "text": "Przepraszam, coś poszło nie tak! Przekieruję Cię na stronę z formularzem kontaktowym",
                     "active_type": "text"
                  },
                  "amb_display_mode": "popup",
                  "amb_timeout": {
                     "variable": null,
                     "int": 3,
                     "active_type": "int"
                  }
               },
               "exit_vars": {
                  "code": "",
                  "value": "",
                  "message": ""
               },
               "on_error": 0
            },
            {
               "action_type": "ActionOpenBrowser",
               "id": "53a2851e54d44ac0bb89cce20e3b4cbf",
               "displayed_name": "Brak nazwy",
               "skip": false,
               "config_params": {
                  "aob_driver": "default",
                  "aob_mode": "normal",
                  "aob_size": "maximized",
                  "aob_profile_name": "use_empty",
                  "aob_profile_work_on_copy": "false",
                  "aob_debugger_mode": "",
                  "aob_debugger_port": {
                     "variable": null,
                     "text": "",
                     "active_type": "text"
                  },
                  "aob_exec_path": {
                     "variable": null,
                     "file": "",
                     "active_type": "file"
                  },
                  "aob_save_path": {
                     "variable": null,
                     "path": "",
                     "active_type": "path"
                  }
               },
               "exit_vars": {
                  "code": "",
                  "value": "",
                  "message": ""
               },
               "on_error": 0
            },
            {
               "action_type": "ActionNavigateToUrl",
               "id": "780e3a3e3d88455c8ee5756229fbd585",
               "displayed_name": "Brak nazwy",
               "skip": false,
               "config_params": {
                  "ant_tab": "current",
                  "url": {
                     "variable": null,
                     "url": "https://botie.pl/contact/",
                     "active_type": "url"
                  },
                  "ant_tab_id": {
                     "variable": null,
                     "_tab_id": null,
                     "active_type": "variable"
                  }
               },
               "exit_vars": {
                  "code": "",
                  "value": "",
                  "message": ""
               },
               "on_error": 0
            }
         ],
         "scene_obj_type": "SceneSequence"
      }
   ],
   "if_boxes": [],
   "extractors": [],
   "merges": [],
   "arrows": [
      {
         "nop": 2,
         "base_id": "********************************",
         "base_place": "bottom",
         "head_id": "********************************",
         "head_place": "top",
         "mid_points": []
      },
      {
         "nop": 2,
         "base_id": "********************************",
         "base_place": "bottom",
         "head_id": "********************************",
         "head_place": "top",
         "mid_points": []
      }
   ],
   "ends": [
      {
         "box_type": "EndBox",
         "id": "********************************",
         "displayed_name": "Koniec",
         "housing": "SceneEnd",
         "col": 4.0,
         "row": 8.0,
         "next_obj_id": "",
         "scene_obj_type": "SceneEnd"
      }
   ],
   "var_groups": [
      {
         "id": "gid_current",
         "add_checkbox": true,
         "button": "",
         "type_set": "basic",
         "long_term": false,
         "custom_name": null,
         "custom_hint": null
      },
      {
         "id": "gid_actions",
         "add_checkbox": true,
         "button": "save_to_var",
         "type_set": "basic",
         "long_term": false,
         "custom_name": null,
         "custom_hint": null
      },
      {
         "id": "gid_webhunter",
         "add_checkbox": true,
         "button": "expand_down",
         "type_set": "webhunter",
         "long_term": false,
         "custom_name": null,
         "custom_hint": null
      },
      {
         "id": "gid_extractors",
         "add_checkbox": false,
         "button": "expand_down",
         "type_set": "extractors",
         "long_term": false,
         "custom_name": null,
         "custom_hint": null
      }
   ],
   "variables": [
      {
         "id": "78159821e40847a48d7576f8be23914c",
         "group_id": "gid_current",
         "name": "Licznik",
         "value": "0",
         "type_set_name": "basic",
         "catch_exit": null,
         "template": ""
      }
   ]
}
""")


class ConceptScenarioForm(forms.ModelForm):
    class Meta:
        model = Scenario
        fields = ["name", "description", "apps", "tags", "categories"]
        widgets = {
            'name': Textarea(attrs={'rows': 1, 'style': 'width: 100%'}),
            'description': Textarea(attrs={'rows': 10, 'style': 'width: 100%'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Ustawienie posortowanych querysetów dla pól ManyToMany (potrzebne tylko do sortowania)
        self.fields['apps'].queryset = self.fields['apps'].queryset.order_by('name', 'version_number',
                                                                             'build_number', 'producer')
        self.fields['tags'].queryset = self.fields['tags'].queryset.order_by('name')
        self.fields['categories'].queryset = self.fields['categories'].queryset.order_by('name')

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.scenario_version = -1
        instance.author = 'Robosourcing'
        instance.price = 0
        instance.publish_time = timezone.now()
        instance.status = Scenario.ScenarioStatus.ACCEPTED
        instance.available = Scenario.AvailabilityStatus.PUBLIC
        instance.editable = Scenario.AvailabilityStatus.PRIVATE
        instance.updatable = Scenario.AvailabilityStatus.PRIVATE
        instance.scenario = default_scenario
        instance.save()
        self.save_m2m()  # zapisuje relacje ManyToMany
        # print("Zapisano:", instance.apps.all(), instance.tags.all(), instance.categories.all())
        return instance


class ScenarioForm(forms.ModelForm):
    class Meta:
        model = Scenario
        fields = '__all__'
        widgets = {
            'name': Textarea(attrs={'rows': 1, 'style': 'width: 100%'}),
            'description': Textarea(attrs={'rows': 10, 'style': 'width: 100%'}),
            'scenario': Textarea(attrs={'rows': 10, 'style': 'width: 100%'}),
        }
