{% load static %}
{% load i18n %}
{% load crispy_forms_filters %}
{% load user_tags %}

<div class="d-flex flex-column flex-shrink-0" id="sidebar-wrapper">
  <div class="toggler d-table h-100 w-100">
    <div class="d-table-cell align-middle">
      <div class="w-100 text-center py-3" onclick="$('#sidebar-wrapper').toggleClass('sidebar-expanded'); $('i', this).toggleClass('fa-chevron-right').toggleClass('fa-chevron-left')">
        <i class="fa-solid fa-chevron-right"></i>
      </div>
    </div>
  </div>
  <!-- <a href="/" class="d-block p-3 link-dark text-decoration-none" title="" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-original-title="Icon-only">
      <svg class="bi" width="40" height="32"><use xlink:href="#bootstrap"></use></svg>
      <span class="visually-hidden">Icon-only</span>
    </a> -->
  {% if user.is_authenticated %}
  <ul class="nav">
      <div class="sidebar-menu">
        <li>
        <a class="sidebar-logo ms-1" href="{% url 'home' %}">
          <div class="logo-sidebar"></div>
          </a>
        </li>
      <li>
        <a href="{% url 'home' %}" class="nav-link py-2" title="" data-bs-toggle="tooltip" data-bs-placement="right"
          data-bs-original-title="Dashboard">
          {% if 'home' in request.path %}
          <div class="menu-chosen-bg">
            
          {% else %}
          <div class="menu-unchosen-bg">
  
          {% endif %}
          <i class="fa fa-home" aria-hidden="true" style="font-size: larger; padding: 10px 0px 10px 10px;"></i>  
          &emsp;{% trans "Dashboard" %}
        </div>
        
        </a>        
      </li>
      <li>
        <a href="{% url 'store' %}" class="nav-link py-2" title="" data-bs-toggle="tooltip" data-bs-placement="right"
          data-bs-original-title="Dashboard">
          {% if 'store' in request.path %}
          <div class="menu-chosen-bg">
          {% else %}
          <div class="menu-unchosen-bg">
          {% endif %}
          <i class="fa fa-shop" aria-hidden="true" style="font-size: larger; padding: 10px 0px 10px 10px;"></i>
          &emsp;{% trans "Store" %}
          </div>
        </a>
      </li>
      <li>
        <a href="{% url 'credits' %}" class="nav-link py-2" title="" data-bs-toggle="tooltip" data-bs-placement="right"
          data-bs-original-title="Dashboard">
          {% if 'profiles/credits' in request.path %}
          <div class="menu-chosen-bg">
          {% else %}
          <div class="menu-unchosen-bg">
          {% endif %}
          <i class="fa fa-coins" aria-hidden="true" style="font-size: larger; padding: 10px 0px 10px 10px;"></i>
          &emsp;{% trans "Credits" %}
          </div>
        </a>
      </li>
      <li>
        <a href="{% url 'robots' %}" class="nav-link py-2" title="" data-bs-toggle="tooltip" data-bs-placement="right"
          data-bs-original-title="Dashboard">
          {% if 'robots' in request.path %}
          <div class="menu-chosen-bg">
          {% else %}
          <div class="menu-unchosen-bg">
          {% endif %}
          <i class="fa fa-cogs" aria-hidden="true" style="font-size: larger; padding: 10px 0px 10px 10px;"></i>
          &emsp;{% trans "Robots" %}
        </div>
        </a>
      </li>
      <li>
        <a href="{% url 'robot_scenarios_view' %}" class="nav-link py-2" title="" data-bs-toggle="tooltip" data-bs-placement="right"
          data-bs-original-title="Dashboard">
          {% if 'profiles/scenarios' in request.path %}
          <div class="menu-chosen-bg">
          {% else %}
          <div class="menu-unchosen-bg">
          {% endif %}
          <i class="fa fa-book" aria-hidden="true" style="font-size: larger; padding: 10px 0px 10px 10px;"></i>
          &emsp;{% trans "Scenarios" %}
        </div>
        </a>
      </li>

      {% endif %}
      {% if request.user|has_group:"Partner" %}
      <li>
        <a href="{% url 'partner_dashboard_menu' %}" class="nav-link py-2" title="" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-original-title="Dashboard">
          {% if 's/partner_dashboard_menu' in request.path %}
          <div class="menu-chosen-bg">
          {% else %}
          <div class="menu-unchosen-bg">
          {% endif %}
          <i class="fa fa-handshake" aria-hidden="true" style="font-size: larger; padding: 10px 0px 10px 10px;"></i>
          &emsp;{% trans "Partner Panel" %}
          </div>
        </a>
      </li>
    {% endif %}

      {% if request.user|has_group:"Moderator" %}
      <li>
        <a href="{% url 'scenario_mod' %}" class="nav-link py-2" title="" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-original-title="Dashboard">
          {% if 's/scenario_mod' in request.path %}
          <div class="menu-chosen-bg">
          {% else %}
          <div class="menu-unchosen-bg">
          {% endif %}
          <i class="fa fa-user-shield" aria-hidden="true" style="font-size: larger; padding: 10px 0px 10px 10px;"></i>
          &emsp;{% trans "Moderator Panel" %}
          </div>
        </a>
      </li>

    </div>
  </ul>
  {% endif %}
</div>
