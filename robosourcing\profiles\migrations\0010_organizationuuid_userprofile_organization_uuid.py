# Generated by Django 4.2.13 on 2024-08-23 08:55

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('profiles', '0009_alter_address_city_alter_address_country_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrganizationUUID',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tax_id', models.CharField(max_length=25)),
                ('name_1', models.CharField(blank=True, max_length=100, null=True)),
                ('name_2', models.CharField(blank=True, max_length=100, null=True)),
                ('street', models.CharField(blank=True, max_length=100, null=True)),
                ('street_number', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('home_number', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('city', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('postal_code', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'ordering': ['tax_id'],
            },
        ),
        migrations.AddField(
            model_name='userprofile',
            name='organization_uuid',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='profiles.organizationuuid'),
        ),
    ]
