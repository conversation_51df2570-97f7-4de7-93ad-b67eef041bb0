{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Newsletter Analytics" %}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{% trans "Analytics" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fa fa-chart-bar me-2"></i>{% trans "Newsletter Analytics" %}</h2>
        </div>
    </div>

    <!-- Statystyki ogólne -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="dashboard-card stats-card">
                <div class="card-body text-center">
                    <i class="fa fa-users fa-2x text-primary mb-2"></i>
                    <h3 class="mb-0">{{ total_subscribers }}</h3>
                    <p class="text-muted mb-0">{% trans "Total Subscribers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card stats-card">
                <div class="card-body text-center">
                    <i class="fa fa-check-circle fa-2x text-success mb-2"></i>
                    <h3 class="mb-0">{{ confirmed_subscribers }}</h3>
                    <p class="text-muted mb-0">{% trans "Confirmed" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card stats-card">
                <div class="card-body text-center">
                    <i class="fa fa-paper-plane fa-2x text-info mb-2"></i>
                    <h3 class="mb-0">{{ sent_campaigns }}</h3>
                    <p class="text-muted mb-0">{% trans "Campaigns Sent" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card stats-card">
                <div class="card-body text-center">
                    <i class="fa fa-eye fa-2x text-warning mb-2"></i>
                    <h3 class="mb-0">{{ recent_opens }}</h3>
                    <p class="text-muted mb-0">{% trans "Opens (30d)" %}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container">
    <div class="row">
        <!-- Top kampanie -->
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Top Performing Campaigns" %}</h5>
                </div>
                <div class="card-body">
                    {% if top_campaigns %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Campaign" %}</th>
                                        <th>{% trans "Sent" %}</th>
                                        <th>{% trans "Opens" %}</th>
                                        <th>{% trans "Clicks" %}</th>
                                        <th>{% trans "Open Rate" %}</th>
                                        <th>{% trans "Click Rate" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for campaign in top_campaigns %}
                                    <tr>
                                        <td>
                                            <strong>{{ campaign.subject }}</strong>
                                            <br><small class="text-muted">{{ campaign.created_at|date:"d.m.Y" }}</small>
                                        </td>
                                        <td>{{ campaign.newsletterdelivery_set.count }}</td>
                                        <td>{{ campaign.opens_count|default:0 }}</td>
                                        <td>{{ campaign.clicks_count|default:0 }}</td>
                                        <td>
                                            {% if campaign.newsletterdelivery_set.count > 0 %}
                                                {% widthratio campaign.opens_count campaign.newsletterdelivery_set.count 100 %}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if campaign.newsletterdelivery_set.count > 0 %}
                                                {% widthratio campaign.clicks_count campaign.newsletterdelivery_set.count 100 %}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fa fa-chart-bar fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No campaign data yet" %}</h5>
                            <p class="text-muted">{% trans "Send some campaigns to see analytics here." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Dodatkowe statystyki -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Engagement Overview" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ recent_opens }}</h4>
                            <p class="text-muted">{% trans "Total Opens" %}</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ recent_clicks }}</h4>
                            <p class="text-muted">{% trans "Total Clicks" %}</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <p class="text-muted mb-0">{% trans "Last 30 days activity" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Campaign Summary" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-info">{{ total_campaigns }}</h4>
                            <p class="text-muted">{% trans "Total Campaigns" %}</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ sent_campaigns }}</h4>
                            <p class="text-muted">{% trans "Sent Campaigns" %}</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            {% if total_campaigns > 0 %}
                                <p class="text-muted mb-0">
                                    {% widthratio sent_campaigns total_campaigns 100 %}% {% trans "completion rate" %}
                                </p>
                            {% else %}
                                <p class="text-muted mb-0">{% trans "No campaigns created yet" %}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
