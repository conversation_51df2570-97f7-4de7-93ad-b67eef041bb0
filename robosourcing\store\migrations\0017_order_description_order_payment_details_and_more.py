# Generated by Django 4.1.9 on 2024-06-19 17:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0016_alter_subscriptionevent_change_mode'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='description',
            field=models.CharField(default='brak', max_length=250),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='order',
            name='payment_details',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='price_incl_tax',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=99),
        ),
        migrations.AddField(
            model_name='order',
            name='tax',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=99),
        ),
        migrations.AddField(
            model_name='subsequentorder',
            name='price_incl_tax',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=99),
        ),
        migrations.AddField(
            model_name='subsequentorder',
            name='tax',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=99),
        ),
        migrations.AlterField(
            model_name='subsequentorder',
            name='price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=99),
        ),
    ]
