from django.contrib import admin
from .models import StaticPageMeta
from .models import ObjectMeta
from store.models import Product
from django.contrib.contenttypes.admin import GenericTabularInline

@admin.register(StaticPageMeta)
class StaticPageMetaAdmin(admin.ModelAdmin):
    list_display = ('page', 'title')


@admin.register(ObjectMeta)
class ObjectMetaAdmin(admin.ModelAdmin):
    list_display = ('content_type', 'object_id', 'title')
    search_fields = ('title', 'description')

class ObjectMetaInline(GenericTabularInline):
    model = ObjectMeta
    extra = 1
    fields = ['title', 'description', 'keywords', 'og_image']