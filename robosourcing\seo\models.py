from django.db import models
from meta.models import ModelMeta
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey


STATIC_PAGES = [
    ('home', 'Home'),
    ('contact', 'Contact'),
    ('how_it_works', 'How it works'),
    ('scenar<PERSON><PERSON>', '<PERSON><PERSON>rio<PERSON>'),
    ('download', 'Download'),
    ('pricelist', 'Pricelist'),
]

class StaticPageMeta(ModelMeta, models.Model):
    page = models.CharField(max_length=50, choices=STATIC_PAGES, unique=True, verbose_name="Strona")
    title = models.CharField(max_length=200, verbose_name="Tytuł")
    description = models.TextField(blank=True, verbose_name="Opis")
    keywords = models.CharField(max_length=255, blank=True, verbose_name="Słowa kluczowe")
    image = models.ImageField(upload_to='seo/', blank=True, null=True, verbose_name="Obraz do OpenGraph")

    def __str__(self):
        return dict(STATIC_PAGES).get(self.page, self.page)

    class Meta:
        verbose_name = "Metadane statycznej strony"
        verbose_name_plural = "Metadane statycznych stron"


class ObjectMeta(models.Model):
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    keywords = models.CharField(max_length=255, blank=True, null=True) 
    og_image = models.ImageField(upload_to="seo_og/", blank=True, null=True)

    # powiązanie generyczne z dowolnym modelem
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveBigIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    class Meta:
        unique_together = ('content_type', 'object_id')

class ScenarioMeta(models.Model):
    scenario = models.OneToOneField('db.Scenario', on_delete=models.CASCADE, related_name='seo_meta')
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    keywords = models.CharField(max_length=255, blank=True, null=True)
    image = models.ImageField(upload_to='seo_images/', blank=True, null=True)

    def __str__(self):
        return f"SEO for {self.scenario.name}"