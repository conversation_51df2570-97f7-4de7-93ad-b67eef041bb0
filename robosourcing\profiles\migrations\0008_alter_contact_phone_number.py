# Generated by Django 4.2.13 on 2024-08-18 14:41

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('profiles', '0007_alter_organization_street_number'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contact',
            name='phone_number',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator(message='Phone number must be entered with 7 to 10 digits.', regex='^\\d{7,10}$')]),
        ),
    ]
