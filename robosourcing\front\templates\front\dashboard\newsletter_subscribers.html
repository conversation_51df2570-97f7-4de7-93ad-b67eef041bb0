{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Newsletter Subscribers" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item site-name"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Subscribers" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fa fa-users me-2"></i>{% trans "Newsletter Subscribers" %}</h2>
        </div>
    </div>

    <!-- Filtry -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">{% trans "Search by email" %}</label>
                            <input type="text" class="form-control" id="search" name="search" value="{{ search }}" placeholder="{% trans 'Enter email address' %}">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">{% trans "All" %}</option>
                                <option value="confirmed" {% if status_filter == 'confirmed' %}selected{% endif %}>{% trans "Confirmed" %}</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>{% trans "Pending" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> {% trans "Filter" %}
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <a href="{% url 'profiles_newsletter_subscribers' %}" class="btn btn-outline-secondary">
                                    <i class="fa fa-times"></i> {% trans "Clear" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        {% trans "Subscribers" %} 
                        {% if subscribers.paginator.count %}
                            ({{ subscribers.paginator.count }})
                        {% endif %}
                    </h5>
                    <div>
                        <button class="btn btn-sm btn-outline-success" onclick="exportSubscribers()">
                            <i class="fa fa-download"></i> {% trans "Export CSV" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if subscribers %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Email" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Subscribed" %}</th>
                                        <th>{% trans "Confirmed" %}</th>
                                        <th>{% trans "Source" %}</th>
                                        <th>{% trans "Segments" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subscriber in subscribers %}
                                    <tr>
                                        <td>
                                            <strong>{{ subscriber.email }}</strong>
                                            {% if subscriber.first_name or subscriber.last_name %}
                                                <br><small class="text-muted">{{ subscriber.first_name }} {{ subscriber.last_name }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if subscriber.is_confirmed %}
                                                <span class="badge bg-success">{% trans "Confirmed" %}</span>
                                            {% else %}
                                                <span class="badge bg-warning">{% trans "Pending" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ subscriber.created_at|date:"d.m.Y H:i" }}</td>
                                        <td>
                                            {% if subscriber.confirmed_at %}
                                                {{ subscriber.confirmed_at|date:"d.m.Y H:i" }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ subscriber.source|default:"-" }}</td>
                                        <td>
                                            {% for segment in subscriber.segments.all %}
                                                <span class="badge bg-info me-1">{{ segment.name }}</span>
                                            {% empty %}
                                                <span class="text-muted">-</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                {% if not subscriber.is_confirmed %}
                                                    <button class="btn btn-outline-success btn-sm" onclick="confirmSubscriber('{{ subscriber.id }}')">
                                                        <i class="fa fa-check"></i>
                                                    </button>
                                                {% endif %}
                                                <button class="btn btn-outline-danger btn-sm" onclick="deleteSubscriber('{{ subscriber.id }}')">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Paginacja -->
                        {% if subscribers.has_other_pages %}
                        <nav aria-label="Subscribers pagination">
                            <ul class="pagination justify-content-center">
                                {% if subscribers.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ subscribers.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                            {% trans "Previous" %}
                                        </a>
                                    </li>
                                {% endif %}
                                
                                {% for num in subscribers.paginator.page_range %}
                                    {% if subscribers.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > subscribers.number|add:'-3' and num < subscribers.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if subscribers.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ subscribers.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                            {% trans "Next" %}
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fa fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No subscribers found" %}</h5>
                            <p class="text-muted">{% trans "No subscribers match your current filters." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmSubscriber(subscriberId) {
    if (confirm('{% trans "Are you sure you want to confirm this subscriber?" %}')) {
        // AJAX call to confirm subscriber
        fetch(`/newsletter/confirm-subscriber/${subscriberId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error confirming subscriber" %}');
            }
        });
    }
}

function deleteSubscriber(subscriberId) {
    if (confirm('{% trans "Are you sure you want to delete this subscriber?" %}')) {
        // AJAX call to delete subscriber
        fetch(`/newsletter/delete-subscriber/${subscriberId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error deleting subscriber" %}');
            }
        });
    }
}

function exportSubscribers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '?' + params.toString();
}
</script>
{% endblock %}
