# Generated by Django 4.2.13 on 2024-05-19 15:44

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('profiles', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='address',
            name='home_number',
            field=models.Char<PERSON>ield(blank=True, max_length=10, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='address',
            name='postal_code',
            field=models.Char<PERSON>ield(max_length=6, validators=[django.core.validators.RegexValidator('^\\d{2}-\\d{3}$|^\\d{5}$', 'Invalid postal code format. Expected formats: XX-XXX or XXXXX')]),
        ),
        migrations.Alter<PERSON>ield(
            model_name='address',
            name='street_number',
            field=models.Char<PERSON>ield(max_length=10),
        ),
        migrations.Alter<PERSON>ield(
            model_name='organization',
            name='home_number',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=10, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='organization',
            name='postal_code',
            field=models.Char<PERSON>ield(max_length=6, validators=[django.core.validators.RegexValidator('^\\d{2}-\\d{3}$|^\\d{5}$', 'Invalid postal code format. Expected formats: XX-XXX or XXXXX')]),
        ),
        migrations.AlterField(
            model_name='organization',
            name='street_number',
            field=models.CharField(max_length=10),
        ),
    ]
