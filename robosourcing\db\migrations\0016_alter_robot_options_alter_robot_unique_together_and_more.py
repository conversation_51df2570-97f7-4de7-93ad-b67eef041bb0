# Generated by Django 4.2.13 on 2024-07-12 12:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0015_alter_robot_options_remove_robot_robot_not_both_null_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='robot',
            options={'ordering': ['owner', 'last_activ_time']},
        ),
        migrations.AlterUniqueTogether(
            name='robot',
            unique_together={('hid', 'pid')},
        ),
        migrations.AddConstraint(
            model_name='robot',
            constraint=models.CheckConstraint(check=models.Q(('hid__isnull', False), ('owner__isnull', False), _connector='OR'), name='robot_not_both_null'),
        ),
    ]
