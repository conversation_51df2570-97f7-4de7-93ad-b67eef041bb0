# Generated by Django 4.2.13 on 2025-06-05 09:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('seo', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='staticpagemeta',
            name='page',
            field=models.CharField(choices=[('home', 'Home'), ('contact', 'Contact'), ('how_it_works', 'How it works'), ('scenariusze', 'Scenarios'), ('download', 'Download'), ('pricelist', 'Pricelist')], max_length=50, unique=True, verbose_name='Strona'),
        ),
        migrations.CreateModel(
            name='ObjectMeta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('og_image', models.ImageField(blank=True, null=True, upload_to='seo_og/')),
                ('object_id', models.PositiveBigIntegerField()),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'unique_together': {('content_type', 'object_id')},
            },
        ),
    ]
