{% extends 'main.html' %}
{% load i18n %}
{% load static %}

{% block head_title %}{% trans "Newsletter Subscription" %}{% endblock %}

{% block title %}
{{ block.super }} - {% trans "Newsletter Subscription" %}
{% endblock %}

{% block content %}
<div class="container mt-3">
    <div class="container section-title fade-in pb-2">
        <h2>{% trans "Newsletter Subscription" %}</h2>
        <p class="text-muted">{% trans "Stay up to date with the world of robotization and receive a handful of the most important information every month" %}</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <form method="post" action="{% url 'newsletter_subscribe' %}" class="mt-3">
                {% csrf_token %}

                <div class="mb-3">
                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="text-danger">{{ form.email.errors }}</div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.captcha.id_for_label }}" class="form-label">{{ form.captcha.label }}</label>
                    {{ form.captcha }}
                    {% if form.captcha.errors %}
                        <div class="text-danger">{{ form.captcha.errors }}</div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <h5>{% trans "Required Consents" %}</h5>
                    
                    <div class="form-check mb-2">
                        {{ form.data_processing_consent }}
                        <label class="form-check-label" for="{{ form.data_processing_consent.id_for_label }}">
                            {{ form.data_processing_consent.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {% if form.data_processing_consent.errors %}
                            <div class="text-danger">{{ form.data_processing_consent.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-check mb-2">
                        {{ form.marketing_consent }}
                        <label class="form-check-label" for="{{ form.marketing_consent.id_for_label }}">
                            {{ form.marketing_consent.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {% if form.marketing_consent.errors %}
                            <div class="text-danger">{{ form.marketing_consent.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="mb-3">
                    <small class="text-muted">
                        <strong>{% trans "Data Processing Information" %}:</strong><br>
                        {% trans "Your email address will be used solely for sending newsletter content. You can unsubscribe at any time by clicking the link in any newsletter email." %}
                        <br><br>
                        <strong>{% trans "Administrator" %}:</strong> Robosourcing<br>
                        <strong>{% trans "Contact" %}:</strong> <EMAIL>
                    </small>
                </div>

                <button type="submit" class="btn btn-primary">
                    {% trans "Subscribe to Newsletter" %}
                </button>
                
                <a href="{% url 'index' %}" class="btn btn-secondary ms-2">
                    {% trans "Cancel" %}
                </a>
            </form>
        </div>
    </div>
</div>
<div class="mt-5 mb-5 divider"></div>
{% endblock %}
