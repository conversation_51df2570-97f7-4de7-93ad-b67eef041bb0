{% extends "front/dashboard/base.html" %}

{% load i18n %}

{% block title %}{% trans "Scenario history" %}{% endblock %}


{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{%url 'scenario_mod'%}">{% trans "Moderation Panel" %}</a></li>
          <li class="breadcrumb-item site-name">
            <a href="{% url 'scenario_mod_details' scenario.sid %}">
                {{ scenario.name }} - {% trans "Details" %}
            </a>
          </li>
          <li class="breadcrumb-item site-name active">{% trans "Change History" %}</li>
        </ol>
    </nav>
    <div class="row">
        <div class="col-md-2">
            <div class="dashboard-element">
                <h2 class="dashboard-header">{% trans "View" %}</h2>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <a href="{% url 'scenario_mod_details' scenario.sid %}" class="btn btn-sm btn-primary">{% trans "Details" %}</a>
                </div>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <a href="{% url 'scenario_change_history' scenario.sid %}" class="btn btn-sm btn-outline-primary">{% trans "Change History" %}</a>
                </div>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <a href="" class="btn btn-sm btn-primary">{% trans "Comments" %}</a>
                </div>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <a href="" class="btn btn-sm btn-primary">{% trans "Documents" %}</a>
                </div>
            </div>
        </div>
        <div class="col-md-10">
            <div class="dashboard-element">
                <h2 class="dashboard-header">{{ scenario.name }}<span class="fw-lighter">- {% trans "Change History" %}</span></h2>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <div class="dashboard-body-section mb-5">
                        <h6>{% trans "Change History" %} - {{ scenario.name }}</h6>
                        <hr>
                        <table class="table _text-white">
                            <thead>
                                <tr>
                                    <th>{% trans "Change Date" %}</th>
                                    <th>{% trans "Event Type" %}</th>
                                    <th>{% trans "User" %}</th>
                                    <th>{% trans "Details" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for event in events %}
                                <tr>
                                    <td>{{ event.event_time }}</td>
                                    <td>{{ event.get_type_id_display }}</td>
                                    <td>{{ event.user_id.username }}</td>
                                    <td>
                                        {% for message in event.scenario_event_message_set.all %}
                                            {{ message.message }}
                                        {% empty %}
                                            {% trans "No details" %}
                                        {% endfor %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
