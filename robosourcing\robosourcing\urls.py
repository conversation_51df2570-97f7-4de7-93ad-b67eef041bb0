"""robosourcing URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.views.generic import TemplateView
from django.views.i18n import JavaScriptCatalog

urlpatterns = [
    # path('dashboard/', include('dashboard.urls')),
    path('accounts/', include('allauth.urls')),
    path('admin/webalizer/', include('webalizer.urls')),
    path('admin/', admin.site.urls),
    path('api/', include('api.urls')),
    path('profiles/', include('profiles.urls')),
    path('api/scenarios/', include('scenarios.urls')),
    # path('', include('scenariosMacieja.urls')),
    path('', include('front.urls')),
    path('store/', include('store.urls')),
    path('maps/', include('robo_map.urls')),
    path('i18n/', include('django.conf.urls.i18n')),
    path('jsi18n/', JavaScriptCatalog.as_view(), name='javascript-catalog'),
]
