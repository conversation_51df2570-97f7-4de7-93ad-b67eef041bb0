import re

from allauth.account.adapter import Default<PERSON><PERSON>untAdapter
from django.template import TemplateDoesNotExist
from django.template.loader import render_to_string


class CustomAccountAdapter(DefaultAccountAdapter):

    @staticmethod
    def _convert_urls_to_links(text):

        def replace_url_with_link(match):
            url = match.group(0)
            # Jeśli URL zaczyna się od "www", dodaj "http://"
            if url.startswith('www'):
                url = 'http://' + url
            return f'<a href="{url}">{url}</a>'

        url_pattern = re.compile(r'(?<!href=["\'])(https?://\S+|www\.\S+)', re.IGNORECASE)
        return re.sub(url_pattern, replace_url_with_link, text)

    def send_mail(self, template_prefix, email, context):
        """
        Renders an e-mail to `email`.  `template_prefix` identifies the
        e-mail that is to be sent, e.g. "account/email/email_confirmation"
        Relpaces url to anchors.
        Sends after that using Mailer() class.
        """
        to = [email] if isinstance(email, str) else email
        subject = render_to_string("{0}_subject.txt".format(template_prefix), context)
        # remove superfluous line breaks
        subject = " ".join(subject.splitlines()).strip()
        subject = self.format_email_subject(subject)

        bodies = {}
        for ext in ["html", "txt"]:
            try:
                template_name = "{0}_message.{1}".format(template_prefix, ext)
                bodies[ext] = render_to_string(
                    template_name,
                    context,
                    self.request,
                ).strip()
            except TemplateDoesNotExist:
                if ext == "txt" and not bodies:
                    # We need at least one body
                    raise

        # replaces urls (existing in allauth messages) to anchor tags
        if "html" in bodies.keys():
            bodies["html"] = self._convert_urls_to_links(bodies["html"])

        # send (allauth messages) with Mailer()
        from db.mailer import Mailer
        mailer = Mailer()
        return mailer.send_html_mail(None, subject, bodies["txt"], bodies["html"], to)
