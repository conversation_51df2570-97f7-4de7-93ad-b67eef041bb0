{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_templates' %}">{% trans "Templates" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{{ title }}</li>
        </ol>
    </nav>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="newsletter-card">
                <div class="newsletter-card-header">
                    <h5 class="mb-0">
                        {% if action == 'create' %}
                            <i class="fa fa-plus me-2"></i>{% trans "Create New Template" %}
                        {% else %}
                            <i class="fa fa-edit me-2"></i>{% trans "Edit Template" %}
                        {% endif %}
                    </h5>
                </div>
                <div class="newsletter-card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            {{ form.is_active.label }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.subject.id_for_label }}" class="form-label">{{ form.subject.label }}</label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <div class="invalid-feedback d-block">{{ form.subject.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.content_html.id_for_label }}" class="form-label">{{ form.content_html.label }}</label>
                            <div class="newsletter-help-text mb-2">
                                <div class="alert alert-info">
                                    <strong><i class="bi bi-info-circle me-2"></i>{% trans "Template Usage:" %}</strong><br>
                                    <small>
                                        {% trans "Your template will automatically use the Botie email base template with header, footer and styling." %}<br>
                                        {% trans "Just write the main content - no need for full HTML structure." %}
                                    </small>
                                </div>
                                <small class="text-muted">
                                    <strong>{% trans "Available variables:" %}</strong>
                                    <code>{% verbatim %}{{ subscriber.email }}{% endverbatim %}</code>,
                                    <code>{% verbatim %}{{ unsubscribe_url }}{% endverbatim %}</code>,
                                    <code>{% verbatim %}{{ current_date }}{% endverbatim %}</code>,
                                    <code>{% verbatim %}{{ site_url }}{% endverbatim %}</code>
                                </small>
                            </div>
                            {{ form.content_html }}
                            {% if form.content_html.errors %}
                                <div class="invalid-feedback d-block">{{ form.content_html.errors.0 }}</div>
                            {% endif %}
                        </div>



                        <div class="d-flex justify-content-between">
                            <a href="{% url 'profiles_newsletter_templates' %}" class="newsletter-btn-secondary btn">
                                <i class="fa fa-arrow-left"></i> {% trans "Back" %}
                            </a>
                            <div>
                                {% if action == 'edit' %}
                                    <a href="{% url 'profiles_newsletter_template_preview' template.id %}" class="newsletter-btn-secondary btn me-2" target="_blank">
                                        <i class="fa fa-eye"></i> {% trans "Preview" %}
                                    </a>
                                {% endif %}
                                <button type="submit" class="newsletter-btn-primary btn">
                                    <i class="fa fa-save"></i> {% trans "Save Template" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    const subjectField = document.getElementById('{{ form.subject.id_for_label }}');
    
    // Walidacja formularza
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        if (!nameField.value.trim()) {
            isValid = false;
            nameField.classList.add('is-invalid');
        } else {
            nameField.classList.remove('is-invalid');
        }
        
        if (!subjectField.value.trim()) {
            isValid = false;
            subjectField.classList.add('is-invalid');
        } else {
            subjectField.classList.remove('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('{% trans "Please fill in all required fields" %}');
        }
    });
});
</script>
{% endblock %}
