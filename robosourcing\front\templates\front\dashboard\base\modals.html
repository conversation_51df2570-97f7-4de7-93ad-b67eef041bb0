{% load i18n %}

<div class="modal fade bd-example-modal-lg"
             id="exampleModalCenter"
             tabindex="-1"
             role="dialog"
             aria-labelledby="exampleModalCenterTitle"
             aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">{% trans "Are you sure you want to sign out bro!?" %}</h5>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <form method="post" action="{% url 'account_logout' %}">
                    {% csrf_token %}
                    {% if redirect_field_value %}
                        <input type="hidden"
                               name="{{ redirect_field_name }}"
                               value="{{ redirect_field_value }}"/>
                    {% endif %}
                    <button class="btn btn-primary" type="submit">
                        {% trans "Yes" %}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal - create robot -->
<div class="modal fade" id="ModalCreateRobot" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="ModalCreateRobot" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="#">
          <i class="fas fa-key"></i>&nbsp;&nbsp;{% trans "Create new robot:" %}
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans "Close" %}"></button>
      </div>
      <div class="modal-body">
        <form action="{% url 'create_robot' %}" method='POST'>
          {% csrf_token %}
          {{ form.as_p }}
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
            <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal - generate key -->
<div class="modal fade" 
  id="key-modal" 
  tabindex="-1" 
  role="dialog" 
  aria-labelledby="key-modal-label" 
  aria-hidden="true"
  data-bs-backdrop="static">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="key-modal-label">{% trans "New Key" %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans "Close" %}"></button>
      </div>
      <div class="modal-body">
        <h4 id="key-modal-connect-key"><!-- KEY --></h4>
        <hr>
        <p>{% trans "Time created:" %} <span id="key-modal-created-time"><!-- TIME/DATE --></span></p>
        <p>{% trans "You can use key for next:" %} <span id="key-modal-end-time"><!-- TIME LEFT --></span> {% trans "minutes." %}</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="copy-key-btn">{% trans "Copy to Clipboard" %}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal - Assign scenario to robots -->
<div class="modal fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content _bg-dark _text-light">
      <form id="assignForm" method="POST" action="{% url 'add_favorite_scenario' %}">
        {% csrf_token %}
        <input type="hidden" name="scenario" value="">
        <div class="modal-header">
          <h5 class="modal-title" id="assignModalLabel">{% trans "Assign scenario to robots" %}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans "Close" %}"></button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <!-- Robot selector is hidden, we use checkboxes for better control -->
            <select name="robot" id="id_robot" multiple class="form-control" style="display:none;">
              {% for robot in user_robots %}
              <option value="{{ robot.rid }}" {% if robot.rid in assigned_robots %} selected {% endif %}>{{ robot.name }}</option>
              {% endfor %}
            </select>
            <!-- New way of selecting robots by user -->
            <div class="form-group">
              <!-- Select/Deselect all checkbox -->
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="select_all_robots">
                <label class="form-check-label" for="select_all_robots">
                  {% trans "Select/Deselect All" %}
                </label>
              </div>
              {% for robot in user_robots %}
              <div class="form-check">
                <input class="form-check-input robot-checkbox" type="checkbox" value="{{ robot.rid }}" id="robot_{{ robot.rid }}" name="robots[]" {% if robot.rid|stringformat:"s" in assigned_robots %} checked {% endif %}>
                <label class="form-check-label" for="robot_{{ robot.rid }}">
                  {{ robot.name }}
                </label>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
          <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
        </div>
      </form>
    </div>
  </div>
</div>


<!-- Name Modal -->
<div class="modal fade" id="nameModal" tabindex="-1" aria-labelledby="nameModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content _bg-dark _text-light">
      <div class="modal-header">
        <h5 class="modal-title" id="nameModalLabel">{% trans "Change personal data" %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans "Close" %}"></button>
      </div>
      <form method="post" class="needs-validation" novalidate>
        {% csrf_token %}
        <div class="modal-body">
          <p>{% trans "You can only change your first and last name in" %} <strong>{% trans "special cases" %}</strong>, {% trans "when the change is confirmed by court or administrative decision. Account holder change is only possible between spouses." %}</p>
          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="first_name">{% trans "First name" %}</label>
            <input type="text" id="first_name" name="first_name" class="form-control" placeholder="{% trans "First name" %}" value="{{ name_form.first_name.value|default_if_none:'' }}" required>
            <div class="invalid-feedback">{% trans "First name is required." %}</div>
          </div>
          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="last_name">{% trans "Last name" %}</label>
            <input type="text" id="last_name" name="last_name" class="form-control" placeholder="{% trans "Last name" %}" value="{{ name_form.last_name.value|default_if_none:'' }}" required>
            <div class="invalid-feedback">{% trans "Last name is required." %}</div>
          </div>
        </div>
        <div class="modal-footer justify-content-between">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
          <button type="submit" name="submit_name" class="btn btn-primary">{% if name_form.first_name.value %}{% trans "Save changes" %}{% else %}{% trans "Save" %}{% endif %}</button>
        </div>
      </form>
    </div>
  </div>
</div>


<!-- Address Modal -->
<div class="modal fade" id="addressModal" tabindex="-1" aria-labelledby="addressModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content _bg-dark _text-light">
      <div class="modal-header">
        <h5 class="modal-title" id="addressModalLabel">{% trans "Change address data" %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans "Close" %}"></button>
      </div>
      <form id="address-form" method="post" class="needs-validation" novalidate>
        {% csrf_token %}
        <div class="modal-body">
          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="address_country">{% trans "Country" %}</label>
            <select id="address_country" name="country" class="form-control" required>
                <option value="GB" {% if address_form.instance.country == 'GB' %}selected{% endif %}>{% trans "United Kingdom" %}</option>
                <option value="PL" {% if address_form.instance.country == 'PL' %}selected{% endif %}>{% trans "Poland" %}</option>
                <option value="DE" {% if address_form.instance.country == 'DE' %}selected{% endif %}>{% trans "Germany" %}</option>
                <option value="US" {% if address_form.instance.country == 'US' %}selected{% endif %}>{% trans "United States" %}</option>
                <option value="FR" {% if address_form.instance.country == 'FR' %}selected{% endif %}>{% trans "France" %}</option>
                <option value="IT" {% if address_form.instance.country == 'IT' %}selected{% endif %}>{% trans "Italy" %}</option>
                <option value="OTHER" {% if address_form.instance.country == 'OTHER' %}selected{% endif %}>{% trans "Other" %}</option>
            </select>
            <div class="invalid-feedback">{% trans "Country is required." %}</div>
          </div>

          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="street">{% trans "Street" %}</label>
            <input type="text" id="street" name="street" class="form-control" placeholder="{% trans "Street" %}" value="{{ address_form.street.value|default_if_none:'' }}" required>
            <div class="invalid-feedback">{% trans "Street is required." %}</div>
          </div>
          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="street_number">{% trans "Street number" %}</label>
            <input type="text" id="street_number" name="street_number" class="form-control" placeholder="{% trans "Street number" %}" value="{{ address_form.street_number.value|default_if_none:'' }}" required>
            <div class="invalid-feedback">{% trans "Street number is required." %}</div>
          </div>
          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="home_number">{% trans "Apartment number" %}</label>
            <input type="text" id="home_number" name="home_number" class="form-control" placeholder="{% trans "Apartment number" %}" value="{{ address_form.home_number.value|default_if_none:'' }}">
          </div>
          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="address_postal_code">{% trans "Postal code" %}</label>
            <input type="text" id="address_postal_code" name="postal_code" class="form-control" placeholder="{% trans "Postal code" %}" value="{{ address_form.postal_code.value|default_if_none:'' }}" required>
            <div class="invalid-feedback" id="address-postal-code-feedback"></div>
          </div>
          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="city">{% trans "City" %}</label>
            <input type="text" id="city" name="city" class="form-control" placeholder="{% trans "City" %}" value="{{ address_form.city.value|default_if_none:'' }}" required>
            <div class="invalid-feedback">{% trans "City is required." %}</div>
          </div>
          <div class="mb-3 floating-label-group">
            <label class="floating-label" for="timezone">{% trans "Timezone" %}</label>
            <select class="form-control" id="timezone" name="timezone" required>
                <option value=""></option>
                {% for tz in timezones %}
                    <option value="{{ tz.name }}"{% if address_form.timezone.value == tz.name %} selected{% endif %}>({{ tz.offset }}) {{ tz.name }}</option>
                {% endfor %}
            </select>
            <div class="invalid-feedback">{% trans "Timezone selection is required." %}</div>
          </div>
        </div>
        <div class="modal-footer justify-content-between">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
          <button type="submit" name="submit_address" class="btn btn-primary">{% if address_form.street.value %}{% trans "Save changes" %}{% else %}{% trans "Save" %}{% endif %}</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- PhoneNumber Modal -->
<div class="modal fade" id="phoneNumberModal" tabindex="-1" aria-labelledby="phoneNumberModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content _bg-dark _text-light">
      <div class="modal-header">
        <h5 class="modal-title" id="nameModalLabel">{% trans "Change phone number" %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans "Close" %}"></button>
      </div>
      <form id="phone-number-form" method="post" class="needs-validation" novalidate>
        {% csrf_token %}
        <div class="modal-body">
          <div class="row justify-content-center align-items-center g-2">
            <div class="col-4">
              <div class="mb-3 floating-label-group">
                <label class="floating-label" for="area_code">{% trans "Area code" %}</label>
                <input type="text" id="area_code" name="area_code" class="form-control" placeholder="{% trans "Area code" %}" value="{{ phone_number_form.area_code.value|default_if_none:'+48' }}" pattern="\+\d{2,3}" required>
                <div class="invalid-feedback">{% trans "Enter the country code in the format '+00' or '+000'." %}</div>
              </div>
            </div>
            <div class="col-8">
              <div class="mb-3 floating-label-group">
                <label class="floating-label" for="phone_number">{% trans "Phone number" %}</label>
                <input type="text" id="phone_number" pattern="\d{7,10}" name="phone_number" class="form-control" placeholder="{% trans "Phone number" %}" value="{{ phone_number_form.phone_number.value|default_if_none:'' }}" required>
                <div class="invalid-feedback position-absolute">{% trans "Enter a valid phone number 7-10 digits." %}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
          <button type="submit" name="submit_phone_number" class="btn btn-primary">{% if phone_number_form.phone_number.value %}{% trans "Save changes" %}{% else %}{% trans "Save" %}{% endif %}</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- SCENARIO - MOD - MODAL -->
<div class="modal fade" id="scenarioModal" tabindex="-1" aria-labelledby="scenarioModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content _bg-dark _text-light">
    </div>
  </div>
</div>

<!-- Modal - Block Credits for Offline Use -->
<div class="modal fade" id="blockCreditsModal" tabindex="-1" aria-labelledby="blockCreditsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content _bg-dark _text-light">
      <form id="blockCreditsForm" method="POST" action="{% url 'block_credits' %}">
        {% csrf_token %}
        <input type="hidden" name="robot" value="">
        <div class="modal-header">
          <h5 class="modal-title" id="blockCreditsModalLabel">{% trans "Block Credits for Offline Use" %}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans "Close" %}"></button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <p>{% trans "Total Credits:" %} <span id="total-credits">{{ user_credits.credits }}</span></p>
            <p>{% trans "Blocked Credits for this Robot:" %} <span id="robot-blocked-credits">{{ robot.cached_credits }}</span></p>
            <div class="form-group">
              <label for="credits">{% trans "Credits to Block/Unblock" %}</label>
              <input type="number" class="form-control" id="credits" name="credits" min="0" required>
              <select class="form-select mt-2" name="action">
                <option value="block">{% trans "Block Credits" %}</option>
                <option value="unblock">{% trans "Unblock Credits" %}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
          <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Organization Modal -->
<div class="modal fade" id="orgModal" tabindex="-1" aria-labelledby="orgModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
      <div class="modal-content _bg-dark _text-light">
          <div class="modal-header">
              <h5 class="modal-title" id="orgModalLabel">{% trans "Change organization data" %}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans "Close" %}"></button>
          </div>
          <form id="organization-form" method="post" class="needs-validation" novalidate>
              {% csrf_token %}
              <div class="modal-body">
                  {% if organization_details_form.errors %}
                      <div class="alert alert-danger" role="alert">
                          {{ organization_details_form.errors }}
                      </div>
                  {% endif %}
                  <!-- Country select dropdown -->
                  <div class="mb-3 floating-label-group">
                    <label class="floating-label" for="org_country">{% trans "Country" %}</label>
                    <select id="org_country" name="country" class="form-control" required>
                        <option value="GB" {% if organization_details_form.instance.country == 'GB' %}selected{% endif %}>{% trans "United Kingdom" %}</option>
                        <option value="PL" {% if organization_details_form.instance.country == 'PL' %}selected{% endif %}>{% trans "Poland" %}</option>
                        <option value="DE" {% if organization_details_form.instance.country == 'DE' %}selected{% endif %}>{% trans "Germany" %}</option>
                        <option value="US" {% if organization_details_form.instance.country == 'US' %}selected{% endif %}>{% trans "United States" %}</option>
                        <option value="FR" {% if organization_details_form.instance.country == 'FR' %}selected{% endif %}>{% trans "France" %}</option>
                        <option value="IT" {% if organization_details_form.instance.country == 'IT' %}selected{% endif %}>{% trans "Italy" %}</option>
                        <option value="OTHER" {% if organization_details_form.instance.country == 'OTHER' %}selected{% endif %}>{% trans "Other" %}</option>
                    </select>
                    <div class="invalid-feedback">{% trans "Country is required." %}</div>
                </div>

                  <!-- Tax ID input with dynamic pattern update -->
                  <div class="mb-3 floating-label-group">
                    <label class="floating-label" for="tax_id">{% trans "TAX number" %}</label>
                    <input type="text" id="tax_id" name="tax_id" class="form-control" placeholder="{% trans "TAX number" %}" value="{{ organization_details_form.tax_id.value|default_if_none:'' }}" required>
                    <div class="invalid-feedback" id="tax-id-feedback" style="display: none;"></div>
                </div>

                  <div class="mb-3 floating-label-group">
                      <label class="floating-label" for="name_1">{% trans "Organization name 1" %}</label>
                      <input type="text" id="name_1" name="name_1" class="form-control" placeholder="{% trans "Organization name 1" %}" value="{{ organization_details_form.name_1.value|default_if_none:'' }}" required>
                      <div class="invalid-feedback">{% trans "Organization name is required." %}</div>
                  </div>
                  <div class="mb-3 floating-label-group">
                      <label class="floating-label" for="name_2">{% trans "Organization name 2" %}</label>
                      <input type="text" id="name_2" name="name_2" class="form-control" placeholder="{% trans "Organization name 2" %}" value="{{ organization_details_form.name_2.value|default_if_none:'' }}">
                  </div>
                  <div class="mb-3 floating-label-group">
                      <label class="floating-label" for="street">{% trans "Street" %}</label>
                      <input type="text" id="street" name="street" class="form-control" placeholder="{% trans "Street" %}" value="{{ organization_details_form.street.value|default_if_none:'' }}" required>
                      <div class="invalid-feedback">{% trans "Organization street is required." %}</div>
                  </div>
                  <div class="row justify-content-center align-items-center g-2">
                      <div class="col">
                          <div class="mb-3 floating-label-group">
                              <label class="floating-label" for="street_number">{% trans "Street number" %}</label>
                              <input type="text" id="street_number" name="street_number" class="form-control" placeholder="{% trans "Street number" %}" value="{{ organization_details_form.street_number.value|default_if_none:'' }}" required>
                              <div class="invalid-feedback">{% trans "Street number is required." %}</div>
                          </div>
                      </div>
                      <div class="col">
                          <div class="mb-3 floating-label-group">
                              <label class="floating-label" for="home_number">{% trans "Apartment number" %}</label>
                              <input type="text" id="home_number" name="home_number" class="form-control" placeholder="{% trans "Apartment number" %}" value="{{ organization_details_form.home_number.value|default_if_none:'' }}">
                          </div>
                      </div>
                  </div>
                  <div class="row justify-content-center align-items-center g-2">
                      <div class="col">
                          <div class="mb-3 floating-label-group">
                            <label class="floating-label" for="org_postal_code">{% trans "Postal code" %}</label>
                            <input type="text" id="org_postal_code" name="postal_code" class="form-control" placeholder="{% trans "Postal code" %}" value="{{ organization_details_form.postal_code.value|default_if_none:'' }}" required>
                            <div class="invalid-feedback" id="org-postal-code-feedback"></div>
                          </div>
                      </div>
                      <div class="col">
                          <div class="mb-3 floating-label-group">
                              <label class="floating-label" for="city">{% trans "City" %}</label>
                              <input type="text" id="city" name="city" class="form-control" placeholder="{% trans "City" %}" value="{{ organization_details_form.city.value|default_if_none:'' }}" required>
                              <div class="invalid-feedback">{% trans "City is required." %}</div>
                          </div>
                      </div>
                  </div>
              </div>
              <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                  <button type="submit" name="submit_organization" class="btn btn-primary">{% trans "Save changes" %}</button>
              </div>
          </form>
      </div>
  </div>
</div>


<script id="validationMessages" type="application/json">
  {
      "GB": {
          "pattern": "^GB\\d{9}$",
          "message": "{% trans 'A valid UK VAT number must have the format GB followed by 9 digits.' %}"
      },
      "FR": {
          "pattern": "^FR\\w{2}\\d{9}$",
          "message": "{% trans 'A valid French VAT number must have the format FR followed by 2 characters and 9 digits.' %}"
      },
      "DE": {
          "pattern": "^DE\\d{9}$",
          "message": "{% trans 'A valid German VAT number must have the format DE followed by 9 digits.' %}"
      },
      "IT": {
          "pattern": "^IT\\d{11}$",
          "message": "{% trans 'A valid Italian VAT number must have the format IT followed by 11 digits.' %}"
      },
      "PL": {
          "pattern": "^\\d{10}$",
          "message": "{% trans 'A valid Polish NIP number must have 10 digits.' %}"
      },
      "US": {
          "pattern": "^\\d{3}-\\d{2}-\\d{4}$",
          "message": "{% trans 'A valid US SSN must have the format ***********.' %}"
      },
      "OTHER": {
          "pattern": ".*",
          "message": "{% trans 'No validation required for Other.' %}"
      }
  }
  </script>
  
  <script id="postalCodePatterns" type="application/json">
  {
      "GB": {
          "pattern": "^[A-Z]{1,2}\\d[A-Z\\d]? \\d[A-Z]{2}$",
          "message": "{% trans 'Example: W1A 1AA or W1A1AA' %}"
      },
      "FR": {
          "pattern": "^\\d{5}$",
          "message": "{% trans 'Example: 75001' %}"
      },
      "DE": {
          "pattern": "^\\d{5}$",
          "message": "{% trans 'Example: 10115' %}"
      },
      "IT": {
          "pattern": "^\\d{5}$",
          "message": "{% trans 'Example: 00100' %}"
      },
      "PL": {
          "pattern": "^\\d{2}-\\d{3}$",
          "message": "{% trans 'Example: 00-001' %}"
      },
      "US": {
          "pattern": "^\\d{5}(?:-\\d{4})?$",
          "message": "{% trans 'Example: 12345 or 12345-6789' %}"
      },
      "OTHER": {
          "pattern": ".*",
          "message": "{% trans 'No validation required for Other.' %}"
      }
  }
  </script>

<script>

</script>
