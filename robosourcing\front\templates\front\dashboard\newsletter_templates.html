{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Newsletter Templates" %}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{% trans "Templates" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fa fa-file-alt me-2"></i>{% trans "Newsletter Templates" %}</h2>
                <a href="{% url 'profiles_newsletter_template_create' %}" class="newsletter-btn-primary btn">
                    <i class="fa fa-plus"></i> {% trans "New Template" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <div class="row">
        {% if templates %}
            {% for template in templates %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="newsletter-card h-100">
                    <div class="newsletter-card-body">
                        <h5 class="card-title">{{ template.name }}</h5>
                        <p class="card-text" style="color: var(--muted-foreground);">{{ template.description|truncatechars:100 }}</p>
                        <div class="mt-auto">
                            <small style="color: var(--muted-foreground);">
                                {% trans "Created" %}: {{ template.created_at|date:"d.m.Y" }}
                            </small>
                        </div>
                    </div>
                    <div class="newsletter-card-footer">
                        <div class="btn-group w-100">
                            <a href="{% url 'profiles_newsletter_template_edit' template.id %}" class="newsletter-btn-primary btn btn-sm">
                                <i class="fa fa-edit"></i> {% trans "Edit" %}
                            </a>
                            <a href="{% url 'profiles_newsletter_template_preview' template.id %}" class="newsletter-btn-secondary btn btn-sm" target="_blank">
                                <i class="fa fa-eye"></i> {% trans "Preview" %}
                            </a>
                            <a href="{% url 'profiles_newsletter_template_delete' template.id %}" class="newsletter-btn-secondary btn btn-sm" onclick="return confirm('{% trans "Are you sure you want to delete this template?" %}')"
                                <i class="fa fa-trash"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fa fa-file-alt fa-3x mb-3" style="color: var(--muted-foreground);"></i>
                    <h5 style="color: var(--muted-foreground);">{% trans "No templates yet" %}</h5>
                    <p style="color: var(--muted-foreground);">{% trans "Create your first newsletter template to get started." %}</p>
                    <a href="{% url 'profiles_newsletter_template_create' %}" class="newsletter-btn-primary btn">
                        <i class="fa fa-plus"></i> {% trans "Create Template" %}
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
