{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Newsletter Templates" %}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{% trans "Templates" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fa fa-file-alt me-2"></i>{% trans "Newsletter Templates" %}</h2>
                <button class="btn btn-primary">
                    <i class="fa fa-plus"></i> {% trans "New Template" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container">
    <div class="row">
        {% if templates %}
            {% for template in templates %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="dashboard-card h-100">
                    <div class="card-body">
                        <h5 class="card-title">{{ template.name }}</h5>
                        <p class="card-text text-muted">{{ template.description|truncatechars:100 }}</p>
                        <div class="mt-auto">
                            <small class="text-muted">
                                {% trans "Created" %}: {{ template.created_at|date:"d.m.Y" }}
                            </small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="fa fa-edit"></i> {% trans "Edit" %}
                            </button>
                            <button class="btn btn-outline-secondary btn-sm">
                                <i class="fa fa-eye"></i> {% trans "Preview" %}
                            </button>
                            <button class="btn btn-outline-danger btn-sm">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fa fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "No templates yet" %}</h5>
                    <p class="text-muted">{% trans "Create your first newsletter template to get started." %}</p>
                    <button class="btn btn-primary">
                        <i class="fa fa-plus"></i> {% trans "Create Template" %}
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
