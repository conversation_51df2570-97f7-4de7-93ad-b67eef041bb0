from django import template
from django.template.defaultfilters import stringfilter

register = template.Library()

@register.filter
@stringfilter
def tolist(value):
    parts = value.split("\r\n\r\n")
    for index, part in enumerate(parts):
        lst = ("\r\n* ", "\r\n- ")
        for key in lst:
            part = part.replace(key, "<ul><li>", 1)
            part = part.replace(key, "<li>")
        part += '</ul>'
        parts[index] = part
    return "\r\n\r\n".join(parts)