{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load divide %}
{% load theme %}
{% load split %}
{% load reldelta %}

{% block title %}{{ block.super }} - {% trans "Update payment card" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{%url 'store'%}">{% trans "Store" %}</a></li>
          <li class="breadcrumb-item site-name active">{% trans "Payment card update" %}</li>
        </ol>
    </nav>
    <div class="dashboard-element">

        <!-- <h1>{% trans "Subscription change" %}</h1> -->
        <h3 class="db-section-title">{% blocktrans %}You can update you payment card details here.{% endblocktrans %}</h3>

        <hr>

        <div class="container">
            <div class="row align-items-center">
                <div class="col-xl-6">

                    <section class="payu-container">
                        <div class="card-container">
                            <aside>{% trans "Card Number" %}</aside>
                            <div class="payu-card-form" id="payu-card-number"></div>
                            <div class="card-details clearfix">
                                <div class="expiration">
                                    <aside>{% trans "Valid Thru" %}</aside>
                                    <div class="payu-card-form" id="payu-card-date"></div>
                                </div>
                                <div class="cvv">
                                    <aside>{% trans "CVV" %}</aside>
                                    <div class="payu-card-form" id="payu-card-cvv"></div>
                                </div>
                            </div>
                        </div>
                        <br/>
                        <div><button id="tokenizeButton" class="btn btn-primary">{% trans "Tokenize" %}</button></div>
                        <br/>
                        <div id="responseTokenize"></div>
                    </section>

                </div>

                <div class="col-xl-6">

                    <p class="text-center text-muted"><i class="fa-solid fa-lock fa-10x"></i></p>
                    <p class="text-center">{% blocktrans %}The tokenization of your payment card is handled by PayU, our trusted payment provider.
                        We do not store or process your card details on our servers.
                        PayU ensures that your data is securely encrypted and protected according to the highest industry standards.{% endblocktrans %}</p>
                </div>

            </div>
        </div>

    </div>

</div>

<script type="text/javascript" src="https://secure.snd.payu.com/javascript/sdk"></script>
<!-- <script type="text/javascript" src="https://secure.payu.com/javascript/sdk"></script> -->
<script type="text/javascript">
var optionsForms = {
  cardIcon: true,
  style: {
    basic: {
      fontSize: "24px",
    },
  },
  placeholder: {
    number: "",
    date: "{% trans "MM/YY" %}",
    cvv: "",
  },
{% if request.LANGUAGE_CODE == 'pl' %}
  lang: "pl",
{% else %}
  lang: "en",
{% endif %}
};

var renderError = function (element, errors) {
  element.className = "response-error";
  var messages = [];
  errors.forEach(function (error) {
    messages.push(error.message);
  });
  element.innerText = messages.join(", ");
};

var renderSuccess = function (element, msg) {
  element.className = "response-success";
  element.innerText = msg;
};

//initialize the SDK by providing your POS ID and create secureForms object
//var payuSdkForms = PayU("484830");
var payuSdkForms = PayU("{{ merchant_pos_id }}");
var secureForms = payuSdkForms.secureForms();

//create the forms by providing type and options
var cardNumber = secureForms.add("number", optionsForms);
var cardDate = secureForms.add("date", optionsForms);
var cardCvv = secureForms.add("cvv", optionsForms);

//render the form in selected element
cardNumber.render("#payu-card-number");
cardDate.render("#payu-card-date");
cardCvv.render("#payu-card-cvv");

var tokenizeButton = document.getElementById("tokenizeButton");
var responseElement = document.getElementById("responseTokenize");

function getCookie(name) {
    var value = '; ' + document.cookie,
        parts = value.split('; ' + name + '=');
    if (parts.length == 2) return parts.pop().split(';').shift();
}

function saveToken(token) {
    alert('saving token: ' + token);
    alert('X-CSRFToken: ' + getCookie('csrftoken'));
    $.ajax({
        type: 'POST',
        url: '{%url 'save_token'%}',
        data: {token: token},
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    }).done(function(data, textStatus, jqXHR) {
        renderSuccess(responseElement, data)
    });
}

tokenizeButton.addEventListener("click", function () {
  responseElement.innerText = "";

  try {
    ////tokenize the card (communicate with PayU server)
    payuSdkForms.tokenize("MULTI").then(function (result) {
      // example for SINGLE type token
      result.status === "SUCCESS"
        ? saveToken(result.body.token) //pass the token to your back-end
        : renderError(responseElement, result.error.messages); //check the business error type and messages and display appropriate information to the user
    });
  } catch (e) {
    console.log(e); // technical errors
  }
});
</script>

<style>
/*.payu-container * {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 14px;
  color: #ffffff;
}*/

.payu-container {
  text-align: center;
  width: 420px;
  margin: 20px auto 10px;
  display: block;
  border-radius: 5px;
  box-sizing: border-box;
}

.payu-container .card-container {
  width: 100%;
  margin: 0 auto;
  border-radius: 6px;
  padding: 25px 15px;
  /*background: rgb(2, 0, 60);*/
  background: rgba(0, 0, 0, .1);
  text-align: left;
  box-sizing: border-box;
  border: solid 1px dimgrey;
}

.payu-container .card-container aside {
  padding-bottom: 6px;
}

.payu-container .payu-card-form {
  background-color: #ffffff;
  padding: 5px;
  border-radius: 4px;
}

.payu-container .card-details {
  clear: both;
  overflow: auto;
  margin-top: 10px;
}

.payu-container .card-details .expiration {
  width: 50%;
  float: left;
  padding-right: 5%;
}

.payu-container .card-details .cvv {
  width: 45%;
  float: left;
}

/*.payu-container button {
  border: none;
  background: #438f29;
  padding: 8px 15px;
  margin: 10px auto;
  cursor: pointer;
}*/

.payu-container .response-success {
  color: #438f29;
}

.payu-container .response-error {
  color: #990000;
}
</style>

{% endblock %}