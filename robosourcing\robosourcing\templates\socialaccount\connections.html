{% extends "socialaccount/base.html" %}
{% load crispy_forms_filters %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block head_title %}{% trans "Account Connections" %}{% endblock %}

{% block title %}
{{ block.super }} - {% trans "Account Connections" %}
{% endblock title %}

{% block content %}
<div class="container">
    <div class="row justify-content-center _text-light">
        <section class="col-md-8 col-lg-6 col-xxl-5">
            <h1>{% trans "Account Connections" %}</h1>

            {% if form.accounts %}
            <p>{% blocktrans %}You can sign in to your account using any of the following third party accounts:{% endblocktrans %}</p>
            <form method="post" action="{% url 'socialaccount_connections' %}">
                {% csrf_token %}
                <fieldset>
                    {% if form.non_field_errors %}
                    <div id="errorMsg">{{ form.non_field_errors }}</div>
                    {% endif %}

                    {% for base_account in form.accounts %}
                        {% with base_account.get_provider_account as account %}
                        <div>
                            <label for="id_account_{{ base_account.id }}">
                                <input id="id_account_{{ base_account.id }}" type="radio" name="account"
                                       value="{{ base_account.id }}"/>
                                <span class="socialaccount_provider {{ base_account.provider }} {{ account.get_brand.id }}">{{account.get_brand.name}}</span>
                                {{ account }}
                            </label>
                        </div>
                        {% endwith %}
                    {% endfor %}

                    <div class="mt-3 mb-3">
                        <button class="btn btn-outline-secondary" type="submit">{% trans 'Remove' %}</button>
                    </div>

                </fieldset>

            </form>

            {% else %}
            <p>{% trans 'You currently have no social network accounts connected to this account.' %}</p>
            {% endif %}
            <h2>{% trans 'Add a 3rd Party Account' %}</h2>

            <ul class="socialaccount_providers mt-3 mb-5">
                {% include "socialaccount/snippets/provider_list.html" with process="connect" %}
            </ul>
        </section>
    </div>
</div>
{% include "socialaccount/snippets/login_extra.html" %}

{% endblock %}
