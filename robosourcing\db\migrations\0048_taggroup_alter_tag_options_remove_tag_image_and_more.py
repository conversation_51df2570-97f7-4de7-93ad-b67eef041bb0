# Generated by Django 4.2.13 on 2025-04-28 07:56

import db.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0047_merge_20250309_2107'),
    ]

    operations = [
        migrations.CreateModel(
            name='TagGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='Group name')),
                ('icon', models.ImageField(blank=True, null=True, upload_to='tag_groups/', validators=[db.models.validate_image], verbose_name='Group icon')),
            ],
            options={
                'verbose_name': 'Tag group',
                'verbose_name_plural': 'Tag groups',
                'ordering': ['name'],
            },
        ),
        migrations.AlterModelOptions(
            name='tag',
            options={'ordering': ['name'], 'verbose_name': 'Tag', 'verbose_name_plural': 'Tags'},
        ),
        migrations.RemoveField(
            model_name='tag',
            name='image',
        ),
        migrations.AlterField(
            model_name='tag',
            name='name',
            field=models.CharField(max_length=50, primary_key=True, serialize=False, verbose_name='Tag name'),
        ),
        migrations.AddField(
            model_name='tag',
            name='group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tags', to='db.taggroup', verbose_name='Tag group'),
        ),
    ]
