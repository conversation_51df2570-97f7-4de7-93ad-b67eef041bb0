{% load gettype %}

<li>
{% if not val %}
    <a class="dropdown-item">{{ key }}</i></a>
{% else %}
    <a href="#" class="dropdown-item">{{ key }}<span class="dropdown-toggle position-absolute end-0 me-0"></span></a>
    <ul class="dropdown-menu position-static ps-3">
    {% for key, val in val.items %}
        {% include "front/home_page/scenario_subs.html" %}
    {% endfor %}
    </ul>
{% endif %}
</li>