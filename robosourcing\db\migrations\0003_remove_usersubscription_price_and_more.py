# Generated by Django 4.1.9 on 2024-06-06 13:15

import db.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0002_scheduledfreebies_remove_credit_credits_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='usersubscription',
            name='price',
        ),
        migrations.RemoveField(
            model_name='usersubscription',
            name='subscription',
        ),
        migrations.AlterField(
            model_name='usersubscription',
            name='renew_date',
            field=models.DateTimeField(default=db.models.UserSubscription.one_month_later),
        ),
    ]
