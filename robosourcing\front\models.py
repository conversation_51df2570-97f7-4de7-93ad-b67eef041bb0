from django.db import models
from datetime import datetime
import secrets
import uuid

BUTTON_COLOR_CHOICES = [
    ('#fbab18', 'Brand Yellow'),
    ('#84b436', 'Brand Green'),
    ('#b72f2f', 'Brand Red'),
    ('#a580b2', 'Brand Purple'),
    ('#0077E4', 'Brand Blue'),
    ('#ffffff', 'Brand White'),
    ('#000000', 'Brand Black'),
    ('#666666', 'Brand Gray'),
]

class CarouselItem(models.Model):
    LAYOUT_CHOICES = [
        ('a', 'Grafika z lewej, tekst i przyciski z prawej'),
        ('b', 'Graf<PERSON> z prawej, tekst i przyciski z lewej'),
        ('c', 'Tekst wyśrodkowany na tle'),
    ]
    
    # Teksty (osobne pola dla angielskiego i polskiego)
    title_en = models.CharField("Title (English)", max_length=200, blank=True, null=True)
    title_pl = models.CharField("Title (Polish)", max_length=200, blank=True, null=True)
    description_en = models.TextField("Description (English)", blank=True, null=True,
                                      help_text="Możesz używać HTML, np. <strong>pogrubienie</strong>")
    description_pl = models.TextField("Description (Polish)", blank=True, null=True,
                                      help_text="Możesz używać HTML, np. <strong>pogrubienie</strong>")
    
    # Przyciski – pierwszy (główny)
    button_url = models.CharField(max_length=200, blank=True, null=True, help_text="Primary button URL")
    button_description_en = models.CharField("Button Text (English)", max_length=100, blank=True, null=True)
    button_description_pl = models.CharField("Button Text (Polish)", max_length=100, blank=True, null=True)
    button_color = models.CharField("Button Color", max_length=7, choices=BUTTON_COLOR_CHOICES,
                                    blank=True, null=True, default='#0077E4')
    
    # Drugi przycisk – opcjonalnie
    button2_url = models.CharField(max_length=200, blank=True, null=True, help_text="Secondary button URL (optional)")
    button2_description_en = models.CharField("Button 2 Text (English)", max_length=100, blank=True, null=True)
    button2_description_pl = models.CharField("Button 2 Text (Polish)", max_length=100, blank=True, null=True)
    
    # Wybór układu
    layout = models.CharField("Layout", max_length=1, choices=LAYOUT_CHOICES, default='c')
    
    # Obrazy – tło dla trybu jasnego i ciemnego (pełnoekranowe w layout C)
    background_image = models.ImageField(
        upload_to='carousel_backgrounds', 
        blank=True, 
        null=True,
        help_text="Background image for light mode"
    )
    background_image_dark = models.ImageField(
        upload_to='carousel_backgrounds', 
        blank=True, 
        null=True,
        help_text="Background image for dark mode"
    )
    # Obraz wykorzystywany przy layoutach A i B – ilustracja (graphic_image)
    graphic_image = models.ImageField(
        upload_to='carousel_graphics', 
        blank=True, 
        null=True,
        help_text="Graphic image for layout a or b"
    )
    
    # Obrazy responsywne – osobne zdjęcia dla desktopu, tabletu i urządzeń mobilnych (wersje jasne)
    background_image_desktop = models.ImageField(
        upload_to='carousel_backgrounds', 
        blank=True, 
        null=True,
        help_text="Desktop background image (light mode)"
    )
    background_image_tablet = models.ImageField(
        upload_to='carousel_backgrounds', 
        blank=True, 
        null=True,
        help_text="Tablet background image (light mode)"
    )
    background_image_mobile = models.ImageField(
        upload_to='carousel_backgrounds', 
        blank=True, 
        null=True,
        help_text="Mobile background image (light mode)"
    )
    
    # Obrazy responsywne – wersje ciemne:
    background_image_desktop_dark = models.ImageField(
        upload_to='carousel_backgrounds', 
        blank=True, 
        null=True,
        help_text="Desktop background image (dark mode)"
    )
    background_image_tablet_dark = models.ImageField(
        upload_to='carousel_backgrounds', 
        blank=True, 
        null=True,
        help_text="Tablet background image (dark mode)"
    )
    background_image_mobile_dark = models.ImageField(
        upload_to='carousel_backgrounds', 
        blank=True, 
        null=True,
        help_text="Mobile background image (dark mode)"
    )
    
    # Ustawienia wyświetlania
    is_displayed = models.BooleanField(default=True, help_text="Should this slide be displayed?")
    display_start_date = models.DateTimeField(blank=True, null=True, help_text="Start date/time for display")
    display_end_date = models.DateTimeField(blank=True, null=True, help_text="End date/time for display")
    
    # Kolejność wyświetlania
    order = models.PositiveIntegerField(default=0)
    
    class Meta:
        ordering = ['order']
    
    def __str__(self):
        return self.title_en if self.title_en else "CarouselItem"



class ContactRequest(models.Model):
    MESSAGE_TYPE_CHOICES = [
        ('custom_scenario', 'I need a custom scenario'),
        ('existing_scenario', 'I need a custom scenario based on an existing one'),
        ('technical_support', 'I need technical support'),
        ('other', 'Something else'),
    ]

    CONTACT_PREFERENCE_CHOICES = [
        ('email', 'Email'),
        ('phone', 'Phone'),
        ('whatsapp', 'WhatsApp'),
        ('skype', 'Skype'),
    ]

    case_number = models.CharField(max_length=50, blank=True, null=True)
    name = models.CharField(max_length=100)
    email = models.EmailField()
    message_type = models.CharField(max_length=50, choices=MESSAGE_TYPE_CHOICES)
    scenario_name = models.CharField(max_length=200, blank=True, null=True)
    description = models.TextField()
    base_scenario = models.CharField(max_length=200, blank=True, null=True)
    contact_preference = models.CharField(max_length=50, choices=CONTACT_PREFERENCE_CHOICES)
    contact_info = models.CharField(max_length=100)
    app_version = models.CharField(max_length=50, blank=True, null=True)

    def get_prefix(self):
        prefix_map = {
            'custom_scenario': 'CS',
            'existing_scenario': 'ES',
            'technical_support': 'TS',
            'other': 'OT'
        }
        return prefix_map[self.message_type]

    def save(self, *args, **kwargs):
        if not self.case_number:
            prefix = self.get_prefix()
            number = ContactCounter.increment(self.message_type)
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')  # Dodanie timestampu do numeru sprawy
            self.case_number = f"{prefix}{timestamp}-{number:04d}"
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Contact request from {self.name} - Case #{self.case_number}"
    
class ContactFile(models.Model):
    contact_request = models.ForeignKey(ContactRequest, related_name='files', on_delete=models.CASCADE)
    file = models.FileField(upload_to='uploads/')

class ContactCounter(models.Model):
    message_type = models.CharField(max_length=50, unique=True)
    current_number = models.IntegerField(default=0)

    @classmethod
    def increment(cls, message_type):
        obj, created = cls.objects.get_or_create(message_type=message_type, defaults={'current_number': 1})
        if not created:
            obj.current_number += 1
            obj.save()
        return obj.current_number

class CategoryCard(models.Model):
    title_en = models.CharField('Title (English)', max_length=255)
    title_pl = models.CharField('Title (Polish)', max_length=255)
    img = models.ImageField('Image', upload_to='images/category_images/')

    def __str__(self):
        return self.title_en


class NewsletterSubscription(models.Model):
    email = models.EmailField(unique=True)
    is_confirmed = models.BooleanField(default=False)
    confirmation_token = models.CharField(max_length=64, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)

    # Zgody RODO
    marketing_consent = models.BooleanField(default=False, verbose_name="Zgoda na marketing")
    data_processing_consent = models.BooleanField(default=False, verbose_name="Zgoda na przetwarzanie danych")

    def save(self, *args, **kwargs):
        if not self.confirmation_token:
            self.confirmation_token = secrets.token_urlsafe(32)
        super().save(*args, **kwargs)

    def __str__(self):
        status = "✓" if self.is_confirmed else "?"
        return f"{self.email} {status}"

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Subskrypcja newslettera"
        verbose_name_plural = "Subskrypcje newslettera"


class NewsletterSegment(models.Model):
    """Segmenty subskrybentów newslettera"""
    name = models.CharField(max_length=100, verbose_name="Nazwa segmentu")
    description = models.TextField(blank=True, verbose_name="Opis segmentu")
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True, verbose_name="Aktywny")

    # Kryteria segmentacji
    registration_date_from = models.DateTimeField(null=True, blank=True, verbose_name="Data rejestracji od")
    registration_date_to = models.DateTimeField(null=True, blank=True, verbose_name="Data rejestracji do")
    confirmed_only = models.BooleanField(default=True, verbose_name="Tylko potwierdzone")

    def __str__(self):
        return self.name

    def get_subscribers(self):
        """Zwraca QuerySet subskrybentów pasujących do segmentu"""
        queryset = NewsletterSubscription.objects.all()

        if self.confirmed_only:
            queryset = queryset.filter(is_confirmed=True)

        if self.registration_date_from:
            queryset = queryset.filter(created_at__gte=self.registration_date_from)

        if self.registration_date_to:
            queryset = queryset.filter(created_at__lte=self.registration_date_to)

        return queryset

    class Meta:
        verbose_name = "Segment newslettera"
        verbose_name_plural = "Segmenty newslettera"


class NewsletterTemplate(models.Model):
    """Szablony newsletterów"""
    name = models.CharField(max_length=100, verbose_name="Nazwa szablonu")
    subject = models.CharField(max_length=200, verbose_name="Temat")
    content_html = models.TextField(verbose_name="Treść HTML")
    content_text = models.TextField(verbose_name="Treść tekstowa")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True, verbose_name="Aktywny")

    # Zmienne dostępne w szablonie
    AVAILABLE_VARIABLES = [
        '{{ subscriber.email }}',
        '{{ unsubscribe_url }}',
        '{{ current_date }}',
        '{{ site_url }}',
    ]

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Szablon newslettera"
        verbose_name_plural = "Szablony newsletterów"


class NewsletterCampaign(models.Model):
    """Kampanie newsletterowe"""
    STATUS_CHOICES = [
        ('draft', 'Szkic'),
        ('scheduled', 'Zaplanowana'),
        ('sending', 'Wysyłanie'),
        ('sent', 'Wysłana'),
        ('cancelled', 'Anulowana'),
    ]

    name = models.CharField(max_length=100, verbose_name="Nazwa kampanii")
    template = models.ForeignKey(NewsletterTemplate, on_delete=models.CASCADE, verbose_name="Szablon")
    segment = models.ForeignKey(NewsletterSegment, on_delete=models.CASCADE, verbose_name="Segment")

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="Status")

    # Harmonogram
    scheduled_at = models.DateTimeField(null=True, blank=True, verbose_name="Zaplanowane na")
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name="Wysłane")

    # Metadane
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey('auth.User', on_delete=models.CASCADE, verbose_name="Utworzone przez")

    # Statystyki
    total_recipients = models.IntegerField(default=0, verbose_name="Liczba odbiorców")
    emails_sent = models.IntegerField(default=0, verbose_name="Wysłane emaile")
    emails_failed = models.IntegerField(default=0, verbose_name="Nieudane emaile")

    def __str__(self):
        return f"{self.name} ({self.get_status_display()})"

    class Meta:
        verbose_name = "Kampania newslettera"
        verbose_name_plural = "Kampanie newsletterów"
        ordering = ['-created_at']


class NewsletterDelivery(models.Model):
    """Rejestr wysłanych newsletterów do konkretnych odbiorców"""
    campaign = models.ForeignKey(NewsletterCampaign, on_delete=models.CASCADE, verbose_name="Kampania")
    subscription = models.ForeignKey(NewsletterSubscription, on_delete=models.CASCADE, verbose_name="Subskrypcja")

    # Status dostarczenia
    sent_at = models.DateTimeField(auto_now_add=True, verbose_name="Wysłane")
    delivery_status = models.CharField(max_length=20, choices=[
        ('sent', 'Wysłane'),
        ('delivered', 'Dostarczone'),
        ('bounced', 'Odrzucone'),
        ('failed', 'Nieudane'),
    ], default='sent', verbose_name="Status dostarczenia")

    # Unikalne identyfikatory dla trackingu
    tracking_id = models.CharField(max_length=64, unique=True, verbose_name="ID śledzenia")

    def save(self, *args, **kwargs):
        if not self.tracking_id:
            self.tracking_id = secrets.token_urlsafe(32)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.campaign.name} → {self.subscription.email}"

    class Meta:
        verbose_name = "Dostarczenie newslettera"
        verbose_name_plural = "Dostarczenia newsletterów"
        unique_together = ['campaign', 'subscription']


class NewsletterOpen(models.Model):
    """Statystyki otwarć newsletterów"""
    delivery = models.ForeignKey(NewsletterDelivery, on_delete=models.CASCADE, verbose_name="Dostarczenie")
    opened_at = models.DateTimeField(auto_now_add=True, verbose_name="Otwarte")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="Adres IP")
    user_agent = models.TextField(null=True, blank=True, verbose_name="User Agent")

    def __str__(self):
        return f"Otwarcie: {self.delivery.campaign.name} → {self.delivery.subscription.email}"

    class Meta:
        verbose_name = "Otwarcie newslettera"
        verbose_name_plural = "Otwarcia newsletterów"


class NewsletterClick(models.Model):
    """Statystyki kliknięć w newsletterach"""
    delivery = models.ForeignKey(NewsletterDelivery, on_delete=models.CASCADE, verbose_name="Dostarczenie")
    url = models.URLField(verbose_name="URL")
    clicked_at = models.DateTimeField(auto_now_add=True, verbose_name="Kliknięte")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="Adres IP")
    user_agent = models.TextField(null=True, blank=True, verbose_name="User Agent")

    def __str__(self):
        return f"Kliknięcie: {self.url} w {self.delivery.campaign.name}"

    class Meta:
        verbose_name = "Kliknięcie w newsletterze"
        verbose_name_plural = "Kliknięcia w newsletterach"


class ExternalMailingService(models.Model):
    """Konfiguracja zewnętrznych serwisów mailingowych"""
    SERVICE_CHOICES = [
        ('mailchimp', 'Mailchimp'),
        ('sendgrid', 'SendGrid'),
        ('mailgun', 'Mailgun'),
        ('aws_ses', 'Amazon SES'),
        ('custom', 'Niestandardowy'),
    ]

    name = models.CharField(max_length=100, verbose_name="Nazwa serwisu")
    service_type = models.CharField(max_length=20, choices=SERVICE_CHOICES, verbose_name="Typ serwisu")

    # Konfiguracja API
    api_key = models.CharField(max_length=200, verbose_name="Klucz API")
    api_url = models.URLField(blank=True, verbose_name="URL API")

    # Ustawienia
    is_active = models.BooleanField(default=False, verbose_name="Aktywny")
    is_default = models.BooleanField(default=False, verbose_name="Domyślny")

    # Limity
    daily_limit = models.IntegerField(null=True, blank=True, verbose_name="Dzienny limit")
    monthly_limit = models.IntegerField(null=True, blank=True, verbose_name="Miesięczny limit")

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.get_service_type_display()})"

    class Meta:
        verbose_name = "Zewnętrzny serwis mailingowy"
        verbose_name_plural = "Zewnętrzne serwisy mailingowe"

class CardDescription(models.Model):
    card = models.ForeignKey(CategoryCard, related_name='descriptions', on_delete=models.CASCADE)
    text_en = models.TextField('Text (English)')
    text_pl = models.TextField('Text (Polish)')
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return self.text_en
