# Generated by Django 4.2.13 on 2024-12-05 20:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('db', '0033_remove_affiliatelink_commission_rate_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommissionChangeEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('new_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('affiliate_link', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.affiliatelink')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
