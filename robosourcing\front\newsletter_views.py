"""
Widoki dla zaawansowanego systemu newslettera
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.db.models import Count, Q
from django.template.loader import render_to_string
from django.urls import reverse
import csv
from datetime import datetime, timedelta

from .models import (NewsletterSubscription, NewsletterSegment, NewsletterTemplate, 
                     NewsletterCampaign, NewsletterDelivery, NewsletterOpen, 
                     NewsletterClick, ExternalMailingService)
from db.mailer import Mailer


@staff_member_required
def newsletter_dashboard(request):
    """Dashboard z statystykami newslettera"""
    # Podstawowe statystyki
    total_subscribers = NewsletterSubscription.objects.count()
    confirmed_subscribers = NewsletterSubscription.objects.filter(is_confirmed=True).count()
    
    # Statystyki z ostatnich 30 dni
    last_30_days = timezone.now() - timedelta(days=30)
    new_subscribers_30d = NewsletterSubscription.objects.filter(created_at__gte=last_30_days).count()
    
    # Statystyki kampanii
    total_campaigns = NewsletterCampaign.objects.count()
    sent_campaigns = NewsletterCampaign.objects.filter(status='sent').count()
    
    # Najnowsze kampanie
    recent_campaigns = NewsletterCampaign.objects.order_by('-created_at')[:5]
    
    # Statystyki otwarć i kliknięć z ostatnich kampanii
    recent_opens = NewsletterOpen.objects.filter(opened_at__gte=last_30_days).count()
    recent_clicks = NewsletterClick.objects.filter(clicked_at__gte=last_30_days).count()
    
    context = {
        'total_subscribers': total_subscribers,
        'confirmed_subscribers': confirmed_subscribers,
        'new_subscribers_30d': new_subscribers_30d,
        'total_campaigns': total_campaigns,
        'sent_campaigns': sent_campaigns,
        'recent_campaigns': recent_campaigns,
        'recent_opens': recent_opens,
        'recent_clicks': recent_clicks,
    }
    
    return render(request, 'front/newsletter/dashboard.html', context)


@staff_member_required
def campaign_stats(request, campaign_id):
    """Szczegółowe statystyki kampanii"""
    campaign = get_object_or_404(NewsletterCampaign, id=campaign_id)
    
    # Statystyki dostarczenia
    deliveries = NewsletterDelivery.objects.filter(campaign=campaign)
    total_sent = deliveries.count()
    
    # Statystyki otwarć
    opens = NewsletterOpen.objects.filter(delivery__campaign=campaign)
    unique_opens = opens.values('delivery').distinct().count()
    total_opens = opens.count()
    
    # Statystyki kliknięć
    clicks = NewsletterClick.objects.filter(delivery__campaign=campaign)
    unique_clicks = clicks.values('delivery').distinct().count()
    total_clicks = clicks.count()
    
    # Najpopularniejsze linki
    popular_links = clicks.values('url').annotate(
        click_count=Count('id')
    ).order_by('-click_count')[:10]
    
    # Wskaźniki
    open_rate = (unique_opens / total_sent * 100) if total_sent > 0 else 0
    click_rate = (unique_clicks / total_sent * 100) if total_sent > 0 else 0
    click_to_open_rate = (unique_clicks / unique_opens * 100) if unique_opens > 0 else 0
    
    context = {
        'campaign': campaign,
        'total_sent': total_sent,
        'unique_opens': unique_opens,
        'total_opens': total_opens,
        'unique_clicks': unique_clicks,
        'total_clicks': total_clicks,
        'popular_links': popular_links,
        'open_rate': round(open_rate, 2),
        'click_rate': round(click_rate, 2),
        'click_to_open_rate': round(click_to_open_rate, 2),
    }
    
    return render(request, 'front/newsletter/campaign_stats.html', context)


@staff_member_required
def send_campaign(request, campaign_id):
    """Wysyłanie kampanii newslettera"""
    campaign = get_object_or_404(NewsletterCampaign, id=campaign_id)
    
    if campaign.status != 'draft' and campaign.status != 'scheduled':
        messages.error(request, 'Ta kampania nie może być wysłana.')
        return redirect('newsletter_dashboard')
    
    if request.method == 'POST':
        # Pobierz subskrybentów z segmentu
        subscribers = campaign.segment.get_subscribers()
        campaign.total_recipients = subscribers.count()
        campaign.status = 'sending'
        campaign.save()
        
        # Wysyłanie emaili (w rzeczywistości powinno być w tle)
        mailer = Mailer()
        sent_count = 0
        failed_count = 0
        
        for subscriber in subscribers:
            try:
                # Utwórz rekord dostarczenia
                delivery = NewsletterDelivery.objects.create(
                    campaign=campaign,
                    subscription=subscriber
                )
                
                # Przygotuj treść emaila
                context = {
                    'subscriber': subscriber,
                    'unsubscribe_url': request.build_absolute_uri(
                        reverse('newsletter_unsubscribe', args=[subscriber.confirmation_token])
                    ),
                    'current_date': timezone.now(),
                    'site_url': request.build_absolute_uri('/'),
                    'tracking_pixel_url': request.build_absolute_uri(
                        reverse('newsletter_track_open', args=[delivery.tracking_id])
                    ),
                }
                
                # Renderuj szablony
                subject = render_to_string('string:' + campaign.template.subject, context).strip()
                html_content = render_to_string('string:' + campaign.template.content_html, context)
                text_content = render_to_string('string:' + campaign.template.content_text, context)
                
                # Wyślij email
                result = mailer.send_html_mail(
                    user=None,
                    subject=subject,
                    text_content=text_content,
                    html_content=html_content,
                    recipients=[subscriber.email]
                )
                
                if result:
                    delivery.delivery_status = 'sent'
                    sent_count += 1
                else:
                    delivery.delivery_status = 'failed'
                    failed_count += 1
                
                delivery.save()
                
            except Exception as e:
                failed_count += 1
                print(f"Błąd wysyłania do {subscriber.email}: {e}")
        
        # Aktualizuj statystyki kampanii
        campaign.emails_sent = sent_count
        campaign.emails_failed = failed_count
        campaign.status = 'sent'
        campaign.sent_at = timezone.now()
        campaign.save()
        
        messages.success(request, f'Kampania wysłana! Wysłano: {sent_count}, Błędy: {failed_count}')
        return redirect('campaign_stats', campaign_id=campaign.id)
    
    # Podgląd kampanii
    subscribers_count = campaign.segment.get_subscribers().count()
    
    context = {
        'campaign': campaign,
        'subscribers_count': subscribers_count,
    }
    
    return render(request, 'front/newsletter/send_campaign.html', context)


def track_open(request, tracking_id):
    """Śledzenie otwarć newslettera"""
    try:
        delivery = NewsletterDelivery.objects.get(tracking_id=tracking_id)
        
        # Zapisz otwarcie
        NewsletterOpen.objects.create(
            delivery=delivery,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        # Zwróć przezroczysty pixel 1x1
        response = HttpResponse(
            b'\x47\x49\x46\x38\x39\x61\x01\x00\x01\x00\x80\x00\x00\x00\x00\x00\x00\x00\x00\x21\xF9\x04\x01\x00\x00\x00\x00\x2C\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02\x04\x01\x00\x3B',
            content_type='image/gif'
        )
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except NewsletterDelivery.DoesNotExist:
        return HttpResponse(status=404)


def track_click(request, tracking_id):
    """Śledzenie kliknięć w newsletterze"""
    try:
        delivery = NewsletterDelivery.objects.get(tracking_id=tracking_id)
        url = request.GET.get('url', '')
        
        if url:
            # Zapisz kliknięcie
            NewsletterClick.objects.create(
                delivery=delivery,
                url=url,
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            # Przekieruj na docelowy URL
            return redirect(url)
        
        return HttpResponse(status=400)
        
    except NewsletterDelivery.DoesNotExist:
        return HttpResponse(status=404)


@staff_member_required
def export_subscribers(request, segment_id=None):
    """Eksport subskrybentów do CSV"""
    if segment_id:
        segment = get_object_or_404(NewsletterSegment, id=segment_id)
        subscribers = segment.get_subscribers()
        filename = f"newsletter_subscribers_{segment.name}.csv"
    else:
        subscribers = NewsletterSubscription.objects.filter(is_confirmed=True)
        filename = "newsletter_subscribers_all.csv"
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    writer = csv.writer(response)
    writer.writerow(['Email', 'Potwierdzone', 'Data rejestracji', 'Data potwierdzenia'])
    
    for subscriber in subscribers:
        writer.writerow([
            subscriber.email,
            'Tak' if subscriber.is_confirmed else 'Nie',
            subscriber.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            subscriber.confirmed_at.strftime('%Y-%m-%d %H:%M:%S') if subscriber.confirmed_at else ''
        ])
    
    return response


@staff_member_required
def confirm_subscriber_ajax(request, subscriber_id):
    """AJAX endpoint do potwierdzania subskrybenta"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    try:
        subscriber = NewsletterSubscription.objects.get(id=subscriber_id)

        if subscriber.is_confirmed:
            return JsonResponse({'success': False, 'error': 'Subscriber already confirmed'})

        subscriber.is_confirmed = True
        subscriber.confirmed_at = timezone.now()
        subscriber.save()

        return JsonResponse({'success': True, 'message': 'Subscriber confirmed successfully'})

    except NewsletterSubscription.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Subscriber not found'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


@staff_member_required
def delete_subscriber_ajax(request, subscriber_id):
    """AJAX endpoint do usuwania subskrybenta"""
    if request.method != 'DELETE':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    try:
        subscriber = NewsletterSubscription.objects.get(id=subscriber_id)
        subscriber_email = subscriber.email
        subscriber.delete()

        return JsonResponse({'success': True, 'message': f'Subscriber {subscriber_email} deleted successfully'})

    except NewsletterSubscription.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Subscriber not found'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
