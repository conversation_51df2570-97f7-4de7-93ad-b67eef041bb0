import traceback
from requests import Response
from rest_framework import status
from api.serializers import DetailedResponseSerializer


def robo_exception_handler(func):
    def inner_function(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f'-----===== {str(e)} =====-----')
            print(traceback.format_exc())
            print(f'-----===== {repr(e)} =====-----')
            response = DetailedResponseSerializer(detail=repr(e))
            return Response(response.data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    return inner_function