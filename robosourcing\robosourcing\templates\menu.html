{% load i18n %}
{% load static %}
<nav class="navbar navbar-expand-xl navbar-home fixed-top">

    {% block navbar_buttons %}
    <div class="container">

        <a class="navbar-brand" href="{% url 'index' %}"><img src="{% static 'images/brandbook/logo_white_text_100.png' %}"></a>

        

        <!-- responsive menu toggler -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- top menu -->
        <div class="collapse navbar-collapse ms-3" id="navbarSupportedContent">

            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    {% trans "Product" %}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark">
                    <li><a class="dropdown-item" href="{% url 'how_it_works' %}">{% trans "How It Works" %}</a></li>
                    <li><a class="dropdown-item" href="{% url 'download' %}">{% trans "Download Botie" %}</a></li>
                    <li><a class="dropdown-item" href="{% url 'user_guide_index' %}" target="_blank">{% trans "User Guide" %}</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'scenarios_page' %}">{% trans "BotBook" %}</a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    {% trans "Price list" %}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark">
                    <li><a class="dropdown-item" href="{% url 'pricelist' %}">{% trans "Price list" %}</a></li>
                    <li><a class="dropdown-item disabled" href="{% url 'pricelist' %}">{% trans "Skills Development" %}</a></li>
                    <li><a class="dropdown-item disabled" href="{% url 'pricelist' %}" target="_blank">{% trans "Automation as a Service" %}</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    {% trans "Help center" %}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item disabled" href="#">{% trans "FAQ" %}</a></li>
                        <li><a class="dropdown-item disabled" href="#">{% trans "Botie Academy" %}</a></li>
                        <li><a class="dropdown-item disabled" href="#">{% trans "Forum" %}</a></li>
                        <li><a class="dropdown-item disabled" href="#">{% trans "Editor" %}</a></li>
                        <li><a class="dropdown-item disabled" href="{% url 'features' %}">{% trans "Features" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'newsletter_status' %}">{% trans "Newsletter" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'contact' %}">{% trans "Contact" %}</a></li>
                    </ul>
                </li>

            </ul>

        {% if user.is_authenticated %}

            <div class="navbar-expand ms-auto">
                <div class="navbar-nav">

                    <div class="double-menu me-3 mt-3 mt-md-0">
                        <li class="nav-item">
                            <a class="nav-link" href="#"><i class="fa-solid fa-user"></i></a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="dropdown01" data-bs-toggle="dropdown" aria-expanded="false">{{ user }}</a>
                            <ul class="dropdown-menu dropdown-menu-end dropdown-menu-dark" aria-labelledby="dropdown01">
                                <li><a class="dropdown-item" href="{% url 'socialaccount_connections' %}">{% trans "Account Connections" %}</a></li>
                                <li><a class="dropdown-item" href="{% url 'account_email' %}">{% trans "E-mail Addresses" %}</a></li>
                                <li><a class="dropdown-item" href="{% url 'account_change_password' %}">{% trans "Change password" %}</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <form method="post" action="{% url 'account_logout' %}" class="d-flex">
                                {% csrf_token %}
                                {% if redirect_field_value %}
                                    <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}"/>
                                {% endif %}
                                <button class="btn dropdown-item" type="submit">{% trans "Sign Out" %}</button>
                                </form>
                            </ul>
                        </li>
                    </div>

                    <a class="btn btn-primary mt-3 mt-md-0" href="{% url 'home' %}">{% trans "DASHBOARD" %} <i class="fa fa-caret-right"></i></a>

                </div>
            </div>

        {% else %}

            <div class="text-end ms-auto">
                <a class="btn btn-outline-light me-2" href="{% url 'account_login' %}?next={{ request.get_full_path|urlencode }}">{% trans "Sign In" %}</a>
                <a class="btn btn-primary" href="{% url 'account_signup' %}">{% trans "Sign Up for a free account" %}</a>
            </div>

        {% endif %}

        

        </div>

        <div class="navbar-expand ms-3 me-auto">
            <ul class="navbar-nav">
                <!-- {% include "lang_picker.html" %} -->
                {% include "theme_picker.html" %}
            </ul>
        </div>

    </div>
    {% endblock %}
</nav>