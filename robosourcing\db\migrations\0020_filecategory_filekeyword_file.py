# Generated by Django 4.2.13 on 2024-09-18 18:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0019_alter_startscenario_end_date_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileCategory',
            fields=[
                ('name', models.CharField(max_length=50, primary_key=True, serialize=False)),
            ],
        ),
        migrations.CreateModel(
            name='FileKeyword',
            fields=[
                ('keyword', models.CharField(max_length=50, primary_key=True, serialize=False)),
            ],
        ),
        migrations.CreateModel(
            name='File',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('local_name', models.CharField(max_length=50)),
                ('local_path', models.Char<PERSON>ield(max_length=500)),
                ('export_name', models.Char<PERSON>ield(max_length=50)),
                ('file_type', models.CharField(max_length=5)),
                ('file_hash', models.Char<PERSON>ield(max_length=32)),
                ('keywords_hash', models.CharField(max_length=32)),
                ('categories', models.ManyToManyField(to='db.filecategory')),
                ('keywords', models.ManyToManyField(to='db.filekeyword')),
                ('scenario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.scenario')),
            ],
            options={
                'unique_together': {('file_hash', 'scenario')},
            },
        ),
    ]
