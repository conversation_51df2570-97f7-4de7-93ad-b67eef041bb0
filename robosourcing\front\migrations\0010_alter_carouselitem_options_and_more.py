# Generated by Django 4.2.13 on 2025-02-03 20:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('front', '0009_categorycard_carddescription'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='carouselitem',
            options={'ordering': ['order']},
        ),
        migrations.RemoveField(
            model_name='carouselitem',
            name='button_description',
        ),
        migrations.RemoveField(
            model_name='carouselitem',
            name='button_undertext',
        ),
        migrations.RemoveField(
            model_name='carouselitem',
            name='description',
        ),
        migrations.RemoveField(
            model_name='carouselitem',
            name='image',
        ),
        migrations.RemoveField(
            model_name='carouselitem',
            name='title',
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='background_image',
            field=models.ImageField(blank=True, help_text='Background image for light mode', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='background_image_dark',
            field=models.ImageField(blank=True, help_text='Background image for dark mode', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='background_image_desktop',
            field=models.ImageField(blank=True, help_text='Desktop background image', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='background_image_mobile',
            field=models.ImageField(blank=True, help_text='Mobile background image', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='background_image_tablet',
            field=models.ImageField(blank=True, help_text='Tablet background image', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='button2_description_en',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Button 2 Text (English)'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='button2_description_pl',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Button 2 Text (Polish)'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='button2_url',
            field=models.CharField(blank=True, help_text='Secondary button URL (optional)', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='button_description_en',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Button Text (English)'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='button_description_pl',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Button Text (Polish)'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='description_en',
            field=models.TextField(blank=True, null=True, verbose_name='Description (English)'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='description_pl',
            field=models.TextField(blank=True, null=True, verbose_name='Description (Polish)'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='graphic_image',
            field=models.ImageField(blank=True, help_text='Graphic image for layout a or b', null=True, upload_to='carousel_graphics'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='layout',
            field=models.CharField(choices=[('a', 'Grafika z lewej, tekst i przyciski z prawej'), ('b', 'Grafika z prawej, tekst i przyciski z lewej'), ('c', 'Tekst wyśrodkowany na tle')], default='c', max_length=1, verbose_name='Layout'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='order',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='title_en',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Title (English)'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='title_pl',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Title (Polish)'),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='button_url',
            field=models.CharField(blank=True, help_text='Primary button URL', max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='display_end_date',
            field=models.DateTimeField(blank=True, help_text='End date/time for display', null=True),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='display_start_date',
            field=models.DateTimeField(blank=True, help_text='Start date/time for display', null=True),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='is_displayed',
            field=models.BooleanField(default=True, help_text='Should this slide be displayed?'),
        ),
    ]
