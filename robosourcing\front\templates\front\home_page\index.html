{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {{ block.super }} - Home
{% endblock title %}

{% block scripts %}
{% endblock %}

{% block navbar_buttons %}
  {{ block.super }}
{% endblock %}

{% block content %}
    <!-- <section class="py-3 text-center">
        <div class="container container-border fade-in">
            <h2>{% blocktrans %}Welcome to the world of the Botie personal robot{% endblocktrans %}</h2>
            <h5>{% blocktrans %}When you download and install the personal robot app {% endblocktrans %}<a href="{% url 'download' %}" class="link-info">Botie</a>{% blocktrans %}, you will receive 100 credits so you can test <PERSON><PERSON> without any obstacles.{% endblocktrans %}
                {% blocktrans %}Go ahead, {% endblocktrans %}<a href="{% url 'account_signup' %}" class="link-info">{% trans "sign up" %}</a>{% blocktrans %} and we'll renew your package every month up to 100 credits at no charge.{% endblocktrans %}
                {% blocktrans %}Log in, use more scenarios from {% endblocktrans %}<a href="{% url 'scenarios_page' %}" class="link-info">{% trans "BotBook" %}</a>{% blocktrans %} and discover new inspirations and the power of Botie.{% endblocktrans %}</h5>
      </div>
    </section> -->

    <section id="about-2" class="about-2 section">

        <div class="container fade-in"> 
            <div class="content">
                <div class="row justify-content-center">
                    <div class="col-sm-12 col-md-5 col-lg-4 col-xl-4 order-lg-2 offset-xl-1 mb-4">
                    <div class="img-wrap text-center text-md-left" data-aos="fade-up" data-aos-delay="100">
                        <div class="img">
                        <img src="{% static 'images/homepage/Asset <EMAIL>' %}" alt="{% trans 'circle image' %}" class="img-fluid">
                        </div>
                    </div>
                    </div>

                    <div class="offset-md-0 offset-lg-1 col-sm-12 col-md-5 col-lg-5 col-xl-4" data-aos="fade-up">
                    <div class="px-3">
                        <h2 class="content-title text-start">
                          {% blocktrans %}Welcome to the world of the Botie personal robot{% endblocktrans %}
                        </h2>
                        <p class="lead">
                        {% blocktrans %}When you download and install the personal robot app {% endblocktrans %}
                        <a href="{% url 'download' %}" class="link-info">Botie</a>{% blocktrans %}, you will receive 100 credits so you can test Botie without any obstacles.{% endblocktrans %}
                        {% blocktrans %}Go ahead, {% endblocktrans %}<a href="{% url 'account_signup' %}" class="link-info">
                        {% trans "sign up" %}</a>{% blocktrans %} and we'll renew your package every month up to 100 credits at no charge.{% endblocktrans %}
                        </p>
                        <p class="mb-5">
                        {% blocktrans %}Log in, use more skills from {% endblocktrans %}
                        <a href="{% url 'scenarios_page' %}" class="link-info">{% trans "BotBook" %}</a>{% blocktrans %} and discover new inspirations and the power of Botie.{% endblocktrans %}
                        </p>
                        <p>
                        <a href="#steps" class="btn-get-started">{% trans "Get Started" %}</a>
                        </p>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Steps Section -->
    <section id="steps" class="section">
        <!-- Section Title -->
        <div class="container section-title fade-in">
            <h2>{% trans "How it works" %}</h2>
            <p>{% trans "Five steps guide" %}</p>
        </div><!-- End Section Title -->
        <div class="steps site-section slider-steps-wrap">
        <div class="container fade-in">

          <div class="slider-nav d-flex justify-content-end mb-3 d-block d-xl-none">
            <a href="#" class="js-prev js-custom-prev"><i class="bi bi-arrow-left-short"></i></a>
            <a href="#" class="js-next js-custom-next"><i class="bi bi-arrow-right-short"></i></a>
          </div>

            <div class="swiper init-swiper" data-aos="fade-up" data-aos-delay="300">
            <script type="application/json" class="swiper-config">
                {
                "loop": false,
                "speed": 600,
                "autoplay": {
                    "delay": 5000
                },
                "slidesPerView": 1,
                "spaceBetween": 20,
                "pagination": {
                    "el": ".swiper-pagination",
                    "type": "bullets",
                    "clickable": true
                },
                "navigation": {
                    "nextEl": ".js-custom-next",
                    "prevEl": ".js-custom-prev"
                },
                "breakpoints": {
                    "0": {
                    "slidesPerView": 1,
                    "spaceBetween": 20
                    },
                    "768": {
                    "slidesPerView": 3,
                    "spaceBetween": 20
                    },
                    "1200": {
                    "slidesPerView": 5,
                    "spaceBetween": 20
                    }
                }
                }
            </script>
            <div class="swiper-wrapper">
                <!-- Step 1 -->
                <div class="swiper-slide">
                <div class="step">
                    <div class="pic">
                    <img src="{% static 'images/img_download.svg' %}" alt="{% trans 'Step 1' %}" class="img-fluid invert-svg">
                    </div>
                    <h3>
                    <a href="{% url 'download' %}">
                        <span>{% trans "Step 1: " %}</span>{% trans "Download" %}
                    </a>
                    </h3>
                    <p>
                    {% trans "Download the Botie application from the 'Download' section." %}
                    </p>
                    <p class="mb-0 mt-auto">
                    <a href="{% url 'app_download_guide' %}" class="more dark">
                        {% trans "See our app installation guide" %}
                        <span class="bi bi-arrow-right-short"></span>
                    </a>
                    </p>
                </div>
                </div>
                <!-- Step 2 -->
                <div class="swiper-slide">
                <div class="step">
                    <div class="pic">
                    <img src="{% static 'images/img_instal.svg' %}" alt="{% trans 'Step 2' %}" class="img-fluid invert-svg">
                    </div>
                    <h3>
                        <span>{% trans "Step 2: " %}</span>{% trans "Install" %}
                    </h3>
                    <p>
                    {% trans "Install the Botie application on your computer." %}
                    </p>
                    <p class="mb-0 mt-auto">
                    <a href="{% url 'app_install_guide' %}" class="more dark">
                        {% trans "See our app installation guide" %}
                        <span class="bi bi-arrow-right-short"></span>
                    </a>
                    </p>
                </div>
                </div>
                <!-- Step 3 -->
                <div class="swiper-slide">
                <div class="step">
                    <div class="pic">
                    <img src="{% static 'images/img_lunch.svg' %}" alt="{% trans 'Step 3' %}" class="img-fluid invert-svg">
                    </div>
                    <h3>
                        <span>{% trans "Step 3: " %}</span>{% trans "Launch" %}
                    </h3>
                    <p>
                    {% trans "Launch the Botie application." %}
                    </p>
                    <p class="mb-0 mt-auto">
                    <a href="{% url 'app_launch_guide' %}" class="more dark">
                        {% trans "See our app launch guide" %}
                        <span class="bi bi-arrow-right-short"></span>
                    </a>
                    </p>
                </div>
                </div>
                <!-- Step 4 -->
                <div class="swiper-slide">
                <div class="step">
                    <div class="pic">
                    <img src="{% static 'images/img_scenario.svg' %}" alt="{% trans 'Step 4' %}" class="img-fluid invert-svg">
                    </div>
                    <h3>
                        <span>{% trans "Step 4: " %}</span>{% trans "Find a skill" %}
                    </h3>
                    <p>
                    {% trans "Find skill in BootBoook and automate your work." %}
                    </p>
                    <p class="mb-0 mt-auto">
                    <a href="{% url 'scenarios_page' %}" class="more dark">
                        {% trans "Explore the list of our ready-to-use skills" %}
                        <span class="bi bi-arrow-right-short"></span>
                    </a>
                    </p>
                </div>
                </div>
                <!-- Step 5 -->
                <div class="swiper-slide">
                <div class="step">
                    <div class="pic">
                    <img src="{% static 'images/img_start.svg' %}" alt="{% trans 'Step 5' %}" class="img-fluid invert-svg">
                    </div>
                    <h3>
                        <span>{% trans "Step 5: " %}</span>{% trans "Manage your robots" %}
                    </h3>
                    <p>
                    {% trans "Manage your robot, skills and payments." %}
                    </p>
                    <p class="mb-0 mt-auto">
                    <a href="{% url 'robots' %}" class="more dark">
                        {% trans "Go to your dashboard" %}
                        <span class="bi bi-arrow-right-short"></span>
                    </a>
                    </p>
                </div>
                </div>
            </div>
            <!-- Pagination -->
            <div class="swiper-pagination"></div>
            </div>
        </div>
        <!-- /.container -->
        </div>
    </section><!-- /Steps Section -->

    <!-- Robotization Section -->
    <section id="robotyzacja" class="robotyzacja section light-background">

        <div class="container">
          <div class="row gap-x-lg-4 justify-content-between">
            <!-- Left Menu -->
            <div class="col-lg-4 js-custom-dots">
              <!-- Section 1 -->
              <a href="#" class="service-item link horizontal d-flex active" data-aos="fade-left" data-aos-delay="0">
                <div class="service-icon color-1 mb-4">
                  <i class="fas fa-robot"></i>
                </div>
                <!-- /.icon -->
                <div class="service-contents">
                  <h3>{% trans "Robotization" %}</h3>
                  <p>
                    {% trans "Streamline your daily tasks through business process automation." %}
                  </p>
                </div>
                <!-- /.service-contents-->
              </a>
              <!-- /.service -->
      
              <!-- Section 2 -->
              <a href="#" class="service-item link horizontal d-flex" data-aos="fade-left" data-aos-delay="100">
                <div class="service-icon color-2 mb-4">
                  <i class="fas fa-cogs"></i>
                </div>
                <!-- /.icon -->
                <div class="service-contents">
                  <h3>{% trans 'Key features' %}</h3>
                  <p>
                    {% trans "Features that make your work more efficient." %}
                  </p>
                </div>
                <!-- /.service-contents-->
              </a>
              <!-- /.service -->
      
              <!-- Section 3 -->
              <a href="#" class="service-item link horizontal d-flex" data-aos="fade-left" data-aos-delay="200">
                <div class="service-icon color-3 mb-4">
                  <i class="fas fa-chart-line"></i>
                </div>
                <!-- /.icon -->
                <div class="service-contents">
                  <h3>{% trans 'Botie enables' %}</h3>
                  <p>
                    {% trans "Solutions that set us apart from the competition." %}
                  </p>
                </div>
                <!-- /.service-contents-->
              </a>
              <!-- /.service -->
            </div>
      
            <!-- Content on the right -->
            <div class="col-lg-8">
              <div class="swiper init-swiper-tabs">
                <script type="application/json" class="swiper-config">
                  {
                    "loop": true,
                    "speed": 600,
                    "autoHeight": true,
                    "autoplay": {
                      "delay": 15000
                    },
                    "slidesPerView": 1,
                    "pagination": {
                      "el": ".swiper-pagination",
                      "type": "bullets",
                      "clickable": true
                    }
                  }
                </script>
                <div class="swiper-wrapper">
                  <!-- Content for Section 1 -->
                  <div class="swiper-slide">
                    <img src="{% static 'images/homepage/robo_2.jpg' %}" alt="{% trans 'Automation of processes' %}" class="img-fluid">
                    <div class="p-4">
                      <h3 class="text-black h5 mb-3">{% trans "Robotization" %}</h3>
                      <div class="row">
                        
                          <p>
                            {% trans "Are you the owner of a small or medium-sized company or do you manage a group of employees?" %}
                          </p>
                          <p>
                            {% trans "Do you have to divide all your time and attention between many things?" %}
                          </p>
                          <p>
                            {% trans "Do you respect the time and energy of your employees, looking for the best way for them to use their skills?" %}
                          </p>
                          <p>
                            <a href="{% url 'download' %}" class="link-info">Botie</a>
                            {% blocktrans %}is your PERSONAL ROBOT and virtual Business Partner, “Wingman“. It frees you from burdensome duties and gives you inspiration to develop your company.{% endblocktrans %}
                            {% blocktrans %}Botie is an effective office automation dedicated to SMEs, which stands out with its unique {% endblocktrans %}
                            <a href="{% url 'scenarios_page' %}" class="link-info">{% trans "BotBook" %}</a>
                            {% blocktrans %}- a database of ready-to-download and implement skills. Thanks to this, process automation does not require specialist knowledge or long-term implementations and can bring results immediately after downloading.
                            By using the database of solutions, entrepreneurs receive inspiration and tips on further processes that they can automate.{% endblocktrans %}
                          </p>
                          <p>
                            {% trans "Check out" %} <a href="{% url 'how_it_works' %}" class="link-info">{% trans "How it works" %}</a>
                          </p>
                        </div>
                        
                    </div>
                  </div>
                  <!-- Content for Section 2 -->
                  <div class="swiper-slide">
                    <img src="{% static 'images/homepage/robo_1.jpg' %}" alt="{% trans 'Key features' %}" class="img-fluid">
                    <div class="p-4">
                      <h3 class="text-black h5 mb-3">{% trans 'Key features' %}</h3>
                      <p>
                        {% trans "Discover a range of powerful automation features designed for ease of use and efficiency. With a quick installation process, accessible to anyone, even without technical expertise, you can implement proven automation scenarios in just a few steps. Our system ensures security, transparency, and cost-effectiveness with a pay-per-use model." %}
                      </p>
                      <ul class="list-unstyled list-check">
                        <li>{% trans "Ready to download, proven automation skills" %}</li>
                        <li>{% trans "Speed and simplicity of installation - automation in 5 steps" %}</li>
                        <li>{% trans "Pay-Per-Use settlement system" %}</li>
                        <li>{% trans "No programming knowledge required" %}</li>
                        <li>{% trans "Simple and clear UX" %}</li>
                        <li>{% trans "Security and transparency" %}</li>
                      </ul>
                    </div>
                  </div>
                  <!-- Content for Section 3 -->
                  <div class="swiper-slide">
                    <img src="{% static 'images/homepage/robo_3.jpg' %}" alt="{% trans 'Botie enables' %}" class="img-fluid">
                    <div class="p-4">
                      <h3 class="text-black h5 mb-3">{% trans 'Botie enables' %}</h3>
                      <p>
                        {% trans 'Botie offers smart automation of everyday tasks, allowing businesses to streamline operations and free up valuable employee time for more important activities. With proven solutions and easy access to automation tools like BotBook, you can boost productivity while reducing errors and saving costs through a pay-per-use model.' %}
                      </p>
                      <ul class="list-unstyled list-check">
                        <li>{% trans "Automation of repetitive, routine and secondary activities" %}</li>
                        <li>{% trans "Time savings and relief for employees, better allocation of work" %}</li>
                        <li>{% trans "Savings (pay per use)" %}</li>
                        <li>{% trans "Easy access to automation solutions (BotBook)" %}</li>
                        <li>{% trans "Inspiration for further automation processes and company development" %}</li>
                        <li>{% trans "Proven scenario database - allows you to use the experience of others" %}</li>
                        <li>{% trans "Repeatability - reducing human errors" %}</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <!-- Pagination -->
                <div class="swiper-pagination"></div>
              </div>
            </div>
          </div>
        </div>
    </section><!-- /Robotization Section -->

    <!-- BotBook Section -->
    {% get_current_language as LANGUAGE_CODE %}
    <section class="py-3 botbook">
      <section id="steps" class="section">
          <!-- Section Title -->
          <div class="container section-title fade-in">
              <h2>{% trans "BotBook" %}</h2>
              <p>{% trans "Choose ready-made skills and get inspired in which company departments Botie can reduce the workload of employees." %}</p>
          </div><!-- End Section Title -->
      <div class="container fade-in">
        <div class="slide-container-categ swiper">
            <div class="slide-content-categ">
                <div class="card-wrapper-categ swiper-wrapper">
                  {% for card in cards %}
                    <div class="card-categ swiper-slide">
                        <div class="image-content">
                            <span class="overlay"></span>
                            <div class="card-image">
                                <img src="{{ card.img.url }}" alt="" class="card-img">
                            </div>
                        </div>
                        <div class="card-content">
                            <h2 class="name">
                                {% if LANGUAGE_CODE|slice:":2" == 'pl' %}
                                    {{ card.title_pl }}
                                {% else %}
                                    {{ card.title_en }}
                                {% endif %}
                            </h2>
                            <div class="description mt-3 _text-light text-center">
                              {% for item in card.descriptions.all %}
                                  {% if LANGUAGE_CODE|slice:":2" == 'pl' %}
                                      <p>{{ item.text_pl }}</p>
                                  {% else %}
                                      <p>{{ item.text_en }}</p>
                                  {% endif %}
                              {% endfor %}
                            </div>
                            <a class="btn btn-primary" href="{% url 'scenarios_page' %}?q=&cat={{ card.title_pl|urlencode }}">
                                {% trans "See the skills" %}
                            </a>
                        </div>
                    </div>
                  {% endfor %}
                </div>
            </div>
            <!-- Nawigacja Swiper -->
            <div class="swiper-button-next swiper-navBtn"></div>
            <div class="swiper-button-prev swiper-navBtn"></div>
            <div class="swiper-pagination"></div>
        </div>
      </div>
    </section>

    <section class="py-3">
      <div class="container fade-in">
      {% include 'front/home_page/contact_promo_section.html' %}
      </div>
    </section>

    <div class="mt-5 mb-5 divider"></div>


<script>
$( document ).ready(function() {
  // carousel auto-slide (currently data-bs-ride and data-bs-interval do the job)
  /*var myCarousel = document.querySelector('#carouselExampleDark')
  var carousel = new bootstrap.Carousel(myCarousel, {
    interval: 5000,
    wrap: false
  });*/

  //swiper init
  var swiper = new Swiper(".slide-content-categ", {
    slidesPerView: 3,
    spaceBetween: 25,
    loop: false,
    centerSlide: 'true',
    fade: 'true',
    grabCursor: 'true',
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
      dynamicBullets: true,
    },
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },

    breakpoints:{
        0: {
            slidesPerView: 1,
        },
        992: {
            slidesPerView: 2,
        },
        1400: {
            slidesPerView: 3,
        },
    },
  });
});

document.addEventListener("DOMContentLoaded", function() {
    function initSwiper() {
      document.querySelectorAll(".init-swiper").forEach(function(swiperElement) {
        let config = JSON.parse(
          swiperElement.querySelector(".swiper-config").innerHTML.trim()
        );

        new Swiper(swiperElement, config);
      });
    }

    initSwiper();
  });

  document.addEventListener("DOMContentLoaded", function() {
  function initSwiperTabs() {
    document.querySelectorAll(".init-swiper-tabs").forEach(function(swiperElement) {
      let config = JSON.parse(
        swiperElement.querySelector(".swiper-config").innerHTML.trim()
      );

      const dotsContainer = swiperElement.closest("section").querySelector(".js-custom-dots");
      if (!dotsContainer) return;

      const customDots = dotsContainer.querySelectorAll(".service-item.link");

      // Remove the default pagination setting
      delete config.pagination;

      const swiperInstance = new Swiper(swiperElement, config);

      swiperInstance.on("slideChange", function() {
        updateSwiperTabsPagination(swiperInstance, customDots);
      });

      customDots.forEach((dot, index) => {
        dot.addEventListener("click", function(e) {
          e.preventDefault();
          swiperInstance.slideToLoop(index);
          updateSwiperTabsPagination(swiperInstance, customDots);
        });
      });

      updateSwiperTabsPagination(swiperInstance, customDots);
    });
  }

  function updateSwiperTabsPagination(swiperInstance, customDots) {
  const activeIndex = swiperInstance.realIndex;
  console.log('Active Index:', activeIndex);
  customDots.forEach((dot, index) => {
    console.log('Dot Index:', index, 'Dot:', dot);
    if (index === activeIndex) {
      dot.classList.add("active");
    } else {
      dot.classList.remove("active");
    }
  });
}

  initSwiperTabs();
});

</script>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    // Pobieramy wszystkie elementy z klasą "typed-word"
    var elements = document.querySelectorAll(".typed-word");
    
    // Dla każdego elementu ustawiamy kolor i uruchamiamy efekt "pisania"
    elements.forEach(function(element) {
      // Ustawiamy kolor elementu na brand red (#b72f2f)
      element.style.color = "#b72f2f";
      
      // Pobieramy tekst z elementu i zapisujemy go do zmiennej
      var text = element.textContent.trim();
      
      // Funkcja rozpoczynająca efekt wpisywania dla tego elementu
      function typeEffect() {
        var i = 0;
        // Czyścimy zawartość, aby rozpocząć animację od nowa
        element.textContent = "";
        
        function typeLetter() {
          if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(typeLetter, 150); // Opóźnienie 150 ms między literami
          } else {
            // Po zakończeniu efektu czekamy 15 sekund i uruchamiamy efekt ponownie
            setTimeout(typeEffect, 15000);
          }
        }
        
        typeLetter();
      }
      
      // Uruchamiamy efekt dla aktualnego elementu
      typeEffect();
    });
  });
  </script>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    var isDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    var pictures = document.querySelectorAll('.responsive-bg picture');
    
    pictures.forEach(function(picture) {
      var sources = picture.querySelectorAll('source');
      sources.forEach(function(source) {
        // Jeśli media attribute zawiera prefers-color-scheme: dark, a nie jesteśmy w dark mode – usuń ten element
        if (source.media && source.media.indexOf('prefers-color-scheme: dark') !== -1 && !isDark) {
          source.remove();
        }
        // Jeśli media attribute zawiera prefers-color-scheme: light, a jesteśmy w dark mode – usuń ten element
        if (source.media && source.media.indexOf('prefers-color-scheme: light') !== -1 && isDark) {
          source.remove();
        }
      });
    });
  });
  </script>

<script>
  // Funkcja pobierająca wartość ciasteczka o podanej nazwie
  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        // Sprawdzamy, czy cookie zaczyna się od "name="
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  // Funkcja określająca tryb na podstawie ciasteczka "theme"
  const runColorMode = (fn) => {
    // Zakładamy, że ciasteczko "theme" zawiera "dark" lub "light"
    const theme = getCookie('theme') || 'light';
    const isDark = theme.toLowerCase().indexOf('dark') !== -1;
    fn(isDark);
  };

  // Funkcja aktualizująca obrazy dla elementów slidera:
  function updateSliderImages(isDarkMode) {
    // Aktualizacja dla layoutu C – elementy .slider-bg
    document.querySelectorAll('.slider-bg').forEach(function(el) {
      const lightImg = el.getAttribute('data-light-image');
      const darkImg = el.getAttribute('data-dark-image');
      if (isDarkMode && darkImg) {
        el.style.backgroundImage = "url('" + darkImg + "')";
      } else if (lightImg) {
        el.style.backgroundImage = "url('" + lightImg + "')";
      } else {
        el.style.backgroundImage = "";
      }
    });
  }

  // Funkcja aktualizująca responsywne obrazy wewnątrz <picture>
  function updateResponsiveImages(isDarkMode) {
    // Dla każdego <source> w elementach o klasie .responsive-image
    document.querySelectorAll('.responsive-image source').forEach(function(source) {
      if (isDarkMode) {
        const darkSrcset = source.getAttribute('data-dark-srcset');
        if (darkSrcset) {
          source.setAttribute('srcset', darkSrcset);
        }
      } else {
        const lightSrcset = source.getAttribute('data-light-srcset');
        if (lightSrcset) {
          source.setAttribute('srcset', lightSrcset);
        }
      }
    });
    // Aktualizacja fallbackowego <img> wewnątrz .responsive-image
    document.querySelectorAll('.responsive-image img').forEach(function(img) {
      if (isDarkMode) {
        const darkSrc = img.getAttribute('data-dark-src');
        if (darkSrc) {
          img.src = darkSrc;
        }
      } else {
        const lightSrc = img.getAttribute('data-light-src');
        if (lightSrc) {
          img.src = lightSrc;
        }
      }
    });
  }

  // Łączymy aktualizację slidera i responsywnych obrazów
  function updateAllSliderImages(isDarkMode) {
    updateSliderImages(isDarkMode);
    updateResponsiveImages(isDarkMode);
  }

  // Po załadowaniu DOM uruchamiamy aktualizację
  document.addEventListener('DOMContentLoaded', function() {
    runColorMode(updateAllSliderImages);
  });
</script>


{% endblock %}