# Generated by Django 4.1.9 on 2024-08-12 10:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0017_remove_robot_offline_credits'),
    ]

    operations = [
        migrations.CreateModel(
            name='StartScenario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, help_text='Czy ten element powinien być wyświetlany?')),
                ('start_date', models.DateTimeField(blank=True, help_text='Data i czas rozpoczęcia wyświetlania elementu.', null=True)),
                ('end_date', models.DateTimeField(blank=True, help_text='Data i czas zakończenia wyświetlania elementu.', null=True)),
                ('sid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.scenario')),
            ],
        ),
    ]
