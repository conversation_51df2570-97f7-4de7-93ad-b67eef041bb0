:root {
--bs-link-color-rgb: 51,168,255; /* brand-blue */
--bs-link-hover-color-rgb: 91,198,255; /* brand-blue modified */
--bs-dashboard-footer-rgb: rgb(232,232,232) /* brand-gray modified */

--tile-border-color: #ccc;
--tile-background-color: #eef4fa; /* light gray background */
--tile-hover-background-color: #e2e6ea; /* slightly darker gray on hover/select */
--tile-hover-border-color: #adb5bd; /* gray border */
--card-text-color: #212529; /* default text color */
--form-group-margin-bottom: 15px;
}

.navbar-home {
background-color: var(--gray-600);
}

.dashboard-header {
background-color: var(--gray-200);
}

.robotyzacja-header {
background-color: var(--bs-white);
}

.card-categ {
background-color: var(--gray-100);
}

.btn-primary {
background-color: var(--brand-blue) !important;
border-color: var(--brand-blue) !important;
}

.btn-outline-primary {
border-color: var(--brand-blue) !important;
color: var(--brand-blue) !important;
}

.btn-outline-primary:hover {
background-color: var(--brand-blue) !important;
color: #f0f0f0 !important;
}

.swiper-pagination-bullet {
background-color: rgb(51,168,255);
}

.swiper-pagination-bullet-active {
background-color: rgb(91,198,255);
}

/* PRICE LIST */
.accordion.pricelist-regulations {
	--bs-accordion-btn-color: black;
	--bs-accordion-active-color: var(--brand-blue);
}

.pricingTable {
background: var(--bs-light);
box-shadow-color: var(--bs-dark);
}

/* CUSTOM SWITCH */
.custom_switch .form-check-input, .custom_switch .form-check-input:focus, .custom_switch .form-check-input:checked {
    background-color: transparent;
    border-color: var(--brand-blue);
    box-shadow: 0 0 0 0.2rem rgba(51, 168, 255, 0.25);
}

.custom_switch .form-check-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, .9%29'/%3e%3c/svg%3e");
}

/* PAGINATION */
.page-link {
color: var(--brand-blue) !important;
}

.page-link:hover{
color: var(--brand-blue) !important;
}

.page-item.active .page-link {
border-color: var(--brand-blue) !important;
background-color: var(--brand-blue) !important;
color: white !important;
}

/* PRIMARY TEXT */
.text-primary {
color: var(--brand-blue) !important;
}

/* PRIMARY BACKGROUND */
.bg-primary {
background-color: var(--brand-blue) !important;
}

/* FORM-CONTROL GLOW */
.form-control:focus, .page-link:focus {
border-color: var(--brand-blue);
box-shadow: 0 0 0 0.2rem rgba(51, 168, 255, 0.25);
}

/* DROPDOWN PRIMARY */
.dropdown-item.active, .dropdown-item:active {
background-color: var(--brand-blue) !important;
}

/* SIDEBAR */
.sidebar-menu {
background-color: var(--brand-lightgray);
}
.menu-chosen-bg {
background-color: var(--brand-blue);
}
.menu-unchosen-bg:hover {
background-color: var(--brand-semilightgray);
}

/* SCENARIOS */
/* .scenario-card {
border-color: var(--brand-blue);
}

.scenario-card:hover {
border-color: var(--brand-blue);
} */

/* HOME PAGE */
.index-robotyzacja-border {
border-color: var(--brand-blue) !important;
}

.index-botie-border {
border-color: var(--brand-blue) !important;
}

.index-botieweb-border {
border-color: var(--brand-blue) !important;
}

.border-lead {
border-color: var(--brand-blue) !important;
}

.steps-container .number {
color: var(--brand-blue);
}

/* DASHBOARD */
.header-dashboard div.solid {
    background-color: var(--gray-100);
    height: 70px;
}
.header-dashboard div.gradient {
    background-image: linear-gradient(to top, rgba(238, 240, 244, 0), rgba(238, 240, 244, 1));
}
.footer-dashboard div.gradient {
    background-image: linear-gradient(to bottom, rgba(238, 240, 244, 0), rgba(238, 240, 244, 1));
}
.footer-dashboard div.solid {
    background-color: var(--gray-100);
    height: 30px;
}

.main-dashboard-content {
    background-color: var(--gray-100);
}

/* NAV */
.navbar-dashboard {
    background-color: var(--gray-200);
}

.site-name {
    color: var(--gray-700)!important;
}

.site-name a {
    color: var(--brand-red-500)!important;
}

/* SIDEBAR */
#sidebar-wrapper {
    background-color: var(--gray-200);
}

.logo-sidebar {
    background-image: url("/static/images/brandbook/logo_red_icon_text_100.png");
}

.sidebar-menu {
    background-color: var(--gray-200) !important;
}
.menu-chosen-bg {
    background-color: var(--primary-600);
}

/* ELEMENT */
.dashboard-element {
    background-color: var(--gray-200);
    box-shadow: 0 1px 5px 0 var(--accent-color);
}

.welcome-tile-menu{
    background-color: var(--gray-200);
}

/* BUTTONS */
.btn-dashboard {
    background-color: var(--primary-600) !important;
    border-color: var(--primary-600) !important;
}

.btn-dashboard:hover {
    background-color: var(--primary-500) !important;
    color: #ffffff !important;
}

.btn-outline-primary-db {
    color: var(--primary-600) !important;
    border-color: var(--primary-600) !important;
}

.page-link-dashboard {
    color: var(--primary-600) !important;
}

.page-link-dashboard:hover {
    color: var(--primary-600) !important;
}

.page-item-dashboard.active .page-link {
    border-color: var(--primary-600) !important;
    background-color: var(--primary-600) !important;
    color: white !important;
}

.site-name a {
    text-decoration: none;
    color: var(--primary-600);
}

/* ADDITIONAL ELEMENTS WITH BRAND RED */
.btn-brand-red {
    background-color: var(--brand-red-600) !important;
    border-color: var(--brand-red-600) !important;
}

.btn-brand-red:hover {
    background-color: var(--brand-red-500) !important;
    color: #ffffff !important;
}

.text-brand-red {
    color: var(--brand-red-600) !important;
}

.border-brand-red {
    border-color: var(--brand-red-600) !important;
}

.sort-link {
    color: var(--brand-black);
}

/* CUSTOM TABLES */
.my-custom-table {
    color: var(--gray-700);
    --bs-table-bg: var(--gray-200) !important;
}

.my-custom-table-level-2 {
    color: var(--gray-700);
    --bs-table-bg: var(--gray-300) !important;
}

/* HOMEPAGE - GUIDE CARDS */
.guide-card {
    background-color: var(--bs-light);
}

.invert-svg {
    filter: invert(100%);
}

#category-dropdown {
    background-color: var(--gray-600);
}