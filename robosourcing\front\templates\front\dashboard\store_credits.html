{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load divide %}

{% block title %}{{ block.super }} - {% trans "Credits" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{%url 'store'%}">{% trans "Store" %}</a></li>
          <li class="breadcrumb-item site-name active">{% trans "Credit packages" %}</li>
        </ol>
    </nav>

    <div class="dashboard-element">
        <h3 class="db-section-title">{% blocktrans %}Buy a credit package and enjoy flexibility. You buy as many as you need
    and use them at any time, after your subscription runs out (if you have one).{% endblocktrans %}</h3>
        <hr>
        <div class="row">

            {% for product in products %}
            <div class="col-md-6 col-lg-4 col-xxl-3 mt-3">
                <form action="{% url 'add_to_basket' %}" method="post">
                    {% csrf_token %}
                    <input type="hidden" name="pricelist_item_id" value="{{ product.id }}">
                    <div class="dashboard-card shop-item">
                        <div class="card-header">
                            <h4>{{ product.name }}</h4>
                            <h5>{{ product.description }}</h5>
                        </div>
                        <div class="card-body">
                            <h6 class="text-secondary">{% trans 'Credits' %}: {{ product.value|floatformat:"0g" }}</h6>
                            <h6 class="text-secondary">{% trans 'Price' %}: {{ product.price|floatformat:"2g" }} zł</h6>
                            <div class="d-flex justify-content-end">
                                <div class="input-group w-auto">
                                    <!-- Przycisk do dodawania do koszyka bez przekierowania -->
                                    <button class="btn btn-secondary" type="submit" name="add_to_basket">
                                        <i class="fa fa-cart-arrow-down"></i>
                                    </button>
                                    <!-- Przycisk do dodawania do koszyka i przekierowania do koszyka -->
                                    <button type="submit" class="btn btn-primary" name="buy_now">
                                        {% trans 'Buy' %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            {% endfor %}

        </div>
    </div>
</div>
<script>
function addToBasket(el) {
    $(el).attr('action','{% url 'add_to_basket' %}');
    $(el).submit();
}
</script>
{% endblock %}