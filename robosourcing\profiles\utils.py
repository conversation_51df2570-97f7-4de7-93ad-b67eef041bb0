from django.db.models import Sum
from db.models import PartnerCommission, AffiliateWithdrawalRequest
from decimal import Decimal

def get_available_balance(user):
    # całkowita prowizja
    total_commission = PartnerCommission.objects.filter(partner=user).aggregate(total=Sum('amount'))['total'] or Decimal('0')
    
    # suma zgłoszonych wypłat w statusie PENDING lub PAID
    requested_sum = AffiliateWithdrawalRequest.objects.filter(
        user=user,
        status__in=[AffiliateWithdrawalRequest.Status.PENDING, AffiliateWithdrawalRequest.Status.PAID]
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

    available = total_commission - requested_sum
    return max(available, Decimal('0'))