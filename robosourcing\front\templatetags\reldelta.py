from django import template
from datetime import datetime
from dateutil.relativedelta import relativedelta

register = template.Library()

@register.filter(name='reldelta')
def relative_delta(value, args):
    """
        Returns the shifted date (taking into account the variable number of days in months)
    """
    period, num = args.split(',')
    date = datetime.strptime(value, "%Y-%m-%d")
    date += relativedelta(**{period: int(num)})
    return date.strftime("%Y-%m-%d")