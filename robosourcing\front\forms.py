from django import forms
from django.core.exceptions import ValidationError
from .models import CarouselItem, ContactRequest, NewsletterSubscription
from db.models import DownloadItem

class CarouselItemForm(forms.ModelForm):
    class Meta:
        model = CarouselItem
        fields = '__all__'

class DownloadItemForm(forms.ModelForm):
    class Meta:
        model = DownloadItem
        fields = '__all__'

class ContactForm(forms.ModelForm):
    class Meta:
        model = ContactRequest
        fields = [
            'name',
            'email',
            'message_type',
            'scenario_name',
            'description',
            'base_scenario',
            'contact_preference',
            'contact_info',
            'app_version',
        ]


class NewsletterForm(forms.ModelForm):
    # Pole captcha (proste zabezpieczenie)
    captcha = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={
            'placeholder': 'Wpisz wynik: 5 + 3 = ?',
            'class': 'form-control'
        }),
        label="Zabezpieczenie przed robotami"
    )

    class Meta:
        model = NewsletterSubscription
        fields = ['email', 'marketing_consent', 'data_processing_consent']
        widgets = {
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Twój adres e-mail'
            }),
            'marketing_consent': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'data_processing_consent': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        labels = {
            'email': 'Adres e-mail',
            'marketing_consent': 'Wyrażam zgodę na otrzymywanie informacji marketingowych',
            'data_processing_consent': 'Wyrażam zgodę na przetwarzanie moich danych osobowych',
        }

    def clean_captcha(self):
        captcha = self.cleaned_data.get('captcha')
        if captcha != '8':
            raise ValidationError('Nieprawidłowa odpowiedź. Spróbuj ponownie.')
        return captcha

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if NewsletterSubscription.objects.filter(email=email).exists():
            raise ValidationError('Ten adres e-mail jest już zapisany do newslettera.')
        return email

    def clean_marketing_consent(self):
        consent = self.cleaned_data.get('marketing_consent')
        if not consent:
            raise ValidationError('Zgoda na marketing jest wymagana do zapisania się do newslettera.')
        return consent

    def clean_data_processing_consent(self):
        consent = self.cleaned_data.get('data_processing_consent')
        if not consent:
            raise ValidationError('Zgoda na przetwarzanie danych jest wymagana.')
        return consent