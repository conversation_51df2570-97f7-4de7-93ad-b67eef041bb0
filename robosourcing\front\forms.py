from django import forms
from django.core.exceptions import ValidationError
from .models import CarouselItem, ContactRequest, NewsletterSubscription, NewsletterCampaign, NewsletterTemplate, NewsletterSegment
from db.models import DownloadItem

class CarouselItemForm(forms.ModelForm):
    class Meta:
        model = CarouselItem
        fields = '__all__'

class DownloadItemForm(forms.ModelForm):
    class Meta:
        model = DownloadItem
        fields = '__all__'

class ContactForm(forms.ModelForm):
    class Meta:
        model = ContactRequest
        fields = [
            'name',
            'email',
            'message_type',
            'scenario_name',
            'description',
            'base_scenario',
            'contact_preference',
            'contact_info',
            'app_version',
        ]


class NewsletterForm(forms.ModelForm):
    # Pole captcha (proste zabezpieczenie)
    captcha = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={
            'placeholder': 'Wpisz wynik: 5 + 3 = ?',
            'class': 'form-control'
        }),
        label="Zabezpieczenie przed robotami"
    )

    class Meta:
        model = NewsletterSubscription
        fields = ['email', 'marketing_consent', 'data_processing_consent']
        widgets = {
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Twój adres e-mail'
            }),
            'marketing_consent': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'data_processing_consent': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        labels = {
            'email': 'Adres e-mail',
            'marketing_consent': 'Wyrażam zgodę na otrzymywanie informacji marketingowych',
            'data_processing_consent': 'Wyrażam zgodę na przetwarzanie moich danych osobowych',
        }

    def clean_captcha(self):
        captcha = self.cleaned_data.get('captcha')
        if captcha != '8':
            raise ValidationError('Nieprawidłowa odpowiedź. Spróbuj ponownie.')
        return captcha


class NewsletterCampaignForm(forms.ModelForm):
    """Formularz do tworzenia i edycji kampanii newslettera"""

    class Meta:
        model = NewsletterCampaign
        fields = ['name', 'template', 'segment', 'scheduled_at']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'newsletter-form-control form-control',
                'placeholder': 'Nazwa kampanii'
            }),
            'template': forms.Select(attrs={
                'class': 'newsletter-form-control form-control'
            }),
            'segment': forms.Select(attrs={
                'class': 'newsletter-form-control form-control'
            }),
            'scheduled_at': forms.DateTimeInput(attrs={
                'class': 'newsletter-form-control form-control',
                'type': 'datetime-local'
            }),
        }
        labels = {
            'name': 'Nazwa kampanii',
            'template': 'Szablon',
            'segment': 'Segment odbiorców',
            'scheduled_at': 'Zaplanuj wysyłkę (opcjonalnie)',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Pobierz tylko aktywne szablony i segmenty
        self.fields['template'].queryset = NewsletterTemplate.objects.filter(is_active=True)
        self.fields['segment'].queryset = NewsletterSegment.objects.all()

        # Ustaw puste opcje
        self.fields['template'].empty_label = "Wybierz szablon"
        self.fields['segment'].empty_label = "Wybierz segment"

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if NewsletterSubscription.objects.filter(email=email).exists():
            raise ValidationError('Ten adres e-mail jest już zapisany do newslettera.')
        return email


class NewsletterTemplateForm(forms.ModelForm):
    """Formularz dla szablonów newslettera"""

    class Meta:
        model = NewsletterTemplate
        fields = ['name', 'subject', 'content_html', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'newsletter-form-control form-control',
                'placeholder': 'Nazwa szablonu'
            }),
            'subject': forms.TextInput(attrs={
                'class': 'newsletter-form-control form-control',
                'placeholder': 'Temat wiadomości'
            }),
            'content_html': forms.Textarea(attrs={
                'class': 'newsletter-form-control form-control',
                'rows': 15,
                'placeholder': 'Główna treść newslettera (bez nagłówka i stopki - zostaną dodane automatycznie)...'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        labels = {
            'name': 'Nazwa szablonu',
            'subject': 'Temat',
            'content_html': 'Treść HTML',
            'content_text': 'Treść tekstowa',
            'is_active': 'Aktywny',
        }

    def clean_marketing_consent(self):
        consent = self.cleaned_data.get('marketing_consent')
        if not consent:
            raise ValidationError('Zgoda na marketing jest wymagana do zapisania się do newslettera.')
        return consent

    def clean_data_processing_consent(self):
        consent = self.cleaned_data.get('data_processing_consent')
        if not consent:
            raise ValidationError('Zgoda na przetwarzanie danych jest wymagana.')
        return consent


class NewsletterSegmentForm(forms.ModelForm):
    class Meta:
        model = NewsletterSegment
        fields = ['name', 'description', 'confirmed_only', 'registration_date_from', 'registration_date_to', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'newsletter-form-control form-control',
                'placeholder': 'Nazwa segmentu...'
            }),
            'description': forms.Textarea(attrs={
                'class': 'newsletter-form-control form-control',
                'rows': 3,
                'placeholder': 'Opis segmentu...'
            }),
            'confirmed_only': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'registration_date_from': forms.DateTimeInput(attrs={
                'class': 'newsletter-form-control form-control',
                'type': 'datetime-local'
            }),
            'registration_date_to': forms.DateTimeInput(attrs={
                'class': 'newsletter-form-control form-control',
                'type': 'datetime-local'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        labels = {
            'name': 'Nazwa segmentu',
            'description': 'Opis segmentu',
            'confirmed_only': 'Tylko potwierdzone subskrypcje',
            'registration_date_from': 'Data rejestracji od',
            'registration_date_to': 'Data rejestracji do',
            'is_active': 'Aktywny segment',
        }