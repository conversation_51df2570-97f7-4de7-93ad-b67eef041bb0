# Generated by Django 4.1.7 on 2024-05-06 20:49

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CarouselItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, help_text='Wprowadź nagłówek.', max_length=100, null=True)),
                ('description', models.TextField(blank=True, help_text='Wprowadź opis.', null=True)),
                ('button_url', models.CharField(blank=True, help_text="Wprowadź URL (np. 'download').", max_length=100, null=True)),
                ('button_description', models.CharField(blank=True, help_text='Wprowadź tekst buttona.', max_length=100, null=True)),
                ('button_undertext', models.TextField(blank=True, help_text='Wprowadź tekst pod przyciskiem.', null=True)),
                ('image', models.ImageField(blank=True, help_text='Wgraj zdjęcie w tle. Zalecana rodzielczość.', null=True, upload_to='carousel_images')),
                ('is_displayed', models.BooleanField(default=True, help_text='Czy ten element powinien być wyświetlany?')),
                ('display_start_date', models.DateTimeField(blank=True, help_text='Data i czas rozpoczęcia wyświetlania elementu.', null=True)),
                ('display_end_date', models.DateTimeField(blank=True, help_text='Data i czas zakończenia wyświetlania elementu.', null=True)),
            ],
        ),
    ]
