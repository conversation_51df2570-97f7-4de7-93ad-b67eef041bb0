# Generated by Django 4.2.13 on 2025-06-25 20:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('front', '0014_newslettersubscription'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExternalMailingService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nazwa serwisu')),
                ('service_type', models.CharField(choices=[('mailchimp', 'Mailchimp'), ('sendgrid', 'SendGrid'), ('mailgun', 'Mailgun'), ('aws_ses', 'Amazon SES'), ('custom', 'Niestandardowy')], max_length=20, verbose_name='Typ serwisu')),
                ('api_key', models.CharField(max_length=200, verbose_name='Klucz API')),
                ('api_url', models.URLField(blank=True, verbose_name='URL API')),
                ('is_active', models.BooleanField(default=False, verbose_name='Aktywny')),
                ('is_default', models.BooleanField(default=False, verbose_name='Domyślny')),
                ('daily_limit', models.IntegerField(blank=True, null=True, verbose_name='Dzienny limit')),
                ('monthly_limit', models.IntegerField(blank=True, null=True, verbose_name='Miesięczny limit')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Zewnętrzny serwis mailingowy',
                'verbose_name_plural': 'Zewnętrzne serwisy mailingowe',
            },
        ),
        migrations.CreateModel(
            name='NewsletterCampaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nazwa kampanii')),
                ('status', models.CharField(choices=[('draft', 'Szkic'), ('scheduled', 'Zaplanowana'), ('sending', 'Wysyłanie'), ('sent', 'Wysłana'), ('cancelled', 'Anulowana')], default='draft', max_length=20, verbose_name='Status')),
                ('scheduled_at', models.DateTimeField(blank=True, null=True, verbose_name='Zaplanowane na')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='Wysłane')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('total_recipients', models.IntegerField(default=0, verbose_name='Liczba odbiorców')),
                ('emails_sent', models.IntegerField(default=0, verbose_name='Wysłane emaile')),
                ('emails_failed', models.IntegerField(default=0, verbose_name='Nieudane emaile')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Utworzone przez')),
            ],
            options={
                'verbose_name': 'Kampania newslettera',
                'verbose_name_plural': 'Kampanie newsletterów',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NewsletterDelivery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sent_at', models.DateTimeField(auto_now_add=True, verbose_name='Wysłane')),
                ('delivery_status', models.CharField(choices=[('sent', 'Wysłane'), ('delivered', 'Dostarczone'), ('bounced', 'Odrzucone'), ('failed', 'Nieudane')], default='sent', max_length=20, verbose_name='Status dostarczenia')),
                ('tracking_id', models.CharField(max_length=64, unique=True, verbose_name='ID śledzenia')),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='front.newslettercampaign', verbose_name='Kampania')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='front.newslettersubscription', verbose_name='Subskrypcja')),
            ],
            options={
                'verbose_name': 'Dostarczenie newslettera',
                'verbose_name_plural': 'Dostarczenia newsletterów',
                'unique_together': {('campaign', 'subscription')},
            },
        ),
        migrations.CreateModel(
            name='NewsletterSegment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nazwa segmentu')),
                ('description', models.TextField(blank=True, verbose_name='Opis segmentu')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True, verbose_name='Aktywny')),
                ('registration_date_from', models.DateTimeField(blank=True, null=True, verbose_name='Data rejestracji od')),
                ('registration_date_to', models.DateTimeField(blank=True, null=True, verbose_name='Data rejestracji do')),
                ('confirmed_only', models.BooleanField(default=True, verbose_name='Tylko potwierdzone')),
            ],
            options={
                'verbose_name': 'Segment newslettera',
                'verbose_name_plural': 'Segmenty newslettera',
            },
        ),
        migrations.CreateModel(
            name='NewsletterTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nazwa szablonu')),
                ('subject', models.CharField(max_length=200, verbose_name='Temat')),
                ('content_html', models.TextField(verbose_name='Treść HTML')),
                ('content_text', models.TextField(verbose_name='Treść tekstowa')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True, verbose_name='Aktywny')),
            ],
            options={
                'verbose_name': 'Szablon newslettera',
                'verbose_name_plural': 'Szablony newsletterów',
            },
        ),
        migrations.CreateModel(
            name='NewsletterOpen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('opened_at', models.DateTimeField(auto_now_add=True, verbose_name='Otwarte')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Adres IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='User Agent')),
                ('delivery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='front.newsletterdelivery', verbose_name='Dostarczenie')),
            ],
            options={
                'verbose_name': 'Otwarcie newslettera',
                'verbose_name_plural': 'Otwarcia newsletterów',
            },
        ),
        migrations.CreateModel(
            name='NewsletterClick',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(verbose_name='URL')),
                ('clicked_at', models.DateTimeField(auto_now_add=True, verbose_name='Kliknięte')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Adres IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='User Agent')),
                ('delivery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='front.newsletterdelivery', verbose_name='Dostarczenie')),
            ],
            options={
                'verbose_name': 'Kliknięcie w newsletterze',
                'verbose_name_plural': 'Kliknięcia w newsletterach',
            },
        ),
        migrations.AddField(
            model_name='newslettercampaign',
            name='segment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='front.newslettersegment', verbose_name='Segment'),
        ),
        migrations.AddField(
            model_name='newslettercampaign',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='front.newslettertemplate', verbose_name='Szablon'),
        ),
    ]
