from django.db.models.signals import pre_delete, post_delete, pre_save, post_save
from django.dispatch import receiver
from db.models import Credit, Robot, Arch_Robot, Scenario, Scenario_Event, UserSubscription

@receiver(pre_delete, sender=Robot)
def archive_robot(sender, instance, **kwargs):
    print('pre-delete')
    #FIXME:
    # print(instance._meta.get_fields())
    # Arch_Robot.objects.create(**instance.__dict__)
    Arch_Robot.objects.create(
        rid             = instance.rid,
        hid             = instance.hid,
        pid             = instance.pid,
        owner           = instance.owner,
        create_time     = instance.create_time,
        last_activ_time = instance.last_activ_time,
    )
    instance.remove_credits_from_robot()


@receiver(post_delete, sender=Robot)
def archive_robot(sender, **kwargs):
    print('post-delete')


@receiver(pre_save, sender=Credit)
def check_credit_ceiling(sender, instance, **kwargs):
    print('pre_save Credit')
    sub = UserSubscription.objects.filter(user_id=instance.user_id).first()
    if not sub:
        instance.subscription_credits = 0
    elif sub.value < instance.subscription_credits:
        instance.subscription_credits = sub.value


@receiver(pre_save, sender=Scenario)
def set_scenario_defaults(sender, instance, **kwargs):
    if not instance.root_id:
        instance.root_id = instance


@receiver(post_save, sender=Scenario)
def set_scenario_defaults(sender, instance, created, **kwargs):
    if created and instance.available in (Scenario.AvailabilityStatus.PRIVATE, Scenario.AvailabilityStatus.ACCOUNT) and instance.status is not Scenario.ScenarioStatus.ACCEPTED:
        instance.status = Scenario.ScenarioStatus.ACCEPTED