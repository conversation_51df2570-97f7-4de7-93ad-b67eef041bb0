{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }} - Newsletter{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_campaigns' %}">{% trans "Campaigns" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{{ title }}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fa fa-plus me-2"></i>{{ title }}</h2>
                <a href="{% url 'profiles_newsletter_campaigns' %}" class="newsletter-btn-secondary btn">
                    <i class="fa fa-arrow-left"></i> {% trans "Back to Campaigns" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="newsletter-card">
                <div class="newsletter-card-header">
                    <h5 class="mb-0">
                        {% if action == 'create' %}
                            <i class="fa fa-plus me-2"></i>{% trans "Create New Campaign" %}
                        {% else %}
                            <i class="fa fa-edit me-2"></i>{% trans "Edit Campaign" %}
                        {% endif %}
                    </h5>
                </div>
                <div class="newsletter-card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.name.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.template.id_for_label }}" class="form-label">{{ form.template.label }}</label>
                            {{ form.template }}
                            {% if form.template.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.template.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small style="color: var(--muted-foreground);" class="form-text">
                                {% trans "Choose the email template for this campaign" %}
                            </small>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.segment.id_for_label }}" class="form-label">{{ form.segment.label }}</label>
                            {{ form.segment }}
                            {% if form.segment.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.segment.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small style="color: var(--muted-foreground);" class="form-text">
                                {% trans "Select which subscribers will receive this campaign" %}
                            </small>
                        </div>

                        <div class="mb-4">
                            <label for="{{ form.scheduled_at.id_for_label }}" class="form-label">{{ form.scheduled_at.label }}</label>
                            {{ form.scheduled_at }}
                            {% if form.scheduled_at.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.scheduled_at.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small style="color: var(--muted-foreground);" class="form-text">
                                {% trans "Leave empty to save as draft. Set a future date to schedule the campaign." %}
                            </small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'profiles_newsletter_campaigns' %}" class="newsletter-btn-secondary btn">
                                <i class="fa fa-times"></i> {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="newsletter-btn-primary btn">
                                <i class="fa fa-save"></i> 
                                {% if action == 'create' %}
                                    {% trans "Create Campaign" %}
                                {% else %}
                                    {% trans "Update Campaign" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Dodaj walidację po stronie klienta
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const templateSelect = document.getElementById('{{ form.template.id_for_label }}');
    const segmentSelect = document.getElementById('{{ form.segment.id_for_label }}');
    
    // Podgląd liczby odbiorców przy zmianie segmentu
    if (segmentSelect) {
        segmentSelect.addEventListener('change', function() {
            // Tutaj można dodać AJAX call do pobrania liczby odbiorców
            console.log('Selected segment:', this.value);
        });
    }
    
    // Walidacja formularza
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        if (!templateSelect.value) {
            isValid = false;
            templateSelect.classList.add('is-invalid');
        } else {
            templateSelect.classList.remove('is-invalid');
        }
        
        if (!segmentSelect.value) {
            isValid = false;
            segmentSelect.classList.add('is-invalid');
        } else {
            segmentSelect.classList.remove('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('{% trans "Please fill in all required fields" %}');
        }
    });
});
</script>
{% endblock %}
