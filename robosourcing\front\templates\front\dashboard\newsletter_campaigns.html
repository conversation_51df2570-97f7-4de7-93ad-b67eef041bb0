{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Newsletter Campaigns" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Campaigns" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fa fa-paper-plane me-2"></i>{% trans "Newsletter Campaigns" %}</h2>
                <button class="btn btn-primary" onclick="createCampaign()">
                    <i class="fa fa-plus"></i> {% trans "New Campaign" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        {% trans "All Campaigns" %} 
                        {% if campaigns.paginator.count %}
                            ({{ campaigns.paginator.count }})
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if campaigns %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Subject" %}</th>
                                        <th>{% trans "Template" %}</th>
                                        <th>{% trans "Segment" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Created" %}</th>
                                        <th>{% trans "Scheduled" %}</th>
                                        <th>{% trans "Stats" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for campaign in campaigns %}
                                    <tr>
                                        <td>
                                            <strong>{{ campaign.subject }}</strong>
                                            {% if campaign.preview_text %}
                                                <br><small class="text-muted">{{ campaign.preview_text|truncatechars:50 }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if campaign.template %}
                                                <span class="badge bg-info">{{ campaign.template.name }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if campaign.segment %}
                                                <span class="badge bg-secondary">{{ campaign.segment.name }}</span>
                                            {% else %}
                                                <span class="text-muted">{% trans "All subscribers" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if campaign.status == 'draft' %}
                                                <span class="badge bg-secondary">{% trans "Draft" %}</span>
                                            {% elif campaign.status == 'scheduled' %}
                                                <span class="badge bg-warning">{% trans "Scheduled" %}</span>
                                            {% elif campaign.status == 'sending' %}
                                                <span class="badge bg-info">{% trans "Sending" %}</span>
                                            {% elif campaign.status == 'sent' %}
                                                <span class="badge bg-success">{% trans "Sent" %}</span>
                                            {% elif campaign.status == 'cancelled' %}
                                                <span class="badge bg-danger">{% trans "Cancelled" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ campaign.created_at|date:"d.m.Y H:i" }}</td>
                                        <td>
                                            {% if campaign.scheduled_at %}
                                                {{ campaign.scheduled_at|date:"d.m.Y H:i" }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if campaign.status == 'sent' %}
                                                <small>
                                                    <div>📧 {{ campaign.newsletterdelivery_set.count }}</div>
                                                    <div>👁️ {{ campaign.newsletterdelivery_set.aggregate.opens_count|default:0 }}</div>
                                                    <div>🔗 {{ campaign.newsletterdelivery_set.aggregate.clicks_count|default:0 }}</div>
                                                </small>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                {% if campaign.status == 'draft' %}
                                                    <button class="btn btn-outline-primary btn-sm" onclick="editCampaign('{{ campaign.id }}')">
                                                        <i class="fa fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success btn-sm" onclick="sendCampaign('{{ campaign.id }}')">
                                                        <i class="fa fa-paper-plane"></i>
                                                    </button>
                                                {% elif campaign.status == 'scheduled' %}
                                                    <button class="btn btn-outline-warning btn-sm" onclick="cancelCampaign('{{ campaign.id }}')">
                                                        <i class="fa fa-times"></i>
                                                    </button>
                                                {% elif campaign.status == 'sent' %}
                                                    <button class="btn btn-outline-info btn-sm" onclick="viewStats('{{ campaign.id }}')">
                                                        <i class="fa fa-chart-bar"></i>
                                                    </button>
                                                {% endif %}
                                                <button class="btn btn-outline-secondary btn-sm" onclick="duplicateCampaign('{{ campaign.id }}')">
                                                    <i class="fa fa-copy"></i>
                                                </button>
                                                {% if campaign.status == 'draft' %}
                                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteCampaign('{{ campaign.id }}')">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Paginacja -->
                        {% if campaigns.has_other_pages %}
                        <nav aria-label="Campaigns pagination">
                            <ul class="pagination justify-content-center">
                                {% if campaigns.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ campaigns.previous_page_number }}">
                                            {% trans "Previous" %}
                                        </a>
                                    </li>
                                {% endif %}
                                
                                {% for num in campaigns.paginator.page_range %}
                                    {% if campaigns.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > campaigns.number|add:'-3' and num < campaigns.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if campaigns.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ campaigns.next_page_number }}">
                                            {% trans "Next" %}
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fa fa-paper-plane fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No campaigns yet" %}</h5>
                            <p class="text-muted">{% trans "Create your first newsletter campaign to get started." %}</p>
                            <button class="btn btn-primary" onclick="createCampaign()">
                                <i class="fa fa-plus"></i> {% trans "Create Campaign" %}
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function createCampaign() {
    // Redirect to campaign creation page
    window.location.href = '{% url "profiles_newsletter_campaigns" %}create/';
}

function editCampaign(campaignId) {
    window.location.href = `{% url "profiles_newsletter_campaigns" %}${campaignId}/edit/`;
}

function sendCampaign(campaignId) {
    if (confirm('{% trans "Are you sure you want to send this campaign?" %}')) {
        fetch(`/newsletter/campaigns/${campaignId}/send/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error sending campaign" %}');
            }
        });
    }
}

function cancelCampaign(campaignId) {
    if (confirm('{% trans "Are you sure you want to cancel this scheduled campaign?" %}')) {
        fetch(`/newsletter/campaigns/${campaignId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error cancelling campaign" %}');
            }
        });
    }
}

function viewStats(campaignId) {
    window.location.href = `{% url "profiles_newsletter_campaigns" %}${campaignId}/stats/`;
}

function duplicateCampaign(campaignId) {
    if (confirm('{% trans "Create a copy of this campaign?" %}')) {
        fetch(`/newsletter/campaigns/${campaignId}/duplicate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error duplicating campaign" %}');
            }
        });
    }
}

function deleteCampaign(campaignId) {
    if (confirm('{% trans "Are you sure you want to delete this campaign?" %}')) {
        fetch(`/newsletter/campaigns/${campaignId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error deleting campaign" %}');
            }
        });
    }
}
</script>
{% endblock %}
