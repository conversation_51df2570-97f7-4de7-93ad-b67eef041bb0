from django import forms
from .models import User<PERSON>rofile, Address, OrganizationUUID, Contact, UserConsent
from db.models import Scenario
from django.core.exceptions import ValidationError
from db.models import AffiliateLink
from .models import UserProfile
from django.core.exceptions import ValidationError
from allauth.account.forms import SignupForm
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _

class CustomSignupForm(SignupForm):
    ref_code = forms.CharField(
        max_length=100, 
        required=False, 
        label='Kod referencyjny', 
        widget=forms.TextInput(attrs={'placeholder': 'Kod referencyjny'})
    )

    def __init__(self, *args, **kwargs):
        super(CustomSignupForm, self).__init__(*args, **kwargs)
        # Jeśli ref_code jest w initial, ukryj pole
        if self.initial.get('ref_code'):
            self.fields['ref_code'].widget = forms.HiddenInput()

    def clean_ref_code(self):
        ref_code = self.cleaned_data.get('ref_code')
        if ref_code:
            try:
                AffiliateLink.objects.get(code=ref_code)
            except AffiliateLink.DoesNotExist:
                raise ValidationError('Nieprawidłowy kod referencyjny.')
        return ref_code

    def save(self, request):
        user = super(CustomSignupForm, self).save(request)
        ref_code = self.cleaned_data.get('ref_code')
        if ref_code:
            affiliate_link = AffiliateLink.objects.get(code=ref_code)
            user_profile = UserProfile.objects.get(user=user)
            user_profile.referred_by = affiliate_link.user
            user_profile.save()
        return user

class UserNameForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ['first_name', 'last_name']
        labels = {
            'first_name': 'Imię',
            'last_name': 'Nazwisko',
        }

class UserConsentForm(forms.ModelForm):
    class Meta:
        model = UserConsent
        fields = ['email_consent', 'app_consent', 'phone_sms_consent']

class PhoneNumberForm(forms.ModelForm):
    class Meta:
        model = Contact
        fields = ['area_code', 'phone_number']

class AddressForm(forms.ModelForm):
    class Meta:
        model = Address
        fields = ['street', 'street_number', 'home_number', 'city', 'postal_code', 'country', 'timezone']

class OrganizationDetailsForm(forms.ModelForm):
    class Meta:
        model = OrganizationUUID
        fields = ['tax_id', 'name_1', 'name_2', 'street', 'street_number', 'home_number', 'city', 'postal_code', 'country']
        labels = {
            'tax_id': 'NIP',
            'name_1': 'Organization name 1',
            'name_2': 'Organization name 2',
            'street': 'Street',
            'city': 'City',
            'postal_code': 'Postal Code',
            'country': 'Country',
        }

    def clean(self):
        # Optional: Completely remove or keep minimal required cleaning if needed.
        return super().clean()


class ChangeScenarioStatusForm(forms.Form):
    status        = forms.ChoiceField(label=_("Scenario status"), widget=forms.Select(attrs={'class':'form-control','id':'status'}))
    moderator     = forms.ModelChoiceField(queryset=UserProfile.objects.none(), required=False, label=_("Moderator"), widget=forms.Select(attrs={'class':'form-control','id':'moderator'}))
    customMessage = forms.CharField(label=_("Message"), required=False, widget=forms.Textarea(attrs={'class':'form-control','id':'customMessage','rows':3}))

    def __init__(self, *args, moderators_qs=None, **kwargs):
        super().__init__(*args, **kwargs)
        allowed = [
            (val, name)
            for val, name in Scenario.ScenarioStatus.choices
            if val != Scenario.ScenarioStatus.TO_MOD
        ]
        self.fields['status'].choices = allowed
        if moderators_qs is not None:
            self.fields['moderator'].queryset = moderators_qs
            self.fields['moderator'].label_from_instance = lambda p: (
                f"{p.user.first_name} {p.user.last_name}".strip() or p.user.username
            )

    def clean(self):
        cleaned = super().clean()
        status = int(cleaned.get('status'))
        msg    = cleaned.get('customMessage','').strip()
        if status == Scenario.ScenarioStatus.REJECTED and not msg:
            self.add_error('customMessage', _("Message is required when rejecting a scenario."))
        return cleaned


class ScenarioTagForm(forms.ModelForm):
    new_tag = forms.CharField(max_length=50, required=False, label=_("Add tag"), widget=forms.TextInput(attrs={'id':'new-tag-input','autocomplete':'off'}))
    class Meta:
        model  = Scenario
        fields = []