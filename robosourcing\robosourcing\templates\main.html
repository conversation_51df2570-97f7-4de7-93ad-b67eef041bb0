<!DOCTYPE html>
{% load i18n %}
{% load static %}
{% load split %}
{% load theme %}
{#{% load sass_tags %}#}
{% load meta %}

{% get_current_language as LANGUAGE_CODE %}
{% if request|theme in 'dark auto-dark'|split:" " %}
<html lang="{{ LANGUAGE_CODE }}" data-bs-theme="dark">
{% else %}
<html lang="{{ LANGUAGE_CODE }}" data-bs-theme="light">
{% endif %}
<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    {% if meta %}
        <title>{{ meta.title }}</title>
        <meta name="description" content="{{ meta.description }}">
        <meta name="keywords" content="{{ meta.keywords }}">
        <meta property="og:title" content="{{ meta.title }}">
        <meta property="og:description" content="{{ meta.description }}">
        <meta property="og:image" content="{{ meta.image_url }}">
        <meta name="robots" content="{{ meta.robots }}">
    {% endif %}


    <!-- Bootstrap CSS -->
    {% load bootstrap5 %}

    {#{% bootstrap_css %}#}
    <link rel="stylesheet" href="{% static 'styles/bootstrap.min.css' %}">

    {#{% bootstrap_javascript %}#}
    <script src="{% static 'script/bootstrap.bundle.min.js' %}"></script>

    <!-- Custom styles -->
    {% load static %}
    {#<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css" />#}
    <link rel="stylesheet" href="{% static 'styles/bootstrap-icons.css' %}">

    {#<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">#}
    <link rel="stylesheet" href="{% static 'styles/all.min.css' %}">

    {#<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css"/>#}
    <link rel="stylesheet" href="{% static 'styles/swiper-bundle.min.css' %}">

    <link rel="stylesheet" href="{% static 'styles/flag-icons.min.css' %}">

    <link rel="stylesheet" href="{% static 'styles/quantumalert.css' %}" />

    <link rel="stylesheet" href="{% static 'styles/style.css' %}">
    {% if request|theme in 'dark auto-dark'|split:" " %}
    {#<link rel="stylesheet" href="{% sass_src 'styles/bootstrap_dark.scss' %}">#}
    <link rel="stylesheet" href="{% static 'styles/style_dark.css' %}">
    {% else %}
    <link rel="stylesheet" href="{% static 'styles/style_light.css' %}">
    {% endif %}

    {% block styles %}
    {% endblock %}

    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <!-- AOS CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css">

    <!-- JS includes -->
    {#<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>#}
    <script src="{% static 'script/swiper-bundle.min.js' %}"></script>

    {#<script src="https://kit.fontawesome.com/3502cd6637.js" crossorigin="anonymous"></script>#}

    <script src="{% static 'script/jquery-3.7.1.min.js' %}"></script>

    <script src="{% static 'script/chart.js' %}"></script>
    <script src="{% static 'script/luxon.min.js' %}"></script>
    <script src="{% static 'script/chartjs-utils.js' %}"></script>
    <script src="{% static 'script/chartjs-adapter-luxon.js' %}"></script>

    <script src="{% static 'script/quantumalert.js' %}"></script>

    <script src="{% static 'script/script.js' %}"></script>
    <script src="{% static 'script/script-modals.js' %}"></script>

    <script type="text/javascript">
    // Theme mode switcher django-javascript connector
    var current_theme_mode = 'auto';
    {% if request|theme %}
    current_theme_mode = '{{ request|theme }}';
    {% endif %}
    //alert('is: '+current_theme_mode);

    // Validity check django-javascript connector
    var validity_properties = new Array(
        {name: 'valueMissing', trans: '{% trans "Value is missing" %}'},
        {name: 'typeMismatch', trans: '{% trans "Type mismatch" %}'},
        {name: 'patternMismatch', trans: '{% trans "Pattern mismatch" %}'},
        {name: 'tooLong', trans: '{% trans "Value is too long" %}'},
        {name: 'tooShort', trans: '{% trans "Value is too short" %}'},
        {name: 'badInput', trans: '{% trans "Bad input" %}'},
        {name: 'rangeOverflow', trans: '{% trans "Range overflow" %}'},
        {name: 'rangeUnderflow', trans: '{% trans "Range underflow" %}'},
        {name: 'stepMismatch', trans: '{% trans "Step mismatch" %}'},
    );
    </script>

    {% block scripts %}
    {% endblock %}

    <title>{% block title %}Botie{% endblock %}</title>
    <link rel="shortcut icon" type="image/ico" href="{% static 'images/favicon.ico' %}"/>

    <!-- GOOGLE ANALITICS -->
     <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-DVYTQM4QF9"></script>
        <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-DVYTQM4QF9');
        </script>
</head>
<body>
    <div id="page-container">
        <div id="content-wrap">
                {% block navbar %}
                    {% include "menu.html" %}
                {% endblock navbar %}
            <main>
                {% block main-content %}
                <div class="main-content{% if request.path != '/' %} pt-3 pb-5{% endif %}">
                {% endblock main-content %}
                    {% if request.path == '/' %}
                    {% block carousel %}
                        {% include "front/home_page/top_slider.html" %}
                    {% endblock %}
                    {% endif %}

                    {% block messages %}
                    <div class="container{% if request.path == '/' %} pt-3 {% endif %}">
                        {% include "front/dashboard/base/messages.html" %}
                    </div>
                    {% endblock %}

                    {% block content %}
                    {% endblock %}

                </div>
            </main>

        </div>
        
        <footer class="footer">
            {% block footer %}
                {% include "footer.html" %}
            {% endblock footer %}
        </footer>
        
    </div>

    {% include 'front/home_page/cookie_modal.html' %}

    {% include "front/dashboard/base/modals.html" %}

    <!-- AOS JS -->
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

<!-- Inicjalizacja AOS -->
<script>
  AOS.init({
    duration: 600,
    easing: 'ease-in-out',
    once: true,
    mirror: false
  });
</script>

{% block extra_js %}
{% endblock %}

</body>
</html>