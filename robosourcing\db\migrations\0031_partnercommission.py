# Generated by Django 4.2.13 on 2024-11-24 18:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('db', '0030_affiliatelink'),
    ]

    operations = [
        migrations.CreateModel(
            name='PartnerCommission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commissions', to=settings.AUTH_USER_MODEL)),
                ('related_event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.creditevent')),
            ],
        ),
    ]
