# Generated by Django 4.2.13 on 2025-01-17 05:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0038_alter_affiliatewithdrawalrequest_unique_together'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='scenario',
            name='origin_id',
        ),
        migrations.AddField(
            model_name='scenario',
            name='branch_id',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='branch_nodes', to='db.scenario'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='previous_id',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='next', to='db.scenario'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='root_id',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='all_nodes', to='db.scenario'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='update_lock',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='scenario',
            name='update_lock_by',
            field=models.CharField(null=True),
        ),
    ]
