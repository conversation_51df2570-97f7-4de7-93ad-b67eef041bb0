# Generated by Django 4.2.13 on 2025-02-06 18:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('front', '0012_alter_carouselitem_title_en_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='carouselitem',
            name='background_image_desktop_dark',
            field=models.ImageField(blank=True, help_text='Desktop background image (dark mode)', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='background_image_mobile_dark',
            field=models.ImageField(blank=True, help_text='Mobile background image (dark mode)', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AddField(
            model_name='carouselitem',
            name='background_image_tablet_dark',
            field=models.ImageField(blank=True, help_text='Tablet background image (dark mode)', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='background_image_desktop',
            field=models.ImageField(blank=True, help_text='Desktop background image (light mode)', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='background_image_mobile',
            field=models.ImageField(blank=True, help_text='Mobile background image (light mode)', null=True, upload_to='carousel_backgrounds'),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='background_image_tablet',
            field=models.ImageField(blank=True, help_text='Tablet background image (light mode)', null=True, upload_to='carousel_backgrounds'),
        ),
    ]
