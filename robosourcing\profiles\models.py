from datetime import datetime
from django.utils import timezone
import uuid
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User


class Address(models.Model):
    street = models.CharField(max_length=50, blank=True, null=True)
    street_number = models.CharField(max_length=50, blank=True, null=True)  # Updated to CharField
    home_number = models.CharField(max_length=50, blank=True, null=True)  # Updated to CharField
    city = models.CharField(max_length=50, blank=True, null=True)
    postal_code = models.CharField(
        max_length=50, blank=True, null=True
    
    )
    country = models.CharField(max_length=50, blank=True, null=True)
    timezone = models.CharField(max_length=63, default='UTC')

    def __str__(self):
        return f"{self.street} {self.street_number}, {self.city}, {self.country}"

class Contact(models.Model):
    phone_number_validator = RegexValidator(regex=r'^\d{7,10}$', message="Phone number must be entered with 7 to 10 digits.")
    area_code = models.CharField(max_length=5, null=True, blank=True)
    phone_number = models.CharField(
        max_length=10,
        validators=[phone_number_validator],
        null=True,
        blank=True
    )

    def __str__(self):
        return f"{self.area_code} {self.phone_number}"
    
class OrganizationUUID(models.Model):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tax_id = models.CharField(max_length=25)
    name_1 = models.CharField(max_length=100, null=True, blank=True)
    name_2 = models.CharField(max_length=100, null=True, blank=True)
    street = models.CharField(max_length=100, null=True, blank=True)
    street_number = models.CharField(max_length=100, null=True, blank=True)  
    home_number = models.CharField(max_length=100, null=True, blank=True)  
    city = models.CharField(max_length=100, null=True, blank=True)
    postal_code = models.CharField(max_length=100, null=True, blank=True)
    country = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        ordering = ['tax_id']

    def __str__(self):
        return f"{self.name_1} ({self.tax_id})"

class Organization(models.Model):
    tax_id = models.CharField(primary_key=True, max_length=25)
    name_1 = models.CharField(max_length=100, null=True, blank=True)
    name_2 = models.CharField(max_length=100, null=True, blank=True)
    street = models.CharField(max_length=100, null=True, blank=True)
    street_number = models.CharField(max_length=100, null=True, blank=True)  
    home_number = models.CharField(max_length=100, null=True, blank=True)  
    city = models.CharField(max_length=100, null=True, blank=True)
    postal_code = models.CharField(max_length=100, null=True, blank=True)
    country = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        ordering = ['tax_id']

    def __str__(self):
        return self.name_1

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    address = models.ForeignKey('Address', on_delete=models.RESTRICT, null=True, blank=True)
    contact = models.ForeignKey('Contact', on_delete=models.RESTRICT, null=True, blank=True)
    organization_uuid = models.ForeignKey('OrganizationUUID', on_delete=models.CASCADE, null=True, blank=True)
    external_payer = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='external_payer', null=True, blank=True)
    referred_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='referred_users')

    class Meta:
        ordering = ['user']

    def has_role(self, role_name):
        return self.user.groups.filter(name=role_name).exists()
    
    def is_partner(self):
        return self.user.groups.filter(name='Partner').exists()

class Consent(models.Model):
    GENERAL = 'general'
    USER = 'user'
    MODERATOR = 'moderator'
    TYPE_CHOICES = [
        (GENERAL, 'Ogólne'),
        (USER, 'Użytkownik'),
        (MODERATOR, 'Moderator'),
    ]
    name = models.CharField(max_length=100)
    description = models.TextField()
    additional_description = models.TextField(null=True, blank=True)
    type = models.CharField(max_length=10, choices=TYPE_CHOICES)
    is_displayed = models.BooleanField(default=True, help_text="Czy ten element powinien być wyświetlany?")
    email_required = models.BooleanField(default=True, verbose_name="Wymagana zgoda na e-mail")
    app_required = models.BooleanField(default=True, verbose_name="Wymagana zgoda na aplikację")
    phone_sms_required = models.BooleanField(default=True, verbose_name="Wymagana zgoda na telefon/SMS")

    def __str__(self):
        return self.description

class UserConsent(models.Model):
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='user_consents')
    consent = models.ForeignKey(Consent, on_delete=models.CASCADE, related_name='user_consents')
    email_consent = models.BooleanField(default=False)
    app_consent = models.BooleanField(default=False)
    phone_sms_consent = models.BooleanField(default=False)

    def __str__(self):
        full_name = f'{self.user.first_name} {self.user.last_name}'.strip()
        if not full_name:  # Fallback to username if full_name is empty
            full_name = self.user.username
        return f"{full_name}'s consent for {self.consent.description}"


class UserConnectKey(models.Model):
    user_id         = models.OneToOneField(User, on_delete=models.CASCADE, editable=False, primary_key=True)
    connect_key     = models.UUIDField(default=uuid.uuid4, auto_created=True, unique=True)
    create_time     = models.DateTimeField()
    duration_time   = models.DurationField()

    def is_valid(self):
        valid_time = self.create_time + self.duration_time
        actual_time = datetime.now(tz=timezone.utc)
        if valid_time >= actual_time:
            print(f'connect_key is still vadid for: {valid_time-actual_time}')
            return True
        else:
            print(f'connect_key is invalid!')
            return False


class RobotResetKey(models.Model):
    rid             = models.ForeignKey('db.Robot', on_delete=models.CASCADE, null=True)
    reset_key       = models.UUIDField(default=uuid.uuid4, auto_created=True)
    create_time     = models.DateTimeField()
    duration_time   = models.DurationField()

    def is_valid(self):
        valid_time = self.create_time + self.duration_time
        actual_time = datetime.now(tz=timezone.utc)
        if valid_time >= actual_time:
            print(f'reset_key is still vadid for: {valid_time-actual_time}')
            return True
        else:
            print(f'reset_key is invalid!')
            return False

