"""
Management command do tworzenia przykładowych danych dla systemu newslettera
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta

from front.models import (NewsletterSubscription, NewsletterSegment, NewsletterTemplate, 
                         NewsletterCampaign, ExternalMailingService)


class Command(BaseCommand):
    help = 'Tworzy przykładowe dane dla systemu newslettera'

    def handle(self, *args, **options):
        self.stdout.write('Tworzenie przykładowych danych dla newslettera...')
        
        # Utwórz przykładowe subskrypcje
        self.create_sample_subscriptions()
        
        # Utwórz segmenty
        self.create_sample_segments()
        
        # Utwórz szablony
        self.create_sample_templates()
        
        # Utwórz kampanie
        self.create_sample_campaigns()
        
        # Utwórz zewnętrzne serwisy
        self.create_external_services()
        
        self.stdout.write(self.style.SUCCESS('Przykładowe dane zostały utworzone!'))

    def create_sample_subscriptions(self):
        """Tworzy przykładowe subskrypcje"""
        emails = [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for i, email in enumerate(emails):
            if not NewsletterSubscription.objects.filter(email=email).exists():
                subscription = NewsletterSubscription.objects.create(
                    email=email,
                    marketing_consent=True,
                    data_processing_consent=True,
                    is_confirmed=(i >= 3),  # Ostatnie 2 potwierdzone
                    created_at=timezone.now() - timedelta(days=i*10)
                )
                
                if subscription.is_confirmed:
                    subscription.confirmed_at = subscription.created_at + timedelta(hours=1)
                    subscription.save()
                
                self.stdout.write(f'Utworzono subskrypcję: {email}')

    def create_sample_segments(self):
        """Tworzy przykładowe segmenty"""
        segments = [
            {
                'name': 'Wszyscy potwierdzone',
                'description': 'Wszystkie potwierdzone subskrypcje',
                'confirmed_only': True,
            },
            {
                'name': 'Nowi subskrybenci',
                'description': 'Subskrybenci z ostatnich 30 dni',
                'confirmed_only': True,
                'registration_date_from': timezone.now() - timedelta(days=30),
            },
            {
                'name': 'Wszyscy subskrybenci',
                'description': 'Wszystkie subskrypcje (potwierdzone i niepotwierdzone)',
                'confirmed_only': False,
            }
        ]
        
        for segment_data in segments:
            segment, created = NewsletterSegment.objects.get_or_create(
                name=segment_data['name'],
                defaults=segment_data
            )
            if created:
                self.stdout.write(f'Utworzono segment: {segment.name}')

    def create_sample_templates(self):
        """Tworzy przykładowe szablony"""
        templates = [
            {
                'name': 'Szablon powitalny',
                'subject': 'Witaj w społeczności Botie!',
                'content_html': '''
                <h2>🎉 Witamy w społeczności Botie!</h2>
                <p>Dziękujemy za dołączenie do naszej społeczności automatyzacji. Jesteś teraz częścią grupy, która odkrywa przyszłość pracy!</p>

                <h3>Co możesz zrobić już teraz:</h3>
                <ul>
                    <li><strong>🔍 Przeglądaj scenariusze:</strong> <a href="{{ site_url }}/botbook/">Odkryj setki gotowych automatyzacji</a></li>
                    <li><strong>⬇️ Pobierz aplikację:</strong> <a href="{{ site_url }}/download/">Zainstaluj Botie na swoim komputerze</a></li>
                    <li><strong>💰 Sprawdź cennik:</strong> <a href="{{ site_url }}/pricelist/">Zobacz nasze przystępne plany</a></li>
                </ul>

                <p>Masz pytania? Napisz do nas na <a href="mailto:<EMAIL>"><EMAIL></a></p>
                ''',

            },
            {
                'name': 'Miesięczny newsletter',
                'subject': 'Botie Newsletter - {{ current_date|date:"F Y" }}',
                'content_html': '''
                <h2>Miesięczny przegląd Botie</h2>
                <p>Cześć {{ subscriber.email }}!</p>
                <p>Oto najważniejsze nowości z tego miesiąca:</p>
                <ul>
                    <li>Nowe scenariusze automatyzacji</li>
                    <li>Ulepszenia w aplikacji</li>
                    <li>Porady od ekspertów</li>
                </ul>
                <p>Data: {{ current_date }}</p>
                <p><a href="{{ unsubscribe_url }}">Wypisz się</a></p>
                ''',
                'content_text': '''
                Miesięczny przegląd Botie
                
                Cześć {{ subscriber.email }}!
                
                Oto najważniejsze nowości z tego miesiąca:
                - Nowe scenariusze automatyzacji
                - Ulepszenia w aplikacji  
                - Porady od ekspertów
                
                Data: {{ current_date }}
                Wypisz się: {{ unsubscribe_url }}
                '''
            }
        ]
        
        for template_data in templates:
            template, created = NewsletterTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f'Utworzono szablon: {template.name}')

    def create_sample_campaigns(self):
        """Tworzy przykładowe kampanie"""
        try:
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.create_superuser(
                    'admin', '<EMAIL>', 'admin123'
                )
            
            template = NewsletterTemplate.objects.first()
            segment = NewsletterSegment.objects.first()
            
            if template and segment:
                campaign, created = NewsletterCampaign.objects.get_or_create(
                    name='Kampania testowa',
                    defaults={
                        'template': template,
                        'segment': segment,
                        'created_by': admin_user,
                        'status': 'draft'
                    }
                )
                if created:
                    self.stdout.write(f'Utworzono kampanię: {campaign.name}')
        except Exception as e:
            self.stdout.write(f'Błąd tworzenia kampanii: {e}')

    def create_external_services(self):
        """Tworzy przykładowe zewnętrzne serwisy"""
        services = [
            {
                'name': 'Mailchimp Demo',
                'service_type': 'mailchimp',
                'api_key': 'demo-key-123',
                'daily_limit': 1000,
                'monthly_limit': 30000,
            },
            {
                'name': 'SendGrid Demo', 
                'service_type': 'sendgrid',
                'api_key': 'demo-sendgrid-key',
                'daily_limit': 500,
                'monthly_limit': 15000,
            }
        ]
        
        for service_data in services:
            service, created = ExternalMailingService.objects.get_or_create(
                name=service_data['name'],
                defaults=service_data
            )
            if created:
                self.stdout.write(f'Utworzono serwis: {service.name}')
