{% load i18n %}
{% load static %}

<!-- <div class="w-100 baner login-baner mb-5">
    
    <h2 class="baner-header">R<PERSON><PERSON> co lubisz, reszt<PERSON> zostaw robotom</h2>
</div> -->

<div class="container">
    <div class="row _text-light">
        <div class="col-12 col-lg-3 pb-3">
            <h5>{% trans "Quick contact" %}</h5>
            <ul class="nav flex-column">
                <li class="nav-item mb-2 p-0">{% trans "Support" %}/{% trans "Sales" %}:</li>
                <li class="nav-item mb-2 text-muted p-0">tel. +48 781-780-222</li>
                <li class="nav-item mb-2 text-muted p-0"><EMAIL></li>
                <!--<li class="nav-item mb-2 p-0">{% trans "Sales" %}:</li>
                <li class="nav-item mb-2 text-muted p-0">tel. +48 781-780-222</li>
                <li class="nav-item mb-2 text-muted p-0"><EMAIL></li>-->
            </ul>
        </div>

        <div class="col-12 col-lg-3 pb-3">
            <h5>{% trans "Shortcuts" %}</h5>
            <ul class="nav flex-column">
                <li class="nav-item mb-2"><a href="{% url 'download' %}" class="nav-link p-0 text-muted">{% trans "Download Botie" %}</a></li>
                <li class="nav-item mb-2"><a href="{% url 'pricelist' %}" class="nav-link p-0 text-muted">{% trans "Price list" %}</a></li>
                <li class="nav-item mb-2"><a href="{% url 'how_it_works' %}" class="nav-link p-0 text-muted">{% trans "How It Works" %}</a></li>
                <li class="nav-item mb-2"><a href="{% url 'store_regulations' %}" class="nav-link p-0 text-muted">{% trans "Store Regulations" %}</a></li>
                <li class="nav-item mb-2"><a href="{% url 'privacy_policy' %}" class="nav-link p-0 text-muted">{% trans "Privacy Policy" %}</a></li>
                <li class="nav-item mb-2"><a href="{% url 'contact' %}" class="nav-link p-0 text-muted">{% trans "Contact" %}</a></li>
            </ul>
        </div>

        <div class="col-12 col-lg-6">
            <div>
                <h5>{% trans "Sign up for the newsletter" %}</h5>
                <p class="text-muted">{% trans "Stay up to date with the world of robotization and receive a handful of the most important information every month" %}</p>
                <div class="d-flex w-100 gap-2">
                    <label for="newsletter1" class="visually-hidden">{% trans "E-mail address" %}</label>
                    <input id="newsletter1" type="email" class="form-control" placeholder="{% trans 'E-mail address' %}">
                    <button class="btn btn-primary" type="button" onclick="showNewsletterModal()">{% trans "Sign up" %}</button>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-between py-4 my-4 border-top">
        <p class="_text-light">
            © <span id="current-year"></span> 
            <a class="_link-light" href="https://www.robosourcing.pl">Robosourcing</a>. {% trans 'All rights reserved.' %}
        </p>
        <ul class="list-unstyled d-flex">
            <li class="ms-3"><i class="fa-brands fa-facebook" style="color: white;"></i></li>
            <li class="ms-3"><i class="fa-brands fa-twitter" style="color: white;"></i></li>
            <li class="ms-3"><i class="fa-brands fa-linkedin" style="color: white;"></i></li>
        </ul>
    </div>
</div>

<!-- Newsletter Modal -->
<div class="modal fade" id="newsletterModal" tabindex="-1" aria-labelledby="newsletterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newsletterModalLabel">{% trans "Sign up for the newsletter" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newsletterForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="modalEmail" class="form-label">{% trans "E-mail address" %}</label>
                        <input type="email" class="form-control" id="modalEmail" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="modalCaptcha" class="form-label">{% trans "Security question" %}: 5 + 3 = ?</label>
                        <input type="text" class="form-control" id="modalCaptcha" name="captcha" placeholder="{% trans 'Enter the result' %}" required>
                    </div>

                    <div class="mb-3">
                        <h6>{% trans "Required Consents" %}</h6>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="dataProcessingConsent" name="data_processing_consent" required>
                            <label class="form-check-label" for="dataProcessingConsent">
                                {% trans "I consent to the processing of my personal data" %} <span class="text-danger">*</span>
                            </label>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="marketingConsent" name="marketing_consent" required>
                            <label class="form-check-label" for="marketingConsent">
                                {% trans "I consent to receiving marketing information" %} <span class="text-danger">*</span>
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">
                            <strong>{% trans "Data Processing Information" %}:</strong><br>
                            {% trans "Your email address will be used solely for sending newsletter content. You can unsubscribe at any time." %}
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" onclick="submitNewsletter()">{% trans "Subscribe" %}</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('current-year').innerText = new Date().getFullYear();

    function showNewsletterModal() {
        const email = document.getElementById('newsletter1').value;
        if (email) {
            document.getElementById('modalEmail').value = email;
        }
        const modal = new bootstrap.Modal(document.getElementById('newsletterModal'));
        modal.show();
    }

    function submitNewsletter() {
        const form = document.getElementById('newsletterForm');
        const formData = new FormData(form);

        // Sprawdź czy wszystkie pola są wypełnione
        const email = formData.get('email');
        const captcha = formData.get('captcha');
        const dataConsent = formData.get('data_processing_consent');
        const marketingConsent = formData.get('marketing_consent');

        if (!email || !captcha || !dataConsent || !marketingConsent) {
            alert('{% trans "Please fill in all required fields and accept the consents." %}');
            return;
        }

        if (captcha !== '8') {
            alert('{% trans "Incorrect answer to the security question." %}');
            return;
        }

        // Wyślij dane
        fetch('{% url "newsletter_subscribe" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert('{% trans "Thank you for subscribing to our newsletter! Please check your email and click the confirmation link." %}');
                const modal = bootstrap.Modal.getInstance(document.getElementById('newsletterModal'));
                modal.hide();
                document.getElementById('newsletterForm').reset();
                document.getElementById('newsletter1').value = '';
            } else {
                alert(data.message || '{% trans "An error occurred. Please try again." %}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "An error occurred. Please try again." %}');
        });
    }
</script>