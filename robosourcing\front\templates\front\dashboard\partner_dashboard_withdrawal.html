<!-- front/templates/front/dashboard/partner_dashboard_withdrawal.html -->

{% extends "front/dashboard/base.html" %}

{% load static %}
{% load i18n %}

{% block title %}{{ block.super }} - {% trans "Withdrawal Cycles" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <div class="row">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
              <li class="breadcrumb-item site-name"><a href="{% url 'partner_dashboard_menu' %}">{% trans "Partner Panel" %}</a></li>
              <li class="breadcrumb-item site-name active" aria-current="page">{% trans "Withdrawal Cycles" %}</li>
            </ol>
        </nav>

        <!-- Formularz filtrów -->
        <div class="col-md-3 col-lg-2 mb-4">
            <div class="dashboard-element">
                <h2 class="dashboard-header">{% trans "Filters" %}</h2>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <form method="get" action="{% url 'partner_dashboard_withdrawal' %}">
                        <!-- Pola filtrów -->
                        <div class="mb-3">
                            <label for="start_date" class="form-label">{% trans "Start date" %}:</label>
                            <input type="date" name="start_date" id="start_date" value="{{ start_date }}" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label for="end_date" class="form-label">{% trans "End date" %}:</label>
                            <input type="date" name="end_date" id="end_date" value="{{ end_date }}" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">{% trans "Withdrawal Status" %}:</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">{% trans "All" %}</option>
                                {% for status_value, status_name in status_choices %}
                                    <option value="{{ status_value }}" {% if withdrawal_status == status_value|stringformat:"s" %} selected {% endif %}>{{ status_name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="min_amount" class="form-label">{% trans "Min Amount" %}:</label>
                            <input type="number" step="0.01" name="min_amount" id="min_amount" value="{{ min_amount }}" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label for="max_amount" class="form-label">{% trans "Max Amount" %}:</label>
                            <input type="number" step="0.01" name="max_amount" id="max_amount" value="{{ max_amount }}" class="form-control">
                        </div>

                        <!-- Wybór liczby rekordów na stronie -->
                        <div class="mb-3">
                            <label for="records_per_page" class="form-label">{% trans "Show" %}</label>
                            <select name="records_per_page" id="records_per_page" class="form-select">
                                <option value="5" {% if records_per_page == 5 %} selected {% endif %}>5</option>
                                <option value="10" {% if records_per_page == 10 %} selected {% endif %}>10</option>
                                <option value="20" {% if records_per_page == 20 %} selected {% endif %}>20</option>
                                <option value="50" {% if records_per_page == 50 %} selected {% endif %}>50</option>
                                <option value="100" {% if records_per_page == 100 %} selected {% endif %}>100</option>
                            </select>
                            {% trans "withdrawals per page" %}
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" type="submit">{% trans "Apply" %}</button>
                            <!-- Przycisk do czyszczenia filtrów -->
                            <button class="btn btn-secondary" type="button" onclick="clearFilters();">{% trans "Clear filters" %}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Tabela z cyklami wypłat -->
        <div class="col-md-9 col-lg-10">
            <div class="dashboard-element">

                <div class="table-responsive">
                    <table class="table table-striped my-custom-table">
                        <thead>
                            <tr>
                                <th>
                                    <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=start_date&direction={% if order_by == 'start_date' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Cycle Start" %}
                                    </a>
                                </th>
                                <th class="d-none d-md-table-cell">
                                    <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=end_date&direction={% if order_by == 'end_date' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Cycle End" %}
                                    </a>
                                </th>
                                <th>
                                    <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=total_commission&direction={% if order_by == 'total_commission' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Total Commission in Cycle (PLN)" %}
                                    </a>
                                </th>
                                <th>
                                    <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=withdrawal_status&direction={% if order_by == 'withdrawal_status' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Withdrawal Status" %}
                                    </a>
                                </th>
                                <th class="d-none d-md-table-cell">
                                    <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=withdrawal_amount&direction={% if order_by == 'withdrawal_amount' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Withdrawal Amount (PLN)" %}
                                    </a>
                                </th>
                                <th class="d-none d-md-table-cell">
                                    <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=processed_at&direction={% if order_by == 'processed_at' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Processed Date" %}
                                    </a>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cycle in page_obj %}
                            <tr>
                                <td>{{ cycle.start_date|date:"Y-m-d H:i" }}</td>
                                <td class="d-none d-md-table-cell">
                                    {% if cycle.end_date %}
                                        {{ cycle.end_date|date:"Y-m-d H:i" }}
                                    {% else %}
                                        {% trans "Ongoing" %}
                                    {% endif %}
                                </td>
                                <td>{{ cycle.total_commission|floatformat:2 }}</td>
                                <td>
                                    {% if cycle.withdrawals.first %}
                                        {{ cycle.withdrawals.first.get_status_display }}
                                    {% else %}
                                        {% trans "N/A" %}
                                    {% endif %}
                                </td>
                                <td class="d-none d-md-table-cell">
                                    {% if cycle.withdrawals.first %}
                                        {{ cycle.withdrawals.first.amount|floatformat:2 }}
                                    {% else %}
                                        {{ 0.0 }}
                                    {% endif %}
                                </td>
                                <td class="d-none d-md-table-cell">
                                    {% if cycle.withdrawals.first.processed_at %}
                                        {{ cycle.withdrawals.first.processed_at|date:"Y-m-d H:i" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6">{% trans "No withdrawal cycles available." %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginacja -->
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">
                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}.
                    </span>
                    <nav>
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page=1" aria-label="{% trans 'First' %}">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" aria-label="{% trans 'Previous' %}">
                                        <span aria-hidden="true">&lsaquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item"><a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a></li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" aria-label="{% trans 'Next' %}">
                                        <span aria-hidden="true">&rsaquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}" aria-label="{% trans 'Last' %}">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearFilters() {
    const url = new URL(window.location.href);
    url.searchParams.delete('affiliate_link');
    url.searchParams.delete('start_date');
    url.searchParams.delete('end_date');
    url.searchParams.delete('min_commission_rate');
    url.searchParams.delete('max_commission_rate');
    url.searchParams.delete('min_amount');
    url.searchParams.delete('max_amount');
    url.searchParams.delete('related_event');
    url.searchParams.delete('related_order');
    url.searchParams.delete('records_per_page');
    url.searchParams.delete('page');
    url.searchParams.delete('order_by');
    url.searchParams.delete('direction');
    window.location.href = url.toString();
}
</script>
</div>
{% endblock %}
