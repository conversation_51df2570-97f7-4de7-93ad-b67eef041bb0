import json
from datetime import datetime, timedelta
import requests
from dateutil.relativedelta import relativedelta
import pytz
import logging
from django.contrib.auth.models import User
from django.urls import reverse

from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.views.decorators.http import require_POST
from django.utils.translation import gettext_lazy as _
# from django.core.paginator import Paginator
from django.utils.dateparse import parse_date
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.db import transaction, models
from django.db.models import Q, Case, Value, When, BooleanField, Sum, Count
# from django.utils.timezone import make_aware
from django.core.exceptions import ObjectDoesNotExist
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from django.utils import timezone
from django.conf import settings
from django.db.models import Sum
from decimal import Decimal
from db.models import TagGroup, Category

from db.models import Robot, Credit, Scenario, Tag, ScenarioTag, Scenario_Event, Scenario_Event_Message, UserStorage, FavoriteScenario, \
    CreditEvent, AffiliateLink, PartnerCommission, AffiliateWithdrawalRequest, WithdrawalCycle
from front.models import NewsletterSubscription, NewsletterSegment, NewsletterTemplate, NewsletterCampaign, NewsletterDelivery, NewsletterOpen, NewsletterClick
from front.forms import NewsletterCampaignForm, NewsletterTemplateForm, NewsletterSegmentForm
from store.models import SubscriptionEvent, Order
from .forms import UserNameForm, AddressForm, PhoneNumberForm, OrganizationDetailsForm, UserConsentForm, ChangeScenarioStatusForm, ScenarioTagForm
from .models import UserProfile, Address, Contact, OrganizationUUID, Consent, UserConsent
from .utils import get_available_balance
from api.forms import CreateRobotForm
from .decorators import group_required
from db.mailer import Mailer

from seo.forms import ScenarioMetaForm
from seo.models import ScenarioMeta

from django.utils.text import slugify

API_URL_NAME = 'register_scenario_event'

@login_required(login_url='home')
def profile_robots(request):
    user = request.user
    roboty = Robot.objects.filter(owner=user).order_by('name')

    user_credits, created = Credit.objects.get_or_create(user=user)

    # Dummy credit usage data for each robot
    credit_usage_data = {
        'day': [10, 17, 23, 84, 50, 50, 95],
        'week': [100, 100, 150, 200, 500, 600, 400],
        'month': [5000, 2000, 4000, 3000, 5000, 5000, 7000],
    }
    
    roboty_data = [
        {
            'id': str(robot.rid),  # Ensure the ID is a string for JSON
            'name': robot.name,
            'credit_usage': credit_usage_data,
        }
        for robot in roboty
    ]

    robot_credits = {str(robot.rid): robot.cached_credits for robot in roboty}

    return render(request, "front/dashboard/roboty.html", {
        "roboty": roboty,
        'user_credits': user_credits,
        'roboty_data': json.dumps(roboty_data),
        'robot_credits': json.dumps(robot_credits),  # Convert to JSON
    })


LOGGER = logging.getLogger('django')

@login_required
def block_credits(request):
    if request.method == "POST":
        robot_id = request.POST.get('robot')
        credits = int(request.POST.get('credits'))
        action = request.POST.get('action')  # Pobieranie wybranej akcji (block/unblock)

        if robot_id:
            try:
                robot = Robot.objects.get(rid=robot_id)
                LOGGER.info(f"Robot ID: {robot_id}")

                if action == 'block':
                    success, cached_credits = robot.block_cached_credits(credits)  # Updated to block_cached_credits
                    action = 'blocked'
                elif action == 'unblock':
                    success, cached_credits = robot.unblock_cached_credits(credits)  # Updated to unblock_cached_credits
                    action = 'unblocked'
                else:
                    success = False

                if success:
                    messages.success(request, f'Credits successfully {action}.')
                else:
                    messages.error(request, 'Failed to block/unblock credits.')
                
                return redirect('roboty')
            except Robot.DoesNotExist:
                LOGGER.error(f'Robot with ID {robot_id} does not exist.')
                messages.error(request, 'Robot not found.')
                return redirect('roboty')
            except Exception as e:
                LOGGER.error(f'An error occurred: {str(e)}')
                messages.error(request, str(e))
                return redirect('roboty')

    messages.error(request, 'Invalid request.')
    return redirect('roboty')

@login_required(login_url='home')
def profile_kredyty(request):
    def append_mid_data(num, data):
        # append same values
        val = 0
        if len(data['balance']):
            val = data['balance'][-1]
        data['balance'] += [val] * (num - len(data['balance']))
        # append zeros
        data['buy_sub'].extend([0] * (num - len(data['buy_sub'])))
        data['chg_sub'].extend([0] * (num - len(data['chg_sub'])))
        data['buy_pkg'].extend([0] * (num - len(data['buy_pkg'])))
        data['top_up'].extend([0] * (num - len(data['top_up'])))
        data['fre_pkg'].extend([0] * (num - len(data['fre_pkg'])))
        data['fre_sub'].extend([0] * (num - len(data['fre_sub'])))
        return data

    def append_day_data(num, data):
        val = 0
        # insert or replace balance
        if num and len(data['balance']) == num:
            data['balance'].pop()
        data['balance'].append(event.credits)
        # insert or add credits
        if event.event_type == 'BUY':
            if event.d_package_credits:
                if num and len(data['buy_pkg']) == num:
                    val = data['buy_pkg'].pop()
                data['buy_pkg'].append(val + event.d_package_credits)
            else:
                if num and len(data['buy_sub']) == num:
                    val = data['buy_sub'].pop()
                data['buy_sub'].append(val + event.d_subscription_credits)
        elif event.event_type == 'CHG':
            if num and len(data['chg_sub']) == num:
                val = data['chg_sub'].pop()
            data['chg_sub'].append(val + event.d_subscription_credits)
        elif event.event_type == 'TOP':
            if num and len(data['top_up']) == num:
                val = data['top_up'].pop()
            data['top_up'].append(val + event.d_subscription_credits)
        elif event.event_type == 'FRE':
            if event.d_package_credits:
                if num and len(data['fre_pkg']) == num:
                    val = data['fre_pkg'].pop()
                data['fre_pkg'].append(val + event.d_package_credits)
            else:
                if num and len(data['fre_sub']) == num:
                    val = data['fre_sub'].pop()
                data['fre_sub'].append(val + event.d_subscription_credits)
        return data

    if 'chart_period' in request.POST:
        chart_period = request.POST['chart_period']
    else:
        chart_period = 'month'

    # get user time zone
    if request.user.userprofile.address and request.user.userprofile.address.timezone:
        user_tz = pytz.timezone(request.user.userprofile.address.timezone)
    else:
        user_tz = pytz.timezone(settings.TIME_ZONE)

    # get chart start date and number of days to show
    date_time = timezone.localtime(timezone.now(), timezone=user_tz)
    # print(date_time)
    now = date_time.date()
    # print(now)
    if chart_period == 'week':
        date_from = now - relativedelta(weeks=1)
    elif chart_period == 'month':
        date_from = now - relativedelta(months=1)
    elif chart_period == 'quarter':
        date_from = now - relativedelta(months=3)
    elif chart_period == 'half':
        date_from = now - relativedelta(months=6)
    else:
        date_from = now - relativedelta(years=1)
    diff = now - date_from
    days = diff.days
    # print('chart')
    # print(now)
    # print(date_from)
    # print(days)
    # prepare data structure for chart
    chart_data = {'balance': [], 'buy_sub': [], 'chg_sub': [], 'buy_pkg': [],
                  'top_up': [], 'fre_pkg': [], 'fre_sub': []}
    # get balance before chart period started
    # print(date_from)
    date_time_from = datetime.combine(date_from, datetime.min.time())
    # print(date_time_from)
    localized_date_from = user_tz.localize(date_time_from)
    # print("Localized datetime (UTC):", localized_date_from)
    events = CreditEvent.objects.filter(user=request.user, event_time__lt=localized_date_from).order_by('-event_time')
    if events:
        chart_data['balance'].append(events[0].credits)
    # get values day by day
    events = CreditEvent.objects.filter(user=request.user, event_time__gte=localized_date_from).order_by('event_time')
    # for index, event in enumerate(events):
    for event in events:
        event_time = timezone.localtime(event.event_time, timezone=user_tz).date()
        diff = event_time - date_from
        # print('data')
        # print(event_time)
        # print(date_from)
        # print(diff.days)
        # append initial days data
        chart_data = append_mid_data(diff.days - 1, chart_data)
        # if diff.days == 0:
        #     chart_data['balance'] = []
        # elif not index:
        #     # append initial days data
        #     chart_data = append_mid_data(diff.days, chart_data)
        chart_data = append_day_data(diff.days, chart_data)
    # append remaining days data
    chart_data = append_mid_data(days, chart_data)

    # get credits
    credit = Credit.objects.filter(user=request.user)

    # get sum of robots credits
    robot_credit = {'cached_credits': 0, 'l_cached_credits': 0}
    for robot in Robot.objects.filter(owner=request.user):
        robot_credit['cached_credits'] += robot.cached_credits
        robot_credit['l_cached_credits'] += robot.l_cached_credits

    context = {
        "credit": credit.first() if credit else None,
        "robot_credit": robot_credit,
        'chart_period': chart_period,
        'days': days,
        'chart_data': chart_data,
    }
    if not credit:
        messages.error(request, _('You don\'t have any credits yet!'))
    return render(request, "front/dashboard/kredyty.html", context)


@login_required(login_url='home')
def robot_scenarios_view(request):
    user = request.user
    user_robots = Robot.objects.filter(owner=user)

    context = {
        'user_robots': user_robots,
    }
    
    return render(request, 'front/dashboard/robot_scenarios_view.html', context)


def filter_scenarios(request, robot_id=None, is_private=False):
    favorite_scenarios_ids = FavoriteScenario.objects.filter(
        user=request.user
    ).values_list('scenario_id', flat=True)
    
    if robot_id:
        user_storages = UserStorage.objects.filter(
            user=request.user,
            rid_id=robot_id
        )
        unique_scenario_sids = set(user_storages.values_list('sid', flat=True))
        scenarios_query = Scenario.objects.filter(sid__in=unique_scenario_sids)
    else:
        if is_private:
            # Get robots connected to the user
            user_robots = Robot.objects.filter(userstorage__user=request.user).distinct()
            scenarios_query = Scenario.objects.filter(
                Q(userstorage__rid__in=user_robots)
            ).distinct()
        else:
            scenarios_query = Scenario.objects.filter(
                status=Scenario.ScenarioStatus.ACCEPTED.value,
                available=Scenario.AvailabilityStatus.PUBLIC.value
            )

    # Apply filtering
    name_query = request.GET.get('name', '')
    if name_query:
        scenarios_query = scenarios_query.filter(name__icontains=name_query)

    author_query = request.GET.get('author', '')
    if author_query:
        scenarios_query = scenarios_query.filter(author__icontains(author_query))

    start_date_query = request.GET.get('start_date', '')
    if start_date_query:
        scenarios_query = scenarios_query.filter(created_at__gte=start_date_query)

    end_date_query = request.GET.get('end_date', '')
    if end_date_query:
        scenarios_query = scenarios_query.filter(created_at__lte(end_date_query))

    tags_query = request.GET.get('tags', '')
    if tags_query:
        tag_list = [tag.strip() for tag in tags_query.split(',')]
        scenarios_query = scenarios_query.filter(scenariotag__tag__name__in(tag_list)).distinct()

    min_cost_query = request.GET.get('min_price', '')
    if min_cost_query.isdigit():
        scenarios_query = scenarios_query.filter(price__gte=int(min_cost_query))

    max_cost_query = request.GET.get('max_price', '')
    if max_cost_query.isdigit():
        scenarios_query = scenarios_query.filter(price__lte=int(max_cost_query))

    sort_column = request.GET.get('sort_column', '')
    sort_order = request.GET.get('sort_order', 'asc')
    if sort_column:
        if sort_order == 'desc':
            sort_column = f'-{sort_column}'
        scenarios_query = scenarios_query.order_by(sort_column)

    scenarios_query = scenarios_query.annotate(
        is_favorited=Case(
            When(sid__in=favorite_scenarios_ids, then=Value(True)),
            default=Value(False),
            output_field=BooleanField()
        )
    ).values('sid', 'name', 'author', 'created_at', 'price', 'available', 'is_favorited', 'scenario_version')

    return scenarios_query


@login_required
def ajax_public_scenarios(request):
    scenarios_query = filter_scenarios(request)
    page_number = request.GET.get('page', 1)
    records_per_page = request.GET.get('records_per_page', 20)

    paginator = Paginator(scenarios_query, records_per_page)
    try:
        scenarios_page = paginator.page(page_number)
    except PageNotAnInteger:
        scenarios_page = paginator.page(1)
    except EmptyPage:
        scenarios_page = paginator.page(paginator.num_pages)

    scenarios_with_robots = []
    for scenario in scenarios_page.object_list:
        assigned_robots = UserStorage.objects.filter(sid=scenario['sid']).values_list('rid_id', flat=True)
        scenario_dict = {
            'sid': scenario['sid'],
            'name': scenario['name'],
            'author': scenario['author'],
            'created_at': scenario['created_at'],
            'price': scenario['price'],
            'scenario_version': scenario['scenario_version'],
            'available': scenario['available'],
            'is_favorited': scenario['is_favorited'],
            'assigned_robots': list(assigned_robots),
        }
        scenarios_with_robots.append(scenario_dict)

    return JsonResponse({
        'scenarios': scenarios_with_robots,
        'num_pages': paginator.num_pages,
        'current_page': scenarios_page.number
    }, safe=False)


@login_required
def ajax_favorite_scenarios(request):
    scenarios_query = filter_scenarios(request).filter(is_favorited=True)
    page_number = request.GET.get('page', 1)
    records_per_page = request.GET.get('records_per_page', 20)

    paginator = Paginator(scenarios_query, records_per_page)
    try:
        scenarios_page = paginator.page(page_number)
    except PageNotAnInteger:
        scenarios_page = paginator.page(1)
    except EmptyPage:
        scenarios_page = paginator.page(paginator.num_pages)

    scenarios_with_robots = []
    for scenario in scenarios_page.object_list:
        assigned_robots = UserStorage.objects.filter(sid=scenario['sid']).values_list('rid_id', flat=True)
        scenario_dict = {
            'sid': scenario['sid'],
            'name': scenario['name'],
            'author': scenario['author'],
            'created_at': scenario['created_at'],
            'price': scenario['price'],
            'scenario_version': scenario['scenario_version'],
            'available': scenario['available'],
            'is_favorited': scenario['is_favorited'],
            'assigned_robots': list(assigned_robots),
        }
        scenarios_with_robots.append(scenario_dict)

    return JsonResponse({
        'scenarios': scenarios_with_robots,
        'num_pages': paginator.num_pages,
        'current_page': scenarios_page.number
    }, safe=False)


@login_required
def ajax_robot_scenarios(request, robot_rid):
    scenarios_query = filter_scenarios(request, robot_id=robot_rid)
    page_number = request.GET.get('page', 1)
    records_per_page = request.GET.get('records_per_page', 20)

    paginator = Paginator(scenarios_query, records_per_page)
    try:
        scenarios_page = paginator.page(page_number)
    except PageNotAnInteger:
        scenarios_page = paginator.page(1)
    except EmptyPage:
        scenarios_page = paginator.page(paginator.num_pages)

    scenarios_with_robots = []
    for scenario in scenarios_page.object_list:
        assigned_robots = UserStorage.objects.filter(sid=scenario['sid']).values_list('rid_id', flat=True)
        scenario_dict = {
            'sid': scenario['sid'],
            'name': scenario['name'],
            'author': scenario['author'],
            'created_at': scenario['created_at'],
            'price': scenario['price'],
            'scenario_version': scenario['scenario_version'],
            'available': scenario['available'],
            'is_favorited': scenario['is_favorited'],
            'assigned_robots': list(assigned_robots),
        }
        scenarios_with_robots.append(scenario_dict)

    return JsonResponse({
        'scenarios': scenarios_with_robots,
        'num_pages': paginator.num_pages,
        'current_page': scenarios_page.number
    }, safe=False)


@login_required
def ajax_private_scenarios(request):
    scenarios_query = filter_scenarios(request, is_private=True)
    page_number = request.GET.get('page', 1)
    records_per_page = request.GET.get('records_per_page', 20)

    paginator = Paginator(scenarios_query, records_per_page)
    try:
        scenarios_page = paginator.page(page_number)
    except PageNotAnInteger:
        scenarios_page = paginator.page(1)
    except EmptyPage:
        scenarios_page = paginator.page(paginator.num_pages)

    scenarios_with_robots = []
    for scenario in scenarios_page.object_list:
        assigned_robots = UserStorage.objects.filter(sid=scenario['sid']).values_list('rid_id', flat=True)
        scenario_dict = {
            'sid': scenario['sid'],
            'name': scenario['name'],
            'author': scenario['author'],
            'created_at': scenario['created_at'],
            'price': scenario['price'],
            'available': scenario['available'],
            'is_favorited': scenario['is_favorited'],
            'assigned_robots': list(assigned_robots),
        }
        scenarios_with_robots.append(scenario_dict)

    return JsonResponse({
        'scenarios': scenarios_with_robots,
        'num_pages': paginator.num_pages,
        'current_page': scenarios_page.number
    }, safe=False)


@login_required
@require_POST
def add_scenario_to_favorites(request):
    scenario_id = request.POST.get('scenario_id')
    try:
        scenario = Scenario.objects.get(sid=scenario_id, status=Scenario.ScenarioStatus.ACCEPTED)
        FavoriteScenario.objects.get_or_create(user=request.user, scenario=scenario)
        return JsonResponse({'status': 'success', 'message': 'Scenario added to favorites'})
    except ObjectDoesNotExist:
        return JsonResponse({'status': 'failed', 'message': 'Scenario not found'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'failed', 'message': str(e)}, status=500)


@login_required
@require_POST
def remove_scenario_from_favorites(request):
    scenario_id = request.POST.get('scenario_id')
    try:
        favorite = FavoriteScenario.objects.get(user=request.user, scenario_id=scenario_id)
        favorite.delete()
        return JsonResponse({'status': 'success', 'message': 'Scenario removed from favorites'})
    except FavoriteScenario.DoesNotExist:
        return JsonResponse({'status': 'failed', 'message': 'Favorite scenario not found'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'failed', 'message': str(e)}, status=500)


@login_required
def scenario_detail(request, sid):
    scenario = get_object_or_404(Scenario, sid=sid)

    # Tagi i kategorie
    tags       = ScenarioTag.objects.filter(sid=scenario).select_related('tag')
    categories = scenario.categories.all()

    # Roboty użytkownika i które już mają ten scenariusz
    user_robots        = Robot.objects.filter(owner=request.user)
    assigned_robot_ids = set(
        UserStorage.objects
                   .filter(user=request.user, sid=scenario)
                   .values_list('rid_id', flat=True)
    )

    # Ulubione?
    is_favorited = FavoriteScenario.objects.filter(
        user=request.user, scenario=scenario
    ).exists()

    return render(request, 'front/dashboard/scenario_detail.html', {
        'scenario':            scenario,
        'tags':                tags,
        'categories':          categories,
        'user_robots':         user_robots,
        'assigned_robot_ids':  assigned_robot_ids,
        'is_favorited':        is_favorited,
    })


@login_required(login_url='home')
def profile_profil(request):
    user_profile = request.user.userprofile

    if request.method == 'POST':
        # needs to be done before initializing organization_instance, so here..
        if 'submit_organization_remove' in request.POST:
            if user_profile.organization_uuid:
                user_profile.organization_uuid.delete()  # This deletes the organization from the database
                user_profile.organization_uuid = None
                user_profile.save()
                messages.success(request, 'Organization data has been removed.')

    # Initialize forms for user's address, contact, and organization
    address_instance = user_profile.address or Address()
    contact_instance = user_profile.contact or Contact()
    organization_instance = user_profile.organization_uuid or OrganizationUUID()

    # Ensure the organization instance has a country value
    if not organization_instance.country:
        organization_instance.country = 'PL'
    
    # Zaktualizowano inicjalizację formularza name_form
    name_form = UserNameForm(instance=request.user)
    address_form = AddressForm(instance=address_instance)
    phone_number_form = PhoneNumberForm(instance=contact_instance)
    organization_details_form = OrganizationDetailsForm(instance=organization_instance)

    # Fetch all consents and initialize forms for each type
    consent_types = {
        Consent.GENERAL: [],
        Consent.USER: [],
        Consent.MODERATOR: [],
    }

    # Fetch and initialize consent forms
    for consent_type, consents_list in consent_types.items():
        consents = Consent.objects.filter(type=consent_type, is_displayed=True)
        for consent in consents:
            user_consent, created = UserConsent.objects.get_or_create(
                user=user_profile, consent=consent
            )
            form = UserConsentForm(instance=user_consent, prefix=f"consent_{consent.id}")
            consents_list.append((consent, form))

    all_consents_valid = True
    form_submission_failed = False

    if request.method == 'POST':
        # Handling form submission for personal data
        if 'submit_name' in request.POST:
            # Zaktualizowano inicjalizację formularza name_form podczas POST
            name_form = UserNameForm(request.POST, instance=request.user)
            if name_form.is_valid():
                name_form.save()
                messages.success(request, 'Your personal details have been updated.')

        # Handling form submission for address
        elif 'submit_address' in request.POST:
            address_form = AddressForm(request.POST, instance=address_instance)
            if address_form.is_valid():
                address = address_form.save()
                user_profile.address = address
                user_profile.save()
                messages.success(request, 'Your address has been updated.')

        # Handling form submission for phone number
        elif 'submit_phone_number' in request.POST:
            phone_number_form = PhoneNumberForm(request.POST, instance=contact_instance)
            if phone_number_form.is_valid():
                phone_number = phone_number_form.save()
                user_profile.contact = phone_number
                user_profile.save()
                messages.success(request, 'Your phone number has been updated.')

        # Handling form submission for organization details
        elif 'submit_organization' in request.POST:
            organization_details_form = OrganizationDetailsForm(request.POST, instance=organization_instance)
            if organization_details_form.is_valid():
                organization = organization_details_form.save()
                user_profile.organization_uuid = organization
                user_profile.save()
                messages.success(request, 'Your organization details have been updated.')
            else:
                form_submission_failed = True  # Set to true if there are errors
                messages.error(request, 'Please correct the errors in the form.')

        # Handling form submission for consents
        elif 'submit_consent' in request.POST:
            for consent_type, consents_list in consent_types.items():
                for consent, form in consents_list:
                    consent_form = UserConsentForm(
                        request.POST, 
                        instance=UserConsent.objects.get(user=user_profile, consent=consent),
                        prefix=f"consent_{consent.id}"
                    )
                    if consent_form.is_valid():
                        consent_form.save()
                    else:
                        all_consents_valid = False
            if all_consents_valid:
                messages.success(request, 'All changes have been saved.')
            else:
                messages.error(request, 'Please correct the errors in the forms.')

            # Redirect to avoid resubmitting the form on refresh
            return redirect('profile')

    context = {
        "profil": user_profile,
        "name_form": name_form,
        "address_form": address_form,
        "phone_number_form": phone_number_form,
        "organization_details_form": organization_details_form,
        "consent_types": consent_types,
        "form_submission_failed": form_submission_failed,
    }

    return render(request, "front/dashboard/profil.html", context)

@login_required(login_url='home')
def profile_sklep(request):
    return render(request, "front/dashboard/profil.html")


@login_required(login_url='home')
def profile_organizacja(request):
    return render(request, "front/dashboard/profil.html")

@login_required(login_url='home')
@group_required('Moderator', login_url='login', raise_exception=True)
def profile_scenario_mod(request):
    # Pobierz parametry sortowania z zapytania GET
    order_by_param = request.GET.get('order_by', 'name')
    order_direction = request.GET.get('direction', 'asc')
    order_by = order_by_param if order_direction == 'asc' else f'-{order_by_param}'
    
    scenarios = Scenario.objects.filter(available=Scenario.AvailabilityStatus.PUBLIC).order_by(order_by)

    # Filtrowanie po nazwie
    name_filter = request.GET.get('name')
    if name_filter:
        scenarios = scenarios.filter(name__icontains=name_filter)

    # Filtrowanie po autorze
    author_filter = request.GET.get('author')
    if author_filter:
        scenarios = scenarios.filter(author__icontains=author_filter)

    # Filtrowanie po dacie
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date:
        start_date = parse_date(start_date)
        scenarios = scenarios.filter(created_at__gte=start_date)

    if end_date:
        end_date = parse_date(end_date)
        scenarios = scenarios.filter(created_at__lte=end_date)

    # Filtrowanie po cenie
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')

    if min_price:
        scenarios = scenarios.filter(price__gte=min_price)

    if max_price:
        scenarios = scenarios.filter(price__lte=max_price)

    # Filtrowanie po statusie
    status_filter = request.GET.getlist('status')
    if status_filter:
        # Konwersja status_filter na listę intów
        int_status_filter = [int(status) for status in status_filter if status.isdigit()]
        scenarios = scenarios.filter(status__in=int_status_filter)

    # Filtrowanie po moderatorze
    moderators = User.objects.filter(groups__name='Moderator')
    moderator_filter = request.GET.get('moderator')
    if moderator_filter:
        if moderator_filter == 'none':
            scenarios = scenarios.filter(moderator__isnull=True)
        else:
            scenarios = scenarios.filter(moderator__id=moderator_filter)

    # Paginacja
    records_per_page = request.GET.get('records_per_page') or 20
    page_number = request.GET.get('page') or 1
    page_number = int(page_number) if str(page_number).isdigit() else 1
    paginator = Paginator(scenarios, records_per_page)
    page_obj = paginator.get_page(page_number)

    context = {
        'status_choices': Scenario.ScenarioStatus.choices,
        'selected_statuses': status_filter,
        'page_obj': page_obj,
        'records_per_page': records_per_page,
        'moderators': moderators,
        'order_by': order_by_param,
        'order_direction': order_direction,
    }

    return render(request, "front/dashboard/scenario_mod.html", context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def download_scenario_botie(request, scenario_sid):
    scenario = get_object_or_404(Scenario, pk=scenario_sid)
    scenario_json = json.dumps(scenario.scenario, indent=4)
    
    # Create a safe filename from the scenario name
    filename = slugify(scenario.name)
    
    response = HttpResponse(scenario_json, content_type='application/json')
    response['Content-Disposition'] = f'attachment; filename="{filename}.botie"'
    return response

@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def scenario_mod_details(request, sid):
    API_EVENT_URL_NAME = "register_scenario_event"   
    scenario       = get_object_or_404(Scenario, sid=sid)
    S              = Scenario.ScenarioStatus          # skrót
    all_groups     = TagGroup.objects.all()

    # ───────────────────────────────────────
    # 1. ZAPIS GRUP TAGÓW
    # ───────────────────────────────────────
    if request.method == "POST" and "assign_groups_form" in request.POST:
        chosen_ids      = request.POST.getlist("groups")          # id zaznaczonych grup
        tags_from_group = Tag.objects.filter(group_id__in=chosen_ids)

        # 1) usuń WSZYSTKIE tagi, które należą do jakiejkolwiek grupy
        scenario.tags.remove(*scenario.tags.filter(group__isnull=False))

        # 2) dodaj tagi z zaznaczonych grup
        scenario.tags.add(*tags_from_group)

        messages.success(request, _("Groups saved – all tags from chosen groups assigned."))
        return redirect("scenario_mod_details", sid=sid)

    # ───────────────────────────────────────
    # 2. ZAPIS KATEGORII
    # ───────────────────────────────────────
    if request.method == "POST" and "assign_categories_form" in request.POST:
        selected_names = request.POST.getlist("categories")
        scenario.categories.set(Category.objects.filter(name__in=selected_names))
        messages.success(request, _("Categories saved."))
        return redirect("scenario_mod_details", sid=sid)

    # ───────────────────────────────────────
    # 3. ZMIANA STATUSU  (bez zmian funkcjonalnych)
    # ───────────────────────────────────────
    if (
        request.method == "POST"
        and "seo_form" not in request.POST
        and "assign_groups_form" not in request.POST
        and "assign_categories_form" not in request.POST
    ):
        try:
            new_status = int(request.POST.get("status"))
        except (TypeError, ValueError):
            messages.error(request, _("Invalid scenario status."))
            return redirect("scenario_mod_details", sid=sid)

        msg = request.POST.get("message", "").strip()
        if new_status == S.REJECTED and not msg:
            messages.error(request, _("Reason is required when rejecting scenario."))
            return redirect("scenario_mod_details", sid=sid)

        payload = {
            "sid"          : str(scenario.sid),
            "event_type_id": scenario.get_event_type_from_status(new_status),
            "uid"          : request.user.id,
            "message"      : msg,
        }
        if new_status in (S.REJECTED, S.ACCEPTED):
            payload.update(
                name           = scenario.name,
                description    = scenario.description,
                execution_cost = scenario.price,
                is_fixed_cost  = scenario.is_fixed_cost,
            )

        try:
            requests.post(request.build_absolute_uri(reverse(API_EVENT_URL_NAME)), json=payload, timeout=5).raise_for_status()
        except requests.RequestException as exc:
            messages.error(request, _("Status change failed: %(err)s") % {"err": exc})
            return redirect("scenario_mod_details", sid=sid)

        scenario.refresh_from_db()
        if new_status == S.FOR_MOD:
            scenario.moderator = request.user
            scenario.save(update_fields=["moderator"])
        elif new_status == S.TO_MOD:
            scenario.moderator = None
            scenario.save(update_fields=["moderator"])

        messages.success(request, _("Scenario status updated."))
        return redirect("scenario_mod_details", sid=sid)

    # ───────────────────────────────────────
    # 4. SEO FORM
    # ───────────────────────────────────────
    try:
        seo_meta = scenario.seo_meta
    except Exception:
        seo_meta = None
    seo_form = ScenarioMetaForm(request.POST or None, request.FILES or None, instance=seo_meta)
    if request.method == "POST" and "seo_form" in request.POST and seo_form.is_valid():
        seo_form.save()
        messages.success(request, _("SEO metadata saved."))
        return redirect("scenario_mod_details", sid=sid)

    # ───────────────────────────────────────
    # 5. DANE DO SZABLONU
    # ───────────────────────────────────────
    assigned_groups = TagGroup.objects.filter(tags__in=scenario.tags.all()).distinct()
    scenario_tags   = scenario.tags.all()
    available_categories = Category.objects.all()

    # aktywne akcje dla moderatora
    actions = []
    if scenario.status == S.TO_MOD:
        actions.append({"status": S.FOR_MOD, "label": _("Assign to me"), "class": "btn-info", "icon": "fas fa-user-plus"})
    elif scenario.status == S.FOR_MOD:
        actions += [
            {"status": S.TO_MOD, "label": _("Return to pool"), "class": "btn-warning", "icon": "fas fa-arrow-left"},
            {"status": S.IN_MOD, "label": _("Start moderation"), "class": "btn-primary", "icon": "fas fa-play"},
        ]
    elif scenario.status == S.IN_MOD:
        actions += [
            {"status": S.FOR_MOD , "label": _("Pause moderation"), "class": "btn-warning", "icon": "fas fa-pause"},
            {"status": S.ACCEPTED, "label": _("Accept"),           "class": "btn-success", "icon": "fas fa-check"},
            {"status": S.REJECTED, "label": _("Reject"),           "class": "btn-danger" , "icon": "fas fa-times"},
        ]

    context = {
        "scenario"            : scenario,
        "all_groups"          : all_groups,
        "assigned_groups"     : assigned_groups,
        "scenario_tags"       : scenario_tags,
        "available_categories": available_categories,
        "scenario_cats"       : scenario.categories.all(),
        "actions"             : actions,
        "seo_form"            : seo_form,
    }
    return render(request, "front/dashboard/scenario_mod_details.html", context)




@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def scenario_change_history(request, sid):
    scenario = get_object_or_404(Scenario, sid=sid)
    events = Scenario_Event.objects.filter(sid=scenario).order_by('-event_time')\
        .prefetch_related('scenario_event_message_set')
    return render(request, 'front/dashboard/scenario_change_history.html', {
        'scenario': scenario,
        'events': events,
    })


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def change_scenario_status(request, sid):
    LOGGER.info(f'change_scenario_status')
    scenario = get_object_or_404(Scenario, sid=sid)
    LOGGER.info(f'{scenario}')
    if request.method == 'POST':
        try:
            with transaction.atomic():
                new_status = request.POST.get('status')
                new_moderator_id = request.POST.get('moderator')
                custom_message = request.POST.get('customMessage', 'nie wprowadzono wiadomości')
                event_type = scenario.get_event_type_from_status(scenario_status=new_status)
                scenario = Scenario.objects.get(sid=sid)
                registration_succesfull, registration_message = scenario.register_scenario_event(event_type=event_type, message=custom_message)
                if registration_succesfull:
                    scenario.refresh_from_db()
                    LOGGER.info(f'{scenario}')
                    if new_moderator_id:
                        user_profile = UserProfile.objects.get(pk=new_moderator_id)
                        scenario.moderator = user_profile.user
                    else:
                        scenario.moderator = None
                    LOGGER.info(f'{scenario}')
                    scenario.save()
                    messages.success(request, registration_message)
                else:
                    messages.error(request, registration_message)

        except Scenario.DoesNotExist:
            messages.error(request, 'Scenariusz nie istnieje.')
        except UserProfile.DoesNotExist:
            messages.error(request, 'Wybrany moderator nie istnieje.')
        except (Exception,):
            messages.error(request, 'Wystąpił błąd podczas zmiany statusu scenariusza.')
        return redirect('scenario_mod_details', sid=sid)
    else:
        messages.warning(request, 'Nieprawidłowe żądanie.')
        return redirect('scenario_mod_details', sid=sid)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def detach_tag(request, sid, tag_name):
    if request.method == 'POST':
        scenario = get_object_or_404(Scenario, sid=sid)
        tag = get_object_or_404(Tag, name=tag_name)
        ScenarioTag.objects.filter(sid=scenario, tag=tag).delete()
        return redirect('scenario_mod_details', sid=sid)
    return redirect('scenario_mod_details')


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def add_tag_to_scenario(request, sid):
    scenario = get_object_or_404(Scenario, sid=sid)
    if request.method == 'POST':
        tag_name = request.POST.get('new_tag').strip()
        if tag_name:
            tag, created = Tag.objects.get_or_create(name=tag_name)
            ScenarioTag.objects.get_or_create(sid=scenario, tag=tag)
        return redirect('scenario_mod_details', sid=sid)
    return redirect('scenario_mod_details', sid=sid)


# @login_required(login_url='home')
# def create_robot(request):
#     robot = Robot.objects.create()
#     return render(request, "dashboard/profil.html", {"profil":profil})

@login_required
@group_required('Partner', login_url='login', raise_exception=True)
def partner_dashboard_menu(request):
    user = request.user
    user_profile = user.userprofile

    # Pobierz lub utwórz link afiliacyjny
    affiliate_link, created = AffiliateLink.objects.get_or_create(user=user)

    # Pobierz aktualną stawkę prowizji
    now = timezone.now()
    current_rate = affiliate_link.rate_history.filter(start_date__lte=now, end_date__isnull=True).first()
    current_commission_rate = current_rate.commission_rate if current_rate else Decimal('0.0')

    # Pobierz użytkowników poleconych
    referred_users = UserProfile.objects.filter(referred_by=user)

    # Pobierz prowizje partnera
    commissions = PartnerCommission.objects.filter(partner=user).aggregate(total=Sum('amount'))
    total_commission = commissions['total'] or Decimal('0.0')

    # Oblicz dostępne środki do wypłaty
    available_balance = get_available_balance(user)

    context = {
        'affiliate_link': affiliate_link.get_link(),
        'current_commission_rate': current_commission_rate,
        'referred_users': referred_users,
        'total_commission': total_commission,
        'available_balance': available_balance,
    }
    return render(request, 'front/dashboard/partner_dashboard_menu.html', context)

@login_required(login_url='home')
@group_required('Partner', login_url='login', raise_exception=True)
def partner_dashboard_commission(request):
    # Pobranie parametrów sortowania z GET
    order_by_param = request.GET.get('order_by', 'created_at')
    order_direction = request.GET.get('direction', 'asc')
    if order_direction not in ['asc', 'desc']:
        order_direction = 'asc'
    order_by = order_by_param if order_direction == 'asc' else f'-{order_by_param}'

    # Poprawny queryset na modelu PartnerCommission
    commissions = PartnerCommission.objects.filter(partner=request.user).select_related('related_order', 'related_event')

    # Filtrowanie według kodu AffiliateLink
    affiliate_link_code = request.GET.get('affiliate_link')
    if affiliate_link_code:
        try:
            affiliate_link = AffiliateLink.objects.get(code=affiliate_link_code)
            # Filtracja po AffiliateLink poprzez powiązany User
            commissions = commissions.filter(partner__affiliate_link=affiliate_link)
        except AffiliateLink.DoesNotExist:
            commissions = commissions.none()

    # Zakres dat
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date:
        start_date = parse_date(start_date)
        if start_date:
            commissions = commissions.filter(created_at__date__gte=start_date)
    if end_date:
        end_date = parse_date(end_date)
        if end_date:
            commissions = commissions.filter(created_at__date__lte=end_date)

    # Zakres stawki prowizji
    min_commission_rate = request.GET.get('min_commission_rate')
    max_commission_rate = request.GET.get('max_commission_rate')
    if min_commission_rate:
        try:
            min_commission_rate = float(min_commission_rate)
            commissions = commissions.filter(commission_rate__gte=min_commission_rate)
        except ValueError:
            pass
    if max_commission_rate:
        try:
            max_commission_rate = float(max_commission_rate)
            commissions = commissions.filter(commission_rate__lte=max_commission_rate)
        except ValueError:
            pass

    # Zakres kwoty prowizji
    min_amount = request.GET.get('min_amount')
    max_amount = request.GET.get('max_amount')
    if min_amount:
        try:
            min_amount = float(min_amount)
            commissions = commissions.filter(amount__gte=min_amount)
        except ValueError:
            pass
    if max_amount:
        try:
            max_amount = float(max_amount)
            commissions = commissions.filter(amount__lte=max_amount)
        except ValueError:
            pass

    # Filtracja po wydarzeniu
    related_event_id = request.GET.get('related_event')
    if related_event_id:
        commissions = commissions.filter(related_event__id=related_event_id)

    # Filtracja po zamówieniu
    related_order_id = request.GET.get('related_order')
    if related_order_id:
        commissions = commissions.filter(related_order__order_id=related_order_id)

    # Sortowanie
    commissions = commissions.order_by(order_by)

    # Paginacja
    records_per_page = request.GET.get('records_per_page', 20)
    try:
        records_per_page = int(records_per_page)
    except ValueError:
        records_per_page = 20
    page_number = request.GET.get('page', 1)
    try:
        page_number = int(page_number)
    except ValueError:
        page_number = 1
    paginator = Paginator(commissions, records_per_page)
    page_obj = paginator.get_page(page_number)

    # Łączna prowizja
    total_commission = commissions.aggregate(total=Sum('amount'))['total'] or 0

    # Lista związanych wydarzeń i zamówień
    related_events = CreditEvent.objects.filter(user=request.user)
    related_orders = Order.objects.filter(user=request.user)

    context = {
        'page_obj': page_obj,
        'records_per_page': records_per_page,
        'order_by': order_by_param,
        'order_direction': order_direction,
        'start_date': request.GET.get('start_date', ''),
        'end_date': request.GET.get('end_date', ''),
        'min_commission_rate': request.GET.get('min_commission_rate', ''),
        'max_commission_rate': request.GET.get('max_commission_rate', ''),
        'min_amount': request.GET.get('min_amount', ''),
        'max_amount': request.GET.get('max_amount', ''),
        'related_events': related_events,
        'related_orders': related_orders,
        'affiliate_link_code': affiliate_link_code,
        'total_commission': total_commission,
    }

    return render(request, 'front/dashboard/partner_dashboard_commission.html', context)

@login_required(login_url='home')
@group_required('Partner', login_url='login', raise_exception=True)
def partner_dashboard_withdrawal(request):
    # Pobranie parametrów sortowania z GET
    order_by_param = request.GET.get('order_by', 'start_date')
    order_direction = request.GET.get('direction', 'asc')
    order_by = order_by_param if order_direction == 'asc' else f'-{order_by_param}'

    # Początkowy queryset
    withdrawal_cycles = WithdrawalCycle.objects.filter(partner=request.user).prefetch_related('withdrawals')

    # Filtrowanie
    # Zakres dat
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date:
        start_date = parse_date(start_date)
        if start_date:
            withdrawal_cycles = withdrawal_cycles.filter(start_date__date__gte=start_date)
    if end_date:
        end_date = parse_date(end_date)
        if end_date:
            withdrawal_cycles = withdrawal_cycles.filter(end_date__date__lte=end_date)

    # Status wypłaty
    withdrawal_status = request.GET.get('status')
    if withdrawal_status:
        withdrawal_cycles = withdrawal_cycles.filter(withdrawals__status=withdrawal_status)

    # Zakres kwoty wypłaty
    min_amount = request.GET.get('min_amount')
    max_amount = request.GET.get('max_amount')
    if min_amount:
        try:
            min_amount = float(min_amount)
            withdrawal_cycles = withdrawal_cycles.filter(withdrawals__amount__gte=min_amount)
        except ValueError:
            pass
    if max_amount:
        try:
            max_amount = float(max_amount)
            withdrawal_cycles = withdrawal_cycles.filter(withdrawals__amount__lte=max_amount)
        except ValueError:
            pass

    # Sortowanie
    withdrawal_cycles = withdrawal_cycles.order_by(order_by)

    # Paginacja
    records_per_page = request.GET.get('records_per_page', 20)
    try:
        records_per_page = int(records_per_page)
    except ValueError:
        records_per_page = 20
    page_number = request.GET.get('page', 1)
    try:
        page_number = int(page_number)
    except ValueError:
        page_number = 1
    paginator = Paginator(withdrawal_cycles, records_per_page)
    page_obj = paginator.get_page(page_number)

    # Łączna wypłata w cyklach
    total_withdrawn = withdrawal_cycles.aggregate(total=Sum('total_withdrawn'))['total'] or 0

    # Lista statusów wypłat
    status_choices = AffiliateWithdrawalRequest.Status.choices

    context = {
        'page_obj': page_obj,
        'records_per_page': records_per_page,
        'order_by': order_by_param,
        'order_direction': order_direction,
        'start_date': request.GET.get('start_date', ''),
        'end_date': request.GET.get('end_date', ''),
        'withdrawal_status': withdrawal_status,
        'min_amount': request.GET.get('min_amount', ''),
        'max_amount': request.GET.get('max_amount', ''),
        'status_choices': status_choices,
        'total_withdrawn': total_withdrawn,
    }

    return render(request, 'front/dashboard/partner_dashboard_withdrawal.html', context)


@login_required
@group_required('Partner', login_url='login', raise_exception=True)
def request_withdrawal(request):
    user = request.user
    available = get_available_balance(user)

    if available <= 0:
        messages.error(request, "Brak dostępnych środków do wypłaty.")
        return redirect('partner_dashboard_menu')

    # Tworzymy żądanie wypłaty
    withdrawal = AffiliateWithdrawalRequest.objects.create(user=user, amount=available)

    # Inicjalizujemy Mailera i wysyłamy maile
    mailer = Mailer()
    mail_result = mailer.send_withdrawal_request_email(user, withdrawal)

    if mail_result:
        messages.success(request, "Twoje żądanie wypłaty zostało zarejestrowane i oczekuje na realizację.")
    else:
        messages.warning(request, "Zgłoszono żądanie wypłaty, ale wystąpił problem z wysyłką e-maili.")

    return redirect('partner_dashboard_menu')


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_dashboard(request):
    """Dashboard zarządzania newsletterem dla moderatorów"""

    # Statystyki podstawowe
    total_subscriptions = NewsletterSubscription.objects.count()
    confirmed_subscriptions = NewsletterSubscription.objects.filter(is_confirmed=True).count()
    pending_subscriptions = NewsletterSubscription.objects.filter(is_confirmed=False).count()

    # Statystyki z ostatnich 30 dni
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_subscriptions = NewsletterSubscription.objects.filter(
        created_at__gte=thirty_days_ago
    ).count()

    # Ostatnie kampanie
    recent_campaigns = NewsletterCampaign.objects.select_related('template', 'segment').order_by('-created_at')[:5]

    # Segmenty z liczbą subskrybentów
    segments_with_counts = NewsletterSegment.objects.all().order_by('name')
    for segment in segments_with_counts:
        segment.subscriber_count = segment.get_subscribers().count()

    # Sortuj według liczby subskrybentów (malejąco)
    segments_with_counts = sorted(segments_with_counts, key=lambda x: x.subscriber_count, reverse=True)

    # Ostatnie subskrypcje
    recent_subscribers = NewsletterSubscription.objects.select_related().order_by('-created_at')[:10]

    context = {
        'total_subscriptions': total_subscriptions,
        'confirmed_subscriptions': confirmed_subscriptions,
        'pending_subscriptions': pending_subscriptions,
        'recent_subscriptions': recent_subscriptions,
        'recent_campaigns': recent_campaigns,
        'segments_with_counts': segments_with_counts,
        'recent_subscribers': recent_subscribers,
    }

    return render(request, 'front/dashboard/newsletter_dashboard.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_subscribers(request):
    """Lista subskrybentów newslettera"""
    subscribers = NewsletterSubscription.objects.all().order_by('-created_at')

    # Filtrowanie
    status_filter = request.GET.get('status')
    if status_filter == 'confirmed':
        subscribers = subscribers.filter(is_confirmed=True)
    elif status_filter == 'pending':
        subscribers = subscribers.filter(is_confirmed=False)

    search = request.GET.get('search', '').strip()
    if search:
        subscribers = subscribers.filter(email__icontains=search)

    # Eksport danych
    export_format = request.GET.get('export')
    if export_format in ['csv', 'json']:
        import csv
        import json
        from django.http import HttpResponse

        if export_format == 'csv':
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="newsletter_subscribers.csv"'

            writer = csv.writer(response)
            writer.writerow(['Email', 'Status', 'Created At', 'Confirmed At', 'Marketing Consent', 'Data Processing Consent', 'IP Address'])

            for subscriber in subscribers:
                writer.writerow([
                    subscriber.email,
                    'Confirmed' if subscriber.is_confirmed else 'Pending',
                    subscriber.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    subscriber.confirmed_at.strftime('%Y-%m-%d %H:%M:%S') if subscriber.confirmed_at else '',
                    'Yes' if subscriber.marketing_consent else 'No',
                    'Yes' if subscriber.data_processing_consent else 'No',
                    subscriber.ip_address or ''
                ])

            return response

        elif export_format == 'json':
            response = HttpResponse(content_type='application/json')
            response['Content-Disposition'] = 'attachment; filename="newsletter_subscribers.json"'

            data = []
            for subscriber in subscribers:
                data.append({
                    'email': subscriber.email,
                    'status': 'confirmed' if subscriber.is_confirmed else 'pending',
                    'created_at': subscriber.created_at.isoformat(),
                    'confirmed_at': subscriber.confirmed_at.isoformat() if subscriber.confirmed_at else None,
                    'marketing_consent': subscriber.marketing_consent,
                    'data_processing_consent': subscriber.data_processing_consent,
                    'ip_address': subscriber.ip_address
                })

            response.write(json.dumps(data, indent=2, ensure_ascii=False))
            return response

    # Paginacja
    paginator = Paginator(subscribers, 25)
    page = request.GET.get('page')
    try:
        subscribers = paginator.page(page)
    except PageNotAnInteger:
        subscribers = paginator.page(1)
    except EmptyPage:
        subscribers = paginator.page(paginator.num_pages)

    context = {
        'subscribers': subscribers,
        'status_filter': status_filter,
        'search': search,
    }
    return render(request, 'front/dashboard/newsletter_subscribers.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_campaigns(request):
    """Lista kampanii newslettera"""
    campaigns = NewsletterCampaign.objects.select_related('template', 'segment').order_by('-created_at')

    # Paginacja
    paginator = Paginator(campaigns, 20)
    page = request.GET.get('page')
    try:
        campaigns = paginator.page(page)
    except PageNotAnInteger:
        campaigns = paginator.page(1)
    except EmptyPage:
        campaigns = paginator.page(paginator.num_pages)

    context = {
        'campaigns': campaigns,
    }
    return render(request, 'front/dashboard/newsletter_campaigns.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_templates(request):
    """Lista szablonów newslettera"""
    templates = NewsletterTemplate.objects.all().order_by('name')

    context = {
        'templates': templates,
    }
    return render(request, 'front/dashboard/newsletter_templates.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_segments(request):
    """Lista segmentów newslettera"""
    segments = NewsletterSegment.objects.all().order_by('name')

    # Dodaj liczbę subskrybentów do każdego segmentu
    for segment in segments:
        segment.subscriber_count = segment.get_subscribers().count()

    context = {
        'segments': segments,
    }
    return render(request, 'front/dashboard/newsletter_segments.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_analytics(request):
    """Analityka newslettera"""
    # Statystyki podstawowe
    total_subscribers = NewsletterSubscription.objects.count()
    confirmed_subscribers = NewsletterSubscription.objects.filter(is_confirmed=True).count()

    # Statystyki kampanii
    total_campaigns = NewsletterCampaign.objects.count()
    sent_campaigns = NewsletterCampaign.objects.filter(status='sent').count()

    # Statystyki z ostatnich 30 dni
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_opens = NewsletterOpen.objects.filter(opened_at__gte=thirty_days_ago).count()
    recent_clicks = NewsletterClick.objects.filter(clicked_at__gte=thirty_days_ago).count()

    # Top kampanie według otwarć
    top_campaigns = NewsletterCampaign.objects.annotate(
        opens_count=Count('newsletterdelivery__newsletteropen'),
        clicks_count=Count('newsletterdelivery__newsletterclick')
    ).filter(status='sent').order_by('-opens_count')[:10]

    context = {
        'total_subscribers': total_subscribers,
        'confirmed_subscribers': confirmed_subscribers,
        'total_campaigns': total_campaigns,
        'sent_campaigns': sent_campaigns,
        'recent_opens': recent_opens,
        'recent_clicks': recent_clicks,
        'top_campaigns': top_campaigns,
    }
    return render(request, 'front/dashboard/newsletter_analytics.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_campaign_create(request):
    """Tworzenie nowej kampanii newslettera"""
    if request.method == 'POST':
        form = NewsletterCampaignForm(request.POST)
        if form.is_valid():
            campaign = form.save(commit=False)
            campaign.created_by = request.user
            campaign.save()
            messages.success(request, 'Kampania została utworzona pomyślnie!')
            return redirect('profiles_newsletter_campaigns')
    else:
        form = NewsletterCampaignForm()

    context = {
        'form': form,
        'title': 'Nowa kampania',
        'action': 'create'
    }
    return render(request, 'front/dashboard/newsletter_campaign_form.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_campaign_edit(request, campaign_id):
    """Edycja kampanii newslettera"""
    campaign = get_object_or_404(NewsletterCampaign, id=campaign_id)

    # Tylko szkice można edytować
    if campaign.status != 'draft':
        messages.error(request, 'Można edytować tylko kampanie w stanie szkicu.')
        return redirect('profiles_newsletter_campaigns')

    if request.method == 'POST':
        form = NewsletterCampaignForm(request.POST, instance=campaign)
        if form.is_valid():
            form.save()
            messages.success(request, 'Kampania została zaktualizowana!')
            return redirect('profiles_newsletter_campaigns')
    else:
        form = NewsletterCampaignForm(instance=campaign)

    context = {
        'form': form,
        'campaign': campaign,
        'title': f'Edytuj kampanię: {campaign.name}',
        'action': 'edit'
    }
    return render(request, 'front/dashboard/newsletter_campaign_form.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_campaign_send(request, campaign_id):
    """Wysyłanie kampanii newslettera"""
    campaign = get_object_or_404(NewsletterCampaign, id=campaign_id)

    if campaign.status not in ['draft', 'scheduled']:
        return JsonResponse({'success': False, 'error': 'Ta kampania nie może być wysłana.'})

    if request.method == 'POST':
        try:
            # Pobierz subskrybentów z segmentu
            subscribers = campaign.segment.get_subscribers()
            if not subscribers.exists():
                return JsonResponse({'success': False, 'error': 'Brak subskrybentów w wybranym segmencie.'})

            campaign.total_recipients = subscribers.count()
            campaign.status = 'sending'
            campaign.save()

            # Rzeczywiste wysyłanie emaili
            print(f"Rozpoczynam wysyłanie kampanii do {subscribers.count()} odbiorców")
            mailer = Mailer()
            sent_count = 0
            failed_count = 0

            for subscriber in subscribers:
                try:
                    print(f"Wysyłam do: {subscriber.email}")

                    # Utwórz rekord dostarczenia
                    delivery = NewsletterDelivery.objects.create(
                        campaign=campaign,
                        subscription=subscriber
                    )

                    # Przygotuj treść emaila
                    context = {
                        'subscriber': subscriber,
                        'unsubscribe_url': request.build_absolute_uri(
                            reverse('newsletter_unsubscribe', args=[subscriber.confirmation_token])
                        ),
                        'current_date': timezone.now(),
                        'site_url': request.build_absolute_uri('/'),
                        'tracking_pixel_url': request.build_absolute_uri(
                            reverse('newsletter_track_open', args=[delivery.tracking_id])
                        ),
                    }

                    # Renderuj szablony
                    from django.template import Template, Context
                    subject_template = Template(campaign.template.subject)
                    html_template = Template(campaign.template.content_html)

                    django_context = Context(context)
                    subject = subject_template.render(django_context).strip()

                    # Renderuj główną treść HTML
                    main_html_content = html_template.render(django_context)

                    # Dodaj prosty header i footer w stylu Botie (używamy CID dla embedded images)
                    html_header = f'''
                    <div style="display: inline-block; width: 600px; max-width:100%; background-color: white; padding: 25px;">
                        <img src="cid:image1" alt="Botie">
                        <img src="cid:image2" style="float: right" alt="Botie">
                        <hr style="border-top: 1px solid lightgrey;"><br>
                        <strong>Witaj {subscriber.email.split('@')[0]},</strong><br><br>
                    '''

                    html_footer = f'''
                        <br><br><hr style="border-top: 1px solid lightgrey;">
                        Pozdrawiamy,<br>Zespół Botie
                        <br><br><div style="color: lightgrey;">--<br>
                        Jeśli nie chcesz otrzymywać wiadomości tego typu, możesz się
                        <a style="color: darkgrey;" href="{context['unsubscribe_url']}">wypisać tutaj</a>.
                        </div>
                        <img src="{context['tracking_pixel_url']}" width="1" height="1" style="display:none;" alt="">
                    </div>
                    '''

                    html_content = html_header + main_html_content + html_footer
                    text_content = f"Witaj {subscriber.email.split('@')[0]},\n\n{main_html_content}\n\nPozdrawiamy,\nZespół Botie\n\nWypisz się: {context['unsubscribe_url']}"

                    print(f"Temat: {subject}")
                    print(f"HTML (pierwsze 100 znaków): {html_content[:100]}...")

                    # Wyślij email
                    result = mailer.send_html_mail(
                        user=None,
                        subject=subject,
                        text=text_content,
                        html=html_content,
                        recipients=[subscriber.email]
                    )

                    print(f"Wynik wysyłania: {result}")

                    if result:
                        delivery.delivery_status = 'sent'
                        sent_count += 1
                    else:
                        delivery.delivery_status = 'failed'
                        failed_count += 1

                    delivery.save()

                except Exception as e:
                    failed_count += 1
                    print(f"Błąd wysyłania do {subscriber.email}: {e}")
                    import traceback
                    traceback.print_exc()

            # Aktualizuj statystyki kampanii
            campaign.emails_sent = sent_count
            campaign.emails_failed = failed_count
            campaign.status = 'sent'
            campaign.sent_at = timezone.now()
            campaign.save()

            return JsonResponse({
                'success': True,
                'message': f'Kampania wysłana! Wysłano: {sent_count}, Błędy: {failed_count}'
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Nieprawidłowe żądanie.'})


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_campaign_cancel(request, campaign_id):
    """Anulowanie zaplanowanej kampanii"""
    campaign = get_object_or_404(NewsletterCampaign, id=campaign_id)

    if campaign.status != 'scheduled':
        return JsonResponse({'success': False, 'error': 'Można anulować tylko zaplanowane kampanie.'})

    if request.method == 'POST':
        campaign.status = 'cancelled'
        campaign.save()
        return JsonResponse({'success': True, 'message': 'Kampania została anulowana.'})

    return JsonResponse({'success': False, 'error': 'Nieprawidłowe żądanie.'})


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_campaign_stats(request, campaign_id):
    """Statystyki kampanii"""
    campaign = get_object_or_404(NewsletterCampaign, id=campaign_id)

    # Podstawowe statystyki
    deliveries = NewsletterDelivery.objects.filter(campaign=campaign)
    opens = NewsletterOpen.objects.filter(delivery__campaign=campaign)
    clicks = NewsletterClick.objects.filter(delivery__campaign=campaign)

    context = {
        'campaign': campaign,
        'total_sent': deliveries.count(),
        'total_opens': opens.count(),
        'total_clicks': clicks.count(),
        'open_rate': (opens.count() / deliveries.count() * 100) if deliveries.count() > 0 else 0,
        'click_rate': (clicks.count() / deliveries.count() * 100) if deliveries.count() > 0 else 0,
    }

    return render(request, 'front/dashboard/newsletter_campaign_stats.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_template_create(request):
    """Tworzenie nowego szablonu newslettera"""
    if request.method == 'POST':
        form = NewsletterTemplateForm(request.POST)
        if form.is_valid():
            template = form.save()
            messages.success(request, 'Szablon został utworzony pomyślnie!')
            return redirect('profiles_newsletter_templates')
    else:
        # Domyślny szablon bazowy
        default_html = """<table style="width: 760px; max-width:100%; background-color: white;" border="0" cellpadding="25">
<tr><td><table border="0" cellpadding="0">
<tr>
<td><img src="cid:image1"></td>
<td align="right"><img src="cid:image2"></td>
</tr>
<tr><td colspan="2"><hr style="border: 1px solid lightgrey;"><br>
Witaj {{ subscriber.email }},<br>
<br><td><tr>
<tr><td colspan="2">

<!-- TREŚĆ NEWSLETTERA - EDYTUJ TUTAJ -->
<h2 style="color: #0b8bcc;">Tytuł newslettera</h2>

<p>Treść twojego newslettera...</p>

<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4 style="color: #0b8bcc; margin-top: 0;">🔍 Sekcja 1</h4>
    <p>Opis sekcji...</p>
    <a href="{{ site_url }}" style="background-color: #0b8bcc; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 14px;">
        Przycisk akcji
    </a>
</div>

<p style="text-align: center; margin-top: 30px; font-size: 18px; color: #0b8bcc;">
    <strong>Dziękujemy za uwagę!</strong>
</p>
<!-- KONIEC TREŚCI -->

<td><tr>
<tr><td colspan="2"><br><hr style="border: 1px solid lightgrey;">
Pozdrawiamy,<br>Zespół Botie<br>
<br>
<p style="font-size: 12px; color: #999; text-align: center;">
    Jeśli nie chcesz już otrzymywać newslettera, możesz się
    <a href="{{ unsubscribe_url }}" style="color: #999;">wypisać tutaj</a>.
</p>
</td></tr>
</table>
</td></tr>
</table>"""

        default_text = """Witaj {{ subscriber.email }},

TYTUŁ NEWSLETTERA

Treść twojego newslettera...

SEKCJA 1
Opis sekcji...
Link: {{ site_url }}

Dziękujemy za uwagę!

Pozdrawiamy,
Zespół Botie

---
Jeśli nie chcesz już otrzymywać newslettera, możesz się wypisać: {{ unsubscribe_url }}"""

        form = NewsletterTemplateForm(initial={
            'content_html': default_html,
            'content_text': default_text,
            'is_active': True
        })

    context = {
        'form': form,
        'title': 'Nowy szablon',
        'action': 'create'
    }
    return render(request, 'front/dashboard/newsletter_template_form.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_template_edit(request, template_id):
    """Edycja szablonu newslettera"""
    template = get_object_or_404(NewsletterTemplate, id=template_id)

    if request.method == 'POST':
        form = NewsletterTemplateForm(request.POST, instance=template)
        if form.is_valid():
            form.save()
            messages.success(request, 'Szablon został zaktualizowany!')
            return redirect('profiles_newsletter_templates')
    else:
        form = NewsletterTemplateForm(instance=template)

    context = {
        'form': form,
        'template': template,
        'title': f'Edycja: {template.name}',
        'action': 'edit'
    }
    return render(request, 'front/dashboard/newsletter_template_form.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_template_delete(request, template_id):
    """Usuwanie szablonu newslettera"""
    template = get_object_or_404(NewsletterTemplate, id=template_id)

    if request.method == 'POST':
        template_name = template.name
        template.delete()
        messages.success(request, f'Szablon "{template_name}" został usunięty!')
        return redirect('profiles_newsletter_templates')

    context = {
        'template': template,
    }
    return render(request, 'front/dashboard/newsletter_template_delete.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_template_preview(request, template_id):
    """Podgląd szablonu newslettera"""
    template = get_object_or_404(NewsletterTemplate, id=template_id)

    # Przykładowe dane do podglądu
    preview_data = {
        'subscriber': {'email': '<EMAIL>'},
        'unsubscribe_url': 'https://example.com/unsubscribe/token123',
        'current_date': timezone.now().strftime('%d.%m.%Y'),
        'site_url': 'https://botie.pl'
    }

    # Renderuj szablon z przykładowymi danymi
    from django.template import Template, Context

    try:
        html_template = Template(template.content_html)
        rendered_html = html_template.render(Context(preview_data))
    except Exception as e:
        rendered_html = f"<p style='color: red;'>Błąd w szablonie HTML: {str(e)}</p>"

    try:
        text_template = Template(template.content_text)
        rendered_text = text_template.render(Context(preview_data))
    except Exception as e:
        rendered_text = f"Błąd w szablonie tekstowym: {str(e)}"

    context = {
        'template': template,
        'rendered_html': rendered_html,
        'rendered_text': rendered_text,
        'preview_data': preview_data,
    }
    return render(request, 'front/dashboard/newsletter_template_preview.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_segment_create(request):
    """Tworzenie nowego segmentu newslettera"""
    if request.method == 'POST':
        form = NewsletterSegmentForm(request.POST)
        if form.is_valid():
            segment = form.save()
            messages.success(request, 'Segment został utworzony pomyślnie!')
            return redirect('profiles_newsletter_segments')
    else:
        form = NewsletterSegmentForm(initial={
            'confirmed_only': True,
            'is_active': True
        })

    context = {
        'form': form,
        'title': 'Nowy segment',
        'action_url': reverse('profiles_newsletter_segment_create'),
    }
    return render(request, 'front/dashboard/newsletter_segment_form.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_segment_edit(request, segment_id):
    """Edycja segmentu newslettera"""
    segment = get_object_or_404(NewsletterSegment, id=segment_id)

    if request.method == 'POST':
        form = NewsletterSegmentForm(request.POST, instance=segment)
        if form.is_valid():
            form.save()
            messages.success(request, 'Segment został zaktualizowany pomyślnie!')
            return redirect('profiles_newsletter_segments')
    else:
        form = NewsletterSegmentForm(instance=segment)

    context = {
        'form': form,
        'segment': segment,
        'title': f'Edytuj segment: {segment.name}',
        'action_url': reverse('profiles_newsletter_segment_edit', args=[segment_id]),
    }
    return render(request, 'front/dashboard/newsletter_segment_form.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_segment_delete(request, segment_id):
    """Usuwanie segmentu newslettera"""
    segment = get_object_or_404(NewsletterSegment, id=segment_id)

    if request.method == 'POST':
        segment_name = segment.name
        segment.delete()
        messages.success(request, f'Segment "{segment_name}" został usunięty pomyślnie!')
        return redirect('profiles_newsletter_segments')

    # Sprawdź czy segment jest używany w kampaniach
    campaigns_count = NewsletterCampaign.objects.filter(segment=segment).count()

    context = {
        'segment': segment,
        'campaigns_count': campaigns_count,
        'subscriber_count': segment.get_subscribers().count(),
    }
    return render(request, 'front/dashboard/newsletter_segment_delete.html', context)


@login_required
@group_required('Moderator', login_url='login', raise_exception=True)
def newsletter_segment_subscribers(request, segment_id):
    """Lista subskrybentów w segmencie"""
    segment = get_object_or_404(NewsletterSegment, id=segment_id)
    subscribers = segment.get_subscribers().order_by('-created_at')

    context = {
        'segment': segment,
        'subscribers': subscribers,
        'subscriber_count': subscribers.count(),
    }
    return render(request, 'front/dashboard/newsletter_segment_subscribers.html', context)