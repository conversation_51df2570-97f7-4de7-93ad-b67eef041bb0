{% extends 'main.html' %}
{% load i18n %}
{% load static %}
{% load tolist %}

{% block title %}
{{ block.super }} - Home
{% endblock title %}

{% block scripts %}
<script>

</script>
{% endblock %}

{% block navbar_buttons %}
{{ block.super }}
{% endblock %}

{% block content %}
<div class="mt-5">
    <section class="download" id="content-section">
        <div class="container mt-5 py-5 _text-light">
            <div class="row">
                <div class="col-md-4 order-last order-md-first">
                    <ul class="nav flex-column">
                        <li class="nav-item mb-2 p-0">{% trans "Installer datails" %}</li>
                        <li class="nav-item mb-2 text-muted p-0">{% trans "Version number" %}: {{ chosen_item.release_version }}</li>
                        <li class="nav-item mb-2 text-muted p-0">{% trans "Released on" %}: {{ chosen_item.release_date }}</li>
                        <li class="nav-item mb-2 text-muted p-0">{% trans "Platform" %}: {{ platform_name }}</li>
                        <li>
                            <hr class="w-75">
                        </li>
                        <li class="nav-item mb-2 text-primary p-0">
                            <strong>{% trans "Major change" %}</strong><br>
                            {{ chosen_item.short_description }}
                        </li>
                        <li>
                            <hr class="w-75">
                        </li>
                    </ul>
                </div>

                <div class="col-md-8 order-first order-md-last">

                    <h2>{% trans "Download Botie for" %} {{ platform_name }}</h2>

                     <form name="serverform" method="post" action="{% url 'download' platform_name|lower package_name|lower chosen_item.id %}">
                         {% csrf_token %}
                         <input type="hidden" name="server_id">

                         {% for item in platforms %}
                         {% if item.name == platform_name %}
                            <a href="{% url 'download' item.name|lower %}" class="badge bg-primary text-decoration-none">{{ item.name }}</a>
                         {% else %}
                            <a href="{% url 'download' item.name|lower %}" class="badge bg-secondary text-decoration-none">{{ item.name }}</a>
                         {% endif %}
                         {% endfor %}

                         <div class="btn-group">
                            <button type="button" class="btn btn-xs btn-primary dropdown-toggle"
                                    data-bs-toggle="dropdown">{% for item in servers %} {% if item.id == chosen_item.server_id_id %} {{ item.name }} {% endif %} {% endfor %}
                            </button>
                            <div class="dropdown-menu">
                                {% for item in servers %}
                                <a class="dropdown-item" onclick="submit_server_change({{ item.id }})">{{ item.name }}</a>
                                {% endfor %}
                            </div>
                         </div>
                    </form>

                    <form method="post" action="{% url 'download' platform_name|lower package_name|lower chosen_item.id 'get' %}">
                        {% csrf_token %}
                        <input type="hidden" name="secret" value="{{ secret }}">
                        <div class="btn-group mt-3 d-block">
                            <button type="submit" class="btn btn-lg btn-primary" onclick="show_info()">{% trans "Download Botie" %} {{ chosen_item.release_version }}</button>
                            <div class="btn-group">
                                <button type="button" class="btn btn-lg btn-danger dropdown-toggle"
                                        data-bs-toggle="dropdown">.{{ package_name|lower }}
                                </button>
                                <div class="dropdown-menu">
                                    {% for item in packages %}
                                    <a class="dropdown-item" href="{% url 'download' platform_name|lower item.name|lower %}">.{{ item|lower }}</a>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </form>

                    <br>

                    <div id="additional_messages"></div>

                </div>
            </div>
            <div class="row">
                <div class="col-md-4 order-last order-md-first">
                    <ul class="nav flex-column">
                        <li class="nav-item mb-2"><a href="#" class="nav-link p-0 active-link"
                                                     data-content="wersji-content">{% trans "About version" %}</a></li>
                        <li class="nav-item mb-2"><a href="#" class="nav-link p-0 text-muted"
                                                     data-content="wymagania-content">{% trans "System requirements" %}</a></li>
                        <li class="nav-item mb-2"><a href="#" class="nav-link p-0 text-muted"
                                                     data-content="instalacji-content">{% trans "Installation guide" %}</a>
                        </li>
                        <li class="nav-item mb-2"><a href="#" class="nav-link p-0 text-muted"
                                                     data-content="inne-content">{% trans "Archive versions" %}</a></li>
                    </ul>
                </div>

                <div class="col-md-8 order-first order-md-last">

                    <div class="info-content">
                        <div class="content" id="wersji-content">
                            {{ chosen_item.full_description|tolist|safe }}
                        </div>
                        <div class="content d-none" id="wymagania-content">
                            {% trans "System requirements" %}:
                            <ul>
                                <li>{% trans "Operating system" %}: {{ chosen_item.requirements.os }}</li>
                                <li>{% trans "Main processor" %}: {{ chosen_item.requirements.processor }}</li>
                                <li>{% trans "RAM memory" %}: {{ chosen_item.requirements.ram }}</li>
                            <li>{% trans "Hard drive empty space" %}: {{ chosen_item.requirements.disk }}</li>
                            <li>{% trans "Graphic card" %}: {{ chosen_item.requirements.graphic }}</li>
                        </ul>
                    </div>

                    <div class="content d-none" id="instalacji-content">
                        Instrukcja Instalacji Aplikacji Botie na Systemie Windows:
                        <ol>
                            <li><strong>Pobierz Instalator:</strong> Przejdź na naszą <a href="link_do_strony">oficjalną
                                stronę internetową</a> lub platformę dystrybucji oprogramowania, aby pobrać
                                najnowszą wersję instalatora aplikacji Botie dla systemu Windows.
                            </li>
                            <li><strong>Uruchom Instalator:</strong> Po pobraniu instalatora, znajdź plik
                                instalacyjny (zazwyczaj z rozszerzeniem .exe) i kliknij dwukrotnie, aby go
                                uruchomić.
                            </li>
                            <li><strong>Wybierz Lokalizację Instalacji:</strong> Podczas instalacji zostanie Cię
                                poproszony o wybór lokalizacji, w której chcesz zainstalować aplikację Botie.
                                Możesz zaakceptować domyślną lokalizację lub wybrać inną.
                            </li>
                            <li><strong>Wybierz Składniki:</strong> Instalator może zapytać, czy chcesz
                                zainstalować dodatkowe składniki lub języki. Wybierz opcje, które Cię
                                interesują, lub pozostaw domyślne ustawienia.
                            </li>
                            <li><strong>Rozpocznij Instalację:</strong> Kliknij przycisk "Instaluj" lub podobny,
                                aby rozpocząć proces instalacji. Może to chwilę potrwać, w zależności od
                                szybkości Twojego komputera.
                            </li>
                            <li><strong>Zakończ Instalację:</strong> Po zakończeniu instalacji, instalator
                                powinien wyświetlić komunikat o sukcesie. Kliknij "Zakończ" lub podobny
                                przycisk, aby zamknąć instalator.
                            </li>
                            <li><strong>Uruchom Aplikację:</strong> Teraz, gdy aplikacja Botie jest
                                zainstalowana, możesz ją uruchomić. Możesz znaleźć skrót na pulpicie lub w menu
                                Start. Kliknij na skrót, aby otworzyć aplikację.
                            </li>
                            <li><strong>Rozpocznij Korzystanie:</strong> Po uruchomieniu aplikacji, zaloguj się
                                lub załóż nowe konto, jeśli to konieczne. Następnie możesz zacząć korzystać z
                                funkcji i cieszyć się inteligentnymi interakcjami z aplikacją Botie!
                            </li>
                        </ol>
                        Uwagi:
                        <ul>
                            <li>Upewnij się, że masz odpowiednie uprawnienia administratora do instalowania
                                oprogramowania na swoim komputerze.
                            </li>
                            <li>Jeśli instalator wymaga, abyś zaakceptował warunki licencji lub inne
                                zobowiązania, zapoznaj się z nimi przed kontynuacją.
                            </li>
                            <li>Pamiętaj, że powyższa instrukcja to tylko ogólny przewodnik. Procedury
                                instalacji mogą się różnić w zależności od konkretnego instalatora i wersji
                                aplikacji Botie.
                            </li>
                        </ul>
                    </div>
                    <div class="content d-none" id="inne-content">
                        <div>
                            <table class="table _text-light">
                                <thead>
                                <th scope="col">{% trans "Version number" %}</th>
                                <th scope="col">{% trans "Released on" %}</th>
                                <th scope="col">{% trans "Major change" %}</th>
                                <th scope="col"></th>
                                </thead>
                                {% for item in download_items %}
                                <tr>
                                    <td>{{ item.release_version }}</td>
                                    <td>{{ item.release_date }}</td>
                                    <td>{{ item.short_description }}</td>
                                    <td>
                                        {% if item.get_download_url %}
                                        <a type="button" class="btn btn-primary"
                                           href="{% url 'download' platform_name|lower package_name|lower item.id %}">{% trans "Details" %}</a>
                                        {% else %}
                                        <!-- Optionally, handle the case where there's no download URL -->
                                        <span>{% trans "not available" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<div class="mt-5 mb-5 divider"></div>
<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function() {
        // Get all navigation links within the content section
        const navLinks = document.querySelectorAll('#content-section .nav-link');

        // Get all content divs within the content section
        const contentDivs = document.querySelectorAll('#content-section .content');

        // Hide all content divs except the first one
        //for (let i = 1; i < contentDivs.length; i++) {
        //    contentDivs[i].style.display = 'none';
        //}

        // Show the content for the first link initially
        //contentDivs[0].style.display = 'block';

        // Add class to the first link to indicate it's active
        navLinks[0].classList.remove('text-muted');
        navLinks[0].classList.add('active-link');

        // Attach click event listener to each navigation link
        navLinks.forEach(link => {
            link.addEventListener('click', function(event) {
                event.preventDefault(); // Prevent default link behavior

                // Hide all content divs
                contentDivs.forEach(contentDiv => {
                    contentDiv.classList.add('d-none');
                });

                // Show the corresponding content based on the clicked link's data-content attribute
                const contentId = link.getAttribute('data-content');
                const targetContentDiv = document.getElementById(contentId);
                targetContentDiv.classList.remove('d-none');

                // Remove active class from all links and then add it to the clicked link
                navLinks.forEach(navLink => {
                    navLink.classList.remove('active-link');
                    navLink.classList.add('text-muted');
                });
                link.classList.remove('text-muted');
                link.classList.add('active-link');
            });
        });
    });

    // Handle server change
    function submit_server_change(sid) {
        document.serverform.server_id.value=sid;
        document.serverform.submit();
    }

    // Auto-dismiss alerts
    setTimeout(function() {
        bootstrap.Alert.getOrCreateInstance(document.querySelector(".alert.alert-dismissible:not([rel=nothide])")).close();
    }, 3000)

    // Show info
    function show_info() {
        setTimeout(function(){
            const alert_code =
            `
                <div class="alert alert-secondary alert-dismissible fade show" role="alert" id="myAlert" rel="nothide">
                    Rozpoczęło się pobieranie pliku instalacyjnego.<br>
                    Informację o postępie i lokalizacji pliku znajdziesz w sekcji pobierania Twojej przeglądarki.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            document.querySelector("#additional_messages").innerHTML = alert_code;
        },1000);
    }
</script>
{% endblock %}