# Generated by Django 4.2.13 on 2025-03-05 21:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0043_merge_20250305_2251'),
    ]

    operations = [
        migrations.AlterField(
            model_name='scenario',
            name='branch_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='branch_nodes', to='db.scenario'),
        ),
        migrations.AlterField(
            model_name='scenario',
            name='previous_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='next', to='db.scenario'),
        ),
        migrations.AlterField(
            model_name='scenario',
            name='root_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='all_nodes', to='db.scenario'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='scenario',
            name='update_lock_by',
            field=models.Char<PERSON>ield(blank=True, null=True),
        ),
    ]
