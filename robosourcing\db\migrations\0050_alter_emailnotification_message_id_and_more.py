# Generated by Django 4.2.13 on 2025-06-25 20:29

import db.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0049_alter_scenario_event_type_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emailnotification',
            name='message_id',
            field=models.IntegerField(choices=[(0, 'Thank you for registering an account'), (1, 'Welcome to our web service'), (2, 'Thank you for making your purchases'), (3, 'Your subscription event completed successfully'), (4, 'Your subscription event has failed'), (5, 'Your payment card expires soon'), (6, 'Your payment card has expired'), (7, 'Contact form submitted'), (8, 'Withdrawal request submitted'), (9, 'Monthly withdrawal summary'), (10, 'Password reset key'), (11, 'Scenario accepted'), (12, 'Sc<PERSON>rio rejected'), (13, 'Newsletter confirmation'), (14, 'Newsletter welcome')]),
        ),
        migrations.AlterField(
            model_name='usersubscription',
            name='renew_date',
            field=models.DateTimeField(default=db.models.get_default_renew_date),
        ),
    ]
