{% extends 'main.html' %}
{% load i18n %}
{% load settings %}
{% load static %}
{% load divide %}

{% block head_title %}{% trans "Payment operator" %}{% endblock %}

{% block title %}
{{ block.super }} - {% trans "Payment operator" %}
{% endblock title %}

{% block scripts %}
{% endblock %}

{% block navbar_buttons %}
{{ block.super }}
{% endblock %}

{% block content %}
<div class="container mt-3">
    <h1>{% trans "Choose a payment operator" %}</h1>
    <form action="{% url 'make_order' %}" method="post">
    {% csrf_token %}
    <input type="hidden" name="mode" value="{{ request.POST.mode }}">
    {% if request.POST.pricelist_item_id %}
    <input type="hidden" name="pricelist_item_id" value="{{ request.POST.pricelist_item_id }}">
    {% endif %}
    {% settings_value 'VIVA_ENABLED' as viva_enabled %}
    {% settings_value 'PAYU_ENABLED' as payu_enabled %}
    {% for item in operators %}
        {% if item.0 == 'PAYU' and payu_enabled or item.0 == 'VIVA' and viva_enabled %}
    <div class="form-check">
      <input class="form-check-input" type="radio" name="operator" value="{{ item.0 }}"{% if not forloop.counter0 %} checked{% endif %}>
      <label class="form-check-label">{{ item.1 }}</label>
    </div>
        {% endif %}
    {% endfor %}
    {% settings_value 'SKIP_PAYMENT_ENABLED' as skip_payment_enabled %}
    {% if skip_payment_enabled %}
    <div class="form-check">
      <input class="form-check-input" type="radio" name="operator" value="SKIP">
      <label class="form-check-label">{% trans 'skip payment (development only!)' %}</label>
    </div>
    {% endif %}
    <input type="submit" class="btn btn-primary mt-3" value="{% trans 'Continue payment' %}" onclick="disableBtn(this)">
    </form>
</div>
<div class="mt-5 mb-5 divider"></div>
<script>
function disableBtn(el) {
    setTimeout(function(){
        $(el).attr('disabled', 'disabled');
    }, 1)
}
</script>
{% endblock %}