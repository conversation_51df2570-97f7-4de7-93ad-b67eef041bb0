from django.contrib import admin
from .models import Robot

@admin.register(Robot)
class RobotAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner_display', 'locked', 'lock_reason', 'cached_credits', 'l_cached_credits',
                    'l_package_credits', 'l_subscription_credits', 'min_buffer', 'create_time', 'last_activ_time']
    list_per_page = 20

    def owner_display(self, obj):
        if not obj.owner:
            return None  # Lub 'Brak właściciela'
        # Pobieramy pełne imię i nazwisko
        full_name = f'{obj.owner.first_name} {obj.owner.last_name}'.strip()
        if full_name:
            return full_name
        # <PERSON><PERSON><PERSON> brak imienia i nazwiska, zwracamy nazwę użytkownika
        return obj.owner.username

    owner_display.short_description = 'Assigned to account'
