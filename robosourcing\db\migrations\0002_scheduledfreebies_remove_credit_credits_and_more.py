# Generated by Django 4.1.7 on 2024-06-04 19:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('store', '0005_alter_order_price_alter_product_price'),
        ('db', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScheduledFreebies',
            fields=[
                ('active', models.BooleanField(default=True)),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('update_counter', models.PositiveIntegerField(default=0)),
                ('max_update_counter', models.PositiveIntegerField(default=0)),
                ('user', models.OneToOneField(editable=False, on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('value', models.PositiveIntegerField()),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='credit',
            name='credits',
        ),
        migrations.RemoveField(
            model_name='credit',
            name='locked_credits',
        ),
        migrations.AddField(
            model_name='credit',
            name='l_package_credits',
            field=models.PositiveBigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='credit',
            name='l_subscription_credits',
            field=models.PositiveBigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='credit',
            name='package_credits',
            field=models.PositiveBigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='credit',
            name='subscription_credits',
            field=models.PositiveBigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='robot',
            name='l_cached_credits',
            field=models.PositiveBigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='robot',
            name='l_package_credits',
            field=models.PositiveBigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='robot',
            name='l_subscription_credits',
            field=models.PositiveBigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='usersubscription',
            name='max_update_counter',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='usersubscription',
            name='subscription',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='store.product'),
        ),
        migrations.AlterField(
            model_name='usersubscription',
            name='price',
            field=models.DecimalField(decimal_places=2, max_digits=99),
        ),
    ]
