{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load crispy_forms_filters %}

{% block title %}{{ block.super }} - {% trans "Profile" %}{% endblock %}

{% block extra_content_1 %}

<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name active">{% trans "Profile" %}</li>
        </ol>
      </nav>
    <div class="row">
        <div class="col-lg-8 col-md-12 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h2 class="dashboard-header">{% trans "My Profile" %}</h2>
                
                <div class="row g-2 mb-4 ms-4 me-4">
                    <!-- LOGIN -->
                    <div class="col-lg-4 col-md-6 col-sm-12 mb-4 mt-4">
                        <h6>LOGIN</h6>
                        <p><strong>{{ profil.user.username }}</strong></p>
                    </div>
                    <!-- E-MAIL -->
                    <div class="col-lg-4 col-md-6 col-sm-12 mb-4 mt-4">
                        <h6>E-MAIL</h6>
                        <p><strong>{{ profil.user.email }}</strong></p>
                    </div>
                    <!-- ACCOUNT TYPE -->
                    <div class="col-lg-4 col-md-6 col-sm-12 mb-4 mt-4">
                        <h6>{% trans "ACCOUNT TYPE" %}</h6>
                        {% with role_display=profil.get_role_display %}
                            <p><strong>{{ role_display }}</strong></p>
                        {% endwith %}
                    </div>
                    <!-- NAME -->
                    <div class="col-lg-4 col-md-6 col-sm-12 mb-4 mt-4">    
                        <h6>{% trans "NAME" %}</h6>
                        <p>
                            {% if profil.user.first_name %}
                                <strong>{{ profil.user.first_name }} {{ profil.user.last_name }}</strong>
                            {% else %}
                                <span class="small">{% trans "add first and last name" %}</span>
                            {% endif %}
                        </p>
                        {% if profil.user.first_name %}
                            <!-- Jeśli imię i nazwisko istnieją, pokaż przycisk edycji -->
                            <button class="btn btn-outline-primary btn-outline-primary-db" data-bs-toggle="modal" data-bs-target="#nameModal">
                                <i class="fas fa-pencil-alt"></i>
                            </button>
                        {% else %}
                            <!-- Jeśli brak imienia i nazwiska, pokaż przycisk dodawania -->
                            <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#nameModal">
                                <i class="fas fa-plus"></i>
                            </button>
                        {% endif %}
                    </div>
                    <!-- PHONE NUMBER -->
                    <div class="col-lg-4 col-md-6 col-sm-12 mb-4 mt-4">
                        <h6>{% trans "PHONE NUMBER" %}</h6>
                        <p>
                            {% if profil.contact and profil.contact.phone_number %}
                                {{ profil.contact.area_code }}<strong> {{ profil.contact.phone_number }}</strong>
                            {% else %}
                                <span class="small">{% trans "add phone number" %}</span>
                            {% endif %}
                        </p>
                        {% if profil.contact and profil.contact.phone_number %}
                            <!-- Jeśli numer telefonu istnieje, pokaż przycisk edycji -->
                            <button class="btn btn-outline-primary btn-outline-primary-db" data-bs-toggle="modal" data-bs-target="#phoneNumberModal">
                                <i class="fas fa-pencil-alt"></i>
                            </button>
                        {% else %}
                            <!-- Jeśli brak numeru telefonu, pokaż przycisk dodawania -->
                            <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#phoneNumberModal">
                                <i class="fas fa-plus"></i>
                            </button>
                        {% endif %}
                    </div>
                    <!-- ADDRESS -->
                    <div class="col-lg-4 col-md-6 col-sm-12 mb-4 mt-4">
                        <h6>{% trans "ADDRESS" %}</h6>
                        <p>
                            {% if profil.address %}
                                <strong>
                                    {{ profil.address.street }} {{ profil.address.street_number }}
                                    {% if profil.address.home_number %}
                                        / {{ profil.address.home_number }}
                                    {% endif %}
                                    <br>
                                    {{ profil.address.postal_code }} {{ profil.address.city }}
                                </strong>
                            {% else %}
                                <span class="small">{% trans "add address" %}</span>
                            {% endif %}
                        </p>
                        {% if profil.address %}
                            <!-- Jeśli adres istnieje, pokaż przycisk edycji -->
                            <button class="btn btn-outline-primary btn-outline-primary-db" data-bs-toggle="modal" data-bs-target="#addressModal">
                                <i class="fas fa-pencil-alt"></i>
                            </button>
                        {% else %}
                            <!-- Jeśli brak adresu, pokaż przycisk dodawania -->
                            <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#addressModal">
                                <i class="fas fa-plus"></i>
                            </button>
                        {% endif %}
                    </div>
                </div>           
            </div>
        </div>
        
        <!-- INVOICE DATA -->
        <div class="col-lg-4 col-md-12 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h2 class="dashboard-header">{% trans "Invoice Data" %}</h2>
                <div class="row g-2 mb-4 ms-4 me-4">
                    <div class="col-lg-12 col-md-6 col-sm-12 mb-4 mt-4">
                        {% if profil.organization_uuid %}
                            <h6>{% trans "TAX NUMBER" %}</h6>
                            <p><strong>{{ profil.organization_uuid.tax_id }}</strong></p>
                            <h6>{% trans "COMPANY NAME" %}</h6>
                            <p><strong>{{ profil.organization_uuid.name_1 }}<br>{% if profil.organization_uuid.name_2 %} {{ profil.organization_uuid.name_2 }} {% endif %}</strong></p>
                            <h6>{% trans "ADDRESS" %}</h6>
                            <p><strong>{{ profil.organization_uuid.street }} {{ profil.organization_uuid.street_number }}
                                {% if profil.organization_uuid.home_number %}
                                    / {{ profil.organization_uuid.home_number }}
                                {% endif %}
                                <br>
                                {{ profil.organization_uuid.postal_code }} {{ profil.organization_uuid.city }}</strong>
                            </p>

                            <div>
                                <!-- Przycisk edycji danych organizacji -->
                                <button type="button" class="btn btn-outline-primary btn-outline-primary-db" data-bs-toggle="modal" data-bs-target="#orgModal">
                                    <i class="fas fa-pencil-alt"></i>
                                </button>

                                <!-- Formularz usuwania danych organizacji -->
                                <form method="post" action="{% url 'profile' %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-danger" name="submit_organization_remove" onclick="showConfirmDialog(event ,'removeOrganizationData', war)">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </form>
                            </div>
                        {% else %}
                            <!-- Jeśli brak danych organizacji, pokaż przycisk dodawania -->
                            <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#orgModal">
                                <i class="fas fa-plus"></i>
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- CONSENT SETTINGS -->
    <div class="dashboard-element">
        <h2 class="dashboard-header">{% trans "Consent settings" %}</h2>
        <div class="mb-4 ms-4 me-4">
            <form method="POST" action="{% url 'profile' %}">
                {% csrf_token %}
                {% for consent_type, consent_list in consent_types.items %}
                    {% if consent_list %}
                        <div class="mb-4">
                            {% if consent_type == 'general' %}
                                <h3>{% trans "General consent" %}</h3>
                            {% elif consent_type == 'user' %}
                                <h3>{% trans "User consent" %}</h3>
                            {% else %}
                                <h3>{% trans "Moderator consent" %}</h3>
                            {% endif %}
                            <div class="row text-center">
                                <div class="col-lg-6 col-9"></div>
                                <div class="col-lg-2 col-1">{% trans "E-mail" %}</div>
                                <div class="col-lg-2 col-1">{% trans "Application" %}</div>
                                <div class="col-lg-2 col-1">{% trans "Phone / Text messages" %}</div>
                            </div>
                            {% for consent, form in consent_list %}
                                <div class="row align-items-center _text-light mb-3">
                                    <div class="col-lg-6 col-9">
                                        <p class="mb-0">
                                            {{ consent.description }}
                                            <!-- Przycisk rozwijający dodatkowy opis -->
                                            {% if consent.additional_description %}
                                                <a href="#" class="text-primary" data-bs-toggle="collapse" data-bs-target="#collapseAddDesc{{ consent.id }}" aria-expanded="false" aria-controls="collapseAddDesc{{ consent.id }}">
                                                    <i class="fas fa-chevron-down"></i>
                                                </a>
                                            {% endif %}
                                        </p>
                                        <!-- Dodatkowy opis w collapsie -->
                                        <div class="collapse" id="collapseAddDesc{{ consent.id }}">
                                            <p class="mt-2 text-muted">{{ consent.additional_description }}</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-1 text-center">
                                        {% if consent.email_required %}{{ form.email_consent }}{% endif %}
                                    </div>
                                    <div class="col-lg-2 col-1 text-center">
                                        {% if consent.app_required %}{{ form.app_consent }}{% endif %}
                                    </div>
                                    <div class="col-lg-2 col-1 text-center">
                                        {% if consent.phone_sms_required %}{{ form.phone_sms_consent }}{% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endfor %}
                <div class="text-center">
                    <button type="submit" name="submit_consent" class="btn btn-primary btn-dashboard">{% trans "Submit" %}</button>
                </div>
            </form>
        </div>
    </div>
    
    
</div>

<!-- Skrypty JavaScript -->
<script>
    function toggleDescription(descId) {
        var description = document.getElementById(descId);
        if (description.style.display === "none") {
            description.style.display = "block";
        } else {
            description.style.display = "none";
        }
    }
    function removeOrganizationData() {
        // Wysyłanie formularza programowo
        var el = document.getElementsByName('submit_organization_remove')[0];
        var hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = el.name;
        el.form.appendChild(hiddenInput);
        el.form.submit();
    }
    function showConfirmDialog(e, feedback, icon) {
        // Zapobieganie domyślnej akcji
        e.preventDefault();
        // Budowanie dialogu
        var params = [
            "{% trans "Are you sure you want to continue?" %}",
            "{% trans "Remove operation can't be undone!" %}",
            icon, // succ, err, war, inf
            'OK',
            'Cancel',
            feedback,
            'close_qual'
        ]
        if (current_theme_mode.replace('auto-','') =='dark') Qual.confirmd(...params); else Qual.confirm(...params);
    }
</script>
{% endblock %}
