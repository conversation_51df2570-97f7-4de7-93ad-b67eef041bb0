# Generated by Django 4.2.13 on 2025-06-25 19:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('front', '0013_carouselitem_background_image_desktop_dark_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewsletterSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('is_confirmed', models.BooleanField(default=False)),
                ('confirmation_token', models.CharField(max_length=64, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('confirmed_at', models.DateTimeField(blank=True, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('marketing_consent', models.<PERSON><PERSON>anField(default=False, verbose_name='Zgoda na marketing')),
                ('data_processing_consent', models.BooleanField(default=False, verbose_name='Zgoda na przetwarzanie danych')),
            ],
            options={
                'verbose_name': 'Subskrypcja newslettera',
                'verbose_name_plural': 'Subskrypcje newslettera',
                'ordering': ['-created_at'],
            },
        ),
    ]
