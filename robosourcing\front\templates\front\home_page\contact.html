{% extends 'main.html' %}
{% load i18n %}
{% load static %}

{% block head_title %}{% trans "Contact" %}{% endblock %}

{% block title %}
{{ block.super }} - {% trans "Contact" %}
{% endblock %}

{% block content %}
<div class="container mt-3">
    <div class="container section-title fade-in pb-2">
        <h2>{% trans "Contact Us" %}</h2>
        
    </div>

    <form id="scenarioForm" method="post" action="{% url 'contact' %}" enctype="multipart/form-data" class="mt-3">
        {% csrf_token %}

        <!-- 'Your Name' and 'Your Email' fields are now inside the form -->
        <div>
            <label for="name" class="form-label">{% trans "Your Name" %}</label>
            <input type="text" class="form-control" id="name" name="name" oninput="updateGreeting(); checkFormValidity();" required>
        </div>
        <div class="mt-3">
            <label for="email" class="form-label">{% trans "Your Email" %}</label>
            <input type="email" class="form-control" id="email" name="email" oninput="updateContactDetails(); checkFormValidity();" required>
        </div>

        <p class="mt-3" id="greeting">{% trans "Hello, how can we help you today?" %}</p>
        <h4 class="mt-3">{% trans "Subject:" %}</h4>

        <div class="row">
            <!-- Your existing tiles -->
            <!-- Tile 1 -->
            <div class="col-md-3 col-sm-6 mt-2">
                <div class="tile card h-100" data-choice="custom_scenario">
                    <div class="card-body text-center">
                        <i class="fa fa-lightbulb fa-2x"></i>
                        <p class="card-text">{% trans "I need a custom scenario" %}</p>
                    </div>
                </div>
            </div>
            <!-- Tile 2 -->
            <div class="col-md-3 col-sm-6 mt-2">
                <div class="tile card h-100" data-choice="existing_scenario">
                    <div class="card-body text-center">
                        <i class="fa fa-copy fa-2x"></i>
                        <p class="card-text">{% trans "I need a custom scenario based on an existing one" %}</p>
                    </div>
                </div>
            </div>
            <!-- Tile 3 -->
            <div class="col-md-3 col-sm-6 mt-2">
                <div class="tile card h-100" data-choice="technical_support">
                    <div class="card-body text-center">
                        <i class="fa fa-wrench fa-2x"></i>
                        <p class="card-text">{% trans "I need technical support" %}</p>
                    </div>
                </div>
            </div>
            <!-- Tile 4 -->
            <div class="col-md-3 col-sm-6 mt-2">
                <div class="tile card h-100" data-choice="other">
                    <div class="card-body text-center">
                        <i class="fa fa-question-circle fa-2x"></i>
                        <p class="card-text">{% trans "Something else..." %}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dynamic fields will be inserted here -->
        <div id="dynamicFields"></div>

        <!-- New section for file uploads -->
        <div class="form-group mt-3" id="fileUploadSection" style="display: none;">
            <label for="files">{% trans "Upload files (max 5 files, 10MB total)" %}</label>
            <input type="file" name="files" id="files" class="form-control" multiple>
        </div>

        <button type="submit" id="submitButton" class="btn btn-primary mt-3" disabled>
            {% trans "Submit" %}
        </button>
    </form>
</div>

<div class="mt-5 mb-5 divider"></div>

<script>
// Zmienne przekazane z Django
const scenarioName = "{{ scenario_name|default:'' }}";
const scenarioSid = "{{ scenario_sid|default:'' }}";
const defaultSelection = "{{ default_selection|default:'custom_scenario' }}";

document.addEventListener('DOMContentLoaded', function() {
    // Automatycznie wybierz pierwszy kafelek (custom_scenario) przy ładowaniu strony
    const defaultTile = document.querySelector(`[data-choice="${defaultSelection}"]`);
    if (defaultTile) {
        defaultTile.classList.add('selected');
        showForm(defaultSelection);
    }
});

document.querySelectorAll('.tile').forEach(tile => {
    tile.addEventListener('click', function() {
        document.querySelectorAll('.tile').forEach(t => t.classList.remove('selected'));
        this.classList.add('selected');
        showForm(this.getAttribute('data-choice'));
    });
});

function updateGreeting() {
    const name = document.getElementById('name').value;
    document.getElementById('greeting').textContent = name ? `{% trans "Hello" %} ${name}, {% trans "how can we help you today?" %}` : '{% trans "Hello, how can we help you today?" %}';
}

function updateContactDetails() {
    const contactPreference = document.getElementById('contactPreference');
    if (contactPreference && contactPreference.value === 'email') {
        const emailField = document.getElementById('email');
        const contactDetails = document.getElementById('contactDetails');
        if (contactDetails) {
            contactDetails.value = emailField.value;
        }
    }
    checkFormValidity();
}

function showForm(choice) {
    const dynamicFields = document.getElementById('dynamicFields');
    const fileUploadSection = document.getElementById('fileUploadSection');  // Referencja do sekcji plików
    dynamicFields.innerHTML = '';  // Clear previous dynamic fields
    fileUploadSection.style.display = 'none';  // Domyślnie ukryj sekcję uploadu plików

    let fields = '';

    if (choice === 'custom_scenario' || choice === 'existing_scenario') {
        fields += `
            <div class="form-group mt-3">
                <label for="scenarioName">{% trans "Suggested scenario name:" %}</label>
                <input type="text" id="scenarioName" name="scenario_name" class="form-control" required>
            </div>
            <div class="form-group mt-3">
                <label for="scenarioDesc">{% trans "Description of the scenario:" %}</label>
                <textarea id="scenarioDesc" name="description" class="form-control" required></textarea>
            </div>
        `;
        if (choice === 'existing_scenario') {
            fields += `
                <div class="form-group mt-3">
                    <label for="baseScenario">{% trans "Base scenario name or link:" %}</label>
                    <input type="text" id="baseScenario" name="base_scenario" class="form-control" required>
                </div>
            `;
        } else {
            fields += `<input type="hidden" name="base_scenario" value="">`;
        }

        fileUploadSection.style.display = 'block';  // Pokaż sekcję uploadu plików
    } else if (choice === 'technical_support') {
        fields += `
            <div class="form-group mt-3">
                <label for="issueDesc">{% trans "Describe your issue or query:" %}</label>
                <textarea id="issueDesc" name="description" class="form-control" required></textarea>
            </div>
            <div class="form-group mt-3">
                <label for="appVersion">{% trans "App Version (optional):" %}</label>
                <input type="text" id="appVersion" name="app_version" class="form-control">
            </div>
            <input type="hidden" name="scenario_name" value="">
            <input type="hidden" name="base_scenario" value="">
        `;

        fileUploadSection.style.display = 'block';  // Pokaż sekcję uploadu plików
    } else {
        fields += `
            <div class="form-group mt-3">
                <label for="issueDesc">{% trans "Describe your issue or query:" %}</label>
                <textarea id="issueDesc" name="description" class="form-control" required></textarea>
            </div>
            <input type="hidden" name="scenario_name" value="">
            <input type="hidden" name="base_scenario" value="">
        `;
    }

    fields += `
        <div class="form-group mt-3">
            <label for="contactPreference">{% trans "Contact preference:" %}</label>
            <select id="contactPreference" name="contact_preference" class="form-control" onchange="adjustContactField(); checkFormValidity();" required>
                <option value="email" selected>Email</option>
                <option value="phone">Phone</option>
                <option value="whatsapp">WhatsApp</option>
                <option value="skype">Skype</option>
            </select>
        </div>
        <div class="form-group mt-3">
            <label for="contactDetails">{% trans "Contact details:" %}</label>
            <input type="text" id="contactDetails" name="contact_info" class="form-control" required>
        </div>
        <input type="hidden" name="message_type" value="${choice}">
    `;

    dynamicFields.innerHTML = fields;

    // Pre-fill contact details if preference is email
    adjustContactField();

    // Wypełnij pola danymi z URL jeśli są dostępne
    if (choice === 'custom_scenario' && scenarioName && scenarioSid) {
        const scenarioNameField = document.getElementById('scenarioName');
        const scenarioDescField = document.getElementById('scenarioDesc');

        if (scenarioNameField) {
            scenarioNameField.value = scenarioName;
        }
        if (scenarioDescField) {
            scenarioDescField.value = `SID: ${scenarioSid}\n\n`;
        }
    }

    // Add event listeners for validation
    const requiredFields = document.querySelectorAll('#scenarioForm [required]');
    requiredFields.forEach(field => {
        field.addEventListener('input', checkFormValidity);
    });
    checkFormValidity();
}

function adjustContactField() {
    const preference = document.getElementById('contactPreference').value;
    const contactDetails = document.getElementById('contactDetails');
    const emailField = document.getElementById('email');

    if (preference === 'email') {
        contactDetails.value = emailField.value;
        contactDetails.readOnly = true;  // Make field read-only when email is selected
    } else {
        contactDetails.value = '';
        contactDetails.readOnly = false; // Allow editing for other contact methods
    }
    checkFormValidity();
}

function checkFormValidity() {
    const form = document.getElementById('scenarioForm');
    const submitButton = document.getElementById('submitButton');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            isValid = false;
        }
    });

    submitButton.disabled = !isValid;
}
</script>
{% endblock %}
