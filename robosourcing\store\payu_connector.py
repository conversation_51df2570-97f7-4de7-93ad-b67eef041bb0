import json
from datetime import datetime

import requests
from hashlib import md5
import logging
import ipaddress

from dateutil.relativedelta import relativedelta
# from django.utils.crypto import md5
# from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from robosourcing.settings import STORE_URL, PAYU_USE_SANDBOX
from db.mailer import Mailer
from db.models import EmailNotification, UserSubscription
from store.models import SubsequentOrder


class SingletonMeta(type):
    """
    The Singleton metaclass.
    """

    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]


class PayUConnector(metaclass=SingletonMeta):
    """
    A class containing methods for handling payments via a website and API functions provided by the PayU operator
    """

    mailer = Mailer()

    if PAYU_USE_SANDBOX:
        # sandbox settings
        _PAYU_SERVER_URL = 'https://secure.snd.payu.com'
        # _PAYU_CLIENT_ID = '477489'
        # _PAYU_SECRET = '********************************'
        _PAYU_CLIENT_ID = '484830'
        _PAYU_SECRET = '********************************'
        _NOTIFY_IPS = ['************', '************', '************', '************', '************', '************']
    else:
        # production settings
        _PAYU_SERVER_URL = 'https://secure.payu.com'
        _PAYU_CLIENT_ID = '4333276'
        _PAYU_SECRET = '********************************'
        _NOTIFY_IPS = ['************', '************', '************', '************', '************', '************']

    # FIXME: development only (remove following line)
    _NOTIFY_IPS.append('127.0.0.1')

    _headers = None
    _logger = None

    # ********************************************
    def __init__(self):
        # init logger
        self._logger = logging.getLogger('PAYU')
        self._logger.setLevel(logging.DEBUG)
        self._file_handler = logging.FileHandler('payu.log')
        self._file_handler.setLevel(logging.INFO)
        self._file_handler.setFormatter(logging.Formatter('%(asctime)s | %(levelname)s | %(message)s'))
        self._logger.addHandler(self._file_handler)

    @staticmethod
    # ********************************************
    def _print_json(code):
        """
        Prints out JSON formatted code
        """
        print(json.dumps(code, indent=4))

    # ********************************************
    @staticmethod
    def _get_client_ip(req):
        """
        Extract the client IP address from the request.

        :param req: The HTTP request object.
        :return: IP address as a string.
        """
        x_forwarded_for = req.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = req.META.get('REMOTE_ADDR')
        return ip

    # ********************************************
    def _make_request_without_redirs(self, url, headers=None, data=None, json_data=None, stop_code=200):
        """
        Makes request redirectios step by step until expected status code is reached.
        """
        # make single request without redirections
        if json_data:
            response = requests.post(url, headers=headers, json=json_data, allow_redirects=False)
        else:
            response = requests.post(url, headers=headers, data=data, allow_redirects=False)
        # print(response.status_code)
        # print(response.content)
        # check status code
        if response.status_code == stop_code:
            return response
        elif 'Location' in response.headers:
            # print(str(response.status_code) + '->' + response.headers['Location'])
            return self._make_request_without_redirs(response.headers['Location'], headers=headers, data=data,
                                                     json_data=json_data, stop_code=stop_code)
        else:
            # print('no location')
            return None

    # ********************************************
    def login(self):
        # auth
        data = {'grant_type': 'client_credentials', 'client_id': self._PAYU_CLIENT_ID,
                'client_secret': self._PAYU_SECRET}
        resp = requests.post(f'{self._PAYU_SERVER_URL}/pl/standard/user/oauth/authorize', data=data)
        # print(resp)
        obj = resp.json()
        # self._print_json(obj)
        self._headers = {'Authorization': f'{obj["token_type"]} {obj["access_token"]}',
                         'Content-Type': 'application/json'}

    # ********************************************
    def get_payment_methods(self, user, personalized=False) -> json:
        # get headers
        if personalized:
            self._logger.info(f'{user.id=}')
            self._logger.info(f'{user.email=}')
            # auth
            data = {'grant_type': 'trusted_merchant', 'client_id': self._PAYU_CLIENT_ID,
                    'client_secret': self._PAYU_SECRET, 'email': user.email, 'ext_customer_id': user.id}
            resp = requests.post(f'{self._PAYU_SERVER_URL}/pl/standard/user/oauth/authorize', data=data)
            # print(resp.status_code)
            # print(resp.content)
            obj = resp.json()
            self._print_json(obj)
            headers = {'Authorization': f'{obj["token_type"]} {obj["access_token"]}',
                       'Content-Type': 'application/json'}
        else:
            headers = self._headers
        self._logger.info(f'{headers=}')
        # get methods
        data = {}
        query = '/?lang=pl'
        resp = requests.get(f'{self._PAYU_SERVER_URL}/api/v2_1/paymethods'+query, headers=headers, data=data)
        # print(resp)
        obj = resp.json()
        # self._print_json(obj)
        return obj

    def get_card_token(self, user, details, expire_weeks=None) -> tuple:

        now = datetime.now()

        print(f'{details=}')

        # get payment methods (incl. user card tokens)
        obj = self.get_payment_methods(user, personalized=True)
        # obj = self.get_payment_methods(user, personalized=False)
        self._print_json(obj)
        self._logger.info(obj)

        token = None

        if 'cardTokens' in obj.keys():
            if details:
                for ct in obj['cardTokens']:
                    if details.find(ct['cardNumberMasked']) > -1:
                        token = ct
                        break
            else:
                for ct in obj['cardTokens']:
                    if ct['preferred']:
                        token = ct
                        break
                if not token:
                    for ct in obj['cardTokens']:
                        if ct['status'] == 'ACTIVE':
                            token = ct
                            break

        if not token:
            return None, None

        if not expire_weeks:
            return token, None

        # check if card expires soon
        expire = datetime(token['cardExpirationYear'], token['cardExpirationMonth'], 1)
        expire += relativedelta(month=1)
        diff = relativedelta(expire, now)
        if 0 <= diff.days <= expire_weeks * 7:
            return token, True

        return token, False

    # ********************************************
    def create_order(self, req, basket_items) -> str | None:

        # prepare_order_data
        from store.store_utils import StoreUtils
        su = StoreUtils()
        products, num, total_price, tax, total_price_incl_tax, desc, vdesc = \
            su.prepare_order_data(basket_items)
        data = {
          "continueUrl": STORE_URL + "/store/continue",
          "notifyUrl": STORE_URL + "/store/notify",
          "customerIp": self._get_client_ip(req),
          "merchantPosId": self._PAYU_CLIENT_ID,
          "description": desc,
          # "additionalDescription": "additionalDescription",
          "visibleDescription": vdesc,  # 0-80 chars
          # "statementDescription": "statementDescription",
          # "extOrderId": "string",
          "currencyCode": "PLN",
          "totalAmount": int(total_price_incl_tax*100),
          # "validityTime": "100000",
          # "cardOnFile": "FIRST",
          # "recurring": "FIRST",
          # "donation": {
          #   "amount": 500,
          #   "organizationId": "string"
          # },
          "buyer": {
            "extCustomerId": req.user.id,
            "email": req.user.email,
            # "phone": "+48 *********",
            "firstName": req.user.first_name,
            "lastName": req.user.last_name,
            # "nin": *********,
            "language": "pl" if req.LANGUAGE_CODE == "pl" else "en",
            # "delivery": {
            #   "street": "string",
            #   "postalBox": "string",
            #   "postalCode": "string",
            #   "city": "string",
            #   "state": "30",
            #   "countryCode": "string",
            #   "name": "string",
            #   "recipientName": "string",
            #   "recipientEmail": "string",
            #   "recipientPhone": "string"
            # }
          },
          # "shoppingCarts": [
          #   {
          #     "extCustomerId": "ext-customer-1",
          #     "amount": 24950,
          #     "fee": 250,
          #     "shippingMethods": [
          #       {
          #         "country": "PL",
          #         "price": 250,
          #         "name": "Postal package"
          #       }
          #     ],
          #     "products": [
          #       {
          #         "name": "My product",
          #         "unitPrice": 999,
          #         "quantity": 4,
          #         "virtual": True
          #       }
          #     ]
          #   }
          # ],
          "products": products,
          # "payMethods": {
          #   "payMethod": {
          #     "type": "PBL",
          #     "value": "c",
          #     "authorizationCode": "777123",
          #     "authorizationType": "PRE_AUTHORIZATION",
          #     "card": {
          #       "number": "****************",
          #       "expirationMonth": "02",
          #       "expirationYear": "29",
          #       "cvv": "123",
          #       "firstTransactionId": "MCC0111LL1121"
          #     },
          #     "specificData": [
          #       {
          #         "name": "string",
          #         "value": "string"
          #       }
          #     ],
          #     "amount": "string",
          #     "blikData": {
          #       "aliasLabelProposal": "string",
          #       "registerTokenValue": "string",
          #       "register": True,
          #       "appKey": "string",
          #       "recommendedAuthLevel": "NO_CONFIRMATION"
          #     },
          #     "threeDsData": {
          #       "status3Ds": "Y",
          #       "status3DsDescription": "Authentication successful",
          #       "xid": "string",
          #       "dsTransactionId": "3b31b19d-1c06-4ea4-a85a-00af10c66588",
          #       "eciCode": 5,
          #       "cavv": "AAABBBEAUAAAABgICABQAAAAAAA="
          #     }
          #   }
          # },
          # "mcpData": {
          #   "mcpCurrency": "EUR",
          #   "mcpAmount": 10000,
          #   "mcpRate": 4.2556,
          #   "mcpFxTableId": 132331,
          #   "mcpPartnerId": "6283a549-8b1a-430d-8a62-eea64327440e"
          # },
          # "threeDsAuthentication": {
          #   "challangeRequested": "YES",
          #   "exemption": {
          #     "value": "LOW_RISK",
          #     "rejectionHandling": "PERFORM_AUTHENTICATION",
          #     "riskScore": "120 - low risk score calculated in Merchants antifraud tool"
          #   },
          #   "browser": {
          #     "acceptHeaders": "string",
          #     "requestIP": "127.0.0.1",
          #     "screenWidth": 1920,
          #     "javaEnabled": False,
          #     "timezoneOffset": 1,
          #     "screenHeight": 1280,
          #     "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36",
          #     "colorDepth": 32,
          #     "language": "pl_PL"
          #   },
          #   "sdk": {
          #     "sdkReferenceNumber": "DS_LOA_SDK_ADBV_739485_94783",
          #     "sdkMaxTimeout": 30,
          #     "sdkAppID": "9063b12c-fcde-43c7-b28e-8d0af5520e8a",
          #     "sdkEncData": "9063b12c-fcde-43c7-b28e-8d0af5520e8a",
          #     "sdkTransID": "b60c9879-ac77-4918-a317-7b01c4317053/8Q==.",
          #     "sdkEphemPubKey": {
          #       "y": "MRy7wofaw77myc7hZef23hmmEOE",
          #       "x": "MRy7wofaw77myc7hZef23hmmEOE",
          #       "kty": "EC",
          #       "crv": "P-256"
          #     }
          #   },
          #   "merchantRiskIndicator": {
          #     "orderType": "LOAN",
          #     "shipIndicator": "TICKETS",
          #     "preOrdered": False,
          #     "preOrderedDate": "2019-03-27T10:57:59.000+01:00",
          #     "deliveryTimeFrame": "OVERNIGHT",
          #     "reordered": False,
          #     "merchantFunds": {
          #       "amount": 100,
          #       "currencyCode": "PLN"
          #     }
          #   },
          #   "recurring": {
          #     "frequency": "7",
          #     "expiry": "2025-03-27T00:00:00.000Z"
          #   },
          #   "cardholder": {
          #     "name": "John Doe",
          #     "accountInformation": {
          #       "createDate": "2019-03-27T10:57:59.000+01:00",
          #       "suspiciousActivity": False,
          #       "deliveryAddressFirstUsedDate": "2019-03-27T10:57:59.000+01:00",
          #       "deliveryAdressUsageIndicator": "THIS_TRANSACTION",
          #       "pastOrdersYear": 46,
          #       "pastOrdersDay": 125,
          #       "purchasesLastSixMonths": 12,
          #       "changeDate": "2019-03-27T10:57:59.000+01:00",
          #       "changeIndicator": "THIS_TRANSACTION",
          #       "passwordChanged": "2019-03-27T10:57:59.000+01:00",
          #       "passwordChangeIndicator": "NO_CHANGE",
          #       "nameToRecipientMatch": True,
          #       "addCardAttemptsDay": 3,
          #       "authMethod": "GUEST",
          #       "authDateTime": "2019-03-27T10:57:59.000+01:00",
          #       "cardAddedDate": "2019-03-27T10:57:59.000+01:00",
          #       "cardAddedIndicator": "THIS_TRANSACTION"
          #     },
          #     "billingAddress": {
          #       "street": "Test Street",
          #       "postalCode": "01-000",
          #       "city": "Test City",
          #       "state": 30,
          #       "countryCode": "PL"
          #     }
          #   }
          # },
          # "credit": {
          #   "shoppingCarts": [
          #     {
          #       "shippingMethod": {
          #         "type": "COURIER",
          #         "price": "string",
          #         "address": {
          #           "pointId": "Parcel locker POZ29A",
          #           "street": "string",
          #           "streetNo": "string",
          #           "flatNo": "string",
          #           "postalCode": "string",
          #           "city": "string",
          #           "countryCode": "string"
          #         }
          #       },
          #       "products": [
          #         {
          #           "name": "string",
          #           "unitPrice": "string",
          #           "quantity": "string",
          #           "virtual": True,
          #           "listingDate": "string"
          #         }
          #       ],
          #       "extCustomerId": "string"
          #     }
          #   ],
          #   "applicant": {
          #     "email": "<EMAIL>",
          #     "phone": *********,
          #     "firstName": "Joe",
          #     "lastName": "Doe",
          #     "language": "pl",
          #     "nin": *********,
          #     "address": {
          #       "street": "Test Street",
          #       "streetNo": 123,
          #       "flatNo": 987,
          #       "postalCode": "01-000",
          #       "city": "Test City",
          #       "countryCode": "PL"
          #     },
          #     "additionalInfo": {
          #       "hasSuccessfullyFinishedOrderInShop": "YES"
          #     }
          #   }
          # },
          # "submerchant": {
          #   "id": "string"
          # },
          # "deviceFingerprint": "3e9fae0b3e6003a4093358167bc1079e"
        }

        # self._print_json(data)
        # print(self._headers)

        # works but reads final html response (code 200) unnecessarily
        # resp = requests.post(f'{self._PAYU_SERVER_URL}/api/v2_1/orders', headers=self._headers, json=data, allow_redirects=True)
        # for hist in resp.history:
        #     if hist.status_code == 302:
        #         obj = hist.json()
        #         self._print_json(obj)
        #         return obj["redirectUri"]
        # return ""

        # print(self._headers)
        # print(data)

        # works better because reads only what is needed (stops on redirection code 302)
        resp = self._make_request_without_redirs(f'{self._PAYU_SERVER_URL}/api/v2_1/orders', headers=self._headers,
                                                 json_data=data, stop_code=302)

        # print(resp.status_code)
        # print(resp.content)

        obj = resp.json()
        # self._print_json(obj)

        if "status" in obj.keys():
            if "statusCode" in obj["status"] and obj["status"]["statusCode"] == 'SUCCESS':

                # handle order before payment
                su.handle_order_before_payment(req, False, 'PAYU', obj["orderId"], basket_items, total_price, tax,
                                               total_price_incl_tax, desc)

                # for development only!
                # req.session['last_order_id'] = obj["orderId"]

                # go to paypal site
                return obj["redirectUri"]

        return None

    # ********************************************
    def create_subscription(self, req, basket_items) -> str | None:

        # prepare_order_data
        from store.store_utils import StoreUtils
        su = StoreUtils()
        products, num, total_price, tax, total_price_incl_tax, desc, vdesc = \
            su.prepare_order_data(basket_items, is_subscription=True)
        data = {
            "continueUrl": STORE_URL + "/store/continue",
            "notifyUrl": STORE_URL + "/store/notify",
            "customerIp": self._get_client_ip(req),
            "merchantPosId": self._PAYU_CLIENT_ID,
            "description": desc,
            # "additionalDescription": "additionalDescription",
            "visibleDescription": vdesc,  # 0-80 chars
            # "statementDescription": "statementDescription",
            # "extOrderId": "string",
            "currencyCode": "PLN",
            "totalAmount": int(total_price_incl_tax*100),
            # "validityTime": "100000",
            # "cardOnFile": "FIRST",
            "recurring": "FIRST",
            # "donation": {
            #   "amount": 500,
            #   "organizationId": "string"
            # },
            "buyer": {
                "extCustomerId": req.user.id,
                "email": req.user.email,
                # "phone": "+48 *********",
                "firstName": req.user.first_name,
                "lastName": req.user.last_name,
                # "nin": *********,
                "language": "pl" if req.LANGUAGE_CODE == "pl" else "en",
                # "delivery": {
                #   "street": "string",
                #   "postalBox": "string",
                #   "postalCode": "string",
                #   "city": "string",
                #   "state": "30",
                #   "countryCode": "string",
                #   "name": "string",
                #   "recipientName": "string",
                #   "recipientEmail": "string",
                #   "recipientPhone": "string"
                # }
            },
            # "shoppingCarts": [
            #   {
            #     "extCustomerId": "ext-customer-1",
            #     "amount": 24950,
            #     "fee": 250,
            #     "shippingMethods": [
            #       {
            #         "country": "PL",
            #         "price": 250,
            #         "name": "Postal package"
            #       }
            #     ],
            #     "products": [
            #       {
            #         "name": "My product",
            #         "unitPrice": 999,
            #         "quantity": 4,
            #         "virtual": True
            #       }
            #     ]
            #   }
            # ],
            "products": products,
            "payMethods": {
                "payMethod": {
                    "type": "PBL",
                    "value": "c",
                    # "authorizationCode": "777123",
                    # "authorizationType": "PRE_AUTHORIZATION",
                    # "card": {
                    #   "number": "****************",
                    #   "expirationMonth": "02",
                    #   "expirationYear": "29",
                    #   "cvv": "123",
                    #   "firstTransactionId": "MCC0111LL1121"
                    # },
                    # "specificData": [
                    #   {
                    #     "name": "string",
                    #     "value": "string"
                    #   }
                    # ],
                    # "amount": "string",
                    # "blikData": {
                    #   "aliasLabelProposal": "string",
                    #   "registerTokenValue": "string",
                    #   "register": True,
                    #   "appKey": "string",
                    #   "recommendedAuthLevel": "NO_CONFIRMATION"
                    # },
                    # "threeDsData": {
                    #   "status3Ds": "Y",
                    #   "status3DsDescription": "Authentication successful",
                    #   "xid": "string",
                    #   "dsTransactionId": "3b31b19d-1c06-4ea4-a85a-00af10c66588",
                    #   "eciCode": 5,
                    #   "cavv": "AAABBBEAUAAAABgICABQAAAAAAA="
                    # }
                }
            },
            # "mcpData": {
            #   "mcpCurrency": "EUR",
            #   "mcpAmount": 10000,
            #   "mcpRate": 4.2556,
            #   "mcpFxTableId": 132331,
            #   "mcpPartnerId": "6283a549-8b1a-430d-8a62-eea64327440e"
            # },
            "threeDsAuthentication": {
                # "challangeRequested": "YES",
                # "exemption": {
                #     "value": "LOW_RISK",
                #     "rejectionHandling": "PERFORM_AUTHENTICATION",
                #     "riskScore": "120 - low risk score calculated in Merchants antifraud tool"
                # },
                # "browser": {
                #     "acceptHeaders": "string",
                #     "requestIP": "127.0.0.1",
                #     "screenWidth": 1920,
                #     "javaEnabled": False,
                #     "timezoneOffset": 1,
                #     "screenHeight": 1280,
                #     "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36",
                #     "colorDepth": 32,
                #     "language": "pl_PL"
                # },
                # "sdk": {
                #     "sdkReferenceNumber": "DS_LOA_SDK_ADBV_739485_94783",
                #     "sdkMaxTimeout": 30,
                #     "sdkAppID": "9063b12c-fcde-43c7-b28e-8d0af5520e8a",
                #     "sdkEncData": "9063b12c-fcde-43c7-b28e-8d0af5520e8a",
                #     "sdkTransID": "b60c9879-ac77-4918-a317-7b01c4317053/8Q==.",
                #     "sdkEphemPubKey": {
                #         "y": "MRy7wofaw77myc7hZef23hmmEOE",
                #         "x": "MRy7wofaw77myc7hZef23hmmEOE",
                #         "kty": "EC",
                #         "crv": "P-256"
                #     }
                # },
                # "merchantRiskIndicator": {
                #     "orderType": "LOAN",
                #     "shipIndicator": "TICKETS",
                #     "preOrdered": False,
                #     "preOrderedDate": "2019-03-27T10:57:59.000+01:00",
                #     "deliveryTimeFrame": "OVERNIGHT",
                #     "reordered": False,
                #     "merchantFunds": {
                #         "amount": 100,
                #         "currencyCode": "PLN"
                #     }
                # },
                "recurring": {
                  "frequency": "1",
                  "expiry": "9999-12-31T00:00:00Z"
                },
                # "cardholder": {
                #     "name": "John Doe",
                #     "accountInformation": {
                #         "createDate": "2019-03-27T10:57:59.000+01:00",
                #         "suspiciousActivity": False,
                #         "deliveryAddressFirstUsedDate": "2019-03-27T10:57:59.000+01:00",
                #         "deliveryAdressUsageIndicator": "THIS_TRANSACTION",
                #         "pastOrdersYear": 46,
                #         "pastOrdersDay": 125,
                #         "purchasesLastSixMonths": 12,
                #         "changeDate": "2019-03-27T10:57:59.000+01:00",
                #         "changeIndicator": "THIS_TRANSACTION",
                #         "passwordChanged": "2019-03-27T10:57:59.000+01:00",
                #         "passwordChangeIndicator": "NO_CHANGE",
                #         "nameToRecipientMatch": True,
                #         "addCardAttemptsDay": 3,
                #         "authMethod": "GUEST",
                #         "authDateTime": "2019-03-27T10:57:59.000+01:00",
                #         "cardAddedDate": "2019-03-27T10:57:59.000+01:00",
                #         "cardAddedIndicator": "THIS_TRANSACTION"
                #     },
                #     "billingAddress": {
                #         "street": "Test Street",
                #         "postalCode": "01-000",
                #         "city": "Test City",
                #         "state": 30,
                #         "countryCode": "PL"
                #     }
                # }
            },
            # "credit": {
            #   "shoppingCarts": [
            #     {
            #       "shippingMethod": {
            #         "type": "COURIER",
            #         "price": "string",
            #         "address": {
            #           "pointId": "Parcel locker POZ29A",
            #           "street": "string",
            #           "streetNo": "string",
            #           "flatNo": "string",
            #           "postalCode": "string",
            #           "city": "string",
            #           "countryCode": "string"
            #         }
            #       },
            #       "products": [
            #         {
            #           "name": "string",
            #           "unitPrice": "string",
            #           "quantity": "string",
            #           "virtual": True,
            #           "listingDate": "string"
            #         }
            #       ],
            #       "extCustomerId": "string"
            #     }
            #   ],
            #   "applicant": {
            #     "email": "<EMAIL>",
            #     "phone": *********,
            #     "firstName": "Joe",
            #     "lastName": "Doe",
            #     "language": "pl",
            #     "nin": *********,
            #     "address": {
            #       "street": "Test Street",
            #       "streetNo": 123,
            #       "flatNo": 987,
            #       "postalCode": "01-000",
            #       "city": "Test City",
            #       "countryCode": "PL"
            #     },
            #     "additionalInfo": {
            #       "hasSuccessfullyFinishedOrderInShop": "YES"
            #     }
            #   }
            # },
            # "submerchant": {
            #   "id": "string"
            # },
            # "deviceFingerprint": "3e9fae0b3e6003a4093358167bc1079e"
        }

        # self._print_json(data)
        # print(self._headers)

        # works but reads final html response (code 200) unnecessarily
        # resp = requests.post(f'{self._PAYU_SERVER_URL}/api/v2_1/orders', headers=self._headers, json=data, allow_redirects=True)
        # for hist in resp.history:
        #     if hist.status_code == 302:
        #         obj = hist.json()
        #         self._print_json(obj)
        #         return obj["redirectUri"]
        # return ""

        # works better because reads only what is needed (stops on redirection code 302)
        resp = self._make_request_without_redirs(f'{self._PAYU_SERVER_URL}/api/v2_1/orders', headers=self._headers, json_data=data, stop_code=302)
        obj = resp.json()
        # self._print_json(obj)

        if "status" in obj.keys():
            if "statusCode" in obj["status"] and obj["status"]["statusCode"] == 'SUCCESS':

                # handle subscription before payment
                su.handle_order_before_payment(req, True, 'PAYU', obj["orderId"], basket_items, total_price, tax,
                                               total_price_incl_tax, desc)

                # create recurring payment record
                # (when transaction is complete - see update_orders_statuses method in views.py)

                # for development only!
                # req.session['last_order_id'] = obj["orderId"]

                # go to paypal site
                return obj["redirectUri"]

        return None

    # ********************************************
    def make_recurring_payment(self, user, reference_order, amount, is_supplement=False) -> tuple:

        now = datetime.now()
        self._logger.info(f'[{now}] making recurring payment (payu): {user=} {reference_order=}')

        # use immediate translation - method is launched from views but also from scheduler
        # so lazy_text may not work properly
        # import gettext and use of gettext() instead of _() does the job
        from django.utils.translation import gettext

        # getting card used for subscription purchase - card info stored in user subscription record is preferred
        # as it may be the one provided later (updated by user himself or on demand sent in email notification)
        payment_details = None
        user_subscription = UserSubscription.objects.filter(user=user).first()
        if user_subscription:
            payment_details = user_subscription.payment_details
        if not payment_details:
            payment_details = reference_order.payment_details

        # find card used in payment for a subscription
        # FIXME: card tokens list is empty
        self._logger.info(f'{reference_order=}')
        self._logger.info(f'{user_subscription=}')
        self._logger.info(f'getting card token for {payment_details=}')
        token, _ = self.get_card_token(user, details=payment_details)

        if not token or token['status'] != 'ACTIVE':
            # send email notification
            now = timezone.now()
            delta = now - relativedelta(days=1)
            notifications = EmailNotification.objects.filter(user=user, status_id=EmailNotification.Status.SENT,
                                                             message_id=EmailNotification.Message.PAYMENT_CARD_EXPIRED,
                                                             sending_time__gte=delta)
            if not notifications:
                self.mailer.payment_card_expired(user, reference_order, token)

            return None, gettext('Your payment card has expired!')

        # prepare payment data
        from store.store_utils import StoreUtils
        su = StoreUtils()
        products, total_price, tax, total_price_incl_tax, desc = \
            su.prepare_payment_data(amount, is_supplement=is_supplement)

        # FIXME: get real user IP address from user session (no request data here)
        data = {
            # "continueUrl": STORE_URL + "/store/continue",
            "notifyUrl": STORE_URL + "/store/notify",
            "customerIp": '************',
            "merchantPosId": self._PAYU_CLIENT_ID,
            "description": desc,
            # "additionalDescription": "additionalDescription",
            # "visibleDescription": "string",  # 0-80 chars
            # "statementDescription": "statementDescription",
            # "extOrderId": "string",
            "currencyCode": "PLN",
            "totalAmount": int(total_price_incl_tax * 100),
            # "validityTime": "100000",
            # "cardOnFile": "FIRST",
            "recurring": "STANDARD",
            "buyer": {
                "extCustomerId": user.id,
                "email": user.email,
                # "phone": "+48 *********",
                "firstName": user.first_name,
                "lastName": user.last_name,
                # "nin": *********,
                # "language": "pl" if req.LANGUAGE_CODE == "pl" else "en",
            },
            "products": products,
            "payMethods": {
                "payMethod": {
                    "type": "CARD_TOKEN",
                    "value": token['value']
                }
            },
            "threeDsAuthentication": {
                "recurring": {
                    "frequency": "1",
                    "expiry": "9999-12-31T00:00:00Z"
                }
            }
        }

        # self._print_json(self._headers)
        self._print_json(data)

        # make payment
        resp = requests.post(f'{self._PAYU_SERVER_URL}/api/v2_1/orders', headers=self._headers, json=data)
        obj = resp.json()
        self._print_json(obj)

        if "status" in obj.keys():
            if "statusCode" in obj["status"].keys() and obj["status"]["statusCode"] == 'SUCCESS':

                # store payment in db
                SubsequentOrder.objects.create(order_id=obj["orderId"], reference_order=reference_order,
                                               description=desc, price=total_price, tax=tax,
                                               price_incl_tax=total_price_incl_tax, card_token=token['value'])

                return obj["orderId"], ''

        return None, gettext('Recurring payment has failed!')

# ********************************************
    def get_card_update_link(self, user) -> tuple:
        data = {
            "notifyUrl": STORE_URL + "/store/notify",
            "customerIp": '************',
            "merchantPosId": self._PAYU_CLIENT_ID,
            "description": "Order description",
            "currencyCode": "PLN",
            "totalAmount": "1",
            "buyer": {
                "extCustomerId": user.id,
                "email": user.email,
                # "phone": "+48 *********",
                "firstName": user.first_name,
                "lastName": user.last_name,
                # "nin": *********,
                # "language": "pl" if req.LANGUAGE_CODE == "pl" else "en",
            },
            "recurring": "STANDARD"
        }

        # self._print_json(self._headers)
        self._print_json(data)

        # make payment
        # resp = requests.post(f'{self._PAYU_SERVER_URL}/api/v2_1/orders', headers=self._headers, json=data)
        resp = self._make_request_without_redirs(f'{self._PAYU_SERVER_URL}/api/v2_1/orders', headers=self._headers,
                                                 json_data=data, stop_code=302)
        # print(resp.status_code)
        # print(resp.content)
        # return resp.content

        obj = resp.json()
        # self._print_json(obj)
        return obj["redirectUri"]

# ********************************************
    def retrieve_order(self, order_id) -> json:

        resp = requests.get(f'{self._PAYU_SERVER_URL}/api/v2_1/orders/'+order_id, headers=self._headers)
        return resp.json()

# ********************************************
    def retrieve_transaction(self, order_id) -> json:

        resp = requests.get(f'{self._PAYU_SERVER_URL}/api/v2_1/orders/' + order_id + '/transactions', headers=self._headers)
        return resp.json()

# ********************************************
    def goes_from_authorized_ip(self, req) -> bool:
        x_forwarded_for = req.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = req.META.get('REMOTE_ADDR')
        match = False
        ip_address = ipaddress.ip_address(ip)
        for ip_range in self._NOTIFY_IPS:
            if '/' in ip_range:
                if ip_address in ipaddress.ip_network(ip_range, strict=False):
                    match = True
            else:
                if ip_address == ipaddress.ip_address(ip_range):
                    match = True
        if not match:
            self._logger.error(f'ip not allowed ({ip})')
        return match

# ********************************************
    def handle_notification(self, req) -> bool:

        # log headers
        self._logger.info('headers\n' + json.dumps(dict(req.headers), indent=4))

        # log JSON payload
        if req.method == "POST":
            try:
                data = json.loads(req.body)
                self._logger.info('json payload\n' + json.dumps(data, indent=4))
            except json.JSONDecodeError:
                pass

        # verify IP
        if not self.goes_from_authorized_ip(req):
            return False

        # verify signature
        key = "OpenPayu-Signature"
        if key not in req.headers.keys():
            self._logger.error(f'no key in header ({key})')
            return False
        data = dict(v.split("=") for v in req.headers[key].split(";"))
        key = 'signature'
        if key not in data.keys():
            self._logger.error(f'no signature in header at key ({key})')
            return False
        incoming_signature = data[key]
        key = 'algorythm'
        if key not in data.keys():
            self._logger.error(f'no encryption algorythm in header at key ({key})')
            return False
        algorythm = data[key]
        if algorythm != 'MD5':
            self._logger.error(f'not supported encryption algorythm ({algorythm})')
            return False
        concatenated = req.response + self._PAYU_SECRET
        expected_signature = md5(concatenated)
        if expected_signature != incoming_signature:
            self._logger.error(f'singnatures do not match')
            return False

        # update order status
        # TODO: will be possible to implement when we get the first response (see payu.log)

        return True
