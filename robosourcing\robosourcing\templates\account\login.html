{% extends "account/base.html" %}
{% load crispy_forms_filters %}
{% load crispy_forms_tags %}
{% load i18n %}
{% load account socialaccount %}

{% block head_title %}{% trans "Sign In" %}{% endblock %}

{% block title %}
    {{ block.super }} - {% trans "Sign In" %}
{% endblock title %}

{% block content %}
<div class="container">
    <div class="row justify-content-center _text-light">
        <section class="col-md-8 col-lg-6 col-xxl-5">
            <div class="row">
                <div class="col-md-3 text-end px-4 mt-3">
                </div>
                <div class="col-md-9 mt-3">
                    <h1>{% trans "Sign In" %}</h1>
                </div>

            {% get_providers as socialaccount_providers %}

            {% if socialaccount_providers %}
                <div class="col-md-3 text-end px-4 mt-3">
                    <hr>
                </div>
                <div class="col-md-9 mt-3">
                    <p>{% blocktrans with site.name as site_name %}Please sign in with one
of your existing third party accounts{% endblocktrans %}:</p>
                </div>

                <div class="col-md-3 text-end px-4 mt-3">
                </div>
                <div class="col-md-9 mt-3">
                    <div class="socialaccount_ballot">
                        <ul class="socialaccount_providers">
                        {% include "socialaccount/snippets/provider_list.html" with process="login" %}
                        </ul>
                    </div>
                </div>

                {% include "socialaccount/snippets/login_extra.html" %}

            {% endif %}

                <div class="col-md-3 text-end px-4 mt-2">
                    <hr>
                </div>
                <div class="col-md-9 mt-2">

                    <p>{% blocktrans with site.name as site_name %}or using your local account{% endblocktrans %}:</p>

                    <form class="login" method="POST" action="{% url 'account_login' %}">
                    {% csrf_token %}
                    {{ form|crispy }}
                    {% if redirect_field_value %}
                        <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
                    {% endif %}
                        <div class="row">
                            <div class="col-6 mt-3">
                                <a class="button secondaryAction" href="{% url 'account_reset_password' %}">{% trans "Forgot Password?" %}</a>
                            </div>
                            <div class="col-6 mt-3 text-end">
                                <button class="btn btn-primary" type="submit">{% trans "Sign In" %}</button>
                            </div>
                        </div>
                    </form>

                </div>

                <div class="mt-5">
                    <hr>
                    <p>{% blocktrans %}If you have not created an account yet, then please
<a href="{{ signup_url }}">sign up</a> first.{% endblocktrans %}</p>
                </div>

            </div>
        </section>
    </div>
</div>
{% endblock %}