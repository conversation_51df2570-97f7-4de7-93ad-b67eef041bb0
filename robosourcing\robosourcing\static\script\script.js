document.addEventListener('DOMContentLoaded', function() {
    var generateKeyBtn = document.getElementById('generate-key-btn');
    if (generateKeyBtn) {
        generateKeyBtn.addEventListener('click', function() {
        var xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    var keyModalConnectKey = document.getElementById('key-modal-connect-key');
                    keyModalConnectKey.textContent = JSON.parse(xhr.responseText).connect_key;

                    var keyModalCreatedTime = document.getElementById('key-modal-created-time');
                    keyModalCreatedTime.textContent = JSON.parse(xhr.responseText).create_time;

                    var keyModalEndTime = document.getElementById('key-modal-end-time');
                    keyModalEndTime.textContent = JSON.parse(xhr.responseText).duration_time;

                    var keyModal = document.getElementById('key-modal');
                    var modal = new bootstrap.Modal(keyModal);
                    modal.show();

                    var closeBtn = keyModal.querySelector('[data-bs-dismiss="modal"]');
                    if (closeBtn) {
                        closeBtn.addEventListener('click', function() {
                            modal.hide();
                        });
                    }
                } else {
                  alert('Failed to generate new key');
                }
            }
        };
        xhr.open('GET', '/api/get_connect_key/');
        xhr.send();
        });
    }

    var copyKeyBtn = document.getElementById('copy-key-btn');
    if (copyKeyBtn) {
        if (location.protocol !== 'https:') {
            // Display a message asking the user to copy the key manually
            var copyKeyMsg = document.createElement('p');
            copyKeyMsg.innerHTML = 'Your connection is not secure. Please copy the key manually.';
            copyKeyBtn.parentNode.insertBefore(copyKeyMsg, copyKeyBtn);

            // Hide the copy key button
            copyKeyBtn.style.display = 'none';

        } else {
            // Attach the copy key event listener
            copyKeyBtn.addEventListener('click', function() {
                var keyModalConnectKey = document.getElementById('key-modal-connect-key');
                var key = keyModalConnectKey.textContent;
                navigator.clipboard.writeText(key).then(function() {
                    alert('Key copied to clipboard');
                }, function() {
                    alert('Failed to copy key to clipboard');
                });
            });
        }
    }

    // Get all elements with the class "fade-in"
    const fadeElements = document.querySelectorAll(".fade-in");

    // Callback function to handle intersection
    function handleIntersection(entries, observer) {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                // If the element is in view, add a class to trigger the animation
                entry.target.classList.add("fade-in-animate");
                // Stop observing the element once it's faded in
                observer.unobserve(entry.target);
            }
        });
    }

    // Create the Intersection Observer and configure it
    const options = {
        root: null,
        rootMargin: "0px",
        threshold: 0.3, // Adjust this value to control when the elements fade in
    };

    const observer = new IntersectionObserver(handleIntersection, options);

    // Observe all elements with the class "fade-in"
    fadeElements.forEach((element) => {
        observer.observe(element);
    });

    // Change language & theme from dropdowns
    // Follow color mode change in Windows settings
    function getCookie(name) {
        var value = '; ' + document.cookie,
            parts = value.split('; ' + name + '=');
        if (parts.length == 2) return parts.pop().split(';').shift();
    }
    function switch_and_reload(url, data) {
        // alert('switching to: ' + data.name + " url: " + url);
        $.ajax({
            type: 'POST',
            url: url,
            data: data,
            headers: {"X-CSRFToken": getCookie('csrftoken')}
        }).done(function(data, textStatus, jqXHR) {
            reload_page();
        });
    }
    function reload_page() {
        window.location.reload(true);
    }
    $('#language-list a').on('click', function(event) {
        event.preventDefault();
        var target = $(event.target);
        if (target.prop('tagName') != 'A') target = target.parent();
        var url = target.attr('href');
        var language_code = target.data('language-code');
        switch_and_reload(url, {language: language_code});
    });
    const runColorMode = (fn) => {
      if (!window.matchMedia) {
        return;
      }
      const query = window.matchMedia('(prefers-color-scheme: dark)');
      fn(query.matches);
      query.addEventListener('change', (event) => fn(event.matches));
    }
    runColorMode((isDarkMode) => {
        var target = $('#theme-list a:first-child()');
        var url = target.attr('href');
        if (current_theme_mode == 'auto') {
            if (isDarkMode) switch_and_reload(url, {name: 'auto-dark'});
            else switch_and_reload(url, {name: 'auto-light'});
        } else if (current_theme_mode == 'auto-dark' && !isDarkMode) {
            switch_and_reload(url, {name: 'auto-light'});
        } else if (current_theme_mode == 'auto-light' && isDarkMode) {
            switch_and_reload(url, {name: 'auto-dark'});
        }
    })
    $('#theme-list a').on('click', function(event) {
        event.preventDefault();
        var target = $(event.target);
        if (target.prop('tagName') != 'A') target = target.parent();
        var url = target.attr('href');
        var theme_name = target.data('theme-name');
        if (theme_name == 'auto') {
            runColorMode((isDarkMode) => {
                if (isDarkMode) theme_name = 'auto-dark'; else theme_name = 'auto-light';
            });
        }
        switch_and_reload(url, {name: theme_name});
    });

    // Translate validity check messages
    function setCustomValidityMessage(field) {
        field.setCustomValidity('');
        if (!field.validity.valid) {
            for (var i=0; i<validity_properties.length; i++) {
                if (field.validity[validity_properties[i]['name']]) {
                    field.setCustomValidity(validity_properties[i]['trans']);
                    break;
                }
            }
        }
    }
    $('input, select, textarea').on('keypress change invalid', function() {
        var field = $(this).get(0);
        setCustomValidityMessage(field); // imediate set for FF
        setTimeout(setCustomValidityMessage, 1, field); // delayed set for MS Edge
    });

});