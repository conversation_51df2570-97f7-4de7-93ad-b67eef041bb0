from django.views.generic import TemplateView
from seo.models import StaticPageMeta

class HomePageView(TemplateView):
    template_name = "home.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            meta = StaticPageMeta.objects.get(page='home')
            context['meta'] = meta
        except StaticPageMeta.DoesNotExist:
            context['meta'] = None
        return context
