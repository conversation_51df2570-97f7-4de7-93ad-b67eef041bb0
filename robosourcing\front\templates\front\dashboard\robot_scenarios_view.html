{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load crispy_forms_filters %}

{% block title %}{{ block.super }} - {% trans "Scenario Library" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item site-name active">{% trans "Scenarios" %} <i class="fa fa-refresh text-muted ms-3" role="button" id="refresh-button"></i></li>
        </ol>
    </nav>

    <div class="row">
                <!-- Filters panel -->
<div class="col-md-3 col-lg-2 mb-4">
    <div class="dashboard-element">
      <h2 class="dashboard-header">{% trans "Filters" %}</h2>
      <div class="dashboard-body mb-4 ms-4 me-4">
        <form method="get" action="javascript:applyFilters()">
  
          <!-- Name -->
          <div class="mb-3">
            <label for="name" class="form-label">{% trans "Name" %}:</label>
            <input
              type="text"
              id="name"
              name="name"
              class="form-control"
              placeholder="{% trans "Name" %}"
              value="{{ request.GET.name }}"
            >
          </div>
  
          <!-- Author -->
          <div class="mb-3">
            <label for="author" class="form-label">{% trans "Author" %}:</label>
            <input
              type="text"
              id="author"
              name="author"
              class="form-control"
              placeholder="{% trans "Author" %}"
              value="{{ request.GET.author }}"
            >
          </div>
  
          <!-- Date range -->
          <div class="mb-3">
            <label class="form-label">{% trans "Start date" %}:</label>
            <input
              type="date"
              name="start_date"
              class="form-control"
              value="{{ request.GET.start_date }}"
            >
          </div>
          <div class="mb-3">
            <label class="form-label">{% trans "End date" %}:</label>
            <input
              type="date"
              name="end_date"
              class="form-control"
              value="{{ request.GET.end_date }}"
            >
          </div>
  
          <!-- Cost range -->
          <div class="mb-3">
            <label class="form-label">{% trans "Min cost" %}:</label>
            <input
              type="number"
              name="min_price"
              min="0" step="0.01"
              class="form-control"
              value="{{ request.GET.min_price }}"
            >
          </div>
          <div class="mb-3">
            <label class="form-label">{% trans "Max cost" %}:</label>
            <input
              type="number"
              name="max_price"
              min="0" step="0.01"
              class="form-control"
              value="{{ request.GET.max_price }}"
            >
          </div>
  
          <!-- Tags -->
          <div class="mb-3">
            <label for="tags" class="form-label">{% trans "Tags" %}:</label>
            <input
              type="text"
              id="tags"
              name="tags"
              class="form-control"
              placeholder="{% trans "Enter tags separated by commas" %}"
              value="{{ request.GET.tags }}"
            >
          </div>
  
          <!-- Show per page -->
          <div class="mb-3">
            <label for="records_per_page" class="form-label">{% trans "Show" %}:</label>
            <select
              name="records_per_page"
              id="records_per_page"
              class="form-select"
            >
              <option value="5"  {% if request.GET.records_per_page == '5'  %}selected{% endif %}>5</option>
              <option value="10" {% if request.GET.records_per_page == '10' %}selected{% endif %}>10</option>
              <option value="20" {% if request.GET.records_per_page == '20' %}selected{% endif %}>20</option>
              <option value="50" {% if request.GET.records_per_page == '50' %}selected{% endif %}>50</option>
              <option value="100"{% if request.GET.records_per_page == '100'%}selected{% endif %}>100</option>
            </select>
            <div class="form-text">{% trans "scenarios per page" %}</div>
          </div>
  
          <!-- Buttons -->
          <div class="d-grid gap-2">
            <button class="btn btn-primary" type="submit">
              {% trans "Apply" %}
            </button>
            <button class="btn btn-secondary" type="button" onclick="clearFilters();">
              {% trans "Clear filters" %}
            </button>
          </div>
  
        </form>
      </div>
    </div>
  </div>
  

        <div class="col-md-10">
            <div id="messages"></div>
            <div class="dashboard-element">
                <!-- Tabs -->
                <ul class="nav nav-tabs" id="scenarioTabs">
                    <li class="nav-item">
                        <a class="nav-link p-2 active" id="public-tab" data-bs-toggle="tab" href="#public" data-scenario-type="public">{% trans "Public Scenarios" %}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link p-2" id="favorites-tab" data-bs-toggle="tab" href="#favorites" data-scenario-type="favorites">{% trans "Favorites" %}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link p-2" id="robot-tab" data-bs-toggle="tab" href="#robot" data-scenario-type="robot">{% trans "Assigned to Robot" %}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link p-2" id="private-tab" data-bs-toggle="tab" href="#private" data-scenario-type="private">{% trans "Private Scenarios" %}</a>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content" id="scenarioTabsContent">
                    <div class="tab-pane fade show active" id="public">
                        <!-- Table for public scenarios -->
                        <table class="table my-custom-table" id="public-scenarios-table">
                            <thead>
                                <tr>
                                    <th><a href="#" class="sort-link" data-sort="name">{% trans "Scenario name" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="author">{% trans "Author" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="created_at">{% trans "Creation date" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="price">{% trans "Cost" %}</a></th>
                                    <th>{% trans "Version" %}</th>
                                    <th>{% trans "Favorite" %}</th>
                                    <th>{% trans "Availability" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="tab-pane fade" id="favorites">
                        <!-- Table for favorite scenarios -->
                        <table class="table my-custom-table" id="favorite-scenarios-table">
                            <thead>
                                <tr>
                                    <th><a href="#" class="sort-link" data-sort="name">{% trans "Scenario name" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="author">{% trans "Author" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="created_at">{% trans "Creation date" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="price">{% trans "Cost" %}</a></th>
                                    <th>{% trans "Version" %}</th>
                                    <th>{% trans "Favorite" %}</th>
                                    <th>{% trans "Availability" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="tab-pane fade" id="robot">
                        <!-- Robot selector -->
                        <select id="robot-selector" onchange="fetchAndUpdateScenarios('robot')">
                            <option value="">{% trans "Select robot" %}</option>
                            {% for robot in user_robots %}
                                <option value="{{ robot.rid }}">{{ robot.name }}</option>
                            {% endfor %}
                        </select>
                        <!-- Table for robot's scenarios -->
                        <table class="table my-custom-table" id="robot-scenarios-table">
                            <thead>
                                <tr>
                                    <th><a href="#" class="sort-link" data-sort="name">{% trans "Scenario name" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="author">{% trans "Author" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="created_at">{% trans "Creation date" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="price">{% trans "Cost" %}</a></th>
                                    <th>{% trans "Version" %}</th>
                                    <th>{% trans "Favorite" %}</th>
                                    <th>{% trans "Availability" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="tab-pane fade" id="private">
                        <!-- Table for private scenarios -->
                        <table class="table my-custom-table" id="private-scenarios-table">
                            <thead>
                                <tr>
                                    <th><a href="#" class="sort-link" data-sort="name">{% trans "Scenario name" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="author">{% trans "Author" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="created_at">{% trans "Creation date" %}</a></th>
                                    <th><a href="#" class="sort-link" data-sort="price">{% trans "Cost" %}</a></th>
                                    <th>{% trans "Version" %}</th>
                                    <th>{% trans "Favorite" %}</th>
                                    <th>{% trans "Availability" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <nav aria-label="Page navigation">
                    <ul class="pagination" id="pagination">
                      <!-- Pagination buttons will be dynamically added here -->
                    </ul>
                </nav>

            </div>
        </div>


    </div>
</div>
</div>
{% endblock %}

{% block extra_content_2 %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to switch to the appropriate tab and select the robot
    function switchTabAndSelectRobot() {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab');
        const robotId = urlParams.get('robot_id');

        if (tab) {
            const tabLink = document.querySelector(`#scenarioTabs a[data-scenario-type="${tab}"]`);
            if (tabLink) {
                tabLink.click();
            }
        }

        if (robotId) {
            const robotSelector = document.getElementById('robot-selector');
            if (robotSelector) {
                robotSelector.value = robotId;
                fetchAndUpdateScenarios('robot');
            }
        }
    }

    // Call the function on page load
    switchTabAndSelectRobot();

    // Load public scenarios by default
    fetchAndUpdateScenarios('public');

    // Tab switcher
    document.querySelectorAll('#scenarioTabs a').forEach(tab => {
        console.log('Tab switched');
        tab.addEventListener('click', function(event) {
            event.preventDefault();
            const scenarioType = this.getAttribute('data-scenario-type');
            fetchAndUpdateScenarios(scenarioType);
        });
    });

    // Robot selector
    const robotSelector = document.querySelector('#robot-selector');
    if (robotSelector) {
        robotSelector.addEventListener('change', function() {
            fetchAndUpdateScenarios('robot');
        });
    }

    // Pagination switcher
    const recordsPerPageSelector = document.querySelector('select[name="records_per_page"]');
    if (recordsPerPageSelector) {
        recordsPerPageSelector.addEventListener('change', function() {
            // Reload data when changing records per page
            fetchAndUpdateScenarios(document.querySelector('.nav-link.active').getAttribute('data-scenario-type'));
        });
    }

    // Find buttons that open the modal and add listeners to them
    var assignModal = document.getElementById('assignModal');
    assignModal.addEventListener('show.bs.modal', function(event) {
        // Element that triggered the modal
        var button = event.relatedTarget;
        // Get the scenario ID value
        var scenarioId = button.getAttribute('data-scenario-id');
        // Set the value in the hidden input field
        var scenarioInput = assignModal.querySelector('input[name="scenario"]');
        scenarioInput.value = scenarioId;

        // Set assigned robots checkboxes
        const assignedRobots = button.getAttribute('data-assigned-robots').split(',').map(id => id.trim());
        document.querySelectorAll('#assignModal .robot-checkbox').forEach(checkbox => {
            checkbox.checked = assignedRobots.includes(checkbox.value);
        });
    });

    // Select/Deselect all checkboxes
    const selectAllCheckbox = document.getElementById('select_all_robots');
    selectAllCheckbox.addEventListener('change', function() {
        document.querySelectorAll('#assignModal .robot-checkbox').forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    const assignForm = document.getElementById('assignForm');
    if (assignForm) {
        assignForm.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent normal form submission

            // Create a new FormData object
            const formData = new FormData(assignForm);

            // Gather all robot checkboxes and add their values to FormData
            document.querySelectorAll('#assignModal .robot-checkbox').forEach(checkbox => {
                if (!checkbox.checked) {
                    formData.append('remove[]', checkbox.value);
                }
            });

            // Log the form data for debugging
            for (var pair of formData.entries()) {
                console.log(pair[0]+ ': ' + pair[1]);
            }

            fetch(assignForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'), // Get CSRF token from cookies
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'OK') {
                    // Close the modal
                    const assignModalInstance = bootstrap.Modal.getInstance(assignModal);
                    assignModalInstance.hide();

                    // Display success message
                    displayMessage('{% trans "Scenario successfully assigned to robot." %}', 'success');

                    // Refresh scenarios list
                    const activeTab = document.querySelector('.nav-link.active').getAttribute('data-scenario-type');
                    fetchAndUpdateScenarios(activeTab);
                } else {
                    // Display form errors or other error messages
                    displayMessage('{% trans "There was an issue assigning the scenario." %}', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                displayMessage('{% trans "An error occurred while submitting data." %}', 'error');
            });
        });
    }

    // Add event listener for the refresh button
    const refreshButton = document.getElementById('refresh-button');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            $('#refresh-button').addClass('fa-spin');
            const activeTab = document.querySelector('.nav-link.active').getAttribute('data-scenario-type');
            fetchAndUpdateScenarios(activeTab);
        });
    }

    document.body.addEventListener('click', function(event) {
        if (event.target.matches('[data-bs-toggle="modal"][data-bs-target="#assignModal"]')) {
            console.log("Modal opening button clicked");

            const button = event.target;
            const scenarioId = button.getAttribute('data-scenario-id');
            const assignedRobots = button.getAttribute('data-assigned-robots').split(',').map(id => id.trim());
            console.log("Assigned robots:", assignedRobots);

            document.querySelectorAll('#assignModal .robot-checkbox').forEach(checkbox => {
                checkbox.checked = assignedRobots.includes(checkbox.value);
            });

            const modalScenarioInput = document.querySelector('#assignModal input[name="scenario"]');
            modalScenarioInput.value = scenarioId;
        }
    });

    document.querySelectorAll('.sort-link').forEach(link => {
        link.addEventListener('click', function(event) {
            event.preventDefault();
            const sortColumn = this.getAttribute('data-sort');
            const sortOrder = this.classList.contains('asc') ? 'desc' : 'asc';
            this.classList.toggle('asc', sortOrder === 'asc');
            this.classList.toggle('desc', sortOrder === 'desc');
            fetchAndUpdateScenarios(document.querySelector('.nav-link.active').getAttribute('data-scenario-type'), 1, sortColumn, sortOrder);
        });
    });
});

function applyFilters() {
    const activeTab = document.querySelector('.nav-link.active').getAttribute('data-scenario-type');
    fetchAndUpdateScenarios(activeTab);
}

function clearFilters() {
    const activeTab = document.querySelector('.nav-link.active').getAttribute('data-scenario-type');
    const url = new URL(window.location.href);
    url.searchParams.delete('name');
    url.searchParams.delete('author');
    url.searchParams.delete('start_date');
    url.searchParams.delete('end_date');
    url.searchParams.delete('min_price');
    url.searchParams.delete('max_price');
    url.searchParams.delete('tags');
    url.searchParams.set('tab', activeTab);
    window.location.href = url.toString();
}

function fetchAndUpdateScenarios(scenarioType, page = 1, sortColumn = '', sortOrder = '') {
    console.log(`Fetching scenarios for type: ${scenarioType} and page: ${page}, sortColumn: ${sortColumn}, sortOrder: ${sortOrder}`)
    let url = '';

    // Get values from filter form
    const nameFilter = document.querySelector('input[name="name"]').value;
    const authorFilter = document.querySelector('input[name="author"]').value;
    const startDateFilter = document.querySelector('input[name="start_date"]').value;
    const endDateFilter = document.querySelector('input[name="end_date"]').value;
    const minPriceFilter = document.querySelector('input[name="min_price"]').value;
    const maxPriceFilter = document.querySelector('input[name="max_price"]').value;
    const tagsFilter = document.querySelector('input[name="tags"]').value;
    const recordsPerPage = document.querySelector('select[name="records_per_page"]').value;

    // Add records per page to URL parameters
    const filterParams = new URLSearchParams({
        name: nameFilter,
        author: authorFilter,
        start_date: startDateFilter,
        end_date: endDateFilter,
        min_price: minPriceFilter,
        max_price: maxPriceFilter,
        tags: tagsFilter,
        page: page,
        records_per_page: recordsPerPage,
        sort_column: sortColumn,
        sort_order: sortOrder
    }).toString();

    switch (scenarioType) {
        case 'public':
            url = `/profiles/ajax_public_scenarios?${filterParams}`;
            break;
        case 'favorites':
            url = `/profiles/ajax_favorite_scenarios?${filterParams}`;
            break;
        case 'robot':
            const robotId = document.querySelector('#robot-selector').value;
            url = robotId ? `/profiles/ajax_robot_scenarios/${robotId}/?${filterParams}` : '';
            break;
        case 'private':
            url = `/profiles/ajax_private_scenarios?${filterParams}`;
            break;
    }

    if (url) {
        fetch(url)
            .then(response => response.json())
            .then(data => {
                const tableId = scenarioType === 'favorites' ? 'favorite-scenarios-table' : `${scenarioType}-scenarios-table`;
                updateScenariosTable(data.scenarios || data, tableId);
                updatePaginationControls(data.num_pages, data.current_page, scenarioType);
                $('#refresh-button').removeClass('fa-spin');
            })
            .catch(error => {
                console.error('Error:', error);
                $('#refresh-button').removeClass('fa-spin');
            });
    } else $('#refresh-button').removeClass('fa-spin');
}

function updatePaginationControls(numPages, currentPage, scenarioType) {
    const paginationUl = document.getElementById('pagination');
    paginationUl.innerHTML = ''; // Clear current pagination buttons

    for (let page = 1; page <= numPages; page++) {
        const li = document.createElement('li');
        li.className = `page-item page-item-dashboard ${page === currentPage ? 'active' : ''}`;
        const a = document.createElement('a');
        a.className = 'page-link page-link-dashboard';
        a.href = '#';
        a.innerText = page;
        a.addEventListener('click', (e) => {
            e.preventDefault();
            fetchAndUpdateScenarios(scenarioType, page);
        });
        li.appendChild(a);
        paginationUl.appendChild(li);
    }
}

function updateScenariosTable(scenarios, tableId) {
    console.log(`Updating scenarios table: ${tableId}`);
    const tableBody = document.querySelector(`#${tableId} tbody`);
    if (!tableBody) {
        console.error(`No table body found for ${tableId}`);
        return;
    }
    // Check if scenarios exists and is an array
    if (!scenarios || !Array.isArray(scenarios)) {
        console.error('Invalid or undefined scenarios data.');
        return;
    }

    tableBody.innerHTML = '';
    scenarios.forEach(scenario => {
        const row = document.createElement('tr');

        // Scenario name
        const nameCell = document.createElement('td');
        nameCell.textContent = scenario.name;
        row.appendChild(nameCell);

        // Author
        const authorCell = document.createElement('td');
        authorCell.textContent = scenario.author;
        row.appendChild(authorCell);

        // Creation date
        const createdAtCell = document.createElement('td');
        createdAtCell.textContent = new Date(scenario.created_at).toLocaleDateString(); // Format date as per preference
        row.appendChild(createdAtCell);

        // Cost
        const costCell = document.createElement('td');
        costCell.textContent = scenario.price;
        row.appendChild(costCell);

        // Version
        const versionCell = document.createElement('td');
        versionCell.textContent = scenario.scenario_version ?? '-';
        row.appendChild(versionCell);

        // Favorite
        const favoriteCell = document.createElement('td');
        const favoriteIcon = document.createElement('i');
        favoriteIcon.style.cursor = 'pointer';
        favoriteIcon.className = scenario.is_favorited ? 'fas fa-heart' : 'far fa-heart';
        favoriteIcon.setAttribute('data-scenario-id', scenario.sid); // Add data-scenario-id attribute
        favoriteIcon.setAttribute('data-is-favorited', scenario.is_favorited); // Add data-is-favorited attribute
        favoriteIcon.onclick = function() {
            const scenarioId = this.getAttribute('data-scenario-id');
            const isFavorited = this.getAttribute('data-is-favorited') === 'true';
            toggleFavorite(scenarioId, isFavorited);
        };
        favoriteCell.appendChild(favoriteIcon);
        row.appendChild(favoriteCell);

        // Availability
        const availabilityCell = document.createElement('td');
        availabilityCell.textContent = mapAvailabilityStatus(scenario.available); // Map availability status function
        row.appendChild(availabilityCell);

        // Actions
        const actionsCell = document.createElement('td');

        // 1) Details jako <a>
        const detailLink = document.createElement('a');
        detailLink.classList.add('btn','btn-primary','btn-sm','me-1');
        detailLink.textContent = '{% trans "Details" %}';
        // Zakładam, że masz url name="scenario_detail" na ścieżce /profiles/scenarios/<sid>/
        detailLink.href = `/profiles/scenarios/${scenario.sid}/`;

        actionsCell.appendChild(detailLink);

        // Pin to robot
        const pinButton = document.createElement('button');
        pinButton.classList.add('btn', 'btn-secondary', 'btn-sm');
        pinButton.textContent = '{% trans "Pin to robot" %}';
        // Set attributes required to open modal
        pinButton.setAttribute('data-bs-toggle', 'modal');
        pinButton.setAttribute('data-bs-target', '#assignModal');
        pinButton.setAttribute('data-scenario-id', scenario.sid);

        // Add assigned robots to data-assigned-robots attribute
        pinButton.setAttribute('data-assigned-robots', scenario.assigned_robots.join(','));

        actionsCell.appendChild(pinButton);

        row.appendChild(actionsCell);

        tableBody.appendChild(row);
        setupFavoriteIcons();
    });
}

function mapAvailabilityStatus(status) {
    const statusMap = {
        // Map availability values to readable labels
        10: "{% trans "Private" %}",
        20: "{% trans "Account restricted" %}",
        50: "{% trans "Group restricted" %}",
        90: "{% trans "Public" %}",
    };
    return statusMap[status] || "{% trans "Unknown" %}"; // Return readable label or "Unknown" if status not found
}

function toggleFavorite(scenarioId, isFavorited) {
    const url = isFavorited ? '/profiles/remove_scenario_from_favorites/' : '/profiles/add_scenario_to_favorites/';
    const csrftoken = getCookie('csrftoken');

    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrftoken,
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `scenario_id=${scenarioId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Toggle favorite response:', data);
        if (data.status === 'success') {
            document.querySelectorAll(`i[data-scenario-id="${scenarioId}"]`).forEach(icon => {
                icon.classList.toggle('fas', !isFavorited); // full heart if was favorited
                icon.classList.toggle('far', isFavorited); // empty heart if was not favorited
                icon.setAttribute('data-is-favorited', !isFavorited);
            });

            // Display appropriate message
            const message = isFavorited ? '{% trans "Scenario removed from favorites." %}' : '{% trans "Scenario added to favorites." %}';
            displayMessage(message, 'success');
        } else {
            displayMessage('{% trans "There was an error updating favorites." %}', 'error');
        }
    })
    .catch(error => {
        console.error('Error toggling favorite:', error);
        displayMessage('{% trans "There was an error connecting to the server." %}', 'error');
    });
}

function setupFavoriteIcons() {
    // Check if flag already exists
    if (!window.iconsSetupDone) {
        document.querySelectorAll('i[data-scenario-id]').forEach((icon, index) => {
            // Logic for setting event listener
            icon.removeEventListener('click', favoriteClickHandler);
            icon.addEventListener('click', favoriteClickHandler);
        });
        // Set flag that icons are initialized
        window.iconsSetupDone = true;
    }
}

let isClickBlocked = false;

function favoriteClickHandler() {
    console.log("favoriteClickHandler called for scenarioId:", this.getAttribute('data-scenario-id'));
    if (isClickBlocked) return;
    isClickBlocked = true;

    const scenarioId = this.getAttribute('data-scenario-id');
    const isFavorited = this.getAttribute('data-is-favorited') === 'true';
    toggleFavorite(scenarioId, isFavorited);

    setTimeout(() => {
        isClickBlocked = false;
    }, 300); // Unblock after a short time
}

function displayMessage(message, type) {
    const messagesContainer = document.getElementById('messages') || createMessagesContainer();
    const messageDiv = document.createElement('div');

    // Add appropriate Bootstrap classes based on message type
    if (type === 'success') {
        messageDiv.className = 'alert alert-success';
    } else if (type === 'error') {
        messageDiv.className = 'alert alert-danger';
    }

    messageDiv.textContent = message;
    messagesContainer.appendChild(messageDiv);

    // Optionally remove message after some time
    setTimeout(() => messageDiv.remove(), 2000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
