{% extends "account/base.html" %}
{% load i18n %}

{% block head_title %}{% trans "E-mail Addresses" %}{% endblock %}

{% block title %}
    {{ block.super }} - {% trans "E-mail Addresses" %}
{% endblock title %}

{% block content %}
<div class="container">
    <div class="row justify-content-center _text-light">
        <div class="col-md-10 col-lg-8 col-xxl-6">

            <h1>{% trans "E-mail Addresses" %}</h1>

            {% if user.emailaddress_set.all %}
            <p>{% trans 'The following e-mail addresses are associated with your account:' %}</p>

            <form action="{% url 'account_email' %}" class="email_list" method="post">
            {% csrf_token %}
            <fieldset class="blockLabels">

              {% for emailaddress in user.emailaddress_set.all %}
            <div class="ctrlHolder mt-1">
                  <label for="email_radio_{{forloop.counter}}" class="{% if emailaddress.primary %}primary_email{%endif%}">

                  <input id="email_radio_{{forloop.counter}}" type="radio" name="email" {% if emailaddress.primary or user.emailaddress_set.count == 1 %}checked="checked"{%endif %} value="{{emailaddress.email}}"/>

            {{ emailaddress.email }}
                {% if emailaddress.verified %}
                <span class="verified">-> {% trans "Verified" %}</span>
                {% else %}
                <span class="unverified">-> {% trans "Unverified" %}</span>
                {% endif %}
                  {% if emailaddress.primary %}<span class="primary">-> {% trans "Primary" %}</span>{% endif %}
            </label>
            </div>
              {% endfor %}

            <div class="buttonHolder mt-3">
                  <button class="btn btn-outline-info" type="submit" name="action_primary" >{% trans 'Make Primary' %}</button>
                  <button class="btn btn-outline-secondary" type="submit" name="action_send" >{% trans 'Re-send Verification' %}</button>
                  <button class="btn btn-outline-danger" type="submit" name="action_remove" onclick="showConfirmDialog(event, 'removeEmailAddress', war)">{% trans 'Remove' %}</button>
            </div>

            </fieldset>
            </form>

            {% else %}
            <p><strong>{% trans 'Warning:'%}</strong> {% trans "You currently do not have any e-mail address set up. You should really add an e-mail address so you can receive notifications, reset your password, etc." %}</p>

            {% endif %}
            <hr>
            {% if can_add_email %}
            <h2>{% trans "Add E-mail Address" %}</h2>

            <form method="post" action="{% url 'account_email' %}" class="add_email">
            {% csrf_token %}
            <div class="row align-items-center">
                 <div class="col-sm-3 text-sm-end px-sm-4 mt-2 lh-1">
                      <label for="id_email">{% trans "E-mail" %}:</label>
                  </div>
                  <div class="col-sm-9 mt-2">
                      <input type="email" name="email" placeholder="{% trans 'E-mail address' %}" required="" id="id_email" class="form-control _bg-dark _text-light">
                  </div>
            </div>
            <div class="mt-3 text-end">
                <button class="btn btn-primary" name="action_add" type="submit">{% trans "Add E-mail" %}</button>
            </div>
            </form>
            {% endif %}

        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script type="text/javascript">
    /*(function() {
      var message = "{% trans 'Do you really want to remove the selected e-mail address?' %}";
      var actions = document.getElementsByName('action_remove');
      if (actions.length) {
        actions[0].addEventListener("click", function(e) {
          if (! confirm(message)) {
            e.preventDefault();
          }
        });
      }
    })();*/
    function removeEmailAddress() {
        // send form programically
        var el = document.getElementsByName('action_remove')[0];
        var hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = el.name;
        el.form.appendChild(hiddenInput);
        el.form.submit();
    }
    function showConfirmDialog(e, feedback, icon) {
        // prevent default action
        e.preventDefault();
        // build dialog
        var params = [
            "{% trans "Are you sure you want to continue?" %}",
            "{% trans "Remove operation can't be undone!" %}",
            icon, // succ, err, war, inf
            'OK',
            'Cancel',
            feedback,
            'close_qual'
        ]
        if (current_theme_mode.replace('auto-','') =='dark') Qual.confirmd(...params); else Qual.confirm(...params);
    }
</script>
{% endblock %}