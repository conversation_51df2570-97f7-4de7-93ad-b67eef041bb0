{% if messages %}
    {% for message in messages %}
<div classs="container p-5">
    <div class="alert {{ message.tags }} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
</div>
    {% endfor %}
{% endif %}

{% if form.errors %}
    {% for field in form %}
        {% for error in field.errors %}
<!--<div classs="container p-5">
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        {{ error|escape }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
</div>-->
        {% endfor %}
    {% endfor %}
    {% for error in form.non_field_errors %}
<div classs="container p-5">
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ error|escape }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
</div>
    {% endfor %}
{% endif %}

<script type="text/javascript">
$( document ).ready(function() {
    // Chowanie alertu po 7 sekundach
    setTimeout(function() {
        var elements = document.getElementsByClassName('alert');
        for (i=0 ; i < elements.length ; i++) {
            elem = elements[i];
            elem.classList.remove('show');
            elem.addEventListener('transitionend', (event) => {
                event.target.remove();
            }, { once: true });
        }
    }, 7000);
});
</script>