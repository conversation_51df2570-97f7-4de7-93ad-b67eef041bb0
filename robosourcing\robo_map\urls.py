from django.urls import path
from rest_framework.urlpatterns import format_suffix_patterns
from robo_map import views

urlpatterns = [
    path('', views.RoboMapList.as_view()),
    path('<app>/<map_version>/file', views.RoboMapFile.as_view()),
    path('<app>/<map_version>/report', views.RoboMapReportSubmit.as_view()),
    path('<app>/<map_version>', views.RoboMapFilter.as_view())
]

# allows to test endpoints using browser
# urlpatterns = format_suffix_patterns(urlpatterns)
