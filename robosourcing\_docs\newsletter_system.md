# System Newslettera - Dokumentacja

## Przegląd systemu

System newslettera w RoboDjango umożliwia użytkownikom zapisywanie się do newslettera z pełną zgodą RODO i systemem potwierdzania email.

## Funkcjonalności

### 1. Zapisywanie do newslettera
- **Lokalizacja**: Stopka strony + dedykowana strona `/newsletter/subscribe/`
- **Zabezpieczenia**: Proste captcha (5 + 3 = 8)
- **Zgody RODO**: Wymagane zgody na przetwarzanie danych i marketing
- **Walidacja**: Sprawdzanie duplikatów email, walidacja zgód

### 2. System potwierdzania email
- **Proces**: Double opt-in (wymagane potwierdzenie przez email)
- **Token**: Bezpieczny token generowany automatycznie
- **Szablony email**: Osobne szablony HTML i tekstowe

### 3. Panel administracyjny
- **Lokalizacja**: Admin panel → Users manager → Newsletter Subscriptions
- **Funkcje**: 
  - Lista subskrypcji z filtrami
  - Eksport do CSV
  - Masowe oznaczanie jako potwierdzone
  - Szczegółowe informacje (IP, User Agent)

## Struktura bazy danych

### Model: NewsletterSubscription
```python
- email: EmailField (unique)
- is_confirmed: BooleanField (default=False)
- confirmation_token: CharField (auto-generated)
- created_at: DateTimeField (auto)
- confirmed_at: DateTimeField (nullable)
- ip_address: GenericIPAddressField
- user_agent: TextField
- marketing_consent: BooleanField (required)
- data_processing_consent: BooleanField (required)
```

## URL-e

### Publiczne
- `/newsletter/` - Strona informacyjna o newsletterze
- `/newsletter/subscribe/` - Formularz zapisów
- `/newsletter/confirm/<token>/` - Potwierdzanie subskrypcji
- `/newsletter/unsubscribe/<token>/` - Wypisywanie się

### Zarządzanie (tylko admin)
- `/newsletter/dashboard/` - Dashboard z statystykami
- `/newsletter/campaign/<id>/stats/` - Statystyki kampanii
- `/newsletter/campaign/<id>/send/` - Wysyłanie kampanii
- `/newsletter/export/` - Eksport wszystkich subskrybentów
- `/newsletter/export/<segment_id>/` - Eksport segmentu

### Tracking (automatyczne)
- `/newsletter/track/open/<tracking_id>/` - Śledzenie otwarć
- `/newsletter/track/click/<tracking_id>/` - Śledzenie kliknięć

## Szablony email

### Potwierdzenie subskrypcji
- **Temat**: "Potwierdź subskrypcję newslettera Botie"
- **Zawartość**: Link do potwierdzenia + informacje o korzyściach
- **Pliki**: 
  - `newsletter_confirmation_subject.txt`
  - `newsletter_confirmation_message.txt`
  - `newsletter_confirmation_message.html`

### Email powitalny
- **Temat**: "Witaj w newsletterze Botie! 🤖"
- **Zawartość**: Powitanie + linki do kluczowych sekcji
- **Pliki**:
  - `newsletter_welcome_subject.txt`
  - `newsletter_welcome_message.txt`
  - `newsletter_welcome_message.html`

## Zabezpieczenia

### 1. Ochrona przed robotami
- Proste pytanie matematyczne (5 + 3 = ?)
- Walidacja po stronie serwera

### 2. RODO
- Wymagane zgody na przetwarzanie danych
- Wymagane zgody na marketing
- Informacje o administratorze danych
- Możliwość wypisania się w każdym emailu

### 3. Bezpieczeństwo
- Unikalne tokeny potwierdzające
- Walidacja duplikatów email
- Logowanie IP i User Agent

## Integracja z systemem mailingu

System wykorzystuje istniejący `Mailer` z `db.mailer`:
- `mailer.newsletter_confirmation(subscription)` - email potwierdzający
- `mailer.newsletter_welcome(subscription)` - email powitalny

## Jak używać

### Dla użytkowników
1. Wpisanie email w stopce lub na stronie `/newsletter/subscribe/`
2. Wypełnienie captcha i zaakceptowanie zgód
3. Sprawdzenie email i kliknięcie linku potwierdzającego
4. Otrzymanie emaila powitalnego

### Dla administratorów

#### Podstawowe zarządzanie
1. Przejście do Admin Panel → Users manager → Newsletter Subscriptions
2. Przeglądanie listy subskrypcji
3. Filtrowanie po statusie potwierdzenia
4. Eksport listy email do CSV
5. Masowe operacje na subskrypcjach

#### Zaawansowane zarządzanie kampanii
1. **Dashboard**: `/newsletter/dashboard/` - przegląd statystyk
2. **Tworzenie segmentów**: Admin → Newsletter Segments
3. **Tworzenie szablonów**: Admin → Newsletter Templates
4. **Kampanie**: Admin → Newsletter Campaigns
5. **Wysyłanie**: Przycisk "Send" w kampanii lub dashboard
6. **Statystyki**: Szczegółowe raporty dla każdej kampanii

#### Workflow tworzenia kampanii
1. Utwórz segment subskrybentów
2. Przygotuj szablon newslettera (HTML + tekst)
3. Utwórz kampanię łączącą szablon i segment
4. Zaplanuj lub wyślij natychmiast
5. Monitoruj statystyki otwarć i kliknięć

## Zaawansowane funkcjonalności

System został rozszerzony o pełny zestaw profesjonalnych funkcji:

### ✅ Segmentacja subskrybentów
- **Model**: `NewsletterSegment`
- **Kryteria**: Data rejestracji, status potwierdzenia
- **Dynamiczne segmenty**: Automatyczne dopasowanie subskrybentów
- **Panel admin**: Zarządzanie segmentami z podglądem liczby subskrybentów

### ✅ Szablony newsletterów
- **Model**: `NewsletterTemplate`
- **Formaty**: HTML i tekstowy
- **Zmienne**: `{{ subscriber.email }}`, `{{ unsubscribe_url }}`, `{{ current_date }}`, `{{ site_url }}`
- **Wersjonowanie**: Śledzenie zmian w szablonach

### ✅ Kampanie newsletterowe
- **Model**: `NewsletterCampaign`
- **Statusy**: Szkic, Zaplanowana, Wysyłanie, Wysłana, Anulowana
- **Harmonogram**: Możliwość planowania wysyłki
- **Statystyki**: Liczba odbiorców, wysłane, nieudane

### ✅ Statystyki otwarć i kliknięć
- **Tracking otwarć**: Przezroczysty pixel 1x1
- **Tracking kliknięć**: Przekierowania przez system
- **Modele**: `NewsletterOpen`, `NewsletterClick`
- **Metryki**: Open rate, Click rate, Click-to-open rate

### ✅ Integracja z zewnętrznymi serwisami
- **Model**: `ExternalMailingService`
- **Obsługiwane**: Mailchimp, SendGrid, Mailgun, Amazon SES
- **Limity**: Dzienny i miesięczny limit wysyłek
- **Failover**: Możliwość konfiguracji zapasowych serwisów

### ✅ Dashboard zarządzania
- **URL**: `/newsletter/dashboard/`
- **Statystyki**: Subskrybenci, kampanie, aktywność
- **Szybkie akcje**: Tworzenie szablonów, segmentów, kampanii
- **Wykresy**: Aktywność z ostatnich 30 dni

## Troubleshooting

### Problem: Email nie dociera
- Sprawdź konfigurację SMTP w settings.py
- Sprawdź logi serwera email
- Sprawdź folder spam

### Problem: Token nie działa
- Sprawdź czy token nie wygasł
- Sprawdź czy URL jest kompletny
- Sprawdź logi aplikacji

### Problem: Duplikaty email
- System automatycznie blokuje duplikaty
- Sprawdź czy email nie jest już w bazie
- Użyj funkcji "wypisz się" i zapisz ponownie
