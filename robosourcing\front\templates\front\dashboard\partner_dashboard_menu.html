<!-- front/templates/front/dashboard/partner_dashboard_menu.html -->

{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load split %}

{% block title %}{{ block.super }} - {% trans "Partner Panel" %}{% endblock %}

{% block extra_content_1 %}

<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item site-name active">{% trans "Partner Panel" %}</li>
        </ol>
    </nav>
    <div class="row g-3">
           <!-- Affiliate Link -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h2 class="db-section-title">{% trans "Your Affiliate Link" %}</h2>   
                <hr>
                
                <!-- Instrukcja Nad Linkiem -->
                <div class="mb-2 ms-4 me-4">
                    <p class="text-muted">
                        {% trans "Share this link with your clients. They must use it during registration to be associated with you as their partner." %}
                    </p>
                </div>
                
                <!-- Link Afiliacyjny i Przycisk Kopiowania -->
                <div class="mb-4 ms-4 me-4 d-flex align-items-center">
                    <p id="affiliate-link" class="fs-4 fw-bold mb-0 me-2">{{ affiliate_link }}</p>
                    <button id="copy-button" class="btn btn-primary btn-sm" title="{% trans 'Copy to clipboard' %}">
                        <i class="fa fa-copy" aria-hidden="true"></i> {% trans "Copy" %}
                    </button>
                </div>
                
                <!-- Komunikat Zwrotny po Kopiowaniu -->
                <div id="copy-feedback" class="mt-2 text-success" style="display: none;">
                    {% trans "Link copied to clipboard!" %}
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h2 class="db-section-title">{% trans "Your Commissions" %}</h2>   
                <hr>
                <div class="mb-4 ms-4 me-4">
                    <p><strong>{% trans "Current Commission Rate:" %}</strong> {{ current_commission_rate }}%</p>
                    <p><strong>{% trans "Total Commission:" %}</strong> {{ total_commission }}</p>
                </div>
            </div>
        </div>

        <!-- Referred Users -->
        <div class="col-lg-12 col-md-12 mb-4">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h2 class="db-section-title">{% trans "Your Clients" %}</h2> 
                <hr>
                <div class="mb-4 ms-4 me-4">
                    <ul>
                        {% for user in referred_users %}
                            <li>{{ user.user.username }} - {% trans "Registered" %}: {{ user.user.date_joined }}</li>
                        {% empty %}
                            <li>{% trans "No referred users." %}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>

        <!-- Links to Subpages -->
        <div class="col-auto"> 
            <a href="{% url 'partner_dashboard_withdrawal' %}" class="welcome-tile-menu">
                <i class="fa fa-calendar-alt tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Withdrawal Cycles and History" %}
            </a>
        </div>
        <div class="col-auto"> 
            <a href="{% url 'partner_dashboard_commission' %}" class="welcome-tile-menu">
                <i class="fa fa-chart-line tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Commission Details" %}
            </a>
        </div>
    </div>
</div>


<!-- JavaScript do kopiowania -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const copyButton = document.getElementById('copy-button');
        const affiliateLink = document.getElementById('affiliate-link').innerText;
        const feedback = document.getElementById('copy-feedback');

        copyButton.addEventListener('click', function() {
            // Tworzenie tymczasowego elementu textarea
            const tempTextArea = document.createElement('textarea');
            tempTextArea.value = affiliateLink;
            document.body.appendChild(tempTextArea);
            tempTextArea.select();
            tempTextArea.setSelectionRange(0, 99999); // Dla urządzeń mobilnych

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    // Pokazanie komunikatu o sukcesie
                    feedback.style.display = 'block';
                    setTimeout(() => {
                        feedback.style.display = 'none';
                    }, 2000);
                } else {
                    // Obsługa przypadku nieudanego kopiowania
                    alert('{% trans "Failed to copy the link. Please try manually." %}');
                }
            } catch (err) {
                // Obsługa błędów
                alert('{% trans "Error copying the link." %}');
            }

            // Usunięcie tymczasowego textarea
            document.body.removeChild(tempTextArea);
        });
    });
</script>


{% endblock %}
