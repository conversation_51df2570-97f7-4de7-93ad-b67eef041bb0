{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Newsletter Dashboard" %}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{% trans "Newsletter" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fa fa-envelope me-2"></i>{% trans "Newsletter Dashboard" %}</h2>
        </div>
    </div>

    <!-- Statystyki -->
    <div class="newsletter-stats-grid">
        <div class="newsletter-stat-card">
            <i class="fa fa-users newsletter-action-icon mb-2"></i>
            <span class="newsletter-stat-number">{{ total_subscriptions }}</span>
            <span class="newsletter-stat-label">{% trans "Total Subscriptions" %}</span>
        </div>
        <div class="newsletter-stat-card">
            <i class="fa fa-check-circle newsletter-action-icon mb-2"></i>
            <span class="newsletter-stat-number">{{ confirmed_subscriptions }}</span>
            <span class="newsletter-stat-label">{% trans "Confirmed" %}</span>
        </div>
        <div class="newsletter-stat-card">
            <i class="fa fa-clock newsletter-action-icon mb-2"></i>
            <span class="newsletter-stat-number">{{ pending_subscriptions }}</span>
            <span class="newsletter-stat-label">{% trans "Pending" %}</span>
        </div>
        <div class="newsletter-stat-card">
            <i class="fa fa-calendar newsletter-action-icon mb-2"></i>
            <span class="newsletter-stat-number">{{ recent_subscriptions }}</span>
            <span class="newsletter-stat-label">{% trans "Last 30 days" %}</span>
        </div>
    </div>

    <!-- Akcje szybkie -->
    <div class="newsletter-quick-actions">
        <a href="{% url 'profiles_newsletter_campaigns' %}" class="newsletter-action-tile">
            <i class="fa fa-paper-plane newsletter-action-icon"></i>
            <span>{% trans "Campaigns" %}</span>
        </a>
        <a href="{% url 'profiles_newsletter_subscribers' %}" class="newsletter-action-tile">
            <i class="fa fa-users newsletter-action-icon"></i>
            <span>{% trans "Subscribers" %}</span>
        </a>
        <a href="{% url 'profiles_newsletter_templates' %}" class="newsletter-action-tile">
            <i class="fa fa-file-alt newsletter-action-icon"></i>
            <span>{% trans "Templates" %}</span>
        </a>
        <a href="{% url 'profiles_newsletter_segments' %}" class="newsletter-action-tile">
            <i class="fa fa-layer-group newsletter-action-icon"></i>
            <span>{% trans "Segments" %}</span>
        </a>
        <a href="{% url 'profiles_newsletter_analytics' %}" class="newsletter-action-tile">
            <i class="fa fa-chart-bar newsletter-action-icon"></i>
            <span>{% trans "Analytics" %}</span>
        </a>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <div class="row">
        <!-- Ostatnie kampanie -->
        <div class="col-md-6">
            <div class="newsletter-card">
                <div class="newsletter-card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "Recent Campaigns" %}</h5>
                    <a href="{% url 'profiles_newsletter_campaigns' %}" class="newsletter-btn-secondary">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="newsletter-card-body">
                    {% if recent_campaigns %}
                        <div class="list-group list-group-flush">
                            {% for campaign in recent_campaigns %}
                            <div class="list-group-item d-flex justify-content-between align-items-start border-0 px-0">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ campaign.subject }}</div>
                                    <small class="newsletter-muted-text">
                                        {% if campaign.template %}{{ campaign.template.name }}{% endif %}
                                        {% if campaign.segment %} • {{ campaign.segment.name }}{% endif %}
                                    </small>
                                </div>
                                <span class="newsletter-badge newsletter-badge-{% if campaign.status == 'sent' %}success{% elif campaign.status == 'scheduled' %}warning{% else %}secondary{% endif %}">
                                    {{ campaign.get_status_display }}
                                </span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-center py-3" style="color: var(--muted-foreground);">{% trans "No campaigns yet" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Segmenty -->
        <div class="col-md-6">
            <div class="newsletter-card">
                <div class="newsletter-card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "Segments" %}</h5>
                    <a href="{% url 'profiles_newsletter_segments' %}" class="newsletter-btn-secondary">
                        {% trans "Manage" %}
                    </a>
                </div>
                <div class="newsletter-card-body">
                    {% if segments_with_counts %}
                        <div class="list-group list-group-flush">
                            {% for segment in segments_with_counts %}
                            <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                <div>
                                    <div class="fw-bold">{{ segment.name }}</div>
                                    <small style="color: var(--muted-foreground);">{{ segment.description|truncatechars:50 }}</small>
                                </div>
                                <span class="newsletter-badge newsletter-badge-success">{{ segment.subscriber_count }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-center py-3" style="color: var(--muted-foreground);">{% trans "No segments created yet" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Ostatni subskrybenci -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="newsletter-card">
                <div class="newsletter-card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "Recent Subscribers" %}</h5>
                    <a href="{% url 'profiles_newsletter_subscribers' %}" class="newsletter-btn-secondary">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="newsletter-card-body">
                    {% if recent_subscribers %}
                        <div class="table-responsive">
                            <table class="newsletter-table table">
                                <thead>
                                    <tr>
                                        <th>{% trans "Email" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Subscribed" %}</th>
                                        <th>{% trans "Source" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subscriber in recent_subscribers %}
                                    <tr>
                                        <td>{{ subscriber.email }}</td>
                                        <td>
                                            {% if subscriber.is_confirmed %}
                                                <span class="newsletter-badge newsletter-badge-success">{% trans "Confirmed" %}</span>
                                            {% else %}
                                                <span class="newsletter-badge newsletter-badge-warning">{% trans "Pending" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ subscriber.created_at|date:"d.m.Y H:i" }}</td>
                                        <td>{{ subscriber.source|default:"-" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-center py-3" style="color: var(--muted-foreground);">{% trans "No subscribers yet" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Odświeżanie statystyk co 30 sekund
    setInterval(function() {
        // Można dodać AJAX do odświeżania statystyk
    }, 30000);
});
</script>
{% endblock %}
