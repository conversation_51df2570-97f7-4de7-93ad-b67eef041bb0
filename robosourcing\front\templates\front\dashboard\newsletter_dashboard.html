{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Newsletter Dashboard" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item site-name"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Newsletter" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fa fa-envelope me-2"></i>{% trans "Newsletter Dashboard" %}</h2>
        </div>
    </div>

    <!-- Statystyki -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="dashboard-card stats-card">
                <div class="card-body text-center">
                    <i class="fa fa-users fa-2x text-primary mb-2"></i>
                    <h3 class="mb-0">{{ total_subscriptions }}</h3>
                    <p class="text-muted mb-0">{% trans "Total Subscriptions" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card stats-card">
                <div class="card-body text-center">
                    <i class="fa fa-check-circle fa-2x text-success mb-2"></i>
                    <h3 class="mb-0">{{ confirmed_subscriptions }}</h3>
                    <p class="text-muted mb-0">{% trans "Confirmed" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card stats-card">
                <div class="card-body text-center">
                    <i class="fa fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="mb-0">{{ pending_subscriptions }}</h3>
                    <p class="text-muted mb-0">{% trans "Pending" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card stats-card">
                <div class="card-body text-center">
                    <i class="fa fa-calendar fa-2x text-info mb-2"></i>
                    <h3 class="mb-0">{{ recent_subscriptions }}</h3>
                    <p class="text-muted mb-0">{% trans "Last 30 days" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Akcje szybkie -->
    <div class="row g-3 mb-4">
        <div class="col-auto">
            <a href="{% url 'newsletter_campaigns' %}" class="welcome-tile-menu">
                <i class="fa fa-paper-plane tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Campaigns" %}
            </a>
        </div>
        <div class="col-auto">
            <a href="{% url 'newsletter_subscribers' %}" class="welcome-tile-menu">
                <i class="fa fa-users tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Subscribers" %}
            </a>
        </div>
        <div class="col-auto">
            <a href="{% url 'newsletter_templates' %}" class="welcome-tile-menu">
                <i class="fa fa-file-alt tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Templates" %}
            </a>
        </div>
        <div class="col-auto">
            <a href="{% url 'newsletter_segments' %}" class="welcome-tile-menu">
                <i class="fa fa-layer-group tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Segments" %}
            </a>
        </div>
        <div class="col-auto">
            <a href="{% url 'newsletter_analytics' %}" class="welcome-tile-menu">
                <i class="fa fa-chart-bar tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Analytics" %}
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container">
    <div class="row">
        <!-- Ostatnie kampanie -->
        <div class="col-md-6">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "Recent Campaigns" %}</h5>
                    <a href="{% url 'newsletter_campaigns' %}" class="btn btn-sm btn-outline-primary">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_campaigns %}
                        <div class="list-group list-group-flush">
                            {% for campaign in recent_campaigns %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ campaign.subject }}</div>
                                    <small class="text-muted">
                                        {% if campaign.template %}{{ campaign.template.name }}{% endif %}
                                        {% if campaign.segment %} • {{ campaign.segment.name }}{% endif %}
                                    </small>
                                </div>
                                <span class="badge bg-{% if campaign.status == 'sent' %}success{% elif campaign.status == 'scheduled' %}warning{% else %}secondary{% endif %} rounded-pill">
                                    {{ campaign.get_status_display }}
                                </span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted text-center py-3">{% trans "No campaigns yet" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Segmenty -->
        <div class="col-md-6">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "Segments" %}</h5>
                    <a href="{% url 'newsletter_segments' %}" class="btn btn-sm btn-outline-primary">
                        {% trans "Manage" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if segments_with_counts %}
                        <div class="list-group list-group-flush">
                            {% for segment in segments_with_counts %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="fw-bold">{{ segment.name }}</div>
                                    <small class="text-muted">{{ segment.description|truncatechars:50 }}</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">{{ segment.subscriber_count }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted text-center py-3">{% trans "No segments created yet" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Ostatni subskrybenci -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "Recent Subscribers" %}</h5>
                    <a href="{% url 'newsletter_subscribers' %}" class="btn btn-sm btn-outline-primary">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_subscribers %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Email" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Subscribed" %}</th>
                                        <th>{% trans "Source" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subscriber in recent_subscribers %}
                                    <tr>
                                        <td>{{ subscriber.email }}</td>
                                        <td>
                                            {% if subscriber.is_confirmed %}
                                                <span class="badge bg-success">{% trans "Confirmed" %}</span>
                                            {% else %}
                                                <span class="badge bg-warning">{% trans "Pending" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ subscriber.created_at|date:"d.m.Y H:i" }}</td>
                                        <td>{{ subscriber.source|default:"-" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center py-3">{% trans "No subscribers yet" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Odświeżanie statystyk co 30 sekund
    setInterval(function() {
        // Można dodać AJAX do odświeżania statystyk
    }, 30000);
});
</script>
{% endblock %}
