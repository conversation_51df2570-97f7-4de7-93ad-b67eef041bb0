# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-17 15:18+0200\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n"
"%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n"
"%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#: .\db\mailer.py:193 .\db\models.py:1303
#: .\robosourcing\templates\account\email\extra\after_registration_subject.txt:3
msgid "Thank you for registering an account"
msgstr "Dziękujemy za zarejestrowanie konta"

#: .\db\mailer.py:238 .\db\models.py:1304
#: .\robosourcing\templates\account\email\extra\after_login_subject.txt:3
msgid "Welcome to our web service"
msgstr "Witamy w naszym serwisie internetowym"

#: .\db\models.py:175
msgid "Locked by User"
msgstr "Zablokowany przez użytkownika"

#: .\db\models.py:176
msgid "Locked by Admin"
msgstr "Zablokowany przez administratora"

#: .\db\models.py:177
msgid "Locked by active job"
msgstr "Zablokowany przez aktywne zadanie"

#: .\db\models.py:468
msgid "Purchase"
msgstr "Zakup"

#: .\db\models.py:469 .\front\templates\front\dashboard\store_switch.html:16
#: .\front\templates\front\dashboard\store_switch.html:21
#: .\front\templates\front\dashboard\store_update.html:21
#: .\robosourcing\templates\menu_dashboard.html:37
msgid "Subscription change"
msgstr "Zmiana subskrypcji"

#: .\db\models.py:470
msgid "Credits top-up"
msgstr "Doładowanie kredytów"

#: .\db\models.py:471
msgid "Freebie"
msgstr "Gratis"

#: .\db\models.py:472
msgid "Credits usage"
msgstr "Wykorzystanie kredytów"

#: .\db\models.py:662
msgid "Group name"
msgstr "Nazwa scenariusza"

#: .\db\models.py:669
msgid "Group icon"
msgstr "Ikona dla grupy"

#: .\db\models.py:673 .\db\models.py:699
msgid "Tag group"
msgstr "Grupa tagów"

#: .\db\models.py:674
msgid "Tag groups"
msgstr "Grupa tagów"

#: .\db\models.py:691
msgid "Tag name"
msgstr "Nazwa tagu"

#: .\db\models.py:703
msgid "Tag"
msgstr "Tag"

#: .\db\models.py:704
#: .\front\templates\front\dashboard\robot_scenarios_view.html:95
#: .\front\templates\front\dashboard\scenario_detail.html:69
#: .\front\templates\front\home_page\scenario_detail.html:32
msgid "Tags"
msgstr "Tagi"

#: .\db\models.py:784 .\db\models.py:1111
msgid "Sent to moderation"
msgstr "Wysłano do moderacji"

#: .\db\models.py:785 .\db\models.py:1112
msgid "Assigned to moderator"
msgstr "Przydzielono moderatorowi"

#: .\db\models.py:786 .\db\models.py:1113
msgid "Scenario in moderation"
msgstr "Scenariusz w trakcie moderacji"

#: .\db\models.py:787 .\db\models.py:1114 .\db\models.py:1315
msgid "Scenario rejected"
msgstr "Scenariusz odrzucony"

#: .\db\models.py:788 .\db\models.py:1115 .\db\models.py:1314
msgid "Scenario accepted"
msgstr "Scenariusz zaakceptowany"

#: .\db\models.py:789 .\db\models.py:1118
msgid "Scenario suspended"
msgstr "Scenariusz zawieszony"

#: .\db\models.py:790 .\db\models.py:1120
msgid "Scenario canceled"
msgstr "Scenariusz anulowany"

#: .\db\models.py:793
#: .\front\templates\front\dashboard\robot_scenarios_view.html:631
msgid "Private"
msgstr "Prywatny"

#: .\db\models.py:794
msgid "Limited to account"
msgstr "Ograniczony do konta"

#: .\db\models.py:795
msgid "limited to group"
msgstr "Ograniczony do grupy"

#: .\db\models.py:796
#: .\front\templates\front\dashboard\robot_scenarios_view.html:634
msgid "Public"
msgstr "Publiczny"

#: .\db\models.py:799
msgid "Fresh scenario"
msgstr "Świeży scenariusz"

#: .\db\models.py:800
msgid "Branch related scenario"
msgstr "Scenariusz powiązany za pomocą gałęzi"

#: .\db\models.py:801
msgid "Version related scenario"
msgstr "Scenariusz powiązany za pomoca wersji"

#: .\db\models.py:821
msgid "Assigned moderator"
msgstr "Przydzielony moderator"

#: .\db\models.py:1116
msgid "Scenario added to favorites"
msgstr "Scenariusz dodany do ulubionych"

#: .\db\models.py:1117
msgid "Scenario file dawnloaded"
msgstr "Plik scenariusza pobrany"

#: .\db\models.py:1119
msgid "Scenario restored"
msgstr "Scenariusz przywrócony"

#: .\db\models.py:1121
msgid "Return Scenario to pool"
msgstr "Przywróć scenariusz do puli"

#: .\db\models.py:1122
msgid "Scenario removed from favorites"
msgstr "Scenariusz usunięty z ulubionych"

#: .\db\models.py:1123
msgid "Scenario completed"
msgstr "Scenariusz ukończony"

#: .\db\models.py:1124
msgid "Scenario assigned to robot"
msgstr "Scenariusz został pomyślnie przypisany do robota."

#: .\db\models.py:1305
#: .\robosourcing\templates\account\email\extra\after_purchase_subject.txt:3
msgid "Thank you for making your purchases"
msgstr "Dziękujemy za dokonanie zakupów"

#: .\db\models.py:1306
#: .\robosourcing\templates\account\email\extra\subscription_event_completed_subject.txt:3
msgid "Your subscription event completed successfully"
msgstr "Twoje zdarzenie subskrypcji zakończyło się pomyślnie"

#: .\db\models.py:1307
#: .\robosourcing\templates\account\email\extra\subscription_event_canceled_subject.txt:3
msgid "Your subscription event has failed"
msgstr "Twoje zdarzenie subskrypcji nie powiodło się"

#: .\db\models.py:1308 .\front\templates\front\dashboard\store_switch.html:47
#: .\robosourcing\templates\account\email\extra\payment_card_expire_soon_subject.txt:3
msgid "Your payment card expires soon"
msgstr "Twoja karta płatnicza wkrótce wygaśnie"

#: .\db\models.py:1309
#: .\robosourcing\templates\account\email\extra\payment_card_expired_subject.txt:3
msgid "Your payment card has expired"
msgstr "Twoja karta płatnicza wygasła"

#: .\db\models.py:1310
msgid "Contact form submitted"
msgstr "Formularz kontaktowy wysłany"

#: .\db\models.py:1311
msgid "Withdrawal request submitted"
msgstr "Prośba o wypłatę środków została wysłana"

#: .\db\models.py:1312
msgid "Monthly withdrawal summary"
msgstr "Podsumowanie miesięcznej wypłaty"

#: .\db\models.py:1313
msgid "Password reset key"
msgstr "Klucz do resetu hasła"

#: .\db\models.py:1318
msgid "Failed"
msgstr "Niepowodzenie"

#: .\db\models.py:1319
msgid "Sent"
msgstr "Wysłano"

#: .\front\templates\front\dashboard\base\modals.html:12
msgid "Are you sure you want to sign out bro!?"
msgstr "Czy na pewno chcesz się wylogować, bracie?"

#: .\front\templates\front\dashboard\base\modals.html:15
#: .\front\templates\front\dashboard\base\modals.html:40
#: .\front\templates\front\dashboard\base\modals.html:47
#: .\front\templates\front\dashboard\base\modals.html:68
#: .\front\templates\front\dashboard\base\modals.html:92
#: .\front\templates\front\dashboard\base\modals.html:138
#: .\front\templates\front\dashboard\base\modals.html:156
#: .\front\templates\front\dashboard\base\modals.html:171
#: .\front\templates\front\dashboard\base\modals.html:226
#: .\front\templates\front\dashboard\base\modals.html:240
#: .\front\templates\front\dashboard\base\modals.html:263
#: .\front\templates\front\dashboard\base\modals.html:288
#: .\front\templates\front\dashboard\base\modals.html:319
#: .\front\templates\front\dashboard\base\modals.html:398
msgid "Close"
msgstr "Zamknij"

#: .\front\templates\front\dashboard\base\modals.html:24
msgid "Yes"
msgstr "Tak"

#: .\front\templates\front\dashboard\base\modals.html:38
msgid "Create new robot:"
msgstr "Utwórz nowego robota:"

#: .\front\templates\front\dashboard\base\modals.html:48
#: .\front\templates\front\dashboard\base\modals.html:124
#: .\front\templates\front\dashboard\base\modals.html:157
#: .\front\templates\front\dashboard\base\modals.html:227
#: .\front\templates\front\dashboard\base\modals.html:264
#: .\front\templates\front\dashboard\base\modals.html:306
msgid "Save"
msgstr "Zapisz"

#: .\front\templates\front\dashboard\base\modals.html:67
msgid "New Key"
msgstr "Nowy klucz"

#: .\front\templates\front\dashboard\base\modals.html:73
msgid "Time created:"
msgstr "Czas utworzenia:"

#: .\front\templates\front\dashboard\base\modals.html:74
msgid "You can use key for next:"
msgstr "Możesz używać klucza przez kolejne:"

#: .\front\templates\front\dashboard\base\modals.html:74
msgid "minutes."
msgstr "minuty."

#: .\front\templates\front\dashboard\base\modals.html:77
msgid "Copy to Clipboard"
msgstr "Kopiuj do schowka"

#: .\front\templates\front\dashboard\base\modals.html:91
msgid "Assign scenario to robots"
msgstr "Przypisz scenariusz do robotów"

#: .\front\templates\front\dashboard\base\modals.html:108
msgid "Select/Deselect All"
msgstr "Zaznacz/Odznacz wszystko"

#: .\front\templates\front\dashboard\base\modals.html:123
#: .\front\templates\front\dashboard\base\modals.html:305
msgid "Cancel"
msgstr "Anuluj"

#: .\front\templates\front\dashboard\base\modals.html:137
msgid "Change personal data"
msgstr "Zmień dane osobowe"

#: .\front\templates\front\dashboard\base\modals.html:143
msgid "You can only change your first and last name in"
msgstr "Możesz zmienić swoje imię i nazwisko tylko w"

#: .\front\templates\front\dashboard\base\modals.html:143
msgid "special cases"
msgstr "szczególnych przypadkach"

#: .\front\templates\front\dashboard\base\modals.html:143
msgid ""
"when the change is confirmed by court or administrative decision. Account "
"holder change is only possible between spouses."
msgstr ""
"gdy zmiana jest potwierdzona decyzją sądu lub administracyjną. Zmiana "
"właściciela konta jest możliwa tylko między małżonkami."

#: .\front\templates\front\dashboard\base\modals.html:145
#: .\front\templates\front\dashboard\base\modals.html:146
msgid "First name"
msgstr "Imię"

#: .\front\templates\front\dashboard\base\modals.html:147
msgid "First name is required."
msgstr "Imię jest wymagane."

#: .\front\templates\front\dashboard\base\modals.html:150
#: .\front\templates\front\dashboard\base\modals.html:151
msgid "Last name"
msgstr "Nazwisko"

#: .\front\templates\front\dashboard\base\modals.html:152
msgid "Last name is required."
msgstr "Nazwisko jest wymagane."

#: .\front\templates\front\dashboard\base\modals.html:157
#: .\front\templates\front\dashboard\base\modals.html:227
#: .\front\templates\front\dashboard\base\modals.html:264
#: .\front\templates\front\dashboard\base\modals.html:399
msgid "Save changes"
msgstr "Zapisz zmiany"

#: .\front\templates\front\dashboard\base\modals.html:170
msgid "Change address data"
msgstr "Zmień dane adresowe"

#: .\front\templates\front\dashboard\base\modals.html:177
#: .\front\templates\front\dashboard\base\modals.html:331
msgid "Country"
msgstr "Kraj"

#: .\front\templates\front\dashboard\base\modals.html:179
#: .\front\templates\front\dashboard\base\modals.html:333
msgid "United Kingdom"
msgstr "Wielka Brytania"

#: .\front\templates\front\dashboard\base\modals.html:180
#: .\front\templates\front\dashboard\base\modals.html:334
msgid "Poland"
msgstr "Polska"

#: .\front\templates\front\dashboard\base\modals.html:181
#: .\front\templates\front\dashboard\base\modals.html:335
msgid "Germany"
msgstr "Niemcy"

#: .\front\templates\front\dashboard\base\modals.html:182
#: .\front\templates\front\dashboard\base\modals.html:336
msgid "United States"
msgstr "Stany Zjednoczone"

#: .\front\templates\front\dashboard\base\modals.html:183
#: .\front\templates\front\dashboard\base\modals.html:337
msgid "France"
msgstr "Francja"

#: .\front\templates\front\dashboard\base\modals.html:184
#: .\front\templates\front\dashboard\base\modals.html:338
msgid "Italy"
msgstr "Włochy"

#: .\front\templates\front\dashboard\base\modals.html:185
#: .\front\templates\front\dashboard\base\modals.html:339
msgid "Other"
msgstr "Inne"

#: .\front\templates\front\dashboard\base\modals.html:187
#: .\front\templates\front\dashboard\base\modals.html:341
msgid "Country is required."
msgstr "Kraj jest wymagany."

#: .\front\templates\front\dashboard\base\modals.html:191
#: .\front\templates\front\dashboard\base\modals.html:192
#: .\front\templates\front\dashboard\base\modals.html:361
#: .\front\templates\front\dashboard\base\modals.html:362
msgid "Street"
msgstr "Ulica"

#: .\front\templates\front\dashboard\base\modals.html:193
msgid "Street is required."
msgstr "Ulica jest wymagana."

#: .\front\templates\front\dashboard\base\modals.html:196
#: .\front\templates\front\dashboard\base\modals.html:197
#: .\front\templates\front\dashboard\base\modals.html:368
#: .\front\templates\front\dashboard\base\modals.html:369
msgid "Street number"
msgstr "Numer ulicy"

#: .\front\templates\front\dashboard\base\modals.html:198
#: .\front\templates\front\dashboard\base\modals.html:370
msgid "Street number is required."
msgstr "Numer ulicy jest wymagany."

#: .\front\templates\front\dashboard\base\modals.html:201
#: .\front\templates\front\dashboard\base\modals.html:202
#: .\front\templates\front\dashboard\base\modals.html:375
#: .\front\templates\front\dashboard\base\modals.html:376
msgid "Apartment number"
msgstr "Numer mieszkania"

#: .\front\templates\front\dashboard\base\modals.html:205
#: .\front\templates\front\dashboard\base\modals.html:206
#: .\front\templates\front\dashboard\base\modals.html:383
#: .\front\templates\front\dashboard\base\modals.html:384
msgid "Postal code"
msgstr "Kod pocztowy"

#: .\front\templates\front\dashboard\base\modals.html:210
#: .\front\templates\front\dashboard\base\modals.html:211
#: .\front\templates\front\dashboard\base\modals.html:390
#: .\front\templates\front\dashboard\base\modals.html:391
msgid "City"
msgstr "Miasto"

#: .\front\templates\front\dashboard\base\modals.html:212
#: .\front\templates\front\dashboard\base\modals.html:392
msgid "City is required."
msgstr "Miasto jest wymagane."

#: .\front\templates\front\dashboard\base\modals.html:215
msgid "Timezone"
msgstr "Strefa czasowa"

#: .\front\templates\front\dashboard\base\modals.html:222
msgid "Timezone selection is required."
msgstr "Wybór strefy czasowej jest wymagany."

#: .\front\templates\front\dashboard\base\modals.html:239
msgid "Change phone number"
msgstr "Zmień numer telefonu"

#: .\front\templates\front\dashboard\base\modals.html:248
#: .\front\templates\front\dashboard\base\modals.html:249
msgid "Area code"
msgstr "Kod kierunkowy"

#: .\front\templates\front\dashboard\base\modals.html:250
msgid "Enter the country code in the format '+00' or '+000'."
msgstr "Wprowadź kod kraju w formacie '+00' lub '+000'."

#: .\front\templates\front\dashboard\base\modals.html:255
#: .\front\templates\front\dashboard\base\modals.html:256
msgid "Phone number"
msgstr "Numer telefonu"

#: .\front\templates\front\dashboard\base\modals.html:257
msgid "Enter a valid phone number 7-10 digits."
msgstr "Wprowadź prawidłowy numer telefonu 7-10 cyfr."

#: .\front\templates\front\dashboard\base\modals.html:287
msgid "Block Credits for Offline Use"
msgstr "Zablokuj kredyty do użytku offline"

#: .\front\templates\front\dashboard\base\modals.html:292
msgid "Total Credits:"
msgstr "Całkowita liczba kredytów:"

#: .\front\templates\front\dashboard\base\modals.html:293
msgid "Blocked Credits for this Robot:"
msgstr "Zablokowane kredyty dla tego robota:"

#: .\front\templates\front\dashboard\base\modals.html:295
msgid "Credits to Block/Unblock"
msgstr "Kredyty do zablokowania/odblokowania"

#: .\front\templates\front\dashboard\base\modals.html:298
msgid "Block Credits"
msgstr "Zablokuj kredyty"

#: .\front\templates\front\dashboard\base\modals.html:299
msgid "Unblock Credits"
msgstr "Odblokuj kredyty"

#: .\front\templates\front\dashboard\base\modals.html:318
msgid "Change organization data"
msgstr "Zmień dane organizacji"

#: .\front\templates\front\dashboard\base\modals.html:346
#: .\front\templates\front\dashboard\base\modals.html:347
msgid "TAX number"
msgstr "Numer NIP"

#: .\front\templates\front\dashboard\base\modals.html:352
#: .\front\templates\front\dashboard\base\modals.html:353
msgid "Organization name 1"
msgstr "Nazwa organizacji 1"

#: .\front\templates\front\dashboard\base\modals.html:354
msgid "Organization name is required."
msgstr "Nazwa organizacji jest wymagana."

#: .\front\templates\front\dashboard\base\modals.html:357
#: .\front\templates\front\dashboard\base\modals.html:358
msgid "Organization name 2"
msgstr "Nazwa organizacji 2"

#: .\front\templates\front\dashboard\base\modals.html:363
msgid "Organization street is required."
msgstr "Ulica organizacji jest wymagana."

#: .\front\templates\front\dashboard\base\modals.html:411
msgid "A valid UK VAT number must have the format GB followed by 9 digits."
msgstr ""
"Prawidłowy brytyjski numer VAT musi mieć format GB, po którym następuje 9 "
"cyfr."

#: .\front\templates\front\dashboard\base\modals.html:415
msgid ""
"A valid French VAT number must have the format FR followed by 2 characters "
"and 9 digits."
msgstr ""
"Prawidłowy francuski numer VAT musi mieć format FR, po którym następują 2 "
"znaki i 9 cyfr."

#: .\front\templates\front\dashboard\base\modals.html:419
msgid "A valid German VAT number must have the format DE followed by 9 digits."
msgstr ""
"Prawidłowy niemiecki numer VAT musi mieć format DE, po którym następuje 9 "
"cyfr."

#: .\front\templates\front\dashboard\base\modals.html:423
msgid ""
"A valid Italian VAT number must have the format IT followed by 11 digits."
msgstr ""
"Prawidłowy włoski numer VAT musi mieć format IT, po którym następuje 11 cyfr."

#: .\front\templates\front\dashboard\base\modals.html:427
msgid "A valid Polish NIP number must have 10 digits."
msgstr "Prawidłowy polski numer NIP musi mieć 10 cyfr."

#: .\front\templates\front\dashboard\base\modals.html:431
msgid "A valid US SSN must have the format ***********."
msgstr "Prawidłowy amerykański numer SSN musi mieć format ***********."

#: .\front\templates\front\dashboard\base\modals.html:435
#: .\front\templates\front\dashboard\base\modals.html:468
msgid "No validation required for Other."
msgstr "Brak wymagań walidacji dla opcji Inne."

#: .\front\templates\front\dashboard\base\modals.html:444
msgid "Example: W1A 1AA or W1A1AA"
msgstr "Przykład: W1A 1AA lub W1A1AA"

#: .\front\templates\front\dashboard\base\modals.html:448
msgid "Example: 75001"
msgstr "Przykład: 75001"

#: .\front\templates\front\dashboard\base\modals.html:452
msgid "Example: 10115"
msgstr "Przykład: 10115"

#: .\front\templates\front\dashboard\base\modals.html:456
msgid "Example: 00100"
msgstr "Przykład: 00100"

#: .\front\templates\front\dashboard\base\modals.html:460
msgid "Example: 00-001"
msgstr "Przykład: 00-001"

#: .\front\templates\front\dashboard\base\modals.html:464
msgid "Example: 12345 or 12345-6789"
msgstr "Przykład: 12345 lub 12345-6789"

#: .\front\templates\front\dashboard\base\side-bar.html:37
#: .\front\templates\front\dashboard\basket.html:14
#: .\front\templates\front\dashboard\index.html:6
#: .\front\templates\front\dashboard\index.html:13
#: .\front\templates\front\dashboard\invoices.html:13
#: .\front\templates\front\dashboard\kredyty.html:16
#: .\front\templates\front\dashboard\orders.html:13
#: .\front\templates\front\dashboard\partner_dashboard.html:15
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:16
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:15
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:15
#: .\front\templates\front\dashboard\profil.html:13
#: .\front\templates\front\dashboard\robot_scenarios_view.html:12
#: .\front\templates\front\dashboard\roboty.html:12
#: .\front\templates\front\dashboard\scenario_change_history.html:12
#: .\front\templates\front\dashboard\scenario_detail.html:10
#: .\front\templates\front\dashboard\scenario_mod.html:13
#: .\front\templates\front\dashboard\scenario_mod_details.html:12
#: .\front\templates\front\dashboard\store.html:13
#: .\front\templates\front\dashboard\store_abo.html:11
#: .\front\templates\front\dashboard\store_credits.html:11
#: .\front\templates\front\dashboard\store_switch.html:14
#: .\front\templates\front\dashboard\store_update.html:14
msgid "Dashboard"
msgstr "Panel Użytkownika"

#: .\front\templates\front\dashboard\base\side-bar.html:51
#: .\front\templates\front\dashboard\basket.html:15
#: .\front\templates\front\dashboard\index.html:28
#: .\front\templates\front\dashboard\invoices.html:14
#: .\front\templates\front\dashboard\orders.html:14
#: .\front\templates\front\dashboard\store.html:6
#: .\front\templates\front\dashboard\store.html:14
#: .\front\templates\front\dashboard\store_abo.html:12
#: .\front\templates\front\dashboard\store_credits.html:12
#: .\front\templates\front\dashboard\store_switch.html:15
#: .\front\templates\front\dashboard\store_update.html:15
#: .\front\templates\front\home_page\how_it_works.html:346
#: .\robosourcing\templates\menu_dashboard.html:32
msgid "Store"
msgstr "Sklep"

#: .\front\templates\front\dashboard\base\side-bar.html:64
#: .\front\templates\front\dashboard\basket.html:35
#: .\front\templates\front\dashboard\index.html:35
#: .\front\templates\front\dashboard\kredyty.html:8
#: .\front\templates\front\dashboard\kredyty.html:17
#: .\front\templates\front\dashboard\orders.html:87
#: .\front\templates\front\dashboard\store_abo.html:45
#: .\front\templates\front\dashboard\store_credits.html:5
#: .\front\templates\front\dashboard\store_credits.html:34
msgid "Credits"
msgstr "Kredyty"

#: .\front\templates\front\dashboard\base\side-bar.html:77
#: .\front\templates\front\dashboard\index.html:42
#: .\front\templates\front\dashboard\roboty.html:6
#: .\front\templates\front\dashboard\roboty.html:13
msgid "Robots"
msgstr "Roboty"

#: .\front\templates\front\dashboard\base\side-bar.html:90
#: .\front\templates\front\dashboard\robot_scenarios_view.html:13
#: .\front\templates\front\home_page\scenariusze.html:7
msgid "Scenarios"
msgstr "Scenariusze"

#: .\front\templates\front\dashboard\base\side-bar.html:105
#: .\front\templates\front\dashboard\partner_dashboard.html:16
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:17
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:8
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:16
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:16
msgid "Partner Panel"
msgstr "Panel Partnera"

#: .\front\templates\front\dashboard\base\side-bar.html:120
msgid "Moderator Panel"
msgstr "Panel Moderatora"

#: .\front\templates\front\dashboard\basket.html:8
#: .\front\templates\front\dashboard\basket.html:16
#: .\front\templates\front\dashboard\store.html:50
#: .\robosourcing\templates\menu_dashboard.html:46
msgid "Cart"
msgstr "Koszyk"

#: .\front\templates\front\dashboard\basket.html:25
msgid "Your Cart:"
msgstr "Twój Koszyk"

#: .\front\templates\front\dashboard\basket.html:33
#: .\front\templates\front\dashboard\orders.html:86
#: .\front\templates\front\dashboard\robot_scenarios_view.html:27
#: .\front\templates\front\dashboard\robot_scenarios_view.html:33
#: .\front\templates\front\dashboard\roboty.html:42
#: .\front\templates\front\dashboard\scenario_mod.html:26
#: .\front\templates\front\dashboard\scenario_mod.html:32
#: .\front\templates\front\dashboard\scenario_mod.html:181
msgid "Name"
msgstr "Nazwa"

#: .\front\templates\front\dashboard\basket.html:34
#: .\front\templates\front\dashboard\orders.html:34
#: .\front\templates\front\dashboard\orders.html:122
#: .\front\templates\front\dashboard\partner_dashboard.html:116
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:120
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:123
#: .\front\templates\front\dashboard\scenario_mod_details.html:98
msgid "Description"
msgstr "Opis"

#: .\front\templates\front\dashboard\basket.html:36
#: .\front\templates\front\dashboard\orders.html:72
#: .\front\templates\front\dashboard\orders.html:88
#: .\front\templates\front\dashboard\orders.html:123
#: .\front\templates\front\dashboard\store_credits.html:35
msgid "Price"
msgstr "Cena"

#: .\front\templates\front\dashboard\basket.html:37
#: .\front\templates\front\dashboard\orders.html:89
msgid "Quantity"
msgstr "Ilość"

#: .\front\templates\front\dashboard\basket.html:70
msgid "Your Details:"
msgstr "Twoje Dane"

#: .\front\templates\front\dashboard\basket.html:77
msgid "I want a VAT invoice"
msgstr "Chcę fakturę VAT"

#: .\front\templates\front\dashboard\basket.html:84
msgid "Your Details"
msgstr "Twoje dane"

#: .\front\templates\front\dashboard\basket.html:86
msgid "Name:"
msgstr "Imię i nazwisko:"

#: .\front\templates\front\dashboard\basket.html:94
msgid "Please fill in your name"
msgstr "Proszę wprowadzić swoje imię i nazwisko"

#: .\front\templates\front\dashboard\basket.html:102
msgid "Phone:"
msgstr "Telefon:"

#: .\front\templates\front\dashboard\basket.html:111
msgid "Please fill in your phone number"
msgstr "Proszę wprowadzić swój numer telefonu"

#: .\front\templates\front\dashboard\basket.html:119
#: .\front\templates\front\dashboard\basket.html:168
msgid "Address:"
msgstr "Adres:"

#: .\front\templates\front\dashboard\basket.html:134
msgid "Please fill in your address"
msgstr "Proszę wprowadzić swój adres"

#: .\front\templates\front\dashboard\basket.html:144
msgid "Invoice Details"
msgstr "Dane do faktury"

#: .\front\templates\front\dashboard\basket.html:147
msgid "Organization Name:"
msgstr "Nazwa organizacji:"

#: .\front\templates\front\dashboard\basket.html:154
msgid "Please fill in your organization name"
msgstr "Proszę wprowadzić nazwę organizacji"

#: .\front\templates\front\dashboard\basket.html:159
msgid "Tax ID:"
msgstr "NIP:"

#: .\front\templates\front\dashboard\basket.html:163
msgid "Please fill in your Tax ID"
msgstr "Proszę wprowadzić NIP"

#: .\front\templates\front\dashboard\basket.html:178
msgid "Please fill in your organization address"
msgstr "Proszę wprowadzić adres organizacji"

#: .\front\templates\front\dashboard\basket.html:202
msgid "Payment Method and Terms:"
msgstr "Metoda płatności i warunki:"

#: .\front\templates\front\dashboard\basket.html:215
#: .\front\templates\front\dashboard\basket.html:283
#: .\front\templates\front\home_page\choose_operator.html:43
msgid "skip payment (development only!)"
msgstr "pomiń płatność (tylko do celów deweloperskich!)"

#: .\front\templates\front\dashboard\basket.html:222
#: .\front\templates\front\dashboard\basket.html:258
msgid "I accept the"
msgstr "Akceptuję"

#: .\front\templates\front\dashboard\basket.html:223
#: .\front\templates\front\dashboard\basket.html:259
msgid "terms and conditions"
msgstr "regulamin"

#: .\front\templates\front\dashboard\basket.html:240
msgid "Cart summary:"
msgstr "Podsumowanie koszyka:"

#: .\front\templates\front\dashboard\basket.html:242
msgid "Products"
msgstr "Produkty"

#: .\front\templates\front\dashboard\basket.html:244
msgid "Delivery"
msgstr "Dostawa"

#: .\front\templates\front\dashboard\basket.html:247
msgid "Discount"
msgstr "Rabat"

#: .\front\templates\front\dashboard\basket.html:249
#: .\front\templates\front\dashboard\orders.html:73
#: .\front\templates\front\dashboard\orders.html:124
msgid "VAT"
msgstr "VAT"

#: .\front\templates\front\dashboard\basket.html:251
#: .\front\templates\front\dashboard\kredyty.html:55
#: .\front\templates\front\dashboard\kredyty.html:79
#: .\front\templates\front\dashboard\orders.html:74
#: .\front\templates\front\dashboard\orders.html:125
msgid "TOTAL"
msgstr "RAZEM"

#: .\front\templates\front\dashboard\basket.html:290
msgid "CHECKOUT"
msgstr "PRZEJDŹ DO KASY"

#: .\front\templates\front\dashboard\basket.html:298
msgid "Your cart is currently empty."
msgstr "Twój koszyk jest pusty."

#: .\front\templates\front\dashboard\index.html:21
#: .\front\templates\front\dashboard\partner_dashboard.html:8
#: .\front\templates\front\dashboard\profil.html:6
#: .\front\templates\front\dashboard\profil.html:14
msgid "Profile"
msgstr "Profil"

#: .\front\templates\front\dashboard\index.html:49
msgid "Scenarios Library"
msgstr "Biblioteka scenariuszy"

#: .\front\templates\front\dashboard\index.html:56
msgid "Botie Home Page"
msgstr "Strona główna Botie"

#: .\front\templates\front\dashboard\invoices.html:7
msgid "Invoices"
msgstr "Faktury"

#: .\front\templates\front\dashboard\invoices.html:15
msgid "My invoices"
msgstr "Moje faktury"

#: .\front\templates\front\dashboard\invoices.html:25
msgid "List of issued invoices"
msgstr "Lista wystawionych faktur"

#: .\front\templates\front\dashboard\invoices.html:31
msgid "Issue time"
msgstr "Data wystawienia"

#: .\front\templates\front\dashboard\invoices.html:32
msgid "Invoice number"
msgstr "Numer faktury"

#: .\front\templates\front\dashboard\invoices.html:41
msgid "Get invoice"
msgstr "Pobierz fakturę"

#: .\front\templates\front\dashboard\kredyty.html:25
#: .\front\templates\front\dashboard\kredyty.html:65
#: .\front\templates\front\dashboard\kredyty.html:71
msgid "Your Credits"
msgstr "Twoje kredyty"

#: .\front\templates\front\dashboard\kredyty.html:25
msgid "last update"
msgstr "ostatnia aktualizacja"

#: .\front\templates\front\dashboard\kredyty.html:33
#: .\front\templates\front\dashboard\kredyty.html:72
msgid "Bought"
msgstr "Kupione"

#: .\front\templates\front\dashboard\kredyty.html:33
#: .\front\templates\front\dashboard\kredyty.html:34
#: .\front\templates\front\dashboard\kredyty.html:35
#: .\front\templates\front\dashboard\kredyty.html:36
#: .\front\templates\front\dashboard\store_switch.html:81
#: .\front\templates\front\home_page\pricelist.html:85
#: .\front\templates\front\home_page\pricelist.html:87
#: .\front\templates\front\home_page\pricelist.html:89
#: .\front\templates\front\home_page\pricelist.html:136
#: .\front\templates\front\home_page\pricelist.html:138
#: .\front\templates\front\home_page\pricelist.html:140
#: .\front\templates\front\home_page\pricelist.html:188
#: .\front\templates\front\home_page\scenario_detail.html:77
#: .\front\templates\front\home_page\scenario_detail.html:81
#: .\front\templates\front\home_page\scenario_detail.html:85
#: .\front\templates\front\home_page\scenario_detail.html:93
#: .\front\templates\front\home_page\scenario_detail.html:105
msgid "Hint"
msgstr "Podpowiedź"

#: .\front\templates\front\dashboard\kredyty.html:33
msgid ""
"Credits received as part of the purchased subscription and purchased in "
"packages"
msgstr ""
"Kredyty otrzymane w ramach zakupionej subskrypcji oraz zakupione w pakietach"

#: .\front\templates\front\dashboard\kredyty.html:34
msgid "Assigned"
msgstr "Przypisane"

#: .\front\templates\front\dashboard\kredyty.html:34
msgid "The amount of credits assigned to robots to enable them to work offline"
msgstr "Liczba kredytów przypisanych robotom, aby umożliwić im pracę offline"

#: .\front\templates\front\dashboard\kredyty.html:35
#: .\front\templates\front\dashboard\kredyty.html:73
msgid "Locked"
msgstr "Zablokowane"

#: .\front\templates\front\dashboard\kredyty.html:35
msgid ""
"The amount of credits locked by robots to perform current tasks according to "
"scenario"
msgstr ""
"Liczba kredytów zablokowanych przez roboty do wykonania bieżących zadań "
"zgodnie ze scenariuszem"

#: .\front\templates\front\dashboard\kredyty.html:36
#: .\front\templates\front\dashboard\kredyty.html:74
msgid "Available"
msgstr "Dostępne"

#: .\front\templates\front\dashboard\kredyty.html:36
msgid "The remaining amount of credits available to all robots"
msgstr "Pozostała liczba kredytów dostępnych dla wszystkich robotów"

#: .\front\templates\front\dashboard\kredyty.html:41
msgid "From subscription"
msgstr "Z subskrypcji"

#: .\front\templates\front\dashboard\kredyty.html:48
msgid "From packages"
msgstr "Z pakietów"

#: .\front\templates\front\dashboard\kredyty.html:93
msgid "Chart period"
msgstr "Okres wykresu"

#: .\front\templates\front\dashboard\kredyty.html:101
#: .\front\templates\front\dashboard\roboty.html:120
msgid "week"
msgstr "tydzień"

#: .\front\templates\front\dashboard\kredyty.html:106
#: .\front\templates\front\dashboard\roboty.html:121
msgid "month"
msgstr "miesiąc"

#: .\front\templates\front\dashboard\kredyty.html:111
msgid "3 months"
msgstr "3 miesiące"

#: .\front\templates\front\dashboard\kredyty.html:116
msgid "half a year"
msgstr "pół roku"

#: .\front\templates\front\dashboard\kredyty.html:121
msgid "year"
msgstr "rok"

#: .\front\templates\front\dashboard\kredyty.html:197
msgid "Credits balance"
msgstr "Saldo kredytów"

#: .\front\templates\front\dashboard\kredyty.html:208
msgid "Subscription purchases"
msgstr "Zakupy subskrypcji"

#: .\front\templates\front\dashboard\kredyty.html:217
msgid "Subscription changes"
msgstr "Zmiany subskrypcji"

#: .\front\templates\front\dashboard\kredyty.html:226
msgid "Top-ups from subscriptions"
msgstr "Doładowania z subskrypcji"

#: .\front\templates\front\dashboard\kredyty.html:235
msgid "One-time package purchases"
msgstr "Jednorazowe zakupy pakietów"

#: .\front\templates\front\dashboard\orders.html:7
msgid "Orders"
msgstr "Zamówienia"

#: .\front\templates\front\dashboard\orders.html:15
#: .\robosourcing\templates\menu_dashboard.html:38
msgid "My orders"
msgstr "Moje zamówienia"

#: .\front\templates\front\dashboard\orders.html:25
msgid "List of orders"
msgstr "Lista zamówień"

#: .\front\templates\front\dashboard\orders.html:32
msgid "Order time"
msgstr "Czas zamówienia"

#: .\front\templates\front\dashboard\orders.html:33
msgid "Order ID"
msgstr "ID zamówienia"

#: .\front\templates\front\dashboard\orders.html:35
msgid "Total price"
msgstr "Cena całkowita"

#: .\front\templates\front\dashboard\orders.html:36
#: .\front\templates\front\dashboard\orders.html:126
#: .\front\templates\front\dashboard\scenario_mod.html:206
#: .\front\templates\front\dashboard\store_switch.html:137
msgid "Status"
msgstr "Status"

#: .\front\templates\front\dashboard\orders.html:60
msgid "Order details"
msgstr "Szczegóły zamówienia"

#: .\front\templates\front\dashboard\orders.html:62
msgid "Operator"
msgstr "Operator"

#: .\front\templates\front\dashboard\orders.html:64
#: .\front\templates\front\dashboard\store_switch.html:41
msgid "Payment method"
msgstr "Metoda płatności"

#: .\front\templates\front\dashboard\orders.html:67
msgid "Payment ID"
msgstr "ID płatności"

#: .\front\templates\front\dashboard\orders.html:70
#: .\front\templates\front\dashboard\store_switch.html:42
msgid "Payment details"
msgstr "Szczegóły płatności"

#: .\front\templates\front\dashboard\orders.html:76
msgid "Last updated"
msgstr "Ostatnia aktualizacja"

#: .\front\templates\front\dashboard\orders.html:81
msgid "Order items"
msgstr "Pozycje zamówienia"

#: .\front\templates\front\dashboard\orders.html:85
#: .\front\templates\front\dashboard\store_switch.html:35
msgid "Type"
msgstr "Typ"

#: .\front\templates\front\dashboard\orders.html:99
#: .\front\templates\front\dashboard\store_abo.html:45
#: .\front\templates\front\home_page\pricelist.html:58
#: .\front\templates\front\home_page\pricelist.html:64
#: .\front\templates\front\home_page\pricelist.html:109
#: .\front\templates\front\home_page\pricelist.html:115
msgid "/mo"
msgstr "/mies."

#: .\front\templates\front\dashboard\orders.html:116
msgid "Subsequent charges to account"
msgstr "Kolejne obciążenia konta"

#: .\front\templates\front\dashboard\orders.html:120
msgid "Charge time"
msgstr "Czas obciążenia"

#: .\front\templates\front\dashboard\orders.html:121
msgid "Transaction ID"
msgstr "ID transakcji"

#: .\front\templates\front\dashboard\orders.html:127
msgid "Completed"
msgstr "Zakończono"

#: .\front\templates\front\dashboard\partner_dashboard.html:24
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:23
msgid "Your Affiliate Link"
msgstr "Twój Link Afiliacyjny"

#: .\front\templates\front\dashboard\partner_dashboard.html:34
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:50
msgid "Your Commissions"
msgstr "Twoje Prowizje"

#: .\front\templates\front\dashboard\partner_dashboard.html:37
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:53
msgid "Current Commission Rate:"
msgstr "Obecna Stawka Prowizji:"

#: .\front\templates\front\dashboard\partner_dashboard.html:38
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:54
msgid "Total Commission:"
msgstr "Całkowita Prowizja:"

#: .\front\templates\front\dashboard\partner_dashboard.html:46
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:62
msgid "Your Clients"
msgstr "Twoi Klienci"

#: .\front\templates\front\dashboard\partner_dashboard.html:51
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:67
msgid "Registered"
msgstr "Zarejestrowani"

#: .\front\templates\front\dashboard\partner_dashboard.html:53
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:69
msgid "No referred users."
msgstr "Brak poleconych użytkowników."

#: .\front\templates\front\dashboard\partner_dashboard.html:64
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:81
msgid "Withdrawal Cycles and History"
msgstr "Cykl Wypłat i Historia"

#: .\front\templates\front\dashboard\partner_dashboard.html:69
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:91
msgid "Cycle Start"
msgstr "Rozpoczęcie Cyklu"

#: .\front\templates\front\dashboard\partner_dashboard.html:70
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:96
msgid "Cycle End"
msgstr "Zakończenie Cyklu"

#: .\front\templates\front\dashboard\partner_dashboard.html:71
msgid "Total Commission in Cycle"
msgstr "Łączna Prowizja w Cyklu"

#: .\front\templates\front\dashboard\partner_dashboard.html:72
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:39
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:106
msgid "Withdrawal Status"
msgstr "Stan Wypłaty"

#: .\front\templates\front\dashboard\partner_dashboard.html:73
msgid "Withdrawal Amount"
msgstr "Kwota Wypłaty"

#: .\front\templates\front\dashboard\partner_dashboard.html:74
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:116
msgid "Processed Date"
msgstr "Data Przetworzenia"

#: .\front\templates\front\dashboard\partner_dashboard.html:95
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:157
msgid "No withdrawal cycles available."
msgstr "Brak dostępnych cykli wypłat."

#: .\front\templates\front\dashboard\partner_dashboard.html:105
msgid "Commission Details:"
msgstr "Szczegóły Prowizji:"

#: .\front\templates\front\dashboard\partner_dashboard.html:110
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:92
msgid "Date"
msgstr "Data"

#: .\front\templates\front\dashboard\partner_dashboard.html:111
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:97
msgid "Purchased Products"
msgstr "Kupione Produkty"

#: .\front\templates\front\dashboard\partner_dashboard.html:112
msgid "Net Order Value"
msgstr "Wartość Netto Zamówienia"

#: .\front\templates\front\dashboard\partner_dashboard.html:113
msgid "Gross Order Value"
msgstr "Wartość Brutto Zamówienia"

#: .\front\templates\front\dashboard\partner_dashboard.html:114
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:112
#, python-format
msgid "Commission Rate (%%)"
msgstr "Stawka Prowizji (%%)"

#: .\front\templates\front\dashboard\partner_dashboard.html:115
msgid "Commission Amount"
msgstr "Kwota Prowizji"

#: .\front\templates\front\dashboard\partner_dashboard.html:132
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:143
msgid "Purchased by"
msgstr "Kupione przez"

#: .\front\templates\front\dashboard\partner_dashboard.html:136
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:154
msgid "No commissions available."
msgstr "Prowizja nie dostępna"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:8
#: .\front\templates\front\dashboard\partner_dashboard_commission.html:18
#: .\front\templates\front\dashboard\partner_dashboard_menu.html:88
msgid "Commission Details"
msgstr "Szczegóły Prowizji"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:25
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:24
#: .\front\templates\front\dashboard\robot_scenarios_view.html:21
#: .\front\templates\front\dashboard\scenario_mod.html:20
msgid "Filters"
msgstr "Filtry"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:31
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:29
#: .\front\templates\front\dashboard\robot_scenarios_view.html:53
#: .\front\templates\front\dashboard\scenario_mod.html:93
msgid "Start date"
msgstr "Data rozpoczęcia"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:36
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:34
#: .\front\templates\front\dashboard\robot_scenarios_view.html:62
#: .\front\templates\front\dashboard\scenario_mod.html:103
msgid "End date"
msgstr "Data zakończenia"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:41
#, python-format
msgid "Min Commission Rate (%%)"
msgstr "Minimalna Stawka Prowizji (%%)"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:46
#, python-format
msgid "Max Commission Rate (%%)"
msgstr "Maksymalna Stawka Prowizji (%%)"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:51
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:49
msgid "Min Amount"
msgstr "Min. Kwota"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:56
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:54
msgid "Max Amount"
msgstr "Maks. kwota"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:62
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:60
#: .\front\templates\front\dashboard\robot_scenarios_view.html:108
#: .\front\templates\front\dashboard\scenario_mod.html:141
msgid "Show"
msgstr "Pokaż"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:70
msgid "commissions per page"
msgstr "prowizji na stronę"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:74
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:72
#: .\front\templates\front\dashboard\robot_scenarios_view.html:126
#: .\front\templates\front\dashboard\scenario_mod.html:159
msgid "Apply"
msgstr "Zastosuj"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:76
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:74
#: .\front\templates\front\dashboard\robot_scenarios_view.html:129
#: .\front\templates\front\dashboard\scenario_mod.html:162
msgid "Clear filters"
msgstr "Wyczyść filtry"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:102
msgid "Net Order Value (PLN)"
msgstr "Wartość Netto Zamówienia"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:107
msgid "Gross Order Value (PLN)"
msgstr "Wartość Brutto Zamówienia"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:117
msgid "Commission Amount (PLN)"
msgstr "Kwota Prowizji"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:136
msgid "No products"
msgstr "Brak Produktów"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:164
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:167
#: .\front\templates\front\dashboard\scenario_mod.html:265
msgid "Page"
msgstr "Strona"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:164
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:167
#: .\front\templates\front\dashboard\scenario_mod.html:265
msgid "of"
msgstr "z"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:170
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:173
#: .\front\templates\front\dashboard\scenario_mod.html:260
msgid "First"
msgstr "Pierwszy"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:175
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:178
#: .\front\templates\front\dashboard\scenario_mod.html:261
#: .\front\templates\front\home_page\top_slider.html:205
msgid "Previous"
msgstr "Poprzedni"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:191
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:194
#: .\front\templates\front\dashboard\scenario_mod.html:269
#: .\front\templates\front\home_page\top_slider.html:209
msgid "Next"
msgstr "Następny"

#: .\front\templates\front\dashboard\partner_dashboard_commission.html:196
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:199
#: .\front\templates\front\dashboard\scenario_mod.html:270
msgid "Last"
msgstr "Ostatni"

#: .\front\templates\front\dashboard\partner_dashboard_menu.html:29
msgid ""
"Share this link with your clients. They must use it during registration to "
"be associated with you as their partner."
msgstr ""
"Udostępnij ten link swoim klientom. Muszą go użyć podczas rejestracji, aby "
"zostać powiązani z Tobą jako partnerem."

#: .\front\templates\front\dashboard\partner_dashboard_menu.html:36
msgid "Copy to clipboard"
msgstr "Kopiuj do schowka"

#: .\front\templates\front\dashboard\partner_dashboard_menu.html:37
msgid "Copy"
msgstr "Kopiuj"

#: .\front\templates\front\dashboard\partner_dashboard_menu.html:43
msgid "Link copied to clipboard!"
msgstr "Link został skopiowany do schowka!"

#: .\front\templates\front\dashboard\partner_dashboard_menu.html:120
msgid "Failed to copy the link. Please try manually."
msgstr "Nie udało się skopiować linku. Spróbuj ręcznie."

#: .\front\templates\front\dashboard\partner_dashboard_menu.html:124
msgid "Error copying the link."
msgstr "Błąd podczas kopiowania linku."

#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:8
#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:17
msgid "Withdrawal Cycles"
msgstr "Cykle Wypłat"

#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:41
#: .\front\templates\front\dashboard\scenario_mod.html:76
msgid "All"
msgstr "Wszystkie"

#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:68
msgid "withdrawals per page"
msgstr "wypłat na stronę"

#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:101
msgid "Total Commission in Cycle (PLN)"
msgstr "Łączna Prowizja w Cyklu"

#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:111
msgid "Withdrawal Amount (PLN)"
msgstr "Kwota Wypłaty"

#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:129
msgid "Ongoing"
msgstr "Trwające"

#: .\front\templates\front\dashboard\partner_dashboard_withdrawal.html:137
msgid "N/A"
msgstr "Nie dotyczy"

#: .\front\templates\front\dashboard\profil.html:20
msgid "My Profile"
msgstr "Mój profil"

#: .\front\templates\front\dashboard\profil.html:35
msgid "ACCOUNT TYPE"
msgstr "TYP KONTA"

#: .\front\templates\front\dashboard\profil.html:42
msgid "NAME"
msgstr "NAZWA"

#: .\front\templates\front\dashboard\profil.html:47
msgid "add first and last name"
msgstr "dodaj imię i nazwisko"

#: .\front\templates\front\dashboard\profil.html:64
msgid "PHONE NUMBER"
msgstr "NUMER TELEFONU"

#: .\front\templates\front\dashboard\profil.html:69
msgid "add phone number"
msgstr "dodaj numer telefonu"

#: .\front\templates\front\dashboard\profil.html:86
#: .\front\templates\front\dashboard\profil.html:128
msgid "ADDRESS"
msgstr "ADRES"

#: .\front\templates\front\dashboard\profil.html:98
msgid "add address"
msgstr "dodaj adres"

#: .\front\templates\front\dashboard\profil.html:120
msgid "Invoice Data"
msgstr "Dane do faktury"

#: .\front\templates\front\dashboard\profil.html:124
msgid "TAX NUMBER"
msgstr "NUMER PODATKOWY"

#: .\front\templates\front\dashboard\profil.html:126
msgid "COMPANY NAME"
msgstr "NAZWA FIRMY"

#: .\front\templates\front\dashboard\profil.html:166
msgid "Consent settings"
msgstr "Ustawienia zgód"

#: .\front\templates\front\dashboard\profil.html:174
msgid "General consent"
msgstr "Zgoda ogólna"

#: .\front\templates\front\dashboard\profil.html:176
msgid "User consent"
msgstr "Zgoda użytkownika"

#: .\front\templates\front\dashboard\profil.html:178
msgid "Moderator consent"
msgstr "Zgoda moderatora"

#: .\front\templates\front\dashboard\profil.html:182
#: .\robosourcing\templates\account\email.html:62
msgid "E-mail"
msgstr "E-mail"

#: .\front\templates\front\dashboard\profil.html:183
msgid "Application"
msgstr "Aplikacja"

#: .\front\templates\front\dashboard\profil.html:184
msgid "Phone / Text messages"
msgstr "Telefon / Wiadomości tekstowe"

#: .\front\templates\front\dashboard\profil.html:218
#: .\front\templates\front\home_page\contact.html:84
msgid "Submit"
msgstr "Wyślij"

#: .\front\templates\front\dashboard\profil.html:251
#: .\robosourcing\templates\account\email.html:106
msgid "Are you sure you want to continue?"
msgstr "Czy na pewno chcesz kontynuować?"

#: .\front\templates\front\dashboard\profil.html:252
#: .\robosourcing\templates\account\email.html:107
msgid "Remove operation can't be undone!"
msgstr "Operacja usunięcia jest nieodwracalna!"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:6
#: .\front\templates\front\dashboard\scenario_detail.html:11
msgid "Scenario Library"
msgstr "Biblioteka scenariuszy"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:40
#: .\front\templates\front\dashboard\robot_scenarios_view.html:46
#: .\front\templates\front\dashboard\robot_scenarios_view.html:166
#: .\front\templates\front\dashboard\robot_scenarios_view.html:184
#: .\front\templates\front\dashboard\robot_scenarios_view.html:209
#: .\front\templates\front\dashboard\robot_scenarios_view.html:227
#: .\front\templates\front\dashboard\scenario_mod.html:39
#: .\front\templates\front\dashboard\scenario_mod.html:45
#: .\front\templates\front\dashboard\scenario_mod.html:191
msgid "Author"
msgstr "Autor"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:73
#: .\front\templates\front\dashboard\scenario_mod.html:115
msgid "Min cost"
msgstr "Minimalny koszt"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:83
#: .\front\templates\front\dashboard\scenario_mod.html:127
msgid "Max cost"
msgstr "Maksymalny koszt"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:101
msgid "Enter tags separated by commas"
msgstr "Wprowadź tagi oddzielone przecinkami"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:120
#: .\front\templates\front\dashboard\scenario_mod.html:153
msgid "scenarios per page"
msgstr "scenariuszy na stronę"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:145
msgid "Public Scenarios"
msgstr "Scenariusze publiczne"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:148
msgid "Favorites"
msgstr "Ulubione"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:151
msgid "Assigned to Robot"
msgstr "Przypisane do robota"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:154
msgid "Private Scenarios"
msgstr "Scenariusze prywatne"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:165
#: .\front\templates\front\dashboard\robot_scenarios_view.html:183
#: .\front\templates\front\dashboard\robot_scenarios_view.html:208
#: .\front\templates\front\dashboard\robot_scenarios_view.html:226
msgid "Scenario name"
msgstr "Nazwa scenariusza"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:167
#: .\front\templates\front\dashboard\robot_scenarios_view.html:185
#: .\front\templates\front\dashboard\robot_scenarios_view.html:210
#: .\front\templates\front\dashboard\robot_scenarios_view.html:228
msgid "Creation date"
msgstr "Data utworzenia"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:168
#: .\front\templates\front\dashboard\robot_scenarios_view.html:186
#: .\front\templates\front\dashboard\robot_scenarios_view.html:211
#: .\front\templates\front\dashboard\robot_scenarios_view.html:229
#: .\front\templates\front\dashboard\scenario_mod.html:201
#: .\front\templates\front\dashboard\store_abo.html:47
#: .\front\templates\front\dashboard\store_abo.html:50
msgid "Cost"
msgstr "Koszt"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:169
#: .\front\templates\front\dashboard\robot_scenarios_view.html:187
#: .\front\templates\front\dashboard\robot_scenarios_view.html:212
#: .\front\templates\front\dashboard\robot_scenarios_view.html:230
#: .\front\templates\front\dashboard\scenario_mod.html:186
msgid "Version"
msgstr "Wersja"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:170
#: .\front\templates\front\dashboard\robot_scenarios_view.html:188
#: .\front\templates\front\dashboard\robot_scenarios_view.html:213
#: .\front\templates\front\dashboard\robot_scenarios_view.html:231
msgid "Favorite"
msgstr "Ulubione"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:171
#: .\front\templates\front\dashboard\robot_scenarios_view.html:189
#: .\front\templates\front\dashboard\robot_scenarios_view.html:214
#: .\front\templates\front\dashboard\robot_scenarios_view.html:232
msgid "Availability"
msgstr "Dostępność"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:172
#: .\front\templates\front\dashboard\robot_scenarios_view.html:190
#: .\front\templates\front\dashboard\robot_scenarios_view.html:215
#: .\front\templates\front\dashboard\robot_scenarios_view.html:233
#: .\front\templates\front\dashboard\roboty.html:46
#: .\front\templates\front\dashboard\scenario_detail.html:105
#: .\front\templates\front\dashboard\scenario_mod.html:214
msgid "Actions"
msgstr "Akcje"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:199
msgid "Select robot"
msgstr "Wybierz robota"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:376
#: .\front\templates\front\home_page\scenario_detail.html:171
msgid "Scenario successfully assigned to robot."
msgstr "Scenariusz został pomyślnie przypisany do robota."

#: .\front\templates\front\dashboard\robot_scenarios_view.html:383
#: .\front\templates\front\dashboard\scenario_detail.html:216
#: .\front\templates\front\home_page\scenario_detail.html:174
msgid "There was an issue assigning the scenario."
msgstr "Wystąpił problem z przypisaniem scenariusza."

#: .\front\templates\front\dashboard\robot_scenarios_view.html:388
#: .\front\templates\front\dashboard\scenario_detail.html:221
#: .\front\templates\front\home_page\scenario_detail.html:179
msgid "An error occurred while submitting data."
msgstr "Wystąpił błąd podczas przesyłania danych."

#: .\front\templates\front\dashboard\robot_scenarios_view.html:601
#: .\front\templates\front\dashboard\scenario_change_history.html:16
#: .\front\templates\front\dashboard\scenario_change_history.html:27
#: .\front\templates\front\dashboard\scenario_change_history.html:53
#: .\front\templates\front\dashboard\scenario_mod.html:244
#: .\front\templates\front\dashboard\scenario_mod_details.html:15
#: .\front\templates\front\dashboard\scenario_mod_details.html:25
#: .\front\templates\front\dashboard\scenario_mod_details.html:41
#: .\front\templates\front\home_page\download.html:195
#: .\front\templates\front\home_page\scenariusze.html:175
msgid "Details"
msgstr "Szczegóły"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:610
#: .\front\templates\front\dashboard\scenario_detail.html:117
msgid "Pin to robot"
msgstr "Przypnij do robota"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:632
msgid "Account restricted"
msgstr "Konto ograniczone"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:633
msgid "Group restricted"
msgstr "Grupa ograniczona"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:636
msgid "Unknown"
msgstr "Nieznany"

#: .\front\templates\front\dashboard\robot_scenarios_view.html:662
msgid "Scenario removed from favorites."
msgstr "Scenariusz usunięty z ulubionych."

#: .\front\templates\front\dashboard\robot_scenarios_view.html:662
msgid "Scenario added to favorites."
msgstr "Scenariusz dodany do ulubionych."

#: .\front\templates\front\dashboard\robot_scenarios_view.html:665
msgid "There was an error updating favorites."
msgstr "Wystąpił błąd podczas aktualizacji ulubionych."

#: .\front\templates\front\dashboard\robot_scenarios_view.html:670
msgid "There was an error connecting to the server."
msgstr "Wystąpił błąd podczas łączenia z serwerem."

#: .\front\templates\front\dashboard\roboty.html:22
msgid "Download New Robot"
msgstr "Pobierz nowego robota"

#: .\front\templates\front\dashboard\roboty.html:29
msgid "Link Robot"
msgstr "Połącz robota"

#: .\front\templates\front\dashboard\roboty.html:37
msgid "Your Robots:"
msgstr "Twoje roboty:"

#: .\front\templates\front\dashboard\roboty.html:43
msgid "Create Time"
msgstr "Czas utworzenia"

#: .\front\templates\front\dashboard\roboty.html:44
msgid "Last Active"
msgstr "Ostatnia aktywność"

#: .\front\templates\front\dashboard\roboty.html:45
msgid "Kredyty Offline"
msgstr "Kredyty offline"

#: .\front\templates\front\dashboard\roboty.html:56
msgid "Manage Scenarios"
msgstr "Zarządzaj scenariuszami"

#: .\front\templates\front\dashboard\roboty.html:57
msgid "Manage Credits"
msgstr "Zarządzaj kredytami"

#: .\front\templates\front\dashboard\roboty.html:59
msgid "Show Details"
msgstr "Pokaż szczegóły"

#: .\front\templates\front\dashboard\roboty.html:118
msgid "Chart period:"
msgstr "Okres wykresu:"

#: .\front\templates\front\dashboard\roboty.html:119
msgid "day"
msgstr "dzień"

#: .\front\templates\front\dashboard\roboty.html:135
msgid "Credits Used"
msgstr "Zużyte kredyty"

#: .\front\templates\front\dashboard\scenario_change_history.html:5
msgid "Scenario history"
msgstr "Historia scenariusza"

#: .\front\templates\front\dashboard\scenario_change_history.html:13
#: .\front\templates\front\dashboard\scenario_mod.html:6
#: .\front\templates\front\dashboard\scenario_mod.html:14
#: .\front\templates\front\dashboard\scenario_mod_details.html:13
msgid "Moderation Panel"
msgstr "Panel moderacji"

#: .\front\templates\front\dashboard\scenario_change_history.html:19
#: .\front\templates\front\dashboard\scenario_change_history.html:30
#: .\front\templates\front\dashboard\scenario_change_history.html:42
#: .\front\templates\front\dashboard\scenario_change_history.html:45
#: .\front\templates\front\dashboard\scenario_mod_details.html:28
msgid "Change History"
msgstr "Historia zmian"

#: .\front\templates\front\dashboard\scenario_change_history.html:25
#: .\front\templates\front\dashboard\scenario_mod_details.html:23
msgid "View"
msgstr "Widok"

#: .\front\templates\front\dashboard\scenario_change_history.html:33
#: .\front\templates\front\dashboard\scenario_mod_details.html:31
msgid "Comments"
msgstr "Komentarze"

#: .\front\templates\front\dashboard\scenario_change_history.html:36
#: .\front\templates\front\dashboard\scenario_mod_details.html:34
msgid "Documents"
msgstr "Dokumenty"

#: .\front\templates\front\dashboard\scenario_change_history.html:50
msgid "Change Date"
msgstr "Data zmiany"

#: .\front\templates\front\dashboard\scenario_change_history.html:51
msgid "Event Type"
msgstr "Typ zdarzenia"

#: .\front\templates\front\dashboard\scenario_change_history.html:52
msgid "User"
msgstr "Użytkownik"

#: .\front\templates\front\dashboard\scenario_change_history.html:66
msgid "No details"
msgstr "Brak szczegółów"

#: .\front\templates\front\dashboard\scenario_detail.html:31
msgid "General"
msgstr "Zgoda ogólna"

#: .\front\templates\front\dashboard\scenario_detail.html:34
msgid "Author:"
msgstr "Autor"

#: .\front\templates\front\dashboard\scenario_detail.html:35
msgid "Created:"
msgstr "Utworzono:"

#: .\front\templates\front\dashboard\scenario_detail.html:36
msgid "Version:"
msgstr "Wersja:"

#: .\front\templates\front\dashboard\scenario_detail.html:38
msgid "Changelog:"
msgstr "Historia zmian:"

#: .\front\templates\front\dashboard\scenario_detail.html:45
msgid "Cost & Availability"
msgstr "Cena i Dostępność"

#: .\front\templates\front\dashboard\scenario_detail.html:48
msgid "Cost:"
msgstr "Koszt:"

#: .\front\templates\front\dashboard\scenario_detail.html:49
msgid "Availability:"
msgstr "Dostępność:"

#: .\front\templates\front\dashboard\scenario_detail.html:50
msgid "Editable:"
msgstr "Możliwy do edycji"

#: .\front\templates\front\dashboard\scenario_detail.html:56
msgid "System Info"
msgstr "Informacje systemowe"

#: .\front\templates\front\dashboard\scenario_detail.html:59
msgid "Updated:"
msgstr "Zaktualizowano:"

#: .\front\templates\front\dashboard\scenario_detail.html:60
msgid "Published:"
msgstr "Opublikowano:"

#: .\front\templates\front\dashboard\scenario_detail.html:61
msgid "Accepted:"
msgstr "zaakceptowano:"

#: .\front\templates\front\dashboard\scenario_detail.html:79
msgid "No tags"
msgstr "Brak tagów"

#: .\front\templates\front\dashboard\scenario_detail.html:86
#: .\front\templates\front\home_page\scenario_detail.html:57
#: .\front\templates\front\home_page\scenariusze.html:107
msgid "Categories"
msgstr "Kategorie"

#: .\front\templates\front\dashboard\scenario_detail.html:96
msgid "No categories"
msgstr "Brak Kategorii"

#: .\front\templates\front\dashboard\scenario_detail.html:110
msgid "Remove Favourite"
msgstr "Usuń z Ulubionych"

#: .\front\templates\front\dashboard\scenario_detail.html:110
msgid "Add Favourite"
msgstr "Dodaj do ulubionych"

#: .\front\templates\front\dashboard\scenario_mod.html:52
msgid "Select statuses"
msgstr "Wybierz statusy"

#: .\front\templates\front\dashboard\scenario_mod.html:74
#: .\front\templates\front\dashboard\scenario_mod.html:211
#: .\profiles\forms.py:90
msgid "Moderator"
msgstr "Moderator"

#: .\front\templates\front\dashboard\scenario_mod.html:78
msgid "Unassigned"
msgstr "Nieprzypisany"

#: .\front\templates\front\dashboard\scenario_mod.html:196
msgid "Created"
msgstr "Utworzono"

#: .\front\templates\front\dashboard\scenario_mod.html:239
msgid "unassigned"
msgstr "nieprzypisany"

#: .\front\templates\front\dashboard\scenario_mod.html:250
msgid "No scenarios."
msgstr "Brak scenariuszy."

#: .\front\templates\front\dashboard\scenario_mod_details.html:6
msgid "Scenadio details"
msgstr "Szczegóły scenariusza"

#: .\front\templates\front\dashboard\scenario_mod_details.html:44
msgid "GENERAL INFORMATION"
msgstr "INFORMACJE OGÓLNE"

#: .\front\templates\front\dashboard\scenario_mod_details.html:48
msgid "version"
msgstr "wersja"

#: .\front\templates\front\dashboard\scenario_mod_details.html:50
msgid "Changelog"
msgstr "Historia zmian"

#: .\front\templates\front\dashboard\scenario_mod_details.html:52
msgid "author"
msgstr "autor"

#: .\front\templates\front\dashboard\scenario_mod_details.html:55
msgid "owner"
msgstr "właściciel"

#: .\front\templates\front\dashboard\scenario_mod_details.html:56
msgid "cost"
msgstr "koszt"

#: .\front\templates\front\dashboard\scenario_mod_details.html:61
msgid "DESCRIPTION"
msgstr "OPIS"

#: .\front\templates\front\dashboard\scenario_mod_details.html:66
msgid "SYSTEM INFORMATION"
msgstr "INFORMACJE SYSTEMOWE"

#: .\front\templates\front\dashboard\scenario_mod_details.html:70
msgid "created at"
msgstr "utworzone o"

#: .\front\templates\front\dashboard\scenario_mod_details.html:71
msgid "updated at"
msgstr "zaktualizowane o"

#: .\front\templates\front\dashboard\scenario_mod_details.html:72
msgid "published at"
msgstr "opublikowane o"

#: .\front\templates\front\dashboard\scenario_mod_details.html:75
msgid "availability"
msgstr "dostępność"

#: .\front\templates\front\dashboard\scenario_mod_details.html:76
msgid "editable"
msgstr "edytowalne"

#: .\front\templates\front\dashboard\scenario_mod_details.html:77
msgid "updatable"
msgstr "aktualizowalne"

#: .\front\templates\front\dashboard\scenario_mod_details.html:83
msgid "SEO Metadata"
msgstr "Metadane SEO"

#: .\front\templates\front\dashboard\scenario_mod_details.html:90
msgid "Title"
msgstr "Tytuł"

#: .\front\templates\front\dashboard\scenario_mod_details.html:106
msgid "Keywords"
msgstr "Słowa kluczowe"

#: .\front\templates\front\dashboard\scenario_mod_details.html:111
msgid "Enter comma-separated keywords"
msgstr "Wprowadź tagi oddzielone przecinkami"

#: .\front\templates\front\dashboard\scenario_mod_details.html:115
msgid "OpenGraph Image"
msgstr "Obraz OpenGraph"

#: .\front\templates\front\dashboard\scenario_mod_details.html:123
msgid "Save metadata"
msgstr "Zapisz metadane"

#: .\front\templates\front\dashboard\scenario_mod_details.html:131
msgid "TAGS"
msgstr "TAGI"

#: .\front\templates\front\dashboard\scenario_mod_details.html:164
msgid "No tags yet"
msgstr "Brak tagów"

#: .\front\templates\front\dashboard\scenario_mod_details.html:173
msgid "CATEGORIES"
msgstr "KATEGORIE"

#: .\front\templates\front\dashboard\scenario_mod_details.html:189
msgid "Save categories"
msgstr "Zapisz kategorie"

#: .\front\templates\front\dashboard\scenario_mod_details.html:195
msgid "OPERACJE MODERATORSKIE"
msgstr "OPERACJE MODERATORSKIE"

#: .\front\templates\front\dashboard\scenario_mod_details.html:208
msgid "Powód odrzucenia"
msgstr "Powód odrzucenia"

#: .\front\templates\front\dashboard\scenario_mod_details.html:230
msgid "Brak dostępnych akcji dla tego statusu."
msgstr "Brak dostępnych akcji dla tego statusu."

#: .\front\templates\front\dashboard\scenario_mod_details.html:235
#: .\front\templates\front\home_page\how_it_works.html:261
#: .\front\templates\front\home_page\index.html:123
#: .\front\templates\front\home_page\scenario_detail.html:96
msgid "Download"
msgstr "Pobierz"

#: .\front\templates\front\dashboard\store.html:22
msgid "Credit Packages"
msgstr "Pakiety kredytów"

#: .\front\templates\front\dashboard\store.html:29
msgid "Monthly Subscription"
msgstr "Subskrypcja miesięczna"

#: .\front\templates\front\dashboard\store.html:36
msgid "Yearly Subscription"
msgstr "Subskrypcja roczna"

#: .\front\templates\front\dashboard\store.html:43
msgid "Subscription Change"
msgstr "Zmiana subskrypcji"

#: .\front\templates\front\dashboard\store.html:57
msgid "My Orders"
msgstr "Moje zamówienia"

#: .\front\templates\front\dashboard\store.html:64
msgid "My Invoices"
msgstr "Moje faktury"

#: .\front\templates\front\dashboard\store_abo.html:5
msgid "Subscriptons"
msgstr "Subskrypcje"

#: .\front\templates\front\dashboard\store_abo.html:15
#: .\robosourcing\templates\menu_dashboard.html:36
msgid "Annual subscription"
msgstr "Subskrypcja roczna"

#: .\front\templates\front\dashboard\store_abo.html:17
#: .\robosourcing\templates\menu_dashboard.html:35
msgid "Monthly subscription"
msgstr "Subskrypcja miesięczna"

#: .\front\templates\front\dashboard\store_abo.html:26
msgid ""
"Buy a subscription and we will replenish your pool of credits\n"
"    every month."
msgstr ""
"Kup subskrypcję, a my uzupełnimy Twoją pulę kredytów\n"
"    każdego miesiąca."

#: .\front\templates\front\dashboard\store_abo.html:47
#: .\front\templates\front\dashboard\store_abo.html:50
#: .\front\templates\front\home_page\pricelist.html:126
msgid "netto per month"
msgstr "netto na miesiąc"

#: .\front\templates\front\dashboard\store_abo.html:48
msgid "Annual cost"
msgstr "Koszt roczny"

#: .\front\templates\front\dashboard\store_abo.html:48
msgid "netto"
msgstr "netto"

#: .\front\templates\front\dashboard\store_abo.html:52
#: .\front\templates\front\dashboard\store_credits.html:44
#: .\front\templates\front\home_page\pricelist.html:83
#: .\front\templates\front\home_page\pricelist.html:134
#: .\front\templates\front\home_page\pricelist.html:186
msgid "Buy"
msgstr "Kup"

#: .\front\templates\front\dashboard\store_credits.html:13
#: .\robosourcing\templates\menu_dashboard.html:34
msgid "Credit packages"
msgstr "Pakiety kredytów"

#: .\front\templates\front\dashboard\store_credits.html:18
msgid ""
"Buy a credit package and enjoy flexibility. You buy as many as you need\n"
"    and use them at any time, after your subscription runs out (if you have "
"one)."
msgstr ""
"Kup pakiet kredytów i ciesz się elastycznością. Kup tyle, ile potrzebujesz\n"
"i używaj ich w dowolnym momencie, po wygaśnięciu subskrypcji (jeśli ją masz)."

#: .\front\templates\front\dashboard\store_switch.html:8
msgid "Change Subscriptons"
msgstr "Zmień subskrypcje"

#: .\front\templates\front\dashboard\store_switch.html:22
msgid ""
"You can adjust the number of credits topped up each month to your needs.\n"
"            We offer the following options for increasing it for an "
"additional fee."
msgstr ""
"Możesz dostosować liczbę kredytów doładowywanych każdego miesiąca do swoich "
"potrzeb.\n"
"Oferujemy następujące opcje zwiększenia ich za dodatkową opłatą."

#: .\front\templates\front\dashboard\store_switch.html:34
msgid "Current subscription"
msgstr "Bieżąca subskrypcja"

#: .\front\templates\front\dashboard\store_switch.html:36
msgid "Monthly top-up"
msgstr "Doładowanie miesięczne"

#: .\front\templates\front\dashboard\store_switch.html:37
msgid "Subscription started"
msgstr "Subskrypcja rozpoczęta"

#: .\front\templates\front\dashboard\store_switch.html:38
msgid "Next top-up"
msgstr "Następne doładowanie"

#: .\front\templates\front\dashboard\store_switch.html:39
msgid "Next payment"
msgstr "Następna płatność"

#: .\front\templates\front\dashboard\store_switch.html:40
msgid "To pay"
msgstr "Do zapłaty"

#: .\front\templates\front\dashboard\store_switch.html:45
msgid "Warning"
msgstr "Ostrzeżenie:"

#: .\front\templates\front\dashboard\store_switch.html:47
msgid "Your payment card expired"
msgstr "Twoja karta płatnicza wygasła"

#: .\front\templates\front\dashboard\store_switch.html:48
msgid "Please provide new card details"
msgstr "Wprowadź dane swojej nowej karty"

#: .\front\templates\front\dashboard\store_switch.html:48
msgid "here"
msgstr "tutaj"

#: .\front\templates\front\dashboard\store_switch.html:56
msgid "Requested change"
msgstr "Żądana zmiana"

#: .\front\templates\front\dashboard\store_switch.html:57
msgid "Subscription type"
msgstr "Typ subskrypcji"

#: .\front\templates\front\dashboard\store_switch.html:60
#: .\front\templates\front\home_page\pricelist.html:32
#: .\front\templates\front\home_page\pricelist.html:106
msgid "monthly"
msgstr "miesięcznie"

#: .\front\templates\front\dashboard\store_switch.html:61
#: .\front\templates\front\home_page\pricelist.html:38
#: .\front\templates\front\home_page\pricelist.html:55
msgid "annual"
msgstr "rocznie"

#: .\front\templates\front\dashboard\store_switch.html:64
msgid "Monthly top-ups"
msgstr "Doładowania miesięczne"

#: .\front\templates\front\dashboard\store_switch.html:68
msgid "Change mode"
msgstr "Tryb zmiany"

#: .\front\templates\front\dashboard\store_switch.html:72
msgid "with the end of current period"
msgstr "z końcem bieżącego okresu"

#: .\front\templates\front\dashboard\store_switch.html:76
msgid "immediate"
msgstr "natychmiastowe"

#: .\front\templates\front\dashboard\store_switch.html:83
msgid "Subscription change is in progress"
msgstr "Zmiana subskrypcji jest w toku"

#: .\front\templates\front\dashboard\store_switch.html:84
msgid "You can't make any changes until the process is completed."
msgstr ""
"Nie możesz wprowadzać żadnych zmian, dopóki proces nie zostanie zakończony."

#: .\front\templates\front\dashboard\store_switch.html:96
msgid "current subscription"
msgstr "aktualna subskrypcja"

#: .\front\templates\front\dashboard\store_switch.html:102
msgid "after change"
msgstr "po zmianie"

#: .\front\templates\front\dashboard\store_switch.html:115
msgid "Unsubscribe"
msgstr "Wypisz się"

#: .\front\templates\front\dashboard\store_switch.html:115
msgid "with end of period"
msgstr "z końcem okresu"

#: .\front\templates\front\dashboard\store_switch.html:118
msgid "Request a change"
msgstr "Złóż prośbę o zmianę"

#: .\front\templates\front\dashboard\store_switch.html:129
msgid "Changes history"
msgstr "Historia zmian"

#: .\front\templates\front\dashboard\store_switch.html:133
msgid "Event time"
msgstr "Czas zdarzenia"

#: .\front\templates\front\dashboard\store_switch.html:134
msgid "Event type"
msgstr "Typ zdarzenia"

#: .\front\templates\front\dashboard\store_switch.html:135
#: .\robosourcing\templates\menu.html:23
msgid "Product"
msgstr "Produkt"

#: .\front\templates\front\dashboard\store_switch.html:136
msgid "Mode"
msgstr "Tryb"

#: .\front\templates\front\dashboard\store_switch.html:450
msgid "recurring payments"
msgstr "płatności cykliczne"

#: .\front\templates\front\dashboard\store_switch.html:498
msgid "credit top-ups"
msgstr "doładowania kredytów"

#: .\front\templates\front\dashboard\store_switch.html:606
msgid "the requested change is not allowed"
msgstr "żądana zmiana nie jest dozwolona"

#: .\front\templates\front\dashboard\store_switch.html:632
msgid ""
"the requested change is allowed in the last month of the subscription period "
"only"
msgstr ""
"żądana zmiana jest dozwolona tylko w ostatnim miesiącu okresu subskrypcyjnego"

#: .\front\templates\front\dashboard\store_switch.html:648
msgid "immediate extra payment"
msgstr "natychmiastowa dodatkowa płatność"

#: .\front\templates\front\dashboard\store_switch.html:651
msgid "no extra payment now"
msgstr "brak dodatkowej płatności teraz"

#: .\front\templates\front\dashboard\store_update.html:8
msgid "Update payment card"
msgstr "Zaktualizuj kartę płatniczą"

#: .\front\templates\front\dashboard\store_update.html:16
msgid "Payment card update"
msgstr "Aktualizacja party płatniczej"

#: .\front\templates\front\dashboard\store_update.html:22
msgid "You can update you payment card details here."
msgstr "Tutaj możesz zaktualizować dane karty płatniczej."

#: .\front\templates\front\dashboard\store_update.html:32
msgid "Card Number"
msgstr "Numer Karty"

#: .\front\templates\front\dashboard\store_update.html:36
msgid "Valid Thru"
msgstr "Data ważności"

#: .\front\templates\front\dashboard\store_update.html:40
msgid "CVV"
msgstr "Kod CVV"

#: .\front\templates\front\dashboard\store_update.html:46
msgid "Tokenize"
msgstr "Zatwierdź"

#: .\front\templates\front\dashboard\store_update.html:56
msgid ""
"The tokenization of your payment card is handled by PayU, our trusted "
"payment provider.\n"
"                        We do not store or process your card details on our "
"servers.\n"
"                        PayU ensures that your data is securely encrypted "
"and protected according to the highest industry standards."
msgstr ""
"Proces tokenizacji Twojej karty płatniczej odbywa się po stronie PayU – "
"naszego zaufanego operatora płatności.\n"
"                        Nie przechowujemy ani nie przetwarzamy danych Twojej "
"karty na naszych serwerach.\n"
"                        PayU zapewnia, że Twoje dane są bezpiecznie "
"szyfrowane i chronione zgodnie z najwyższymi standardami branżowymi."

#: .\front\templates\front\dashboard\store_update.html:80
msgid "MM/YY"
msgstr "MM/RR"

#: .\front\templates\front\home_page\app_download_guide.html:5
#: .\front\templates\front\home_page\app_download_guide.html:8
msgid "App download guide"
msgstr "Przewodnik pobierania aplikacji"

#: .\front\templates\front\home_page\app_install_guide.html:5
#: .\front\templates\front\home_page\app_install_guide.html:8
msgid "App install guide"
msgstr "Przewodnik instalacji aplikacji"

#: .\front\templates\front\home_page\app_launch_guide.html:5
#: .\front\templates\front\home_page\app_launch_guide.html:8
msgid "App launch guide"
msgstr "Przewodnik uruchamiania aplikacji"

#: .\front\templates\front\home_page\choose_operator.html:7
#: .\front\templates\front\home_page\choose_operator.html:10
msgid "Payment operator"
msgstr "Operator płatności"

#: .\front\templates\front\home_page\choose_operator.html:22
msgid "Choose a payment operator"
msgstr "Wybierz operatora płatności"

#: .\front\templates\front\home_page\choose_operator.html:46
msgid "Continue payment"
msgstr "Kontynuuj płatność"

#: .\front\templates\front\home_page\contact.html:5
#: .\front\templates\front\home_page\contact.html:8
#: .\robosourcing\templates\footer.html:31
#: .\robosourcing\templates\menu.html:54
msgid "Contact"
msgstr "Kontakt"

#: .\front\templates\front\home_page\contact.html:14
#: .\front\templates\front\home_page\contact_promo_section.html:9
msgid "Contact Us"
msgstr "Skontaktuj się z nami"

#: .\front\templates\front\home_page\contact.html:23
msgid "Your Name"
msgstr "Twoje imię"

#: .\front\templates\front\home_page\contact.html:27
msgid "Your Email"
msgstr "Twój e-mail"

#: .\front\templates\front\home_page\contact.html:31
#: .\front\templates\front\home_page\contact.html:102
msgid "Hello, how can we help you today?"
msgstr "Cześć, jak możemy Ci dzisiaj pomóc?"

#: .\front\templates\front\home_page\contact.html:32
msgid "Subject:"
msgstr "Temat:"

#: .\front\templates\front\home_page\contact.html:41
msgid "I need a custom scenario"
msgstr "Potrzebuję niestandardowego scenariusza"

#: .\front\templates\front\home_page\contact.html:50
msgid "I need a custom scenario based on an existing one"
msgstr "Potrzebuję niestandardowego scenariusza opartego na istniejącym"

#: .\front\templates\front\home_page\contact.html:59
msgid "I need technical support"
msgstr "Potrzebuję wsparcia technicznego"

#: .\front\templates\front\home_page\contact.html:68
msgid "Something else..."
msgstr "Coś innego..."

#: .\front\templates\front\home_page\contact.html:79
msgid "Upload files (max 5 files, 10MB total)"
msgstr "Prześlij pliki (maks. 5 plików, 10MB łącznie)"

#: .\front\templates\front\home_page\contact.html:102
msgid "Hello"
msgstr "Cześć"

#: .\front\templates\front\home_page\contact.html:102
msgid "how can we help you today?"
msgstr "jak możemy Ci dzisiaj pomóc?"

#: .\front\templates\front\home_page\contact.html:128
msgid "Suggested scenario name:"
msgstr "Sugerowana nazwa scenariusza:"

#: .\front\templates\front\home_page\contact.html:132
msgid "Description of the scenario:"
msgstr "Opis scenariusza:"

#: .\front\templates\front\home_page\contact.html:139
msgid "Base scenario name or link:"
msgstr "Nazwa podstawowego scenariusza lub link:"

#: .\front\templates\front\home_page\contact.html:151
#: .\front\templates\front\home_page\contact.html:166
msgid "Describe your issue or query:"
msgstr "Opisz swój problem lub zapytanie:"

#: .\front\templates\front\home_page\contact.html:155
msgid "App Version (optional):"
msgstr "Wersja aplikacji (opcjonalnie):"

#: .\front\templates\front\home_page\contact.html:176
msgid "Contact preference:"
msgstr "Preferencje kontaktu:"

#: .\front\templates\front\home_page\contact.html:185
msgid "Contact details:"
msgstr "Szczegóły kontaktu:"

#: .\front\templates\front\home_page\contact_promo_section.html:11
msgid "Reach Out to Our Robotization Experts"
msgstr "Skontaktuj się z naszymi ekspertami w dziedzinie robotyzacji"

#: .\front\templates\front\home_page\contact_promo_section.html:14
msgid ""
"Our team is ready to assist you with your automation needs. Contact us today "
"to get started!"
msgstr ""
"Nasz zespół jest gotowy, aby pomóc Ci w automatyzacji. Skontaktuj się z nami "
"już dziś, aby zacząć!"

#: .\front\templates\front\home_page\contact_promo_section.html:17
msgid "Contact Form"
msgstr "Formularz kontaktowy"

#: .\front\templates\front\home_page\contact_promo_section.html:30
msgid "Phone"
msgstr "Telefon"

#: .\front\templates\front\home_page\contact_promo_section.html:42
msgid "Email"
msgstr "E-mail"

#: .\front\templates\front\home_page\contact_promo_section.html:54
msgid "LinkedIn"
msgstr "LinkedIn"

#: .\front\templates\front\home_page\contact_promo_section.html:55
msgid "Follow us on LinkedIn"
msgstr "Obserwuj nas na LinkedIn"

#: .\front\templates\front\home_page\contact_promo_section.html:56
msgid "Visit our profile"
msgstr "Odwiedź nasz profil"

#: .\front\templates\front\home_page\cookie_modal.html:8
msgid "We value your privacy"
msgstr "Cenimy Twoją prywatność"

#: .\front\templates\front\home_page\cookie_modal.html:11
msgid ""
"We use cookies on this website to enhance your user experience. By clicking "
"\"I agree\", you are giving your consent for us to set cookies."
msgstr ""
"Używamy plików cookie na tej stronie internetowej, aby poprawić Twoje "
"doświadczenia. Klikając \"Zgadzam się\", wyrażasz zgodę na ustawienie plików "
"cookie."

#: .\front\templates\front\home_page\cookie_modal.html:12
msgid ""
"For more information on what data is contained in the cookies, please see our"
msgstr ""
"Aby uzyskać więcej informacji na temat danych zawartych w plikach cookie, "
"zapoznaj się z naszym"

#: .\front\templates\front\home_page\cookie_modal.html:12
#: .\front\templates\front\home_page\privacy_policy.html:5
#: .\front\templates\front\home_page\privacy_policy.html:8
msgid "Privacy policy"
msgstr "Polityka prywatności"

#: .\front\templates\front\home_page\cookie_modal.html:13
msgid "I decline"
msgstr "Odrzucam"

#: .\front\templates\front\home_page\cookie_modal.html:13
msgid "I agree"
msgstr "Zgadzam się"

#: .\front\templates\front\home_page\download.html:27
msgid "Installer datails"
msgstr "Szczegóły instalatora"

#: .\front\templates\front\home_page\download.html:28
#: .\front\templates\front\home_page\download.html:182
msgid "Version number"
msgstr "Numer wersji"

#: .\front\templates\front\home_page\download.html:29
#: .\front\templates\front\home_page\download.html:183
msgid "Released on"
msgstr "Wydano w dniu"

#: .\front\templates\front\home_page\download.html:30
msgid "Platform"
msgstr "Platforma"

#: .\front\templates\front\home_page\download.html:35
#: .\front\templates\front\home_page\download.html:184
msgid "Major change"
msgstr "Główna zmiana"

#: .\front\templates\front\home_page\download.html:46
msgid "Download Botie for"
msgstr "Pobierz Botie dla"

#: .\front\templates\front\home_page\download.html:76
#: .\robosourcing\templates\footer.html:26
#: .\robosourcing\templates\menu.html:27
msgid "Download Botie"
msgstr "Pobierz Botie"

#: .\front\templates\front\home_page\download.html:100
msgid "About version"
msgstr "O wersji"

#: .\front\templates\front\home_page\download.html:102
#: .\front\templates\front\home_page\download.html:118
msgid "System requirements"
msgstr "Wymagania systemowe"

#: .\front\templates\front\home_page\download.html:104
msgid "Installation guide"
msgstr "Przewodnik instalacji"

#: .\front\templates\front\home_page\download.html:107
msgid "Archive versions"
msgstr "Wersje archiwalne"

#: .\front\templates\front\home_page\download.html:120
msgid "Operating system"
msgstr "System operacyjny"

#: .\front\templates\front\home_page\download.html:121
msgid "Main processor"
msgstr "Główny procesor"

#: .\front\templates\front\home_page\download.html:122
msgid "RAM memory"
msgstr "Pamięć RAM"

#: .\front\templates\front\home_page\download.html:123
msgid "Hard drive empty space"
msgstr "Wolne miejsce na dysku twardym"

#: .\front\templates\front\home_page\download.html:124
msgid "Graphic card"
msgstr "Karta graficzna"

#: .\front\templates\front\home_page\download.html:198
msgid "not available"
msgstr "niedostępne"

#: .\front\templates\front\home_page\how_it_works.html:6
#: .\front\templates\front\home_page\index.html:68
#: .\front\templates\front\home_page\index.html:327
msgid "How it works"
msgstr "Jak to działa"

#: .\front\templates\front\home_page\how_it_works.html:23
#: .\front\templates\front\home_page\how_it_works.html:97
#: .\front\templates\front\home_page\how_it_works.html:324
msgid "How Botie Works"
msgstr "Jak działa Botie"

#: .\front\templates\front\home_page\how_it_works.html:99
msgid ""
"Botie is an application installed on the User's computer, which serves to "
"play scenarios of robotic processes (similar to how a CD player plays CDs), "
"as well as the botie.pl web portal, from which the User can search and "
"download scenarios from various business fields (similar to CDs that require "
"a CD player)."
msgstr ""
"Botie to aplikacja zainstalowana na komputerze Użytkownika, która służy do "
"odtwarzania scenariuszy procesów robotycznych (podobnie jak odtwarzacz CD "
"odtwarza płyty CD), a także portalu internetowego botie.pl, z którego "
"Użytkownik może wyszukiwać i pobierać scenariusze z różnych dziedzin biznesu "
"(podobnie jak płyty CD, które wymagają odtwarzacza CD)."

#: .\front\templates\front\home_page\how_it_works.html:102
msgid ""
"Downloading the application and scenarios from the botie.pl portal is free. "
"With the first installation and launch of the application, the User receives "
"a free welcome package of 100 credits, which Botie consumes for playing "
"scenarios. The costs of executing individual scenarios vary depending on the "
"number of actions and are known to the User before launching the application."
msgstr ""
"Pobieranie aplikacji i scenariuszy z portalu botie.pl jest bezpłatne. Przy "
"pierwszej instalacji i uruchomieniu aplikacji Użytkownik otrzymuje bezpłatny "
"pakiet powitalny 100 kredytów, które Botie zużywa na odtwarzanie "
"scenariuszy. Koszty wykonania poszczególnych scenariuszy różnią się w "
"zależności od liczby działań i są znane Użytkownikowi przed uruchomieniem "
"aplikacji."

#: .\front\templates\front\home_page\how_it_works.html:105
msgid ""
"It is possible to increase the credit package by purchasing them in the "
"Store. After launching, the Botie application synchronizes with the User's "
"account to refresh the number of purchased credits."
msgstr ""
"Można zwiększyć pakiet kredytów, kupując je w Sklepie. Po uruchomieniu "
"aplikacji Botie synchronizuje się z kontem Użytkownika, aby odświeżyć liczbę "
"zakupionych kredytów."

#: .\front\templates\front\home_page\how_it_works.html:117
msgid "Types of Credit Packages:"
msgstr "Rodzaje pakietów kredytów:"

#: .\front\templates\front\home_page\how_it_works.html:128
msgid "One-Time Packages"
msgstr "Pakiety jednorazowe"

#: .\front\templates\front\home_page\how_it_works.html:134
msgid ""
"One-time packages have no time limit and are available until exhausted. "
"Purchasing additional packages adds to the previous ones."
msgstr ""
"Pakiety jednorazowe nie mają limitu czasowego i są dostępne do wyczerpania. "
"Zakup dodatkowych pakietów powiększa poprzednie."

#: .\front\templates\front\home_page\how_it_works.html:144
msgid "Renewable Packages"
msgstr "Pakiety odnawialne"

#: .\front\templates\front\home_page\how_it_works.html:150
msgid ""
"Renewable packages are valid for one calendar month, after which they are "
"replenished to the user-defined limit. Unused credits expire and do not "
"accumulate."
msgstr ""
"Pakiety odnawialne są ważne przez jeden miesiąc kalendarzowy, po czym "
"zostają uzupełnione do określonego przez użytkownika limitu. Niewykorzystane "
"kredyty wygasają i nie kumulują się."

#: .\front\templates\front\home_page\how_it_works.html:159
msgid "Combined Packages"
msgstr "Pakiety łączone"

#: .\front\templates\front\home_page\how_it_works.html:165
msgid ""
"It is possible to have a monthly renewable credit package and purchase a one-"
"time package. In this case, the credits from the renewable package are used "
"first, and after they are fully utilized, Botie will consume credits from "
"the one-time package."
msgstr ""
"Możliwe jest posiadanie miesięcznego odnawialnego pakietu kredytowego oraz "
"zakupienie pakietu jednorazowego. W takim przypadku najpierw wykorzystane "
"zostaną kredyty z pakietu odnawialnego, a po ich pełnym wykorzystaniu Botie "
"zużyje kredyty z pakietu jednorazowego."

#: .\front\templates\front\home_page\how_it_works.html:181
msgid "Our Services"
msgstr "Nasze usługi"

#: .\front\templates\front\home_page\how_it_works.html:189
msgid "Portal botie.pl"
msgstr "Portal botie.pl:"

#: .\front\templates\front\home_page\how_it_works.html:199
msgid "Sing in"
msgstr "Zaloguj się"

#: .\front\templates\front\home_page\how_it_works.html:212
msgid ""
"In the Store, it is possible to buy credits, view purchase history, and "
"manage profile data."
msgstr ""
"W Sklepie możliwe jest kupno kredytów, przeglądanie historii zakupów i "
"zarządzanie danymi profilu."

#: .\front\templates\front\home_page\how_it_works.html:226
msgid ""
"In the Help Center, there is a knowledge base along with a beginner's Botie "
"User academy, inspiring users to explore more robotic and ready-to-use "
"business areas."
msgstr ""
"W Centrum Pomocy znajduje się baza wiedzy wraz z akademią Użytkowników Botie "
"dla początkujących, inspirująca użytkowników do odkrywania kolejnych "
"obszarów robotyzacji i gotowych do użycia w biznesie."

#: .\front\templates\front\home_page\how_it_works.html:238
msgid ""
"In BotBook, there is a collection of ready-made robotic process scenarios "
"prepared by our automation specialists, divided into categories and "
"subcategories. Users can freely browse the database, read the descriptions "
"of individual scenarios, add them to their favorites, and download them to "
"their computer to play in the Botie application."
msgstr ""
"W BotBook znajduje się zbiór gotowych scenariuszy procesów robotycznych "
"przygotowanych przez naszych specjalistów ds. automatyzacji, podzielonych na "
"kategorie i podkategorie. Użytkownicy mogą swobodnie przeglądać bazę danych, "
"czytać opisy poszczególnych scenariuszy, dodawać je do ulubionych i pobierać "
"na swój komputer, aby odtwarzać je w aplikacji Botie."

#: .\front\templates\front\home_page\how_it_works.html:253
#: .\front\templates\front\home_page\how_it_works.html:350
msgid "The Botie application:"
msgstr "Aplikacja Botie:"

#: .\front\templates\front\home_page\how_it_works.html:274
msgid "Plays the robotic process scenarios downloaded from BotBook."
msgstr "Odtwarza scenariusze procesów robotycznych pobrane z BotBook."

#: .\front\templates\front\home_page\how_it_works.html:288
msgid "Allows users to create their own scenarios in the editor."
msgstr "Pozwala użytkownikom tworzyć własne scenariusze w edytorze."

#: .\front\templates\front\home_page\how_it_works.html:300
msgid ""
"Manages user profiles and synchronizes credits with the user's account in "
"the web application botie.pl."
msgstr ""
"Zarządza profilami użytkowników i synchronizuje kredyty z kontem użytkownika "
"w aplikacji internetowej botie.pl."

#: .\front\templates\front\home_page\how_it_works.html:343
#: .\front\templates\front\home_page\index.html:22
#: .\front\templates\front\home_page\index.html:52
#: .\front\templates\front\home_page\index.html:322
#: .\front\templates\front\home_page\index.html:385
#: .\front\templates\front\home_page\scenario_detail.html:7
#: .\robosourcing\templates\menu.html:32
msgid "BotBook"
msgstr "BotBook"

#: .\front\templates\front\home_page\index.html:19
#: .\front\templates\front\home_page\index.html:42
msgid "Welcome to the world of the Botie personal robot"
msgstr "Witamy w świecie osobistego robota Botie"

#: .\front\templates\front\home_page\index.html:20
#: .\front\templates\front\home_page\index.html:45
msgid "When you download and install the personal robot app "
msgstr "Gdy pobierzesz i zainstalujesz aplikację robota osobistego "

#: .\front\templates\front\home_page\index.html:20
#: .\front\templates\front\home_page\index.html:46
msgid ""
", you will receive 100 credits so you can test Botie without any obstacles."
msgstr ", otrzymasz 100 kredytów, abyś mógł testować Botie bez przeszkód."

#: .\front\templates\front\home_page\index.html:21
#: .\front\templates\front\home_page\index.html:47
msgid "Go ahead, "
msgstr "Śmiało, "

#: .\front\templates\front\home_page\index.html:21
#: .\front\templates\front\home_page\index.html:48
msgid "sign up"
msgstr "zarejestruj się"

#: .\front\templates\front\home_page\index.html:21
#: .\front\templates\front\home_page\index.html:48
msgid ""
" and we'll renew your package every month up to 100 credits at no charge."
msgstr " a my co miesiąc odnowimy Twoją paczkę do 100 kredytów bez opłat."

#: .\front\templates\front\home_page\index.html:22
msgid "Log in, use more scenarios from "
msgstr "Zaloguj się, użyj więcej umiejętności z"

#: .\front\templates\front\home_page\index.html:22
#: .\front\templates\front\home_page\index.html:52
msgid " and discover new inspirations and the power of Botie."
msgstr " i odkryj nowe inspiracje oraz moc Botie."

#: .\front\templates\front\home_page\index.html:34
msgid "circle image"
msgstr "zdjęcie w kółku"

#: .\front\templates\front\home_page\index.html:51
msgid "Log in, use more skills from "
msgstr "Zaloguj się, użyj więcej umiejętności z"

#: .\front\templates\front\home_page\index.html:55
msgid "Get Started"
msgstr "Zacznij"

#: .\front\templates\front\home_page\index.html:69
msgid "Five steps guide"
msgstr "Przewodnik po pięciu krokach"

#: .\front\templates\front\home_page\index.html:119
msgid "Step 1"
msgstr "Krok 1"

#: .\front\templates\front\home_page\index.html:123
msgid "Step 1: "
msgstr "Krok 1: "

#: .\front\templates\front\home_page\index.html:127
msgid "Download the Botie application from the 'Download' section."
msgstr "Pobierz aplikację Botie z sekcji 'Pobierz'."

#: .\front\templates\front\home_page\index.html:131
#: .\front\templates\front\home_page\index.html:151
msgid "See our app installation guide"
msgstr "Zobacz nasz przewodnik instalacji aplikacji"

#: .\front\templates\front\home_page\index.html:141
msgid "Step 2"
msgstr "Krok 2"

#: .\front\templates\front\home_page\index.html:144
msgid "Step 2: "
msgstr "Krok 2: "

#: .\front\templates\front\home_page\index.html:144
msgid "Install"
msgstr "Zainstaluj"

#: .\front\templates\front\home_page\index.html:147
msgid "Install the Botie application on your computer."
msgstr "Zainstaluj aplikację Botie na swoim komputerze."

#: .\front\templates\front\home_page\index.html:161
msgid "Step 3"
msgstr "Krok 3"

#: .\front\templates\front\home_page\index.html:164
msgid "Step 3: "
msgstr "Krok 3: "

#: .\front\templates\front\home_page\index.html:164
msgid "Launch"
msgstr "Uruchom"

#: .\front\templates\front\home_page\index.html:167
msgid "Launch the Botie application."
msgstr "Uruchom aplikację Botie."

#: .\front\templates\front\home_page\index.html:171
msgid "See our app launch guide"
msgstr "Zobacz nasz przewodnik uruchamiania aplikacji"

#: .\front\templates\front\home_page\index.html:181
msgid "Step 4"
msgstr "Krok 4"

#: .\front\templates\front\home_page\index.html:184
msgid "Step 4: "
msgstr "Krok 4: "

#: .\front\templates\front\home_page\index.html:184
msgid "Find a skill"
msgstr "Znajdź scenariusz"

#: .\front\templates\front\home_page\index.html:187
msgid "Find skill in BootBoook and automate your work."
msgstr "Znajdź i uruchom scenariusz, który automatyzuje Twoją pracę."

#: .\front\templates\front\home_page\index.html:191
msgid "Explore the list of our ready-to-use skills"
msgstr "Przejrzyj listę naszych gotowych do użycia scenariuszy"

#: .\front\templates\front\home_page\index.html:201
msgid "Step 5"
msgstr "Krok 5"

#: .\front\templates\front\home_page\index.html:204
msgid "Step 5: "
msgstr "Krok 5: "

#: .\front\templates\front\home_page\index.html:204
msgid "Manage your robots"
msgstr "Zarządzaj swoimi robotami"

#: .\front\templates\front\home_page\index.html:207
msgid "Manage your robot, skills and payments."
msgstr "Zarządzaj swoim robotem, scenariuszami i płatnościami."

#: .\front\templates\front\home_page\index.html:211
msgid "Go to your dashboard"
msgstr "Przejdź do swojego pulpitu"

#: .\front\templates\front\home_page\index.html:240
#: .\front\templates\front\home_page\index.html:306
msgid "Robotization"
msgstr "Robotyzacja"

#: .\front\templates\front\home_page\index.html:242
msgid "Streamline your daily tasks through business process automation."
msgstr ""
"Ułatw sobie codzienne zadania dzięki automatyzacji procesów biznesowych."

#: .\front\templates\front\home_page\index.html:256
#: .\front\templates\front\home_page\index.html:335
#: .\front\templates\front\home_page\index.html:337
msgid "Key features"
msgstr "Kluczowe funkcje"

#: .\front\templates\front\home_page\index.html:258
msgid "Features that make your work more efficient."
msgstr "Funkcje, które zwiększają wydajność Twojej pracy."

#: .\front\templates\front\home_page\index.html:272
#: .\front\templates\front\home_page\index.html:353
#: .\front\templates\front\home_page\index.html:355
msgid "Botie enables"
msgstr "Botie umożliwia"

#: .\front\templates\front\home_page\index.html:274
msgid "Solutions that set us apart from the competition."
msgstr "Rozwiązania, które wyróżniają nas na tle konkurencji."

#: .\front\templates\front\home_page\index.html:304
msgid "Automation of processes"
msgstr "Automatyzacja procesów"

#: .\front\templates\front\home_page\index.html:310
msgid ""
"Are you the owner of a small or medium-sized company or do you manage a "
"group of employees?"
msgstr ""
"Czy jesteś właścicielem małej lub średniej firmy, czy zarządzasz grupą "
"pracowników?"

#: .\front\templates\front\home_page\index.html:313
msgid "Do you have to divide all your time and attention between many things?"
msgstr "Czy musisz dzielić swój czas i uwagę między wiele rzeczy?"

#: .\front\templates\front\home_page\index.html:316
msgid ""
"Do you respect the time and energy of your employees, looking for the best "
"way for them to use their skills?"
msgstr ""
"Czy szanujesz czas i energię swoich pracowników, szukając najlepszych "
"sposobów na wykorzystanie ich umiejętności?"

#: .\front\templates\front\home_page\index.html:320
msgid ""
"is your PERSONAL ROBOT and virtual Business Partner, “Wingman“. It frees you "
"from burdensome duties and gives you inspiration to develop your company."
msgstr ""
"jest Twoim OSOBISTYM ROBOTEM i wirtualnym Partnerem Biznesowym, „Wingman“. "
"Uwalnia Cię od uciążliwych obowiązków i daje inspirację do rozwijania Twojej "
"firmy."

#: .\front\templates\front\home_page\index.html:321
msgid ""
"Botie is an effective office automation dedicated to SMEs, which stands out "
"with its unique "
msgstr ""
"Botie to skuteczna automatyzacja biura dedykowana MŚP, która wyróżnia się "
"unikalnymi "

#: .\front\templates\front\home_page\index.html:323
msgid ""
"- a database of ready-to-download and implement skills. Thanks to this, "
"process automation does not require specialist knowledge or long-term "
"implementations and can bring results immediately after downloading.\n"
"                            By using the database of solutions, "
"entrepreneurs receive inspiration and tips on further processes that they "
"can automate."
msgstr ""
"- bazą gotowych do pobrania i wdrożenia scenariuszy automatyzacji. Dzięki "
"temu automatyzacja procesów nie wymaga wiedzy specjalistycznej ani "
"długoterminowych wdrożeń i może przynieść wyniki natychmiast po pobraniu.\n"
"                            Korzystając z bazy rozwiązań, przedsiębiorcy "
"otrzymują inspiracje i wskazówki dotyczące dalszych procesów, które mogą "
"automatyzować."

#: .\front\templates\front\home_page\index.html:327
msgid "Check out"
msgstr "Sprawdź"

#: .\front\templates\front\home_page\index.html:339
msgid ""
"Discover a range of powerful automation features designed for ease of use "
"and efficiency. With a quick installation process, accessible to anyone, "
"even without technical expertise, you can implement proven automation "
"scenarios in just a few steps. Our system ensures security, transparency, "
"and cost-effectiveness with a pay-per-use model."
msgstr ""
"Odkryj szereg potężnych funkcji automatyzacji zaprojektowanych z myślą o "
"łatwości użytkowania i efektywności. Dzięki szybkiemu procesowi instalacji, "
"dostępnym dla wszystkich, nawet bez wiedzy technicznej, możesz wdrożyć "
"sprawdzone scenariusze automatyzacji w zaledwie kilku krokach. Nasz system "
"zapewnia bezpieczeństwo, przejrzystość i opłacalność w modelu płatności za "
"użycie."

#: .\front\templates\front\home_page\index.html:342
msgid "Ready to download, proven automation skills"
msgstr "Gotowe do pobrania, sprawdzone scenariusze automatyzacji"

#: .\front\templates\front\home_page\index.html:343
msgid "Speed and simplicity of installation - automation in 5 steps"
msgstr "Szybkość i prostota instalacji - automatyzacja w 5 krokach"

#: .\front\templates\front\home_page\index.html:344
msgid "Pay-Per-Use settlement system"
msgstr "System rozliczeń Pay-Per-Use"

#: .\front\templates\front\home_page\index.html:345
msgid "No programming knowledge required"
msgstr "Nie wymagana wiedza programistyczna"

#: .\front\templates\front\home_page\index.html:346
msgid "Simple and clear UX"
msgstr "Prosty i przejrzysty UX"

#: .\front\templates\front\home_page\index.html:347
msgid "Security and transparency"
msgstr "Bezpieczeństwo i przejrzystość"

#: .\front\templates\front\home_page\index.html:357
msgid ""
"Botie offers smart automation of everyday tasks, allowing businesses to "
"streamline operations and free up valuable employee time for more important "
"activities. With proven solutions and easy access to automation tools like "
"BotBook, you can boost productivity while reducing errors and saving costs "
"through a pay-per-use model."
msgstr ""
"Botie oferuje inteligentną automatyzację codziennych zadań, pozwalając "
"firmom usprawnić operacje i zwolnić cenny czas pracowników na ważniejsze "
"działania. Dzięki sprawdzonym rozwiązaniom i łatwemu dostępowi do narzędzi "
"automatyzacji, takich jak BotBook, możesz zwiększyć wydajność, redukując "
"błędy i oszczędzając koszty dzięki modelowi płatności za użycie."

#: .\front\templates\front\home_page\index.html:360
msgid "Automation of repetitive, routine and secondary activities"
msgstr "Automatyzacja powtarzalnych, rutynowych i drugorzędnych działań"

#: .\front\templates\front\home_page\index.html:361
msgid "Time savings and relief for employees, better allocation of work"
msgstr "Oszczędność czasu i ulga dla pracowników, lepsze przydzielanie pracy"

#: .\front\templates\front\home_page\index.html:362
msgid "Savings (pay per use)"
msgstr "Oszczędności (płatność za użycie)"

#: .\front\templates\front\home_page\index.html:363
msgid "Easy access to automation solutions (BotBook)"
msgstr "Łatwy dostęp do rozwiązań automatyzacyjnych (BotBook)"

#: .\front\templates\front\home_page\index.html:364
msgid "Inspiration for further automation processes and company development"
msgstr "Inspiracje do dalszych procesów automatyzacji i rozwoju firmy"

#: .\front\templates\front\home_page\index.html:365
msgid "Proven scenario database - allows you to use the experience of others"
msgstr "Sprawdzona baza scenariuszy - pozwala korzystać z doświadczeń innych"

#: .\front\templates\front\home_page\index.html:366
msgid "Repeatability - reducing human errors"
msgstr "Powtarzalność - redukcja błędów ludzkich"

#: .\front\templates\front\home_page\index.html:386
msgid ""
"Choose ready-made skills and get inspired in which company departments Botie "
"can reduce the workload of employees."
msgstr ""
"Wybierz gotowe scenariusze i zainspiruj się, w których działach firmy Botie "
"może zmniejszyć obciążenie pracowników."

#: .\front\templates\front\home_page\index.html:418
msgid "See the skills"
msgstr "Zobacz scenariusze"

#: .\front\templates\front\home_page\pricelist.html:6
#: .\front\templates\front\home_page\pricelist.html:9
#: .\front\templates\front\home_page\pricelist.html:22
#: .\robosourcing\templates\footer.html:27
#: .\robosourcing\templates\menu.html:36 .\robosourcing\templates\menu.html:39
msgid "Price list"
msgstr "Cennik"

#: .\front\templates\front\home_page\pricelist.html:32
#: .\front\templates\front\home_page\pricelist.html:55
#: .\front\templates\front\home_page\pricelist.html:106
msgid "subscription agreement"
msgstr "umowa subskrypcyjna"

#: .\front\templates\front\home_page\pricelist.html:38
msgid "one month for free"
msgstr "jeden miesiąc za darmo"

#: .\front\templates\front\home_page\pricelist.html:58
#: .\front\templates\front\home_page\pricelist.html:70
#: .\front\templates\front\home_page\pricelist.html:109
#: .\front\templates\front\home_page\pricelist.html:121
#: .\front\templates\front\home_page\pricelist.html:162
#: .\front\templates\front\home_page\pricelist.html:173
msgid "credits"
msgstr "kredyty"

#: .\front\templates\front\home_page\pricelist.html:75
msgid "netto per year"
msgstr "netto za rok"

#: .\front\templates\front\home_page\pricelist.html:78
msgid ""
"One purchase and for 12 months your account will receive a refreshed credit "
"limit to be used during the month."
msgstr ""
"Jedno zakupienie i przez 12 miesięcy Twoje konto otrzyma odnowiony limit "
"kredytowy do wykorzystania w danym miesiącu."

#: .\front\templates\front\home_page\pricelist.html:85
#: .\front\templates\front\home_page\pricelist.html:136
#: .\front\templates\front\home_page\pricelist.html:188
msgid "To purchase a credit package or subscription, you must first log in."
msgstr ""
"Aby zakupić pakiet kredytów lub subskrypcję, musisz najpierw się zalogować."

#: .\front\templates\front\home_page\pricelist.html:87
#: .\front\templates\front\home_page\pricelist.html:138
msgid ""
"You currently have an active subscription. We encourage you to buy another "
"one if this one ends. You can also make changes to an existing one from a "
"dashboard at any time."
msgstr ""
"Obecnie masz aktywną subskrypcję. Zachęcamy Cię do zakupu kolejnej, jeśli ta "
"się skończy. Możesz również wprowadzić zmiany w istniejącej w dowolnym "
"momencie z pulpitu."

#: .\front\templates\front\home_page\pricelist.html:89
#: .\front\templates\front\home_page\pricelist.html:140 .\store\views.py:63
msgid ""
"You already bought a subscription and it is about to activate. However your "
"order is still pending, please be patient a little more."
msgstr ""
"Już kupiłeś subskrypcję i zaraz zostanie aktywowana. Twoje zamówienie jest "
"jednak wciąż w toku, prosimy o chwilę cierpliwości."

#: .\front\templates\front\home_page\pricelist.html:129
msgid ""
"Every month the payment operator will collect the above amount and you will "
"receive a refreshed credit limit in your account to be used during the month."
msgstr ""
"Co miesiąc operator płatności pobierze powyższą kwotę, a Ty otrzymasz "
"odnowiony limit kredytowy na swoim koncie do wykorzystania w danym miesiącu."

#: .\front\templates\front\home_page\pricelist.html:155
msgid "we recommend it for beginners"
msgstr "zalecamy to dla początkujących"

#: .\front\templates\front\home_page\pricelist.html:158
msgid "once"
msgstr "jednorazowo"

#: .\front\templates\front\home_page\pricelist.html:158
msgid "pay for package"
msgstr "zapłać za pakiet"

#: .\front\templates\front\home_page\pricelist.html:178
msgid "netto per package"
msgstr "netto za pakiet"

#: .\front\templates\front\home_page\pricelist.html:181
msgid ""
"One-time purchase of credits without a subscription without a time limit "
"until they run out."
msgstr ""
"Jednorazowy zakup kredytów bez subskrypcji bez limitu czasowego, aż do "
"wyczerpania."

#: .\front\templates\front\home_page\pricelist.html:205
msgid "What is an operation?"
msgstr "Co to jest operacja?"

#: .\front\templates\front\home_page\pricelist.html:210
msgid ""
"The scenario (program executed by Botie) consists of a different number of "
"operations."
msgstr ""
"Scenariusz (program uruchamiany przez Botie) składa się z różnej liczby "
"operacji."

#: .\front\templates\front\home_page\pricelist.html:211
msgid ""
"An operation is a single action to be performed i.e. clicking, inserting "
"data into a text box, etc."
msgstr ""
"Operacja to pojedyncza czynność do wykonania, np. kliknięcie, wprowadzenie "
"danych do pola tekstowego itp."

#: .\front\templates\front\home_page\pricelist.html:218
msgid "What is the cost of performing operations?"
msgstr "Jaki jest koszt wykonywania operacji?"

#: .\front\templates\front\home_page\pricelist.html:223
msgid "Individual operations cost 0 (are free), 1 or more credits."
msgstr "Poszczególne operacje kosztują 0 (są darmowe), 1 lub więcej kredytów."

#: .\front\templates\front\home_page\pricelist.html:224
msgid ""
"The cost in credits of a single execution of a given scenario is known "
"before the scenario is executed and is communicated to the User."
msgstr ""
"Koszt w kredytach jednorazowego wykonania danego scenariusza jest znany "
"przed jego wykonaniem i jest komunikowany Użytkownikowi."

#: .\front\templates\front\home_page\pricelist.html:231
msgid "How can I get credits?"
msgstr "Jak mogę zdobyć kredyty?"

#: .\front\templates\front\home_page\pricelist.html:236
msgid ""
"The user receives the first credits for downloading and running the Botie "
"application."
msgstr ""
"Użytkownik otrzymuje pierwsze kredyty za pobranie i uruchomienie aplikacji "
"Botie."

#: .\front\templates\front\home_page\pricelist.html:237
msgid ""
"The User receives the first limit of free monthly credits for registering an "
"account on the website: www.botie.pl."
msgstr ""
"Użytkownik otrzymuje pierwszy limit darmowych kredytów miesięcznych za "
"zarejestrowanie konta na stronie: www.botie.pl."

#: .\front\templates\front\home_page\pricelist.html:238
msgid ""
"When the User has a revolving credit limit, it means that his account "
"balance is replenished to the initial level in accordance with the credit "
"limit."
msgstr ""
"Kiedy Użytkownik ma revolving limit kredytowy, oznacza to, że jego saldo "
"kontowe jest uzupełniane do początkowego poziomu zgodnie z limitem "
"kredytowym."

#: .\front\templates\front\home_page\pricelist.html:239
msgid ""
"At any time, the User may purchase an additional package of credits by "
"paying a one-time fee in addition to the existing subscription."
msgstr ""
"Użytkownik może w każdej chwili zakupić dodatkowy pakiet kredytów, płacąc "
"jednorazową opłatę oprócz istniejącej subskrypcji."

#: .\front\templates\front\home_page\pricelist.html:246
msgid "What is the order in which credits are consumed?"
msgstr "Jakie jest kolejność konsumowania kredytów?"

#: .\front\templates\front\home_page\pricelist.html:251
msgid ""
"Botie will first use the credits from the revolving limit, and secondly "
"those one-time purchased (in packages)."
msgstr ""
"Botie najpierw wykorzysta kredyty z revolving limitu, a następnie te "
"jednorazowo zakupione (w pakietach)."

#: .\front\templates\front\home_page\pricelist.html:258
msgid "What are the types of subscriptions?"
msgstr "Jakie są rodzaje subskrypcji?"

#: .\front\templates\front\home_page\pricelist.html:263
msgid ""
"The subscription can be paid monthly (every month) or annually (12 months in "
"advance)."
msgstr ""
"Subskrypcję można opłacać miesięcznie (co miesiąc) lub rocznie (12 miesięcy "
"z góry)."

#: .\front\templates\front\home_page\pricelist.html:264
msgid ""
"The monthly subscription can be changed at any time with immediate effect "
"(limit increase only) or for the next month."
msgstr ""
"Subskrypcję miesięczną można zmieniać w dowolnym momencie ze skutkiem "
"natychmiastowym (tylko zwiększenie limitu) lub na następny miesiąc."

#: .\front\templates\front\home_page\pricelist.html:265
msgid ""
"The annual subscription can be increased by paying the difference to the "
"higher subscription for the remaining period purchased."
msgstr ""
"Subskrypcję roczną można zwiększyć, płacąc różnicę do wyższej subskrypcji na "
"pozostały zakupiony okres."

#: .\front\templates\front\home_page\pricelist.html:272
msgid "What happens when I run out of funds?"
msgstr "Co się stanie, gdy skończą mi się środki?"

#: .\front\templates\front\home_page\pricelist.html:277
msgid ""
"If the credits run out while the program is running, Botie will ask the User "
"to make a decision: interrupt, complete the operation (while saving the work "
"results, it will only perform operations costing 0 credits), or wait until "
"the User tops up the account (e.g. with a one-time purchase)."
msgstr ""
"Jeśli kredyty się skończą podczas działania programu, Botie poprosi "
"Użytkownika o podjęcie decyzji: przerwać, zakończyć operację (przy "
"zapisywaniu wyników pracy, wykona tylko operacje kosztujące 0 kredytów) lub "
"poczekać, aż Użytkownik doładował konto (np. jednorazowym zakupem)."

#: .\front\templates\front\home_page\privacy_policy.html:341
msgid ""
"If you want to edit your consent to our use of cookies you can do that from "
"here."
msgstr ""
"Jeśli chcesz edytować swoją zgodę na nasze wykorzystanie plików cookie, "
"możesz to zrobić tutaj."

#: .\front\templates\front\home_page\privacy_policy.html:341
msgid "Change consent"
msgstr "Zmień zgodę"

#: .\front\templates\front\home_page\scenario_detail.html:46
msgid "Programs"
msgstr "Programy"

#: .\front\templates\front\home_page\scenario_detail.html:74
msgid "Assign to a robot"
msgstr "Przypisz do robota"

#: .\front\templates\front\home_page\scenario_detail.html:77
msgid ""
"You can assign skills to Robots. You then gain the ability to download them "
"directly from the application."
msgstr ""
"Możesz przypisać scenariusze do robotów. Zyskujesz wtedy możliwość pobrania "
"ich bezpośrednio z aplikacji."

#: .\front\templates\front\home_page\scenario_detail.html:81
msgid ""
"You already have an account on our website. You can link the Botie app to "
"it. Then you get a Robot to which you can assign skills and download them "
"directly from the application."
msgstr ""
"Już masz konto na naszej stronie. Możesz powiązać aplikację Botie z nim. "
"Wtedy otrzymasz robota, do którego możesz przypisać scenariusze i pobrać je "
"bezpośrednio z aplikacji."

#: .\front\templates\front\home_page\scenario_detail.html:85
msgid ""
"You can create or log in to an account on our website. You can link the "
"Botie app to it. Then you get a Robot to which you can assign skills and "
"download them directly from the application."
msgstr ""
"Możesz utworzyć konto lub zalogować się na naszej stronie. Możesz powiązać "
"aplikację Botie z nim. Wtedy otrzymasz robota, do którego możesz przypisać "
"scenariusze i pobrać je bezpośrednio z aplikacji."

#: .\front\templates\front\home_page\scenario_detail.html:93
msgid ""
"After downloading and saving the skill file on your disk, you can double-"
"click on the file name to open the skill immediately in the Botie "
"application editor."
msgstr ""
"Po pobraniu i zapisaniu pliku scenariusza na dysku, możesz dwukrotnie "
"kliknąć nazwę pliku, aby natychmiast otworzyć scenariusz w edytorze "
"aplikacji Botie."

#: .\front\templates\front\home_page\scenario_detail.html:105
msgid "The skill file has not yet been prepared."
msgstr "Plik umiejętności nie został jeszcze przygotowany"

#: .\front\templates\front\home_page\scenario_detail.html:108
msgid "Request Skill"
msgstr "Zapytaj o umiejętność"

#: .\front\templates\front\home_page\scenariusze.html:12
msgid "Skills"
msgstr "Umiejętności"

#: .\front\templates\front\home_page\scenariusze.html:13
msgid "Find skill in BotBoook and automate your work"
msgstr "Znajdź umiejętność w BotBooku i zautomatyzuj swoją pracę."

#: .\front\templates\front\home_page\scenariusze.html:23
#: .\front\templates\front\home_page\scenariusze.html:120
msgid "All Categories"
msgstr "Wszystkie kategorie"

#: .\front\templates\front\home_page\scenariusze.html:55
msgid "Search by a keyword or a tag"
msgstr "Szukaj według słowa kluczowego lub tagu"

#: .\front\templates\front\home_page\scenariusze.html:71
msgid "Tags to add:"
msgstr "Tagi do wybrania:"

#: .\front\templates\front\home_page\scenariusze.html:80
msgid "Selected tags:"
msgstr "Wybrane tagi:"

#: .\front\templates\front\home_page\scenariusze.html:114
msgid "Back to"
msgstr "Wróć do "

#: .\front\templates\front\home_page\store_regulations.html:5
#: .\front\templates\front\home_page\store_regulations.html:8
msgid "Store regulations"
msgstr "Regulamin sklepu"

#: .\front\templates\front\not_in_use\login.html:6
#: .\front\templates\front\not_in_use\login.html:11
#: .\front\templates\front\not_in_use\login.html:46
#: .\robosourcing\templates\account\login.html:7
#: .\robosourcing\templates\account\login.html:10
#: .\robosourcing\templates\account\login.html:21
#: .\robosourcing\templates\account\login.html:67
#: .\robosourcing\templates\menu.html:95
#: .\robosourcing\templates\socialaccount\login.html:4
#: .\robosourcing\templates\socialaccount\login.html:7
msgid "Sign In"
msgstr "Zaloguj się"

#: .\front\templates\front\not_in_use\login.html:16
#, python-format
msgid ""
"Please sign in with one\n"
"    of your existing third party accounts.<br>Or, <a href=\"%(signup_url)s"
"\">sign up</a>\n"
"    for a %(site_name)s account and sign in below:"
msgstr ""
"Zaloguj się za pomocą jednego\n"
"    z istniejących kont zewnętrznych.<br>Lub, <a href=\"%(signup_url)s"
"\">zarejestruj się</a>\n"
"    na konto %(site_name)s i zaloguj się poniżej:"

#: .\front\templates\front\not_in_use\login.html:26
msgid "OR"
msgstr "LUB"

#: .\front\templates\front\not_in_use\login.html:33
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Jeśli jeszcze nie utworzyłeś konta, proszę\n"
"    <a href=\"%(signup_url)s\">zarejestruj się</a> najpierw."

#: .\front\templates\front\not_in_use\login.html:47
#: .\robosourcing\templates\account\login.html:64
#: .\robosourcing\templates\account\password_change.html:23
msgid "Forgot Password?"
msgstr "Zapomniałeś hasła?"

#: .\profiles\forms.py:89
msgid "Scenario status"
msgstr "Status scenariusza"

#: .\profiles\forms.py:91
msgid "Message"
msgstr "Wiadomość"

#: .\profiles\forms.py:112
msgid "Message is required when rejecting a scenario."
msgstr "Żeby odrzucić scenariusz musisz podać powód."

#: .\profiles\forms.py:117
msgid "Add tag"
msgstr "Dodaj tag"

#: .\profiles\views.py:255
msgid "You don't have any credits yet!"
msgstr "Nie masz jeszcze żadnych kredytów!"

#: .\profiles\views.py:791
msgid "Groups saved – all tags from chosen groups assigned."
msgstr "Grupy zostały zapisane - wszystkie tagi z grup przypisane."

#: .\profiles\views.py:800
msgid "Categories saved."
msgstr "Kategorie zapisano."

#: .\profiles\views.py:815
msgid "Invalid scenario status."
msgstr "Błędny status scenariusza."

#: .\profiles\views.py:820
msgid "Reason is required when rejecting scenario."
msgstr "Żeby odrzucić scenariusz muisisz podać powód."

#: .\profiles\views.py:840
#, python-format
msgid "Status change failed: %(err)s"
msgstr "Błąd zmiany statusu: %(err)s"

#: .\profiles\views.py:851
msgid "Scenario status updated."
msgstr "Zmieniono status scenariusza."

#: .\profiles\views.py:864
msgid "SEO metadata saved."
msgstr "Metadane SEO zostały zapisane."

#: .\profiles\views.py:877
msgid "Assign to me"
msgstr "Przydziel mi"

#: .\profiles\views.py:880
msgid "Return to pool"
msgstr "Zwróć do puli"

#: .\profiles\views.py:881
msgid "Start moderation"
msgstr "Rozpocznij moderację"

#: .\profiles\views.py:885
msgid "Pause moderation"
msgstr "Wstrzymaj moderację"

#: .\profiles\views.py:886
msgid "Accept"
msgstr "Zaakceptuj"

#: .\profiles\views.py:887
msgid "Reject"
msgstr "Odrzuć"

#: .\store\models.py:21
msgid "PACKAGE"
msgstr "PAKET"

#: .\store\models.py:22
msgid "MONTHLY SUBSCRIPTION"
msgstr "SUBSKRYPCJA MIESIĘCZNA"

#: .\store\models.py:23
msgid "ANNUAL SUBSCRIPTION"
msgstr "SUBSKRYPCJA ROCZNA"

#: .\store\models.py:55
msgid "Systim"
msgstr "System"

#: .\store\models.py:207
msgid "PENDING"
msgstr "OCZEKUJĄCE"

#: .\store\models.py:208
msgid "CANCELED"
msgstr "ANULOWANE"

#: .\store\models.py:209
msgid "COMPLETED"
msgstr "ZREALIZOWANE"

#: .\store\models.py:212
msgid "SUBSCRIBE"
msgstr "ZAPISZ SIĘ"

#: .\store\models.py:213
msgid "CHANGE"
msgstr "ZMIENIĆ"

#: .\store\models.py:214
msgid "UNSUBSCRIBE"
msgstr "WYPISZ SIĘ"

#: .\store\models.py:217
msgid "IMMEDIATELY"
msgstr "NATYCHMIAST"

#: .\store\models.py:218
msgid "WITH PERIOD END"
msgstr "Z KOŃCEM OKRESU"

#: .\store\payu_connector.py:809
msgid "Your payment card has expired!"
msgstr "Twoja karta płatnicza wygasła!"

#: .\store\payu_connector.py:875 .\store\store_utils.py:410
#: .\store\viva_connector.py:469
msgid "Recurring payment has failed!"
msgstr "Powtarzająca się płatność nie powiodła się!"

#: .\store\store_utils.py:265
msgid "Subscription order"
msgstr "Zamówienie subskrypcyjne"

#: .\store\store_utils.py:266
msgid "You pay for the subscription"
msgstr "Płacisz za subskrypcję"

#: .\store\store_utils.py:269
msgid "Multiple-product order"
msgstr "Zamówienie wielu produktów"

#: .\store\store_utils.py:270
msgid "You pay for the products in your basket"
msgstr "Płacisz za produkty w swoim koszyku"

#: .\store\store_utils.py:272
msgid "Single-product order"
msgstr "Zamówienie jednego produktu"

#: .\store\store_utils.py:273
msgid "You pay for the product"
msgstr "Płacisz za produkt"

#: .\store\store_utils.py:278
msgid "Recurring subscription fee"
msgstr "Opłata za subskrypcję powtarzającą się"

#: .\store\store_utils.py:280
msgid "One-time subscription fee"
msgstr "Jednorazowa opłata za subskrypcję"

#: .\store\views.py:60
msgid ""
"You currently have an active subscription. You can buy a new one after this "
"ends. You can make changes to the existing one at any time."
msgstr ""
"Obecnie masz aktywną subskrypcję. Możesz kupić nową po jej zakończeniu. "
"Możesz wprowadzać zmiany w istniejącej w dowolnym momencie."

#: .\store\views.py:106
msgid ""
"You currently do not have an active subscription. You can purchase one at "
"any time."
msgstr ""
"Obecnie nie masz aktywnej subskrypcji. Możesz ją zakupić w dowolnym czasie."

#: .\store\views.py:128
msgid "Your unsubscription request has been accepted."
msgstr "Twoja prośba o wypisanie została przyjęta."

#: .\store\views.py:146
msgid "Your subscription change request has not been accepted!"
msgstr "Twoja prośba o zmianę subskrypcji nie została przyjęta!"

#: .\store\views.py:189 .\store\views.py:216 .\store\views.py:227
msgid "Your subscription change request has been accepted."
msgstr "Twoja prośba o zmianę subskrypcji została przyjęta."

#: .\store\views.py:195
msgid "Error occured during processing your subscription change request!"
msgstr ""
"Wystąpił błąd podczas przetwarzania Twojej prośby o zmianę subskrypcji!"

#: .\store\views.py:196 .\store\views.py:785 .\store\views.py:799
msgid "Please try again and contact us if the problem persists."
msgstr ""
"Spróbuj ponownie i skontaktuj się z nami, jeśli problem nadal występuje."

#: .\store\views.py:412 .\store\views.py:467 .\store\views.py:714
#: .\store\views.py:745
msgid "Unknown pricelist item!"
msgstr "Nieznany element cennika!"

#: .\store\views.py:420
msgid ""
"All items have been removed from the cart as a subscription should be "
"ordered separately"
msgstr ""
"Wszystkie przedmioty zostały usunięte z koszyka, ponieważ subskrypcję należy "
"zamawiać osobno"

#: .\store\views.py:426
msgid ""
"A subscription has been removed from the cart as they can't be ordered "
"simultaneously with packages."
msgstr ""
"Subskrypcja została usunięta z koszyka, ponieważ nie można ich zamawiać "
"jednocześnie z pakietami."

#: .\store\views.py:444
#, python-format
msgid ""
"\"%(item)s\" has been added to the cart! You have %(qty)s \"%(item)s\" in "
"your shopping cart!"
msgstr ""
"\"%(item)s\" został dodany do koszyka! Masz %(qty)s \"%(item)s\" w koszyku!"

#: .\store\views.py:459
msgid "Unknown basket item!"
msgstr "Nieznany element koszyka!"

#: .\store\views.py:487
msgid "Your shopping cart has been updated!"
msgstr "Twój koszyk został zaktualizowany!"

#: .\store\views.py:694
msgid "Purchase mode not set!"
msgstr "Tryb zakupu nie został ustawiony!"

#: .\store\views.py:699
msgid "Purchase operator not set!"
msgstr "Operator zakupu nie został ustawiony!"

#: .\store\views.py:708
msgid "Pricelist item not set!"
msgstr "Element cennika nie został ustawiony!"

#: .\store\views.py:725
msgid "Your shopping cart is empty!"
msgstr "Twój koszyk jest pusty!"

#: .\store\views.py:735
msgid "Unknown purchase mode!"
msgstr "Nieznany tryb zakupu!"

#: .\store\views.py:752
msgid "Products in your cart can't be ordered at once!"
msgstr "Produkty w Twoim koszyku nie mogą być zamawiane jednocześnie!"

#: .\store\views.py:755
msgid "You can order a single subscription only!"
msgstr "Możesz zamówić tylko jedną subskrypcję!"

#: .\store\views.py:784
msgid "Your cart should contain a single item only!"
msgstr "Twój koszyk powinien zawierać tylko jeden przedmiot!"

#: .\store\views.py:798
msgid "Preparing order form failed!"
msgstr "Przygotowanie formularza zamówienia nie powiodło się!"

#: .\store\views.py:921
msgid "Invoicing has not been activated. Contact the site administrator."
msgstr ""
"Fakturowanie nie zostało aktywowane. Skontaktuj się z administratorem strony."

#: .\store\views.py:923
msgid ""
"Invoices may not be issued because you either did not submit a request or "
"did not enter the necessary data."
msgstr ""
"Faktury mogą nie być wystawiane, ponieważ nie złożyłeś prośby lub nie "
"wprowadziłeś wymaganych danych."

#: .\store\viva_connector.py:451
msgid "Can't make a recurring payment. Transaction ID is missing!"
msgstr ""
"Nie można zrealizować powtarzającej się płatności. Brakuje identyfikatora "
"transakcji!"

#: .\webalizer\templates\webalizer\index.html:7
msgid "settings.WEBALIZER_DIR is not configured"
msgstr "settings.WEBALIZER_DIR nie jest skonfigurowany"

#: .\robosourcing\settings.py:205
msgid "Polish"
msgstr "Polski"

#: .\robosourcing\templates\account\email.html:4
#: .\robosourcing\templates\account\email.html:7
#: .\robosourcing\templates\account\email.html:15
#: .\robosourcing\templates\menu.html:73
msgid "E-mail Addresses"
msgstr "Adresy e-mail"

#: .\robosourcing\templates\account\email.html:18
msgid "The following e-mail addresses are associated with your account:"
msgstr "Następujące adresy e-mail są powiązane z Twoim kontem:"

#: .\robosourcing\templates\account\email.html:32
msgid "Verified"
msgstr "Zweryfikowane"

#: .\robosourcing\templates\account\email.html:34
msgid "Unverified"
msgstr "Niezweryfikowane"

#: .\robosourcing\templates\account\email.html:36
msgid "Primary"
msgstr "Podstawowy"

#: .\robosourcing\templates\account\email.html:42
msgid "Make Primary"
msgstr "Uczyń podstawowym"

#: .\robosourcing\templates\account\email.html:43
msgid "Re-send Verification"
msgstr "Wyślij ponownie weryfikację"

#: .\robosourcing\templates\account\email.html:44
#: .\robosourcing\templates\socialaccount\connections.html:41
msgid "Remove"
msgstr "Usuń"

#: .\robosourcing\templates\account\email.html:51
msgid "Warning:"
msgstr "Ostrzeżenie:"

#: .\robosourcing\templates\account\email.html:51
msgid ""
"You currently do not have any e-mail address set up. You should really add "
"an e-mail address so you can receive notifications, reset your password, etc."
msgstr ""
"Obecnie nie masz ustawionego żadnego adresu e-mail. Powinieneś dodać adres e-"
"mail, aby móc otrzymywać powiadomienia, resetować hasło itp."

#: .\robosourcing\templates\account\email.html:56
msgid "Add E-mail Address"
msgstr "Dodaj adres e-mail"

#: .\robosourcing\templates\account\email.html:65
#: .\robosourcing\templates\footer.html:40
#: .\robosourcing\templates\footer.html:41
#: .\robosourcing\templates\footer_short.html:32
#: .\robosourcing\templates\footer_short.html:33
msgid "E-mail address"
msgstr "Adres e-mail"

#: .\robosourcing\templates\account\email.html:69
msgid "Add E-mail"
msgstr "Dodaj e-mail"

#: .\robosourcing\templates\account\email.html:82
msgid "Do you really want to remove the selected e-mail address?"
msgstr "Czy na pewno chcesz usunąć wybrany adres e-mail?"

#: .\robosourcing\templates\account\email\account_already_exists_message.html:7
#: .\robosourcing\templates\account\email\account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this e-mail because you or someone else tried to signup "
"for an\n"
"account using e-mail address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that e-mail address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Otrzymujesz ten e-mail, ponieważ Ty lub ktoś inny próbował zarejestrować "
"konto używając adresu e-mail:\n"
"\n"
"%(email)s\n"
"\n"
"Jednak konto używające tego adresu e-mail już istnieje. W przypadku, gdy "
"zapomniałeś, użyj procedury resetowania hasła, aby odzyskać\n"
"twoje konto:\n"
"\n"
"%(password_reset_url)s"

#: .\robosourcing\templates\account\email\account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Konto już istnieje"

#: .\robosourcing\templates\account\email\email_confirmation_message.html:10
#: .\robosourcing\templates\account\email\email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this e-mail because user %(user_display)s has given your e-"
"mail address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Otrzymujesz ten e-mail, ponieważ użytkownik %(user_display)s podał Twój e-"
"mail do zarejestrowania konta na %(site_domain)s.\n"
"\n"
"Aby potwierdzić, że to prawda, przejdź do %(activate_url)s"

#: .\robosourcing\templates\account\email\email_confirmation_subject.txt:3
msgid "Please Confirm Your E-mail Address"
msgstr "Proszę potwierdzić swój adres e-mail"

#: .\robosourcing\templates\account\email\extra\password_reset_key_message.html:8
#: .\robosourcing\templates\account\email\extra\password_reset_key_message.txt:5
#, python-format
msgid ""
"You or someone else associated with the account requested a password reset "
"key.\n"
"The key is: %(reset_key)s\n"
"\n"
"Type it in Botie app to unlock password reset form or ignore the message."
msgstr ""
"Ty lub ktoś inny powiązany z kontem poprosił o klucz do resetu "
"hasła.\n"
"Oto klucz: %(reset_key)s\n"
"\n"
"Wprowadź go w aplikacji Botie, żeby odblokować formularz lub zignoruj tę wiadomość."

#: .\robosourcing\templates\account\email\extra\password_reset_key_subject.txt:3
msgid "Botie Password Reset Key"
msgstr "Klucz Resetu Hasła Botie"

#: .\robosourcing\templates\account\email\extra\scenario_accepted_message.html:8
#: .\robosourcing\templates\account\email\extra\scenario_accepted_message.txt:5
#, python-format
msgid ""
"Botie Skill you published: %(scenario.name)s,\n"
"having an ID: %(scenario.sid)s, has been accepted, well done!"
msgstr ""
"Umiejętność Botie, którą opublikowałeś: %(scenario.name)s,\n"
"oznaczona ID: %(scenario.sid)s, została zaakceptowana, dobra robota!"

#: .\robosourcing\templates\account\email\extra\scenario_accepted_subject.txt:3
msgid "Your Botie Skill has been accepted"
msgstr "Twoja Umiejętność Botie została zaakceptowana."

#: .\robosourcing\templates\account\email\extra\scenario_rejected_message.html:8
#: .\robosourcing\templates\account\email\extra\scenario_rejected_message.txt:5
#, python-format
msgid ""
"Botie Skill you published: %(scenario.name)s,\n"
"having an ID: %(scenario.sid)s, has been rejected!\n"
"\n"
"Reason:\n"
"%(message)s"
msgstr ""
"Umiejętność Botie, którą opublikowałeś: %(scenario.name)s,\n"
"oznaczona ID: %(scenario.sid)s, została odrzucona!\n"
"\n"
"Powód odrzucenia:\n"
"%(message)s"

#: .\robosourcing\templates\account\email\extra\scenario_rejected_subject.txt:3
msgid "Your Botie Skill has been rejected"
msgstr "Twoja Umiejętność Botie została odrzucona"

#: .\robosourcing\templates\account\email\password_reset_key_message.html:7
#: .\robosourcing\templates\account\email\password_reset_key_message.txt:4
msgid ""
"You're receiving this e-mail because you or someone else has requested a "
"password for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Otrzymujesz ten e-mail, ponieważ Ty lub ktoś inny poprosił o hasło do "
"swojego konto użytkownika.\n"
"Możesz go zignorować, jeśli nie prosiłeś o zresetowanie hasła. Kliknij "
"poniższy link, aby zresetować swoje hasło."

#: .\robosourcing\templates\account\email\password_reset_key_message.html:13
#: .\robosourcing\templates\account\email\password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "W przypadku zapomnienia, Twoja nazwa użytkownika to %(username)s."

#: .\robosourcing\templates\account\email\password_reset_key_subject.txt:3
#: .\robosourcing\templates\account\email\unknown_account_subject.txt:3
msgid "Password Reset E-mail"
msgstr "E-mail resetowania hasła"

#: .\robosourcing\templates\account\email\unknown_account_message.html:7
#: .\robosourcing\templates\account\email\unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this e-mail because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Otrzymujesz ten e-mail, ponieważ Ty lub ktoś inny poprosił o\n"
"hasło do swojego konta użytkownika. Jednak nie mamy żadnego wpisu "
"użytkownika\n"
"z adresem e-mail %(email)s w naszej bazie danych.\n"
"\n"
"Możesz bezpiecznie zignorować ten e-mail, jeśli nie prosiłeś o zresetowanie "
"hasła.\n"
"\n"
"Jeśli to Ty, możesz zarejestrować się, używając linku poniżej."

#: .\robosourcing\templates\account\email_confirm.html:5
#: .\robosourcing\templates\account\email_confirm.html:8
#: .\robosourcing\templates\account\email_confirm.html:15
msgid "Confirm E-mail Address"
msgstr "Potwierdź adres e-mail"

#: .\robosourcing\templates\account\email_confirm.html:21
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-mail "
"address for user %(user_display)s."
msgstr ""
"Proszę potwierdzić, że <a href=\"mailto:%(email)s\">%(email)s</a> jest "
"adresem e-mail użytkownika %(user_display)s."

#: .\robosourcing\templates\account\email_confirm.html:25
msgid "Confirm"
msgstr "Potwierdź"

#: .\robosourcing\templates\account\email_confirm.html:32
#, python-format
msgid ""
"This e-mail confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgstr ""
"Link potwierdzający ten e-mail wygasł lub jest nieprawidłowy. Proszę <a href="
"\"%(email_url)s\">złożyć nową prośbę o potwierdzenie e-mail</a>."

#: .\robosourcing\templates\account\login.html:31
msgid ""
"Please sign in with one\n"
"of your existing third party accounts"
msgstr ""
"Proszę zalogować się za pomocą jednego\n"
"ze swoich istniejących kont zewnętrznych"

#: .\robosourcing\templates\account\login.html:54
msgid "or using your local account"
msgstr "lub używając swojego lokalnego konta"

#: .\robosourcing\templates\account\login.html:76
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Jeśli jeszcze nie stworzyłeś konta, proszę\n"
"<a href=\"%(signup_url)s\">zarejestruj się</a> najpierw."

#: .\robosourcing\templates\account\logout.html:4
#: .\robosourcing\templates\account\logout.html:8
#: .\robosourcing\templates\account\logout.html:17
#: .\robosourcing\templates\menu.html:81
#: .\robosourcing\templates\menu_dashboard.html:71
msgid "Sign Out"
msgstr "Wyloguj się"

#: .\robosourcing\templates\account\logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Czy na pewno chcesz się wylogować?"

#: .\robosourcing\templates\account\password_change.html:6
#: .\robosourcing\templates\account\password_change.html:9
#: .\robosourcing\templates\account\password_change.html:16
#: .\robosourcing\templates\account\password_reset_from_key.html:6
#: .\robosourcing\templates\account\password_reset_from_key.html:9
#: .\robosourcing\templates\account\password_reset_from_key.html:16
#: .\robosourcing\templates\account\password_reset_from_key_done.html:4
#: .\robosourcing\templates\account\password_reset_from_key_done.html:7
#: .\robosourcing\templates\account\password_reset_from_key_done.html:14
msgid "Change Password"
msgstr "Zmień hasło"

#: .\robosourcing\templates\account\password_change.html:26
#: .\robosourcing\templates\account\password_reset_from_key.html:25
#: .\robosourcing\templates\menu.html:74
msgid "Change password"
msgstr "Zmień hasło"

#: .\robosourcing\templates\account\password_reset.html:7
#: .\robosourcing\templates\account\password_reset.html:10
#: .\robosourcing\templates\account\password_reset.html:18
#: .\robosourcing\templates\account\password_reset_done.html:5
#: .\robosourcing\templates\account\password_reset_done.html:8
#: .\robosourcing\templates\account\password_reset_done.html:15
msgid "Password Reset"
msgstr "Resetowanie hasła"

#: .\robosourcing\templates\account\password_reset.html:24
msgid ""
"Forgotten your password? Enter your e-mail address below, and we'll send you "
"an e-mail allowing you to reset it."
msgstr ""
"Zapomniałeś hasła? Wprowadź poniżej swój adres e-mail, a my wyślemy Ci e-"
"mail pozwalający je zresetować."

#: .\robosourcing\templates\account\password_reset.html:29
msgid "Reset My Password"
msgstr "Zresetuj moje hasło"

#: .\robosourcing\templates\account\password_reset.html:34
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Proszę skontaktuj się z nami, jeśli masz problemy z resetowaniem hasła."

#: .\robosourcing\templates\account\password_reset_done.html:21
msgid ""
"We have sent you an e-mail. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Wysłaliśmy do Ciebie e-mail. Jeśli go nie otrzymałeś, sprawdź folder spam. W "
"przeciwnym razie skontaktuj się z nami, jeśli nie otrzymasz go w ciągu kilku "
"minut."

#: .\robosourcing\templates\account\password_reset_from_key.html:16
msgid "Bad Token"
msgstr "Zły token"

#: .\robosourcing\templates\account\password_reset_from_key.html:20
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Link do resetowania hasła był nieprawidłowy, prawdopodobnie dlatego, że "
"został już użyty. Proszę złożyć <a href=\"%(passwd_reset_url)s\">nową prośbę "
"o reset hasła</a>."

#: .\robosourcing\templates\account\password_reset_from_key_done.html:16
msgid "Your password is now changed."
msgstr "Twoje hasło zostało zmienione."

#: .\robosourcing\templates\account\password_set.html:6
#: .\robosourcing\templates\account\password_set.html:9
#: .\robosourcing\templates\account\password_set.html:16
#: .\robosourcing\templates\account\password_set.html:20
msgid "Set Password"
msgstr "Ustaw hasło"

#: .\robosourcing\templates\account\signup.html:6
#: .\robosourcing\templates\account\signup.html:9
#: .\robosourcing\templates\socialaccount\signup.html:6
#: .\robosourcing\templates\socialaccount\signup.html:9
msgid "Signup"
msgstr "Rejestracja"

#: .\robosourcing\templates\account\signup.html:25
#: .\robosourcing\templates\account\signup.html:31
#: .\robosourcing\templates\socialaccount\signup.html:16
#: .\robosourcing\templates\socialaccount\signup.html:25
msgid "Sign Up"
msgstr "Zarejestruj się"

#: .\robosourcing\templates\account\signup.html:38
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Masz już konto? Proszę <a href=\"%(login_url)s\">zaloguj się</a>."

#: .\robosourcing\templates\account\verification_sent.html:4
#: .\robosourcing\templates\account\verification_sent.html:7
#: .\robosourcing\templates\account\verification_sent.html:14
#: .\robosourcing\templates\account\verified_email_required.html:4
#: .\robosourcing\templates\account\verified_email_required.html:7
#: .\robosourcing\templates\account\verified_email_required.html:14
msgid "Verify Your E-mail Address"
msgstr "Potwierdź swój adres e-mail"

#: .\robosourcing\templates\account\verification_sent.html:15
msgid ""
"We have sent an e-mail to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification e-mail in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification e-mail within a few minutes."
msgstr ""
"Wysłaliśmy do Ciebie e-mail w celu weryfikacji. Kliknij w podany link, aby "
"finalizować proces rejestracji. Jeśli nie widzisz e-maila weryfikacyjnego w "
"głównej skrzynce odbiorczej, sprawdź folder spam. Proszę skontaktuj się z "
"nami, jeśli nie otrzymasz go w ciągu kilku minut."

#: .\robosourcing\templates\account\verified_email_required.html:18
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your e-mail address. "
msgstr ""
"Ta część witryny wymaga od nas weryfikacji, że\n"
"jesteś tym, za kogo się podajesz. W tym celu wymagamy,\n"
"abyś zweryfikował własność swojego adresu e-mail."

#: .\robosourcing\templates\account\verified_email_required.html:22
msgid ""
"We have sent an e-mail to you for\n"
"verification. Please click on the link inside that e-mail. If you do not see "
"the verification e-mail in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Wysłaliśmy do Ciebie e-mail w celu\n"
"weryfikacji. Proszę kliknąć link wewnątrz tego e-maila. Jeśli nie widzisz e-"
"maila weryfikacyjnego w swojej głównej skrzynce odbiorczej, sprawdź folder "
"spam. W przeciwnym razie\n"
"skontaktuj się z nami, jeśli nie otrzymasz go w ciągu kilku minut."

#: .\robosourcing\templates\account\verified_email_required.html:26
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your e-"
"mail address</a>."
msgstr ""
"<strong>Uwaga:</strong> nadal możesz <a href=\"%(email_url)s\">zmienić swój "
"adres e-mail</a>."

#: .\robosourcing\templates\admin\db\scenario\change_list.html:8
#: .\robosourcing\templates\admin\db\scenario\change_list.html:11
#, python-format
msgid "Add %(name)s"
msgstr "Dodaj %(name)s"

#: .\robosourcing\templates\footer.html:12
msgid "Quick contact"
msgstr "Szybki kontakt"

#: .\robosourcing\templates\footer.html:14
#: .\robosourcing\templates\footer_short.html:12
msgid "Support"
msgstr "Wsparcie"

#: .\robosourcing\templates\footer.html:14
#: .\robosourcing\templates\footer.html:17
#: .\robosourcing\templates\footer_short.html:12
#: .\robosourcing\templates\footer_short.html:20
msgid "Sales"
msgstr "Sprzedaż"

#: .\robosourcing\templates\footer.html:24
msgid "Shortcuts"
msgstr "Skróty"

#: .\robosourcing\templates\footer.html:28
#: .\robosourcing\templates\menu.html:26
msgid "How It Works"
msgstr "Jak to działa"

#: .\robosourcing\templates\footer.html:29
msgid "Store Regulations"
msgstr "Regulamin sklepu"

#: .\robosourcing\templates\footer.html:30
msgid "Privacy Policy"
msgstr "Polityka prywatności"

#: .\robosourcing\templates\footer.html:37
#: .\robosourcing\templates\footer_short.html:29
msgid "Sign up for the newsletter"
msgstr "Zapisz się do newslettera"

#: .\robosourcing\templates\footer.html:38
#: .\robosourcing\templates\footer_short.html:30
msgid ""
"Stay up to date with the world of robotization and receive a handful of the "
"most important information every month"
msgstr ""
"Pozostań na bieżąco ze światem robotyzacji i otrzymuj garść najważniejszych "
"informacji co miesiąc."

#: .\robosourcing\templates\footer.html:42
#: .\robosourcing\templates\footer_short.html:34
msgid "Sign up"
msgstr "Zapisz się"

#: .\robosourcing\templates\footer.html:51
msgid "All rights reserved."
msgstr "Wszelkie prawa zastrzeżone."

#: .\robosourcing\templates\main.html:99
msgid "Value is missing"
msgstr "Brak wartości"

#: .\robosourcing\templates\main.html:100
msgid "Type mismatch"
msgstr "Niepasujący typ"

#: .\robosourcing\templates\main.html:101
msgid "Pattern mismatch"
msgstr "Niepasujący wzór"

#: .\robosourcing\templates\main.html:102
msgid "Value is too long"
msgstr "Wartość jest za długa"

#: .\robosourcing\templates\main.html:103
msgid "Value is too short"
msgstr "Wartość jest za krótka"

#: .\robosourcing\templates\main.html:104
msgid "Bad input"
msgstr "Złe dane wejściowe"

#: .\robosourcing\templates\main.html:105
msgid "Range overflow"
msgstr "Przepełnienie zakresu"

#: .\robosourcing\templates\main.html:106
msgid "Range underflow"
msgstr "Niedobór zakresu"

#: .\robosourcing\templates\main.html:107
msgid "Step mismatch"
msgstr "Niepasujący krok"

#: .\robosourcing\templates\menu.html:28
msgid "User Guide"
msgstr "Podręcznik użytkownika"

#: .\robosourcing\templates\menu.html:40
msgid "Skills Development"
msgstr "Tworzenie Umiejętności"

#: .\robosourcing\templates\menu.html:41
msgid "Automation as a Service"
msgstr "Automatyzacja procesów"

#: .\robosourcing\templates\menu.html:46
msgid "Help center"
msgstr "Centrum pomocy"

#: .\robosourcing\templates\menu.html:49
msgid "FAQ"
msgstr "Najczęściej zadawane pytania"

#: .\robosourcing\templates\menu.html:50
msgid "Botie Academy"
msgstr "Akademia Botie"

#: .\robosourcing\templates\menu.html:51
msgid "Forum"
msgstr "Forum"

#: .\robosourcing\templates\menu.html:52
msgid "Editor"
msgstr "Edytor"

#: .\robosourcing\templates\menu.html:53
msgid "Features"
msgstr "Funkcje"

#: .\robosourcing\templates\menu.html:72
#: .\robosourcing\templates\socialaccount\connections.html:6
#: .\robosourcing\templates\socialaccount\connections.html:9
#: .\robosourcing\templates\socialaccount\connections.html:16
msgid "Account Connections"
msgstr "Połączenia konta"

#: .\robosourcing\templates\menu.html:87
msgid "DASHBOARD"
msgstr "PANEL UŻYTKOWNIKA"

#: .\robosourcing\templates\menu.html:96
msgid "Sign Up for a free account"
msgstr "Zarejestruj się, aby uzyskać darmowe konto"

#: .\robosourcing\templates\menu_dashboard.html:28
msgid "Home"
msgstr "Strona główna"

#: .\robosourcing\templates\menu_dashboard.html:53
msgid "Wallet"
msgstr "Portfel"

#: .\robosourcing\templates\menu_dashboard.html:66
msgid "Account"
msgstr "Konto"

#: .\robosourcing\templates\menu_dashboard.html:67
msgid "Settings"
msgstr "Ustawienia"

#: .\robosourcing\templates\menu_lite.html:16
msgid "Go back"
msgstr "Wróć"

#: .\robosourcing\templates\socialaccount\authentication_error.html:4
#: .\robosourcing\templates\socialaccount\authentication_error.html:7
#: .\robosourcing\templates\socialaccount\authentication_error.html:14
msgid "Social Network Login Failure"
msgstr "Niepowodzenie logowania w sieci społecznościowej"

#: .\robosourcing\templates\socialaccount\authentication_error.html:15
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"Wystąpił błąd podczas próby zalogowania się za pomocą konta w sieci "
"społecznościowej."

#: .\robosourcing\templates\socialaccount\connections.html:19
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr ""
"Możesz zalogować się na swoje konto, używając któregokolwiek z poniższych "
"kont zewnętrznych:"

#: .\robosourcing\templates\socialaccount\connections.html:49
msgid ""
"You currently have no social network accounts connected to this account."
msgstr ""
"Obecnie nie masz żadnych kont w sieci społecznościowej połączonych z tym "
"kontem."

#: .\robosourcing\templates\socialaccount\connections.html:51
msgid "Add a 3rd Party Account"
msgstr "Dodaj konto zewnętrzne"

#: .\robosourcing\templates\socialaccount\login.html:15
#, python-format
msgid "Connect %(provider)s"
msgstr "Połącz z %(provider)s"

#: .\robosourcing\templates\socialaccount\login.html:16
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr "Zaraz połączysz nowe zewnętrzne konto z %(provider)s."

#: .\robosourcing\templates\socialaccount\login.html:18
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Zaloguj się przez %(provider)s"

#: .\robosourcing\templates\socialaccount\login.html:19
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr "Zaraz zalogujesz się przy użyciu zewnętrznego konta z %(provider)s."

#: .\robosourcing\templates\socialaccount\login.html:23
msgid "Continue"
msgstr "Kontynuuj"

#: .\robosourcing\templates\socialaccount\login_cancelled.html:4
#: .\robosourcing\templates\socialaccount\login_cancelled.html:7
#: .\robosourcing\templates\socialaccount\login_cancelled.html:14
msgid "Login Canceled"
msgstr "Logowanie anulowane"

#: .\robosourcing\templates\socialaccount\login_cancelled.html:16
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Postanowiłeś anulować logowanie na naszej stronie za pomocą jednego ze "
"swoich istniejących kont. Jeśli to był błąd, przejdź do <a href="
"\"%(login_url)s\">logowania</a>."

#: .\robosourcing\templates\socialaccount\signup.html:17
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Zaraz użyjesz swojego konta %(provider_name)s, aby zalogować się do\n"
"%(site_name)s. Jako ostatni krok, proszę wypełnij poniższy formularz:"

#: .\robosourcing\templates\theme_picker.html:28
msgid "light theme"
msgstr "jasny motyw"

#: .\robosourcing\templates\theme_picker.html:30
msgid "dark theme"
msgstr "ciemny motyw"

#: .\robosourcing\templates\theme_picker.html:32
msgid "auto theme"
msgstr "motyw automatyczny"

#~ msgid "WITHDRAWAL_REQUEST_SUBMITTED"
#~ msgstr "Wniosek o wypłatę został złożony"

#~ msgid "MONTHLY_WITHDRAWAL_SUMMARY"
#~ msgstr "Miesięczne Podsumowanie Wypłat"

#~ msgid "accepted"
#~ msgstr "zaakceptowane"

#~ msgid "Enter tag name"
#~ msgstr "Wprowadź nazwę taga"

#~ msgid "Add"
#~ msgstr "Dodaj"

#~ msgid "best value for money"
#~ msgstr "najlepsza wartość"

#~ msgid "English"
#~ msgstr "Angielski"

#~ msgid "STATUS"
#~ msgstr "STATUS"

#~ msgid "---"
#~ msgstr "---"

#~ msgid "Change status"
#~ msgstr "Zmień status"

#~ msgid "Scenario"
#~ msgstr "Scenariusz"

#~ msgid "French"
#~ msgstr "Francuski"

#~ msgid "German"
#~ msgstr "Niemiecki"

#~ msgid "Italian"
#~ msgstr "Włoski"
