{% load i18n %}
{% load index %}
{% load split %}
{% load static %}
{% load theme %}

{% with 'sun-fill moon-stars-fill circle-half'|split:" " as icons %}
    {% with 'warning white white'|split:" " as colors %}
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            {#{% if 'theme' in request.session and request.session.theme == 'dark' %}#}
            {% if request|theme == 'dark' %}
            <span><i class="bi bi-moon-stars-fill text-white"></i></span>
            {#{% elif 'theme' in request.session and request.session.theme == 'light' %}#}
            {% elif request|theme == 'light' %}
            <span><i class="bi bi-sun-fill text-warning"></i></span>
            {% else %}
            <span><i class="bi bi-circle-half text-white"></i></span>
            {% endif %}
        </a>
        <ul class="dropdown-menu dropdown-menu-dark" id="theme-list">
        {% with 'light dark auto' as list %}
            {% for name in list.split %}
            <li>
                <a class="dropdown-item" href="{% url 'set_theme' %}" data-theme-name="{{ name }}">
                    <i class="bi bi-{{ icons|index:forloop.counter0 }} text-{{ colors|index:forloop.counter0 }}"></i>
                    {% if name == 'light' %}
                    <span> &nbsp; {% trans 'light theme' %}</span>
                    {% elif name == 'dark' %}
                    <span> &nbsp; {% trans 'dark theme' %}</span>
                    {% else %}
                    <span> &nbsp; {% trans 'auto theme' %}</span>
                    {% endif %}
                </a>
            </li>
            {% endfor %}
        {% endwith %}
        </ul>
    </li>
    {% endwith %}
{% endwith %}