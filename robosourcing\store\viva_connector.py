# from django.utils.crypto import md5
# from django.utils.translation import gettext_lazy as _

import base64
import json
import requests
# from hashlib import md5
import logging
import ipaddress
from datetime import datetime

from store.models import SubsequentOrder


class SingletonMeta(type):
    """
    The Singleton metaclass.
    """

    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]


class VivaConnector(metaclass=SingletonMeta):
    """
    A class containing methods for handling payments via a website and API functions provided by Viva Payments
    """

    _CONNECTION_MODE = 'sandbox'
    _VIVA_MERCHANT_ID = '50325656-7906-4ed7-a8b2-0f7fa0621af7'
    _VIVA_API_KEY = ')4Am|W'
    # POS API credentials
    # _VIVA_CLIENT_ID = 'o399j0gjdvqxfs6q969w1v6boc4ty54o5i2suqom3cqo5.apps.vivapayments.com'
    # _VIVA_SECRET = '61mfYTT2sgm0sn5b99oMatdCBJsQdD'
    # smart API credentials
    _VIVA_CLIENT_ID = 'lc2ty9sqgn3215bqhfw73r48f8udva8p5ued7w9y7j489.apps.vivapayments.com'
    _VIVA_SECRET = 'e75mq96rCfsVd9uA254UY782JgmO2X'
    _NOTIFY_IPS = ['************', '***********', '************', '************', '************', '************',
                   '*************', '*************']  # https://developer.viva.com/webhooks-for-payments/

    # _CONNECTION_MODE = 'production'
    # _VIVA_MERCHANT_ID = ''
    # _VIVA_API_KEY = ''
    # smart API credentials
    # _VIVA_CLIENT_ID = ''
    # _VIVA_SECRET = ''
    # _NOTIFY_IPS = ['************', '************', '************', '************', '************', '************']
    # _NOTIFY_IPS = ['*************',	'***********', '************', '************', '***********', '**************/28',
    #                '**************/28']  # https://developer.viva.com/webhooks-for-payments/

    # FIXME: development only (remove following line)
    _NOTIFY_IPS.append('127.0.0.1')

    _basic_headers = None
    _headers = None
    _logger = None

    # ********************************************
    def __init__(self):
        # init logger
        self._logger = logging.getLogger('VIVA')
        self._logger.setLevel(logging.DEBUG)
        self._file_handler = logging.FileHandler('viva.log')
        self._file_handler.setLevel(logging.INFO)
        self._file_handler.setFormatter(logging.Formatter('%(asctime)s | %(levelname)s | %(message)s'))
        self._logger.addHandler(self._file_handler)
        # basic headers
        credentials = f'{self._VIVA_MERCHANT_ID}:{self._VIVA_API_KEY}'
        self._basic_headers = {
            'Authorization': 'Basic ' + base64.b64encode(credentials.encode()).decode(),
            'Content-Type': 'application/json'}

    @staticmethod
    # ********************************************
    def _print_json(code):
        """
        Prints out JSON formatted code
        """
        print(json.dumps(code, indent=4))

    # ********************************************
    @staticmethod
    def _get_client_ip(req):
        """
        Extract the client IP address from the request.

        :param req: The HTTP request object.
        :return: IP address as a string.
        """
        x_forwarded_for = req.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = req.META.get('REMOTE_ADDR')
        return ip

    # ********************************************
    def _make_request_without_redirs(self, url, headers=None, data=None, json_data=None, stop_code=200):
        """
        Makes request redirectios step by step until expected status code is reached.
        """
        # make single request without redirections
        if json_data:
            response = requests.post(url, headers=headers, json=json_data, allow_redirects=False)
        else:
            response = requests.post(url, headers=headers, data=data, allow_redirects=False)
        # print(response.status_code)
        # print(response.content)
        # check status code
        if response.status_code == stop_code:
            return response
        elif 'Location' in response.headers:
            # print(str(response.status_code) + '->' + response.headers['Location'])
            return self._make_request_without_redirs(response.headers['Location'], headers=headers, data=data,
                                                     json_data=json_data, stop_code=stop_code)
        else:
            # print('no location')
            return None

    # ********************************************
    def login(self):
        # auth
        data = {'grant_type': 'client_credentials', 'client_id': self._VIVA_CLIENT_ID,
                'client_secret': self._VIVA_SECRET}
        resp = requests.post('https://demo-accounts.vivapayments.com/connect/token', data=data)
        # print(resp)
        obj = resp.json()
        self._print_json(obj)
        self._headers = {'Authorization': f'{obj["token_type"]} {obj["access_token"]}',
                         'Content-Type': 'application/json'}

    # ********************************************
    def get_payment_methods(self):
        # TODO: find such a PayU method in Viva API and replace
        # data = {}
        # query = '/?lang=pl'
        # resp = requests.get('https://secure.snd.payu.com/api/v2_1/paymethods'+query, headers=self._headers, data=data)
        # # print(resp)
        # obj = resp.json()
        # self._print_json(obj)
        pass

    # ********************************************
    def create_order(self, req, basket_items) -> str:

        # prepare_order_data
        from store.store_utils import StoreUtils
        su = StoreUtils()
        products, num, total_price, tax, total_price_incl_tax, desc, vdesc = \
            su.prepare_order_data(basket_items)
        data = {
            "amount": int(total_price_incl_tax*100),
            "customerTrns": vdesc,
            "customer": {
                # "email": "string",
                "fullName": f'{req.user.first_name} {req.user.last_name}',
                # "phone": "string",
                "countryCode": "PL",
                "requestLang": "pl" if req.LANGUAGE_CODE == "pl" else "en",
            },
            # "dynamicDescriptor": "Descriptor",
            "currencyCode": 985,
            # "paymentTimeout": 0,
            "preauth": False,
            # "allowRecurring": False,
            # "maxInstallments": 0,
            # "forceMaxInstallments": False,
            # "paymentNotification": True,
            # "tipAmount": 1,
            # "disableExactAmount": False,
            # "disableCash": False,
            # "disableWallet": False,
            "sourceCode": 6009,
            # "merchantTrns": "string",
            # "tags": [
            #     "tag 1",
            #     "tag 2"
            # ],
            # "paymentMethodFees": {
            #     "paymentMethodId": "35",
            #     "fee": 250
            # },
            # "cardTokens": [
            #     "ct_5d0a4e3a7e04469f82da228ca98fd661"
            # ],
            # "isCardVerification": False,
            # "klarnaOrderOptions": {
            #     "attachment": {
            #         "body": "string",
            #         "contentType": "string"
            #     },
            #     "billingAddress": {
            #         "city": "London",
            #         "email": "<EMAIL>",
            #         "phone": "01234567890",
            #         "title": "Mr",
            #         "region": "string",
            #         "country": "GB",
            #         "attention": "string",
            #         "givenName": "John",
            #         "familyName": "Doe",
            #         "postalCode": "SW11 1AA",
            #         "streetAddress": "1 Test Street",
            #         "streetAddress2": "Flat 5",
            #         "organizationName": "string"
            #     },
            #     "shippingAddress": {
            #         "city": "London",
            #         "email": "<EMAIL>",
            #         "phone": "01234567890",
            #         "title": "Mr",
            #         "region": "string",
            #         "country": "GB",
            #         "attention": "string",
            #         "givenName": "John",
            #         "familyName": "Doe",
            #         "postalCode": "SW11 1AA",
            #         "streetAddress": "1 Test Street",
            #         "streetAddress2": "Flat 5",
            #         "organizationName": "string"
            #     },
            #     "orderLines": {
            #         "name": "Product",
            #         "type": "physical",
            #         "taxRate": "2000",
            #         "quantity": "1",
            #         "unitPrice": "5000",
            #         "imageUrl": "https://www.example.com/example",
            #         "reference": "987654",
            #         "totalAmount": "4000",
            #         "productUrl": "https://www.example.com/example",
            #         "merchantData": "string",
            #         "quantityUnit": "pcs",
            #         "totalTaxAmount": "1000",
            #         "totalDiscountAmount": "1000",
            #         "subscription": {
            #             "name": "string",
            #             "interval": "MONTH",
            #             "intervalCount": "3"
            #         },
            #         "productIdentifiers": {
            #             "size": "12",
            #             "brand": "Acme",
            #             "color": "Teal",
            #             "categoryPath": "Clothing > Women's > Dresses",
            #             "globalTradeItemNumber": "***********",
            #             "manufacturerPartNumber": "123456789000"
            #         }
            #     }
            # }
        }

        # self._print_json(data)
        # print(self._headers)

        resp = requests.post('https://demo-api.vivapayments.com/checkout/v2/orders', headers=self._headers, json=data)
        # print(resp.status_code)
        # print(resp.content.decode())
        obj = resp.json()
        # self._print_json(obj)

        # handle order before payment
        su.handle_order_before_payment(req, False, 'VIVA', obj["orderCode"], basket_items, total_price, tax,
                                       total_price_incl_tax, desc)

        # for development only!
        # req.session['last_order_id'] = order_id

        # go to viva payments
        # leading_color = '84b436'  # brandbook green (hex notation without a hash sign)
        # method_id = 12  # Blik (https://developer.vivawallet.com/smart-checkout/smart-checkout-integration/)
        # return f'https://demo.vivapayments.com/web/checkout?ref={obj["orderCode"]}&color={leading_color}&paymentMethod={method_id}'
        return f'https://demo.vivapayments.com/web/checkout?ref={obj["orderCode"]}'

    # ********************************************
    def create_subscription(self, req, basket_items) -> str | None:

        # prepare_order_data
        from store.store_utils import StoreUtils
        su = StoreUtils()
        products, num, total_price, tax, total_price_incl_tax, desc, vdesc = \
            su.prepare_order_data(basket_items, is_subscription=True)
        data = {
            "amount": int(total_price_incl_tax * 100),
            "customerTrns": vdesc,
            "customer": {
                # "email": "string",
                "fullName": req.user.get_full_name(),
                # "phone": "string",
                "countryCode": "PL",
                "requestLang": "pl" if req.LANGUAGE_CODE == "pl" else "en",
            },
            # "dynamicDescriptor": "Descriptor",
            "currencyCode": 985,
            # "paymentTimeout": 0,
            "preauth": False,
            "allowRecurring": True,
            # "maxInstallments": 0,
            # "forceMaxInstallments": False,
            # "paymentNotification": True,
            # "tipAmount": 1,
            # "disableExactAmount": False,
            # "disableCash": False,
            # "disableWallet": False,
            "sourceCode": 6009,
            # "merchantTrns": "string",
            # "tags": [
            #     "tag 1",
            #     "tag 2"
            # ],
            # "paymentMethodFees": {
            #     "paymentMethodId": "35",
            #     "fee": 250
            # },
            # "cardTokens": [
            #     "ct_5d0a4e3a7e04469f82da228ca98fd661"
            # ],
            # "isCardVerification": False,
            # "klarnaOrderOptions": {
            #     "attachment": {
            #         "body": "string",
            #         "contentType": "string"
            #     },
            #     "billingAddress": {
            #         "city": "London",
            #         "email": "<EMAIL>",
            #         "phone": "01234567890",
            #         "title": "Mr",
            #         "region": "string",
            #         "country": "GB",
            #         "attention": "string",
            #         "givenName": "John",
            #         "familyName": "Doe",
            #         "postalCode": "SW11 1AA",
            #         "streetAddress": "1 Test Street",
            #         "streetAddress2": "Flat 5",
            #         "organizationName": "string"
            #     },
            #     "shippingAddress": {
            #         "city": "London",
            #         "email": "<EMAIL>",
            #         "phone": "01234567890",
            #         "title": "Mr",
            #         "region": "string",
            #         "country": "GB",
            #         "attention": "string",
            #         "givenName": "John",
            #         "familyName": "Doe",
            #         "postalCode": "SW11 1AA",
            #         "streetAddress": "1 Test Street",
            #         "streetAddress2": "Flat 5",
            #         "organizationName": "string"
            #     },
            #     "orderLines": {
            #         "name": "Product",
            #         "type": "physical",
            #         "taxRate": "2000",
            #         "quantity": "1",
            #         "unitPrice": "5000",
            #         "imageUrl": "https://www.example.com/example",
            #         "reference": "987654",
            #         "totalAmount": "4000",
            #         "productUrl": "https://www.example.com/example",
            #         "merchantData": "string",
            #         "quantityUnit": "pcs",
            #         "totalTaxAmount": "1000",
            #         "totalDiscountAmount": "1000",
            #         "subscription": {
            #             "name": "string",
            #             "interval": "MONTH",
            #             "intervalCount": "3"
            #         },
            #         "productIdentifiers": {
            #             "size": "12",
            #             "brand": "Acme",
            #             "color": "Teal",
            #             "categoryPath": "Clothing > Women's > Dresses",
            #             "globalTradeItemNumber": "***********",
            #             "manufacturerPartNumber": "123456789000"
            #         }
            #     }
            # }
        }

        # self._print_json(data)
        # print(self._headers)

        resp = requests.post('https://demo-api.vivapayments.com/checkout/v2/orders', headers=self._headers, json=data)
        # print(resp.status_code)
        # print(resp.content.decode())
        obj = resp.json()
        # self._print_json(obj)

        if "orderCode" in obj.keys():

            # handle subscription before payment
            su.handle_order_before_payment(req, True, 'VIVA', obj["orderCode"], basket_items, total_price, tax,
                                           total_price_incl_tax, desc)

            # create recurring payment record
            # (when transaction is complete - see update_orders_statuses method in views.py)

            # for development only!
            # req.session['last_order_id'] = obj["orderCode"]

            # go to viva payments
            # leading_color = '0b8bcc'  # brandbook blue (hex notation without a hash sign)
            # method_id = 12  # Blik (https://developer.vivawallet.com/smart-checkout/smart-checkout-integration/)
            # return f'https://demo.vivapayments.com/web/checkout?ref={obj["orderCode"]}&color={leading_color}&paymentMethod={method_id}'
            return f'https://demo.vivapayments.com/web/checkout?ref={obj["orderCode"]}'

        return None

# ********************************************
    def make_recurring_payment(self, user, reference_order, amount, is_supplement=False) -> tuple:

        now = datetime.now()
        self._logger.info(f'[{now}] making recurring payment (viva): {user=} {reference_order=}')

        # use immediate translation - method is launched from views but also from scheduler
        # so lazy_text may not work properly
        # import gettext and use of gettext() instead of _() does the job
        from django.utils.translation import gettext

        # prepare payment data
        from store.store_utils import StoreUtils
        su = StoreUtils()
        _, total_price, tax, total_price_incl_tax, desc = su.prepare_payment_data(amount, is_supplement=is_supplement)

        data = {
            "amount": int(total_price_incl_tax * 100),
            "customerTrns": desc
        }

        self._print_json(data)
        # print(self._basic_headers)

        transaction_id = reference_order.payment_id

        # TODO: remove following lines - development only!
        # hardcoded translate order ID -> transaction ID
        # if reference_order.order_id == '7457120371625013':
        #     transaction_id = '3f56c737-9aa9-4bc0-b0e6-7dc78a185dc4'

        if not transaction_id:
            return None, gettext("Can't make a recurring payment. Transaction ID is missing!")

        # make payment
        resp = requests.post(f'https://demo.vivapayments.com/api/transactions/{transaction_id}',
                             headers=self._basic_headers, json=data)
        obj = resp.json()
        self._print_json(obj)

        if "StatusId" in obj.keys():  # capital "S" here!!!
            if obj["StatusId"] == "F":

                # store payment in db
                SubsequentOrder.objects.create(order_id=obj["TransactionId"], reference_order=reference_order,
                                               description=desc, price=total_price, tax=tax,
                                               price_incl_tax=total_price_incl_tax)

                return obj["TransactionId"], ''

        return None, gettext('Recurring payment has failed!')

# ********************************************
    def retrieve_order(self, order_id) -> json:
        # print(self._basic_headers)
        resp = requests.get(f'https://demo.vivapayments.com/api/orders/{order_id}', headers=self._basic_headers)
        return resp.json()

# ********************************************
    def retrieve_transaction(self, transaction_id) -> json:
        resp = requests.get(f'https://demo-api.vivapayments.com/checkout/v2/transactions/{transaction_id}',
                            headers=self._headers)
        print(resp)
        return resp.json()

# ********************************************
    def authorize_webhook(self) -> str:
        resp = requests.get(f'https://demo.vivapayments.com/api/messages/config/token', headers=self._basic_headers)
        return resp.json()

# ********************************************
    def goes_from_authorized_ip(self, req) -> bool:
        x_forwarded_for = req.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = req.META.get('REMOTE_ADDR')
        match = False
        ip_address = ipaddress.ip_address(ip)
        for ip_range in self._NOTIFY_IPS:
            if '/' in ip_range:
                if ip_address in ipaddress.ip_network(ip_range, strict=False):
                    match = True
            else:
                if ip_address == ipaddress.ip_address(ip_range):
                    match = True
        if not match:
            self._logger.error(f'ip not allowed ({ip})')
        return match

# ********************************************
    def handle_notification(self, req) -> bool:

        # log headers
        self._logger.info('headers\n' + json.dumps(dict(req.headers), indent=4))

        # log JSON payload
        if req.method == "POST":
            try:
                data = json.loads(req.body)
                self._logger.info('json payload\n' + json.dumps(data, indent=4))
            except json.JSONDecodeError:
                pass

        # verify IP
        if not self.goes_from_authorized_ip(req):
            return False

        # verify signature
        # TODO: find such PayU fields in Viva docs and replace
        # key = "OpenPayu-Signature"
        # if not key in req.headers.keys():
        #     self._logger.error(f'no key in header ({key})')
        #     return False
        # data = dict(v.split("=") for v in req.headers[key].split(";"))
        # key = 'signature'
        # if not key in data.keys():
        #     self._logger.error(f'no signature in header at key ({key})')
        #     return False
        # incoming_signature = data[key]
        # key = 'algorythm'
        # if not key in data.keys():
        #     self._logger.error(f'no encryption algorythm in header at key ({key})')
        #     return False
        # algorythm = data[key]
        # if algorythm != 'MD5':
        #     self._logger.error(f'not supported encryption algorythm ({algorythm})')
        #     return False
        # concatenated = req.response + self._VIVA_SECRET
        # expected_signature = md5(concatenated)
        # if expected_signature != incoming_signature:
        #     self._logger.error(f'singnatures do not match')
        #     return False

        # update order status
        # TODO: will be possible to implement when we get the first response (see viva.log)

        return True
