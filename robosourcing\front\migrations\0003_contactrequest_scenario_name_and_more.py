# Generated by Django 4.2.13 on 2024-09-26 17:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('front', '0002_contactrequest'),
    ]

    operations = [
        migrations.AddField(
            model_name='contactrequest',
            name='scenario_name',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='contactrequest',
            name='contact_preference',
            field=models.CharField(choices=[('email', 'Email'), ('phone', 'Phone'), ('whatsapp', 'WhatsApp'), ('skype', 'Skype')], max_length=50),
        ),
        migrations.AlterField(
            model_name='contactrequest',
            name='message_type',
            field=models.CharField(choices=[('custom_scenario', 'I need a custom scenario'), ('existing_scenario', 'I need a custom scenario based on an existing one'), ('technical_support', 'I need technical support'), ('other', 'Something else')], max_length=50),
        ),
    ]
