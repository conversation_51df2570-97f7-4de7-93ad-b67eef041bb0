<!-- basket.html -->
{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load crispy_forms_filters %}
{% load settings %}

{% block title %}{{ block.super }} - {% trans "Cart" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{% url 'store' %}">{% trans "Store" %}</a></li>
          <li class="breadcrumb-item site-name active">{% trans "Cart" %}</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-xxl-8">
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="dashboard-element h-100 d-flex flex-column">
                        <h3 class="db-section-title">1. {% blocktrans %}Your Cart:{% endblocktrans %}</h3>
                        <hr>
                        
                        {% if basket.all %}
                            <div class="table-responsive">
                                <table class="my-custom-table">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Name" %}</th>
                                            <th class="d-none d-lg-table-cell">{% trans "Description" %}</th>
                                            <th class="d-none d-md-table-cell">{% trans "Credits" %}</th>
                                            <th>{% trans "Price" %}</th>
                                            <th>{% trans "Quantity" %}</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in basket.all %}
                                        <tr>
                                            <td>{{ item.product.name }}</td>
                                            <td class="d-none d-lg-table-cell">{{ item.product.description }}</td>
                                            <td class="d-none d-md-table-cell">{{ item.product.value|floatformat:"0g" }}</td>
                                            <td>{{ item.product.price|floatformat:"2g" }} zł</td>
                                            <td>{{ item.quantity }}</td>
                                            <td>
                                                {% if item.product.type == 'PAC' %}
                                                    <a class="btn btn-sm btn-secondary" href="{% url 'update_basket' 'increment' item.product.id %}"><i class="fa fa-plus"></i></a>
                                                    <a class="btn btn-sm btn-secondary" href="{% url 'update_basket' 'decrement' item.product.id %}"><i class="fa fa-minus"></i></a>
                                                {% endif %}
                                                <a class="btn btn-sm btn-danger" href="{% url 'update_basket' 'remove' item.product.id %}"><i class="fa fa-times"></i></a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <!-- Przycisk 'Confirm Cart' i 'Edit Cart' -->
                            <button type="button" class="btn btn-primary btn-dashboard mt-5" id="confirm-cart-btn">Confirm Cart</button>
                            <button type="button" class="btn btn-outline-primary-db mt-5" id="edit-cart-btn" style="display: none;">Edit</button>

                    </div>
                </div>

                <div class="col-12 mb-3">
                    <div class="dashboard-element h-100 d-flex flex-column" id="personal-info-section">
                        <h3 class="db-section-title">2. {% trans "Your Details:" %}</h3>
                        <hr>
                        <form method="post" id="invoiceForm">
                            {% csrf_token %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="want_invoice" name="want_invoice" {% if want_invoice %}checked{% endif %}>
                                <label class="form-check-label" for="want_invoice">
                                    {% trans "I want a VAT invoice" %}
                                </label>
                            </div>
                        </form>
                        <br>
                        <div class="row">
                            <div class="col-6">
                                <h4>{% trans "Your Details" %}</h4>
                                <p>
                                    <strong>{% trans "Name:" %}</strong>
                                    {% if user_profile.user.first_name and user_profile.user.last_name %}
                                        <br>{{ user_profile.user.first_name }} {{ user_profile.user.last_name }}
                                        <br>
                                        <button class="btn btn-outline-primary btn-outline-primary-db" data-bs-toggle="modal" data-bs-target="#nameModal">
                                            <i class="fas fa-pencil-alt"></i>
                                        </button>
                                    {% else %}
                                        <br>{% trans "Please fill in your name" %}
                                        <br>
                                        <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#nameModal">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    {% endif %}
                                </p>
                                <p>
                                    <strong>{% trans "Phone:" %}</strong>
                                    <br>
                                    {% if user_profile.contact.phone_number %}
                                        {{ user_profile.contact.area_code }} {{ user_profile.contact.phone_number }}
                                        <br>
                                        <button class="btn btn-outline-primary btn-outline-primary-db" data-bs-toggle="modal" data-bs-target="#phoneNumberModal">
                                            <i class="fas fa-pencil-alt"></i>
                                        </button>
                                    {% else %}
                                        {% trans "Please fill in your phone number" %}
                                        <br>
                                        <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#phoneNumberModal">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    {% endif %}
                                </p>
                                <p>
                                    <strong>{% trans "Address:" %}</strong>
                                    <br>
                                    {% if user_profile.address.street and user_profile.address.city and user_profile.address.postal_code %}
                                        {{ user_profile.address.street }} {{ user_profile.address.street_number }},
                                        {% if user_profile.address.home_number %}
                                        {{ user_profile.address.home_number }},
                                        {% endif %}
                                        {{ user_profile.address.city }},
                                        {{ user_profile.address.postal_code }},
                                        {{ user_profile.address.country }}
                                        <br>
                                        <button class="btn btn-outline-primary btn-outline-primary-db" data-bs-toggle="modal" data-bs-target="#addressModal">
                                            <i class="fas fa-pencil-alt"></i>
                                        </button>
                                    {% else %}
                                        {% trans "Please fill in your address" %}
                                        <br>
                                        <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#addressModal">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    {% endif %}
                                </p>
                            </div>

                            <div class="col-6" id="invoiceDetails" style="display: none;">
                                <h4>{% trans "Invoice Details" %}</h4>
                                {% if user_profile.organization_uuid %}
                                <p>
                                    <strong>{% trans "Organization Name:" %}</strong>
                                    {% if user_profile.organization_uuid.name_1 %}
                                        <br>{{ user_profile.organization_uuid.name_1 }}
                                        {% if user_profile.organization_uuid.name_2 %}
                                            {{ user_profile.organization_uuid.name_2 }}
                                        {% endif %}
                                    {% else %}
                                        <br>{% trans "Please fill in your organization name" %}
                                    {% endif %}
                                </p>

                                <p>
                                    <strong>{% trans "Tax ID:" %}</strong>
                                    {% if user_profile.organization_uuid.tax_id %}
                                        <br>{{ user_profile.organization_uuid.tax_id }}
                                    {% else %}
                                        <br>{% trans "Please fill in your Tax ID" %}
                                    {% endif %}
                                </p>

                                <p>
                                    <strong>{% trans "Address:" %}</strong>
                                    {% if user_profile.organization_uuid.street and user_profile.organization_uuid.city and user_profile.organization_uuid.postal_code %}
                                        <br>{{ user_profile.organization_uuid.street }} {{ user_profile.organization_uuid.street_number }},
                                        {% if user_profile.organization_uuid.home_number %}
                                            {{ user_profile.organization_uuid.home_number }},
                                        {% endif %}
                                        {{ user_profile.organization_uuid.city }},
                                        {{ user_profile.organization_uuid.postal_code }},
                                        {{ user_profile.organization_uuid.country }}
                                    {% else %}
                                        <br>{% trans "Please fill in your organization address" %}
                                    {% endif %}
                                {% endif %}
                                </p>
                                {% if user_profile.organization_uuid %}
                                <button type="button" class="btn btn-outline-primary btn-outline-primary-db" data-bs-toggle="modal" data-bs-target="#orgModal">
                                    <i class="fas fa-pencil-alt"></i>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#orgModal">
                                    <i class="fas fa-plus"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                        <!-- Przycisk 'Confirm Data' -->
                        <button type="button" class="btn btn-primary btn-dashboard mt-5" id="confirm-data-btn">Confirm Data</button>
                        <button type="button" class="btn btn-outline-primary-db mt-5" id="edit-data-btn" style="display: none;">Edit</button>

                    
                    </div>
                </div>
                <!-- <div class="col-12 mb-3">
                    <div class="dashboard-element h-100 d-flex flex-column" id="payment-info-section">
                        <h3 class="db-section-title">3. {% trans "Payment Method and Terms:" %}</h3>
                        <hr>
                        <form method="post" action="{% url 'make_order' %}" id="paymentForm">
                            {% csrf_token %}
                            <div class="form-check">
                                {% for item in operators %}
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="operator" id="operator-{{ forloop.counter }}" value="{{ item.0 }}" {% if not forloop.counter0 %} checked{% endif %}>
                                    <label class="form-check-label" for="operator-{{ forloop.counter }}">{{ item.1 }}</label>
                                </div>
                                {% endfor %}
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="operator" value="SKIP">
                                    <label class="form-check-label">{% trans 'skip payment (development only!)' %}</label>
                                </div>
                            </div>
                            <hr>
                            <div class="form-check m-auto">
                                <input class="form-check-input" type="checkbox" id="terms_checkbox" name="terms_checkbox">
                                <label class="form-check-label" for="terms_checkbox">
                                    {% trans "I accept the" %}
                                    <a href="{% url 'store_regulations' %}" target="_blank">{% trans "terms and conditions" %}</a>
                                </label>
                            </div>
                        
                            
                            <button type="button" class="btn btn-primary btn-dashboard w-100 mt-5" id="confirm-payment-btn">Confirm Payment Method</button>
                            <button type="button" class="btn btn-outline-primary-db w-100 mt-5" id="edit-payment-btn" style="display: none;">Edit</button>
                        </form>
                    </div>
                </div> -->
                
            </div>
        </div>
        
        {% if basket.all %}
        <div class="col-xxl-4 mb-3">
            <div class="dashboard-element h-100 d-flex flex-column">
                <h3 class="db-section-title">3. {% trans "Cart summary:" %}</h3>
                <hr>
                <p>{% trans "Products" %}: {{ total_price|floatformat:"2g" }} zł</p>
                {% if delivery_cost %}
                <p>{% trans "Delivery" %}: {{ delivery_cost|floatformat:"2g" }} zł</p>
                {% endif %}
                {% if discount_percentage and discount_amount %}
                <p>{% trans "Discount" %} {{ discount_percentage }}%: {{ discount_amount|floatformat:"2g" }} zł</p>
                {% endif %}
                <p>{% trans "VAT" %} {{ tax_rate }}%: {{ tax|floatformat:"2g" }} zł</p>
                <hr>
                <h3>{% trans "TOTAL" %}: {{ total_price_incl_tax|floatformat:"2g" }} zł</h3>
                <hr>
        
                <!-- Terms and Conditions -->
                <div class="form-check m-auto">
                    <input class="form-check-input" type="checkbox" id="terms_checkbox" name="terms_checkbox">
                    <label class="form-check-label" for="terms_checkbox">
                        {% trans "I accept the" %}
                        <a href="{% url 'store_regulations' %}" target="_blank">{% trans "terms and conditions" %}</a>
                    </label>
                </div>
                <hr>
        
                <!-- Payment Method Selection -->
                <form method="post" action="{% url 'make_order' %}" id="checkout-form">
                    {% csrf_token %}
                    <input type="hidden" name="mode" value="basket">
                    <div class="form-check">
                        {% settings_value 'VIVA_ENABLED' as viva_enabled %}
                        {% settings_value 'PAYU_ENABLED' as payu_enabled %}
                        {% for item in operators %}
                            {% if item.0 == 'PAYU' and payu_enabled or item.0 == 'VIVA' and viva_enabled %}
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="operator" id="operator-{{ forloop.counter }}" value="{{ item.0 }}" {% if not forloop.counter0 %} checked{% endif %}>
                            <label class="form-check-label" for="operator-{{ forloop.counter }}">{{ item.1 }}</label>
                        </div>
                            {% endif %}
                        {% endfor %}
                        {% settings_value 'SKIP_PAYMENT_ENABLED' as skip_payment_enabled %}
                        {% if skip_payment_enabled %}
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="operator" value="SKIP">
                            <label class="form-check-label">{% trans 'skip payment (development only!)' %}</label>
                        </div>
                        {% endif %}
                    </div>
                    <hr>
        
                    <!-- Checkout Button -->
                    <input class="btn btn-primary btn-dashboard w-100" type="submit" value="{% trans 'CHECKOUT' %}" id="checkout-button" disabled>
                </form>
                
            </div>
        </div>
        
        {% endif %}
        {% else %}
                    <p class="text-center my-4">{% trans "Your cart is currently empty." %}</p>
        {% endif %}
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
    // Elements related to cart confirmation
    const confirmCartButton = document.getElementById('confirm-cart-btn');
    const editCartButton = document.getElementById('edit-cart-btn');
    const cartElement = document.querySelector('.table-responsive').closest('.dashboard-element');

    // Elements related to user data confirmation
    const confirmButton = document.getElementById('confirm-data-btn');
    const editButton = document.getElementById('edit-data-btn');
    const personalInfoElement = document.getElementById('personal-info-section').closest('.dashboard-element');
    const pencilButtons = personalInfoElement.querySelectorAll('.btn-outline-primary, .btn-outline-success');
    const wantInvoiceCheckbox = document.getElementById('want_invoice');
    const invoiceDetails = document.getElementById('invoiceDetails');

    // Elements related to payment and terms confirmation
    const checkoutButton = document.getElementById('checkout-button');
    const termsCheckbox = document.getElementById('terms_checkbox');
    const paymentRadioButtons = document.querySelectorAll('input[name="operator"]');

    // Variables to store confirmation states (from session)
    let dataConfirmed = {{ data_confirmed|yesno:"true,false" }};
    let cartConfirmed = {{ cart_confirmed|yesno:"true,false" }};
    let paymentConfirmed = {{ payment_confirmed|yesno:"true,false" }};

    // Initially disable the checkout button
    checkoutButton.disabled = true;

    // Function to validate the user's personal data
    function validateForm() {
        const name = "{{ user_profile.user.first_name }}" && "{{ user_profile.user.last_name }}";
        const phone = "{{ user_profile.contact.phone_number }}";
        const address = "{{ user_profile.address.street }}" && "{{ user_profile.address.city }}" && "{{ user_profile.address.postal_code }}";
        const organization = wantInvoiceCheckbox.checked 
            ? "{{ user_profile.organization_uuid.name_1 }}" && "{{ user_profile.organization_uuid.tax_id }}" && "{{ user_profile.organization_uuid.street }}" && "{{ user_profile.organization_uuid.city }}" && "{{ user_profile.organization_uuid.postal_code }}" 
            : true;

        return name && phone && address && organization;
    }

    // Function to validate the payment section
    function validatePaymentSection() {
        return termsCheckbox.checked && Array.from(paymentRadioButtons).some(radio => radio.checked);
    }

    // Function to update the state of the checkout button
    function updateCheckoutButtonState() {
        const termsAccepted = termsCheckbox.checked;
        const paymentSelected = Array.from(paymentRadioButtons).some(radio => radio.checked);

        if (dataConfirmed && cartConfirmed && termsAccepted && paymentSelected) {
            checkoutButton.disabled = false;
        } else {
            checkoutButton.disabled = true;
        }
    }

    // Function to lock the cart section
    function lockCartSection() {
        const cartButtons = cartElement.querySelectorAll('a.btn');
        cartButtons.forEach(button => {
            button.style.pointerEvents = 'none';
            button.style.opacity = '0.5';
        });
    }

    // Function to unlock the cart section
    function unlockCartSection() {
        const cartButtons = cartElement.querySelectorAll('a.btn');
        cartButtons.forEach(button => {
            button.style.pointerEvents = 'auto';
            button.style.opacity = '1';
        });
    }

    // Function to lock the user data section
    function lockUserDataSection() {
        const inputs = personalInfoElement.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.setAttribute('disabled', 'true');
        });
        pencilButtons.forEach(button => {
            button.style.display = 'none';
        });
        wantInvoiceCheckbox.disabled = true;
    }

    // Function to unlock the user data section
    function unlockUserDataSection() {
        const inputs = personalInfoElement.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.removeAttribute('disabled');
        });
        pencilButtons.forEach(button => {
            button.style.display = 'inline-block';
        });
        wantInvoiceCheckbox.disabled = false;
    }

    // Function to lock the payment section
    function lockPaymentSection() {
        paymentRadioButtons.forEach(radio => {
            radio.disabled = true;
        });
        termsCheckbox.disabled = true;
    }

    // Function to unlock the payment section
    function unlockPaymentSection() {
        paymentRadioButtons.forEach(radio => {
            radio.disabled = false;
        });
        termsCheckbox.disabled = false;
    }

    // Restore states after page load
    if (cartConfirmed) {
        lockCartSection();
        cartElement.style.border = '2px solid green';
        confirmCartButton.style.display = 'none';
        editCartButton.style.display = 'inline-block';
    }

    if (dataConfirmed) {
        lockUserDataSection();
        personalInfoElement.style.border = '2px solid green';
        confirmButton.style.display = 'none';
        editButton.style.display = 'inline-block';
    }

    // Initial update of the checkout button state after page load
    updateCheckoutButtonState();

    // Handling the "Confirm Cart" button
    confirmCartButton.addEventListener('click', function() {
        cartElement.style.border = '2px solid green';
        confirmCartButton.style.display = 'none';
        editCartButton.style.display = 'inline-block';

        // Lock cart section
        lockCartSection();

        // Update cart confirmation status
        cartConfirmed = true;
        updateCheckoutButtonState();

        // Save state to the server
        fetch("{% url 'save_cart_confirmation' %}", {
            method: "POST",
            headers: {
                "X-CSRFToken": "{{ csrf_token }}",
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                cart_confirmed: cartConfirmed
            })
        }).catch(error => {
            console.error("Error saving cart confirmation status:", error);
        });
    });

    // Handling the "Edit Cart" button
    editCartButton.addEventListener('click', function() {
        cartElement.style.border = '1px solid #ced4da';
        editCartButton.style.display = 'none';
        confirmCartButton.style.display = 'inline-block';

        // Unlock cart section
        unlockCartSection();

        // Update cart confirmation status
        cartConfirmed = false;
        updateCheckoutButtonState();

        // Save state to the server
        fetch("{% url 'save_cart_confirmation' %}", {
            method: "POST",
            headers: {
                "X-CSRFToken": "{{ csrf_token }}",
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                cart_confirmed: cartConfirmed
            })
        }).catch(error => {
            console.error("Error saving cart confirmation status:", error);
        });
    });

    // Handling the "Confirm Data" button in "Your Details" section
    confirmButton.addEventListener('click', function() {
        if (validateForm()) {
            personalInfoElement.style.border = '2px solid green';
            confirmButton.style.display = 'none';
            editButton.style.display = 'inline-block';

            // Lock user data section
            lockUserDataSection();

            // Update data confirmation status
            dataConfirmed = true;
            updateCheckoutButtonState();

            // Save state to the server
            fetch("{% url 'save_data_confirmation' %}", {
                method: "POST",
                headers: {
                    "X-CSRFToken": "{{ csrf_token }}",
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    data_confirmed: dataConfirmed
                })
            }).catch(error => {
                console.error("Error saving confirmation status:", error);
            });
        } else {
            alert('Please fill in all required fields correctly before confirming your data.');
        }
    });

    // Handling the "Edit" button in "Your Details" section
    editButton.addEventListener('click', function() {
        personalInfoElement.style.border = '1px solid #ced4da';
        editButton.style.display = 'none';
        confirmButton.style.display = 'inline-block';

        // Unlock user data section
        unlockUserDataSection();

        // Update data confirmation status
        dataConfirmed = false;
        updateCheckoutButtonState();

        // Save state to the server
        fetch("{% url 'save_data_confirmation' %}", {
            method: "POST",
            headers: {
                "X-CSRFToken": "{{ csrf_token }}",
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                data_confirmed: dataConfirmed
            })
        }).catch(error => {
            console.error("Error saving confirmation status:", error);
        });
    });

    // Handling the "Checkout" button
    checkoutButton.addEventListener('click', function(event) {
        if (!validatePaymentSection()) {
            event.preventDefault();
            alert('Please accept the terms and conditions and select a payment method.');
        }
    });

    // Event listeners for terms and payment method
    termsCheckbox.addEventListener('change', updateCheckoutButtonState);
    paymentRadioButtons.forEach(radio => radio.addEventListener('change', updateCheckoutButtonState));

    // Handling changes in the "want_invoice" checkbox and updating the invoice section
    wantInvoiceCheckbox.addEventListener('change', function() {
        invoiceDetails.style.display = this.checked ? 'block' : 'none';
        updateConfirmButtonState();
    });

    // Initialize the invoice details section display
    if (wantInvoiceCheckbox.checked) {
        invoiceDetails.style.display = 'block';
    } else {
        invoiceDetails.style.display = 'none';
    }

    // Function to update the "Confirm Data" button state
    function updateConfirmButtonState() {
        if (validateForm()) {
            confirmButton.disabled = false;
        } else {
            confirmButton.disabled = true;
        }
    }
});

</script>

{% endblock %}
