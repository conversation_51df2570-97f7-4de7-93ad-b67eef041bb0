# Generated by Django 4.1.7 on 2024-05-06 20:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('price', models.FloatField(default=0)),
                ('operator', models.CharField(choices=[('PAYU', 'PayU GPO'), ('VIVA', 'Viva Payments')], default='PAYU', max_length=4)),
                ('order_id', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('payment_method', models.CharField(max_length=20, null=True)),
                ('payment_id', models.Char<PERSON><PERSON>(max_length=50, null=True)),
                ('card_token', models.Char<PERSON>ield(max_length=50, null=True)),
                ('status', models.Char<PERSON>ield(max_length=30, null=True)),
                ('update_time', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('description', models.CharField(max_length=250)),
                ('value', models.PositiveIntegerField()),
                ('price', models.FloatField()),
                ('type', models.CharField(choices=[('PAC', 'PACKAGE'), ('SUB', 'MONTH SUBSCRIPTION'), ('YSU', 'YEAR SUBSCRIPTION')], default='PAC', max_length=3)),
                ('active', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='RecurringPayment',
            fields=[
                ('user', models.OneToOneField(editable=False, on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('payment_month_day', models.PositiveIntegerField()),
                ('next_payment_date', models.DateTimeField(null=True)),
                ('next_payment_amount', models.FloatField()),
                ('last_payment_date', models.DateTimeField(null=True)),
                ('reference_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.order')),
            ],
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.order')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.product')),
            ],
        ),
        migrations.CreateModel(
            name='Basket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user', 'product', 'quantity'],
                'unique_together': {('user', 'product')},
            },
        ),
    ]
