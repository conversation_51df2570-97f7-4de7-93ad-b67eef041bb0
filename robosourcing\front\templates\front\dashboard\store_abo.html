{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load divide %}

{% block title %}{{ block.super }} - {% trans "Subscriptons" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{%url 'store'%}">{% trans "Store" %}</a></li>
          <li class="breadcrumb-item site-name active">
            {% if period == 'year' %}
            {% trans "Annual subscription" %}
            {% else %}
            {% trans "Monthly subscription" %}
            {% endif %}
        </li>
        </ol>
    </nav>

    <div class="dashboard-element">


    <h3 class="db-section-title">{% blocktrans %}Buy a subscription and we will replenish your pool of credits
    every month.{% endblocktrans %}</h3>
    <hr>

    <div class="row">

        {% for product in products %}
        <div class="col-md-6 col-lg-4 col-xxl-3 mt-3">
            <!--<form action="{% url 'make_order' %}" method="post">-->
            <form action="{% url 'add_to_basket' %}" method="post">
            {% csrf_token %}
            <input type="hidden" name="mode" value="single">
            <input type="hidden" name="pricelist_item_id" value="{{ product.id }}">
            <div class="dashboard-card shop-item">
                <div class="card-header">
                    <h4>{{ product.name }}</h4>
                    <h5>{{ product.description }}</h5>
                </div>
                <div class="card-body">
                    <h6 class="text-secondary">{% trans 'Credits' %}: {{ product.value|floatformat:"0g" }} ({% trans '/mo' %})</h6>
                    {% if period == 'year' %}
                    <h6 class="text-secondary">{% trans 'Cost' %}: {{ product.price|divide:12|floatformat:"2g" }} zł {% trans 'netto per month' %}</h6>
                    <h6 class="text-secondary">{% trans 'Annual cost' %}: {{ product.price|floatformat:"2g" }} zł {% trans 'netto' %}</h6>
                    {% else %}
                    <h6 class="text-secondary">{% trans 'Cost' %}: {{ product.price|floatformat:"2g" }} zł {% trans 'netto per month' %}</h6>
                    {% endif %}
                    <div class="text-end"><input type="submit" class="btn btn-primary" value="{% trans 'Buy' %}"></div>
                </div>
            </div>
            </form>
        </div>
        {% endfor %}

    </div>
</div>

</div>

{% endblock %}