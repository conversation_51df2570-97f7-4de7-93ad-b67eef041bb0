{% extends "account/base.html" %}
{% load account socialaccount %}
{% load i18n %}
{% load crispy_forms_filters %}

{% block head_title %}{% trans "Sign In" %}{% endblock %}

{% block extra_content_1 %}

  <div class="text-center">
    <h1>{% trans "Sign In" %}</h1>

    {% get_providers as socialaccount_providers %}

    {% if socialaccount_providers %}
    <p>{% blocktrans with site.name as site_name %}Please sign in with one
    of your existing third party accounts.<br>Or, <a href="{{ signup_url }}">sign up</a>
    for a {{ site_name }} account and sign in below:{% endblocktrans %}</p>

    <div class="socialaccount_ballot">

      <div class="socialaccount_providers">
        {% include "socialaccount/snippets/provider_list.html" with process="login" %}
      </div>

      <div class="login-or border-top border-bottom my-3">{% trans 'OR' %}</div>

    </div>

    {% include "socialaccount/snippets/login_extra.html" %}

    {% else %}
    <p>{% blocktrans %}If you have not created an account yet, then please
    <a href="{{ signup_url }}">sign up</a> first.{% endblocktrans %}</p>
    {% endif %}
  </div>
  <div class="row">
    <div class="col-md-6 offset-md-3">
      <form class="login" method="POST" action="{% url 'account_login' %}">
          {% csrf_token %}
          {{ form|crispy }}
          {% if redirect_field_value %}
          <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
          {% endif %}
          <div class="d-grid">
            <button class="primaryAction mt-3 btn btn-dark" type="submit">{% trans "Sign In" %}</button><br>
            <a class="button secondaryAction text-dark text-center" href="{% url 'account_reset_password' %}">{% trans "Forgot Password?" %}</a>
          </div>
        </form>
    </div>
  </div>
{% endblock %}
