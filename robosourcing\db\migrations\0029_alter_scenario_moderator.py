# Generated by Django 4.2.13 on 2024-11-24 15:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('db', '0028_remove_file_local_name_remove_file_local_path'),
    ]

    operations = [
        migrations.AlterField(
            model_name='scenario',
            name='moderator',
            field=models.ForeignKey(blank=True, limit_choices_to={'groups__name': 'Moderator'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_scenarios', to=settings.AUTH_USER_MODEL, verbose_name='Assigned moderator'),
        ),
    ]
