:root {
--bs-link-color-rgb: 99,99,99; /* brand-gray modified */
--bs-link-hover-color-rgb: 132,132,132; /* brand-gray modified */
--bs-dashboard-footer-rgb: rgb(20,20,20); /* brand-gray modified */

--tile-border-color: #555;
--tile-background-color: #343a40; /* dark background */
--tile-hover-background-color: #495057; /* slightly lighter on hover/select */
--tile-hover-border-color: #6c757d; /* lighter gray border */
--card-text-color: #f8f9fa; /* light text color */

--default-color: #ffffff;
--accent-color: #0077E4;
--heading-color: #ffffff;
--contrast-color: #ffffff;
}

.about-2 .content {
background-color: var(--gray-800);
}

.skill .content {
    background-color: var(--gray-800);
    }

.robotyzacja {
    background-color: var(--gray-800);
}

.swiper-slide .step {
background-color: var(--gray-800);
}

.navbar-home {
background-color: var(--gray-800);
}

.robotyzacja-header {
background-color: var(--bs-dark);
}

.card-categ {
background-color: var(--gray-800);
}

/* Styl dla aktywnego elementu oraz przy hover */
.robotyzacja .service-item.link.active,
.robotyzacja .service-item.link:hover {
    background-color: var(--bs-body-bg); 
    color: #fff; 
}

.service-item p {
    color: #fff;
}

.service-item i {
    color: #fff;
}

.service-item h3 {
    color: #fff !important;
}

.swiper-slide h3 {
    color: #fff !important;
}


.btn-primary {
background-color: var(--brand-blue) !important;
border-color: var(--brand-blue) !important;
}

.btn-outline-primary {
border-color: var(--brand-blue) !important;
color: var(--brand-blue) !important;
}

.btn-outline-primary:hover {
background-color: var(--brand-blue) !important;
color: #f0f0f0 !important;
}

.swiper-pagination-bullet {
background-color: rgb(66,66,66);
}

.swiper-pagination-bullet-active {
background-color: rgb(99,99,99);
}

/* PRICE LIST */
.accordion.pricelist-regulations {
	--bs-accordion-btn-color: white;
	--bs-accordion-active-color: var(--brand-purple);
}

.pricingTable {
background: var(--bs-dark);
box-shadow-color: var(--bs-light);
}

/* CUSTOM SWITCH */
.custom_switch .form-check-input, .custom_switch .form-check-input:focus, .custom_switch .form-check-input:checked {
    background-color: transparent;
    border-color: var(--brand-yellow);
    box-shadow: 0 0 0 0.2rem rgba(255, 161, 0, 0.25);
}

.custom_switch .form-check-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255, 255, 255, .9%29'/%3e%3c/svg%3e");
}

/* PAGINATION */
.page-link {
color: var(--brand-blue) !important;
}

.page-link:hover {
color: var(--brand-blue) !important;
}

.page-item.active .page-link {
border-color: var(--brand-blue) !important;
background-color: var(--brand-blue) !important;
color: white !important;
}

/* PRIMARY TEXT */
.text-primary {
color: var(--brand-blue) !important;
}

/* PRIMARY BACKGROUND */
.bg-primary {
background-color: var(--brand-blue) !important;
}

/* FORM-CONTROL GLOW */
.form-control:focus, .page-link:focus {
border-color: var(--brand-blue);
box-shadow: 0 0 0 0.2rem rgba(255, 161, 0, 0.25);
}

/* DROPDOWN PRIMARY */
.dropdown-item.active, .dropdown-item:active {
background-color: var(--brand-blue) !important;
}



/* SCENARIOS */
/* .scenario-card {
border-color: var(--brand-blue);
}

.scenario-card:hover {
border-color: var(--brand-blue);
} */

/* HOME PAGE */
.index-robotyzacja-border {
border-color: var(--brand-blue) !important;
}

.index-botie-border {
border-color: var(--brand-blue) !important;
}

.index-botieweb-border {
border-color: var(--brand-blue) !important;
}

.border-lead {
border-color: var(--brand-publuerple) !important;
}

.steps-container .number {
color: var(--brand-blue);
}

/* DASHBOARD */
.header-dashboard div.solid {
    background-color: var(--gray-0);
    height: 70px;
}
.header-dashboard div.gradient {
    background-image: linear-gradient(to top, rgba(22,27,33,0), rgba(22,27,33,1));
}
.footer-dashboard div.gradient {
    background-image: linear-gradient(to bottom, rgba(22,27,33,0), rgba(22,27,33,1));
}
.footer-dashboard div.solid {
    background-color: var(--gray-0);
    height: 30px;
}

.main-dashboard-content {
background-color: var(--gray-0);
}

/* NAV */
.navbar-dashboard {
    background-color: var(--gray-900);
}

/* SIDEBAR */
#sidebar-wrapper {
background-color: var(--gray-900);
}

.logo-sidebar {
    background-image: url("/static/images/brandbook/logo_white_icon_text_100.png");
}

.sidebar-menu {
background-color: var(--gray-900)!important;
}
.menu-chosen-bg {
    background-color: var(--primary-700);
}

/* ELEMENT */
.dashboard-element {
    background-color: var(--gray-900);
    box-shadow: 0 1px 5px 0 var(--accent-color);
    
}

/* BUTTONS */
.btn-dashboard {
background-color: var(--primary-700) !important;
border-color: var(--primary-700) !important;
}

.btn-dashboard:hover {
background-color: var(--primary-700) !important;
color: #f0f0f0 !important;
}

.btn-outline-primary-db {
    color: var(--primary-700) !important;
    border-color: var(--primary-700) !important;;
}

.page-link-dashboard {
color: var(--primary-700) !important;
}

.page-link-dashboard:hover {
color: var(--primary-700) !important;
}

.page-item-dashboard.active .page-link {
border-color: var(--primary-700) !important;
background-color: var(--primary-700) !important;
color: white !important;
}

.site-name a {
    text-decoration: none;
    color: var(--primary-700);
}

.sort-link {
    color: var(--brand-white);
}

/* CUSTOM TABLES */
.my-custom-table {
    color: #fff;
    --bs-table-bg: var(--gray-900) !important;
}

.my-custom-table-level-2 {
    color: #fff;
    --bs-table-bg: var(--gray-800) !important;
}

/* HOMEPAGE - GUIDE CARDS */
.guide-card {
    background-color: var(--brand-semidarkgray);
}

/* HOMEPAGE - SVG ICONS */
.invert-svg {
    /* do nothing for icons prepared for a dark view */
}

.light-background {
    background-color: var(--gray-800);
    }

#category-dropdown {
    background-color: var(--gray-800);
}

.back-link {
    color: var(--brand-white);
}

.sidebar-categories .category-link {
    text-decoration: none;
    color: var(--brand-white);
}

.botbook-breadcrumb-item {
color: var(--brand-white)!important;
}

.sidebar-categories { 
background-color: var(--gray-800);
}