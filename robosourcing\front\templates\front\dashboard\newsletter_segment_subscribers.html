{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Segment Subscribers" %} - {{ segment.name }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_segments' %}">{% trans "Segments" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{{ segment.name }}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fa fa-users me-2"></i>{% trans "Subscribers" %}</h2>
                    <p class="text-muted mb-0">{{ segment.name }} - {{ subscriber_count }} {% trans "subscribers" %}</p>
                </div>
                <a href="{% url 'profiles_newsletter_segments' %}" class="newsletter-btn-secondary btn">
                    <i class="fa fa-arrow-left"></i> {% trans "Back to Segments" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <div class="row">
        <div class="col-12">
            <div class="newsletter-card">
                <div class="newsletter-card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fa fa-layer-group me-2"></i>{{ segment.name }}
                        <span class="newsletter-badge newsletter-badge-primary ms-2">{{ subscriber_count }}</span>
                    </h5>
                    <div>
                        <a href="{% url 'profiles_newsletter_segment_edit' segment.id %}" class="newsletter-btn-primary btn btn-sm">
                            <i class="fa fa-edit"></i> {% trans "Edit Segment" %}
                        </a>
                    </div>
                </div>
                <div class="newsletter-card-body">
                    {% if segment.description %}
                        <p class="text-muted mb-3">{{ segment.description }}</p>
                    {% endif %}

                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="newsletter-stat-item">
                                <div class="newsletter-stat-label">{% trans "Confirmed Only" %}</div>
                                <div class="newsletter-stat-number">
                                    {% if segment.confirmed_only %}
                                        <i class="fa fa-check text-success"></i> {% trans "Yes" %}
                                    {% else %}
                                        <i class="fa fa-times text-danger"></i> {% trans "No" %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="newsletter-stat-item">
                                <div class="newsletter-stat-label">{% trans "Date From" %}</div>
                                <div class="newsletter-stat-number">
                                    {% if segment.registration_date_from %}
                                        {{ segment.registration_date_from|date:"d.m.Y" }}
                                    {% else %}
                                        {% trans "No limit" %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="newsletter-stat-item">
                                <div class="newsletter-stat-label">{% trans "Date To" %}</div>
                                <div class="newsletter-stat-number">
                                    {% if segment.registration_date_to %}
                                        {{ segment.registration_date_to|date:"d.m.Y" }}
                                    {% else %}
                                        {% trans "No limit" %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="newsletter-stat-item">
                                <div class="newsletter-stat-label">{% trans "Status" %}</div>
                                <div class="newsletter-stat-number">
                                    {% if segment.is_active %}
                                        <span class="newsletter-badge newsletter-badge-success">{% trans "Active" %}</span>
                                    {% else %}
                                        <span class="newsletter-badge newsletter-badge-secondary">{% trans "Inactive" %}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if subscribers %}
                        <div class="table-responsive">
                            <table class="newsletter-table table">
                                <thead>
                                    <tr>
                                        <th>{% trans "Email" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Subscribed" %}</th>
                                        <th>{% trans "Confirmed" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subscriber in subscribers %}
                                    <tr>
                                        <td><strong>{{ subscriber.email }}</strong></td>
                                        <td>
                                            {% if subscriber.is_confirmed %}
                                                <span class="newsletter-badge newsletter-badge-success">{% trans "Confirmed" %}</span>
                                            {% else %}
                                                <span class="newsletter-badge newsletter-badge-warning">{% trans "Pending" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ subscriber.created_at|date:"d.m.Y H:i" }}</td>
                                        <td>
                                            {% if subscriber.confirmed_at %}
                                                {{ subscriber.confirmed_at|date:"d.m.Y H:i" }}
                                            {% else %}
                                                <span class="text-muted">{% trans "Not confirmed" %}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fa fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No subscribers in this segment" %}</h5>
                            <p class="text-muted">{% trans "This segment doesn't match any current subscribers." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
