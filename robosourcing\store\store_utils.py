# import json
import json
from datetime import datetime
import pytz
from dateutil.relativedelta import relativedelta

from django.db.models import Q
# from django.db import transaction
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist

from db.models import UserSubscription, Credit, CreditEvent, PartnerCommission, AffiliateLink
from store.models import SubscriptionEvent, RecurringPayment, Order, SubsequentOrder, OrderItem, Product
from profiles.models import UserProfile
from store.payu_connector import PayUConnector
from store.viva_connector import VivaConnector
from db.mailer import Mailer


class SingletonMeta(type):
    """
    The Singleton metaclass.
    """

    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]


class StoreUtils(metaclass=SingletonMeta):
    """
    A class that contains methods for handling credit top-up subscriptions and related orders and recurring payments
    """

    mailer = Mailer()

    @staticmethod
    def insert_order_payment_id(request, payment_id: str, order_id: str):

        order_obj = Order.objects.filter(user=request.user, operator='VIVA', order_id=order_id, payment_id=None)\
            .order_by('-create_time').get()
        order_obj.payment_id = payment_id
        order_obj.save()

    @staticmethod
    def update_user_subscription(user, renew_date=None, new_value=None, last_renew_date: str | None = '',
                                 payment_method=None, payment_details=None):
        # store user subscription record
        actual_subscription = UserSubscription.objects.filter(user=user)
        if actual_subscription:
            # update user subscription record
            obj = actual_subscription.get()
            if renew_date:
                obj.renew_date = renew_date
            if new_value:
                obj.value = new_value
            if last_renew_date or last_renew_date is None:
                obj.last_renew_date = last_renew_date
            if payment_method:
                obj.payment_method = payment_method
            if payment_details:
                obj.payment_details = payment_details
            obj.save()
        else:
            # create user subscription record
            query_dict: dict = {'user': user}
            if renew_date:
                query_dict['renew_date'] = renew_date
            if new_value:
                query_dict['value'] = new_value
            if last_renew_date or last_renew_date is None:
                query_dict['last_renew_date'] = last_renew_date
            if payment_method:
                query_dict['payment_method'] = payment_method
            if payment_details:
                query_dict['payment_details'] = payment_details
            UserSubscription.objects.create(**query_dict)

    def update_user_credit(self, user, event_type, package_credits=None, subscription_credits=None):
        # store user subscription record
        actual_credit = Credit.objects.filter(user=user)
        if actual_credit:
            # update user credit record
            obj = actual_credit.get()
            if package_credits:
                obj.package_credits += package_credits
            if subscription_credits:
                obj.subscription_credits += subscription_credits
            obj.save()
        else:
            # create user subscription record
            query_dict: dict = {'user': user}
            if package_credits:
                query_dict['package_credits'] = package_credits
            if subscription_credits:
                query_dict['subscription_credits'] = subscription_credits
            obj = Credit.objects.create(**query_dict)
        # store credit event
        self.store_credit_event(user, event_type, obj.package_credits, obj.subscription_credits,
                                obj.l_package_credits, obj.l_subscription_credits,
                                dpc=package_credits, dsc=subscription_credits)

    @staticmethod
    def store_credit_event(user, event_type, pc, sc, lpc, lsc, dpc=None, dsc=None, dlpc=None, dlsc=None, robot=None):
        query_dict: dict = {
            'user': user,
            'event_type': event_type,
            'package_credits': pc,
            'subscription_credits': sc,
            'l_package_credits': lpc,
            'l_subscription_credits': lsc,
        }
        if dpc:
            query_dict['d_package_credits'] = dpc
        if dsc:
            query_dict['d_subscription_credits'] = dsc
        if dlpc:
            query_dict['d_l_package_credits'] = dlpc
        if dlsc:
            query_dict['d_l_subscription_credits'] = dlsc
        if robot:
            query_dict['rid'] = robot
        CreditEvent.objects.create(**query_dict)

    def handle_subscription_renew(self, user, order):
        # get new amount and next date
        item_obj = OrderItem.objects.get(order=order)
        product_obj = Product.objects.get(pk=item_obj.product_id)
        # renew_date = datetime.strptime(order.update_time.strftime('%Y-%m-%d'), '%Y-%m-%d')
        renew_date = order.update_time
        new_amount = product_obj.price
        new_value = product_obj.value
        next_payment = None
        annual_payment = False

        if settings.SCHEDULER_TEST_MODE:
            if settings.SHORT_PERIOD == 'hour':
                short_delta = relativedelta(hours=1)
            elif settings.SHORT_PERIOD == 'day':
                short_delta = relativedelta(days=1)
            if settings.LONG_PERIOD == 'day':
                long_delta = relativedelta(days=1)
            elif settings.LONG_PERIOD == 'week':
                long_delta = relativedelta(weeks=1)
        else:
            short_delta = relativedelta(months=1)
            long_delta = relativedelta(years=1)

        if product_obj.type == 'SUB':
            next_payment = renew_date + short_delta
        elif product_obj.type == 'YSU':
            next_payment = renew_date + long_delta
            annual_payment = True

        # update recurring payment record - DIAGRAM RP1
        self.update_recurring_payment(user, reference_order=order, next_date=next_payment, new_amount=new_amount,
                                      annual_payment=annual_payment)

        # update user subscription record - DIAGRAM US1
        self.update_user_subscription(user, renew_date=renew_date, new_value=new_value, last_renew_date=None,
                                      payment_method=order.payment_method, payment_details=order.payment_details)

        # update user credit record - DIAGRAM UC2
        self.update_user_credit(user, 'BUY', subscription_credits=new_value)

    def handle_subscription_change(self, user, product, immediate_change):

        # get current subscription type
        _, current_type, _, current_credits, start, _ = self.get_subscription_details(user)

        # get new amount
        # item_obj = OrderItem.objects.get(order=order)
        # product_obj = Product.objects.get(pk=item_obj.product_id)
        new_amount = product.price
        new_value = product.value

        # if change from monthly to annual
        if current_type == 'monthly' and product.type == 'YSU':

            # get user time zone
            if user.userprofile.address and user.userprofile.address.timezone:
                user_tz = pytz.timezone(user.userprofile.address.timezone)
            else:
                user_tz = pytz.timezone(settings.TIME_ZONE)

            # parse start date
            # start = datetime.strptime(start.strftime('%Y-%m-%d'), "%Y-%m-%d")
            start = timezone.localtime(start, timezone=user_tz).date()
            start_month = start.month
            start_day = start.day
            # get and parse current date
            # now = datetime.now()
            now = timezone.localtime(timezone.now(), timezone=user_tz).date()
            current_month = now.month
            current_day = now.day
            # calculate time diff
            diff = relativedelta(now, start)
            # calculate next payment date
            next_payment = start
            next_payment += relativedelta(years=diff.years + 1)
            if current_month == start_month and current_day == start_day:
                next_payment += relativedelta(years=-1)

            # update recurring payment record - DIAGRAM RP2
            self.update_recurring_payment(user, next_date=next_payment, new_amount=new_amount, annual_payment=True)

        else:

            # update recurring payment record - DIAGRAM RP3 / RP4
            self.update_recurring_payment(user, new_amount=new_amount)

        # update user subscription record - DIAGRAM US2 / US3 / US4
        self.update_user_subscription(user, new_value=new_value)

        # for immediate change
        if immediate_change:

            # calculate difference in credits
            diff_credits = new_value - current_credits

            # update user credit record - DIAGRAM UC3 / UC4
            self.update_user_credit(user, 'CHG', subscription_credits=diff_credits)

    def handle_subscription_cancel(self, user):

        # update recurring payment record - DIAGRAM RP5
        obj = RecurringPayment.objects.filter(user=user).get()
        self.update_recurring_payment(user, last_date=obj.next_payment_date)

        # update user subscription record - DIAGRAM US5
        self.update_user_subscription(user, last_renew_date=obj.next_payment_date)

    @staticmethod
    def is_subscription_order(order):
        for item in OrderItem.objects.filter(order=order):
            if item.product.type in ('SUB', 'YSU'):
                return True
        return False

    def get_ordered_credits(self, order) -> int:
        if self.is_subscription_order(order):
            item = OrderItem.objects.get(order=order)
            return item.product.value
        else:
            total = 0
            for item in OrderItem.objects.filter(order=order):
                total += item.product.value * item.quantity
            return total

    @staticmethod
    def calculate_tax(price, rate):
        tax = round(price * rate / 100, 2)
        price_incl_tax = price + tax
        return tax, price_incl_tax

    @staticmethod
    def format_order_description(basket_items, is_subscription=False) -> tuple:
        if is_subscription:
            desc = _('Subscription order') + f': {basket_items[0]["name"]} (#{basket_items[0]["id"]})'
            vdesc = _('You pay for the subscription') + f': {basket_items[0]["name"]} (#{basket_items[0]["id"]})'
        else:
            if len(basket_items) > 1:
                desc = _('Multiple-product order')
                vdesc = _('You pay for the products in your basket')
            else:
                desc = _('Single-product order') + f': {basket_items[0]["name"]} (#{basket_items[0]["id"]})'
                vdesc = _('You pay for the product') + f': {basket_items[0]["name"]} (#{basket_items[0]["id"]})'
        return desc, vdesc

    @staticmethod
    def format_payment_description(is_supplement=False) -> str:
        desc = _('Recurring subscription fee')
        if is_supplement:
            desc = _('One-time subscription fee')
        return desc

    def prepare_order_data(self, basket_items, is_subscription=False) -> tuple:
        num = 0
        total_price = 0
        products = []
        for item in basket_items:
            num += item['quantity']
            total_price += item['price'] * item['quantity']
            products.append({
                "name": item['name'],
                "unitPrice": int(item['price'] * 100),
                "quantity": item['quantity'],
                "virtual": True
            })
        tax_rate = 23
        tax, total_price_incl_tax = self.calculate_tax(total_price, tax_rate)
        desc, vdesc = self.format_order_description(basket_items, is_subscription=is_subscription)
        return products, num, total_price, tax, total_price_incl_tax, str(desc), str(vdesc)

    def prepare_payment_data(self, amount, is_supplement=False) -> tuple:
        total_price = amount
        products = []
        tax_rate = 23
        tax, total_price_incl_tax = self.calculate_tax(total_price, tax_rate)
        desc = self.format_payment_description(is_supplement=is_supplement)
        return products, total_price, tax, total_price_incl_tax, str(desc)

    def handle_order_before_payment(self, req, is_subscription, operator, order_id, basket_items, total_price, tax,
                                    total_price_incl_tax, desc):

        # store order in db
        if not operator:
            order = Order.objects.create(order_id=order_id, user=req.user, price=total_price, tax=tax,
                                         price_incl_tax=total_price_incl_tax, description=desc, status='COMPLETED')
        else:
            order = Order.objects.create(order_id=order_id, user=req.user, price=total_price, tax=tax,
                                         price_incl_tax=total_price_incl_tax, description=desc, operator=operator)

        if is_subscription:

            # store item in db
            item = basket_items[0]
            OrderItem.objects.create(order=order, product=Product.objects.filter(active=True).get(pk=item['id']),
                                     quantity=item['quantity'])

            # store subscription event in db (subscribe) - DIAGRAM SE5
            self.store_subscription_event(req, 'SUB', 'PENDING',
                                          product=Product.objects.filter(active=True).get(pk=item['id']),
                                          amount=total_price, payment_id=order_id)

        else:

            # store all items in db
            for item in basket_items:
                OrderItem.objects.create(order=order, product=Product.objects.filter(active=True).get(pk=item['id']),
                                         quantity=item['quantity'])

        return order

    def handle_order_after_payment(self, user, order, status):

        if self.is_subscription_order(order):

            # check subscription event type
            events_query = self.read_subscription_event(user, payment_id=order.order_id)
            # go further in case that a subscription was bought
            if events_query:
                # check subscription event type
                event_obj = events_query.get()
                if event_obj.event_type == 'SUB':
                    if status in ('COMPLETED', 'PAID'):
                        # handle subscription renew
                        self.handle_subscription_renew(user, order)
                        # update subscription event status
                        event_obj = self.update_subscription_event_status(event_obj.id, 'COMPLETED')
                        # send email notification
                        self.mailer.subscription_event_completed(user, event_obj)
                    else:
                        # update subscription event status
                        event_obj = self.update_subscription_event_status(event_obj.id, 'CANCELED')
                        # send email notification
                        self.mailer.subscription_event_canceled(user, event_obj)
        else:

            if status in ('COMPLETED', 'PAID'):
                # update user credit record - DIAGRAM UC1
                self.update_user_credit(user, 'BUY', package_credits=self.get_ordered_credits(order))
                # send email notification
                self.mailer.after_purchase_email(user, order)

        # After handling order, if status is COMPLETED or PAID, calculate partner commission
        if status in ('COMPLETED', 'PAID'):
            # Add partner commission logic here
            user_profile = user.userprofile

            if user_profile.referred_by and user_profile.referred_by.userprofile.has_role('Partner'):
                partner = user_profile.referred_by
                try:
                    affiliate_link = AffiliateLink.objects.get(user=partner)
                    commission_rate = affiliate_link.get_current_commission_rate()
                    # Oblicz kwotę prowizji na podstawie ceny zamówienia bez podatku (kwoty netto)
                    commission_amount = order.price * (commission_rate / 100)

                    related_event = CreditEvent.objects.filter(user=user).order_by('-event_time').first()

                    # Zapisz prowizję w modelu PartnerCommission
                    PartnerCommission.objects.create(
                        partner=partner,
                        amount=commission_amount,
                        commission_rate=commission_rate,
                        related_order=order,
                        related_event=related_event,
                        created_at=timezone.now()
                    )
                except AffiliateLink.DoesNotExist:
                    pass

    @staticmethod
    def handle_recurring_payment(user, reference_order, amount, is_supplement=False):

        # initialize connector
        conn = None
        if reference_order.operator == 'PAYU':
            conn = PayUConnector()
        elif reference_order.operator == 'VIVA':
            conn = VivaConnector()

        if not conn:
            return None, _('Recurring payment has failed!')

        # log in
        conn.login()

        return conn.make_recurring_payment(user, reference_order, amount, is_supplement=is_supplement)

    @staticmethod
    def read_payment_method_details(operator, obj) -> str:
        det: str = ''
        if operator == 'PAYU':
            if 'card' in obj.keys():
                obj = obj['card']
                if 'cardData' in obj.keys():
                    det = obj['cardData'].get('cardScheme', '') + ' ' + obj['cardData'].get('cardNumberMasked', '')
            elif 'bankAccount' in obj.keys():
                obj = obj['bankAccount']
                det = obj.get('name', '') + ' ' + obj.get('number', '')
            elif 'blik' in obj.keys():
                obj = obj['blik']
                det = obj.get('txRef', '') + ' ' + obj.get('extTxRef', '')
        elif operator == 'VIVA':
            if 'bankId' in obj.keys():
                det += obj.get('bankId', '') + ' '
            if 'fullName' in obj.keys():
                det = obj.get('fullName', '') + ' '
            if 'cardNumber' in obj.keys():
                det = obj.get('cardNumber', '') + ' '
        return det.strip()

    def update_orders_statuses(self, status: tuple, user=None, single=None, subsequent=False):

        # payu status: NEW, PENDING, WAITING_FOR_CONFIRMATION, CANCELED, COMPLETED
        # viva StateId: 0(Pending), 1(Expired), 2(Canceled), 3(Paid)
        viva_status: tuple = ('PENDING', 'EXPIRED', 'CANCELED', 'PAID')

        orders = Order.objects

        if single:
            # retrieve single order
            orders = orders.filter(order_id=single)
        elif user:
            # retrieve user's orders
            orders = orders.filter(user=user)

        # retrieve orders with given statuses
        multi_query = Q()
        for s in status:
            multi_query = multi_query | Q(status=s)
        orders = orders.filter(multi_query)

        for order in orders:
            if order.operator == 'PAYU':
                # initialize connector
                payu = PayUConnector()
                # log in
                payu.login()
                # retrieve order and update local db
                obj = payu.retrieve_order(order.order_id)
                print('retrieve order response:')
                print(json.dumps(obj, indent=4))
                if 'orders' in obj.keys():
                    found = False
                    for item in obj['orders']:
                        if item['orderId'] == order.order_id:
                            found = True
                            if 'status' in item.keys():
                                order.status = item['status']
                            if 'payMethod' in item.keys():
                                order.payment_method = item['payMethod']['type']
                            break
                    if not found:
                        continue
                    if 'properties' in obj.keys():
                        for prop in obj['properties']:
                            if prop['name'] == "PAYMENT_ID":
                                order.payment_id = prop['value']
                                break
                    if order.status not in ('NEW', 'PENDING', 'WAITING_FOR_CONFIRMATION'):
                        # update date & time
                        order.update_time = datetime.now()
                    # retrieve transaction details
                    obj = payu.retrieve_transaction(order.order_id)
                    print('retrieve transaction response:')
                    print(json.dumps(obj, indent=4))
                    det = None
                    if 'transactions' in obj.keys() and obj['transactions']:
                        det = self.read_payment_method_details(order.operator, obj['transactions'][0])
                    order.payment_details = det
                    # update order
                    order.save()
                    # perform after order actions
                    if order.status not in ('NEW', 'PENDING', 'WAITING_FOR_CONFIRMATION'):
                        self.handle_order_after_payment(user, order, order.status)

            elif order.operator == 'VIVA':
                # initialize connector
                viva = VivaConnector()
                # log in
                viva.login()
                # retrieve order and update local db
                obj = viva.retrieve_order(order.order_id)
                print('retrieve order response:')
                print(json.dumps(obj, indent=4))
                if 'OrderCode' in obj.keys() and str(obj['OrderCode']) == order.order_id:
                    if 'RequestAmount' in obj.keys() and obj['RequestAmount'] == order.price_incl_tax:
                        if 'StateId' in obj.keys():
                            order.status = viva_status[obj['StateId']]
                    if order.status != 'PENDING':
                        # update date & time
                        order.update_time = datetime.now()
                    # retrieve transaction details
                    if order.payment_id:
                        obj = viva.retrieve_transaction(order.payment_id)
                        print('retrieve transaction response:')
                        print(json.dumps(obj, indent=4))
                        det = self.read_payment_method_details(order.operator, obj)
                        if not det:
                            det = None
                        order.payment_details = det
                    # update order
                    order.save()
                    # perform after order actions
                    if order.status != 'PENDING':
                        self.handle_order_after_payment(user, order, order.status)

        if subsequent:
            # update subsequent orders
            self.update_payment_statuses(status, user=user)

    def handle_transaction_after_payment(self, user, sub, status):

        # check subscription event type
        events_query = self.read_subscription_event(user, payment_id=sub.order_id)
        if events_query:
            event_obj = events_query.get()
            if event_obj.event_type == 'CHG':
                if status in ('COMPLETED', 'PAID'):
                    # get product (new subscription details)
                    product = event_obj.product
                    # handle subscription change
                    self.handle_subscription_change(user, product, True)
                    # update subscription event status
                    event_obj = self.update_subscription_event_status(event_obj.id, 'COMPLETED')
                    # send email notification
                    self.mailer.subscription_event_completed(user, event_obj)
                else:
                    # update subscription event status
                    event_obj = self.update_subscription_event_status(event_obj.id, 'CANCELED')
                    # send email notification
                    self.mailer.subscription_event_canceled(user, event_obj)

        if status in ('COMPLETED', 'PAID'):
            # update next date of payment - DIAGRAM RP6 / RP7
            payments = RecurringPayment.objects.filter(pending_payment=sub)
            if payments:
                payment = payments.get()
                payment.process_payment()

    # @transaction.atomic
    def update_payment_statuses(self, status: tuple, user=None, single=None):

        # transaction-feedback-parameters
        # https://developer.viva.com/integration-reference/response-codes/#transaction-feedback-parameters
        viva_transaction_status: dict = {
            'A': 'PENDING',
            'E': 'ERROR',
            'F': 'PAID',
            'R': 'CANCELED',
            'X': 'CANCELED',
        }

        subs = SubsequentOrder.objects

        if single:
            # retrieve single subsequent order
            subs = subs.filter(order_id=single)
        elif user:
            # retrieve user's subsequent orders
            subs = subs.filter(reference_order__user=user)

        # retrieve subsequent orders with given statuses
        multi_query = Q()
        for s in status:
            multi_query = multi_query | Q(status=s)
        subs = subs.filter(multi_query)

        for sub in subs:
            if sub.reference_order.operator == 'PAYU':
                # initialize connector
                payu = PayUConnector()
                # log in
                payu.login()
                # retrieve order
                obj = payu.retrieve_order(sub.order_id)
                print('retrieve subsequent order response:')
                print(json.dumps(obj, indent=4))
                if 'orders' in obj.keys():
                    found = False
                    for item in obj['orders']:
                        if item['orderId'] == sub.order_id:
                            found = True
                            if 'status' in item.keys():
                                sub.status = item['status']
                            break
                    if not found:
                        continue
                    if sub.status not in ('NEW', 'PENDING', 'WAITING_FOR_CONFIRMATION'):
                        # update date & time
                        sub.update_time = datetime.now()
                    # retrieve transaction details
                    obj = payu.retrieve_transaction(sub.order_id)
                    print('retrieve transaction response:')
                    print(json.dumps(obj, indent=4))
                    det = None
                    if 'transactions' in obj.keys() and obj['transactions']:
                        det = self.read_payment_method_details(sub.reference_order.operator, obj['transactions'][0])
                    sub.payment_details = det
                    # update order
                    sub.save()
                    # perform after payment actions
                    if sub.status not in ('NEW', 'PENDING', 'WAITING_FOR_CONFIRMATION'):
                        self.handle_transaction_after_payment(user, sub, sub.status)

            elif sub.reference_order.operator == 'VIVA':
                # initialize connector
                viva = VivaConnector()
                # log in
                viva.login()
                # retrieve transaction
                obj = viva.retrieve_transaction(sub.order_id)
                print('retrieve transaction response:')
                print(json.dumps(obj, indent=4))
                if "statusId" in obj.keys():  # small "s" here!!!
                    if obj["statusId"] in viva_transaction_status.keys():
                        sub.status = viva_transaction_status[obj["statusId"]]
                    else:
                        sub.status = obj["statusId"]
                if sub.status != "PENDING":
                    # update date & time
                    sub.update_time = datetime.now()
                # retrieve transaction details
                det = self.read_payment_method_details(sub.reference_order.operator, obj)
                if not det:
                    det = None
                sub.payment_details = det
                # update transaction
                sub.save()
                # perform after payment actions
                if sub.status != "PENDING":
                    self.handle_transaction_after_payment(user, sub, sub.status)

    @staticmethod
    def store_subscription_event(request, event_type, status, product=None, change_mode=None, amount=None,
                                 payment_id=None):
        query_dict: dict = {'user': request.user, 'event_type': event_type, 'status': status}
        if product:
            query_dict['product'] = product
        if change_mode:
            query_dict['change_mode'] = change_mode  # IME: immediate, END: with period end
        if amount:
            query_dict['amount'] = amount
        if payment_id:
            query_dict['payment_id'] = payment_id
        SubscriptionEvent.objects.create(**query_dict)

    @staticmethod
    def read_subscription_event(user, payment_id=None) -> SubscriptionEvent.objects:
        if payment_id:
            return SubscriptionEvent.objects.filter(user=user, payment_id=payment_id)
        else:
            return SubscriptionEvent.objects.filter(user=user).order_by('-event_time')

    @staticmethod
    def has_pending_subscription_event(user) -> bool:
        events_query = SubscriptionEvent.objects.filter(user=user, status='PENDING')
        if events_query:
            return True
        return False

    @staticmethod
    def update_subscription_event_status(nr, status):
        events_query = SubscriptionEvent.objects.filter(pk=nr)
        if events_query:
            event_obj = events_query.get()
            event_obj.status = status
            event_obj.update_time = datetime.now()
            event_obj.save()
            return event_obj
        return None

    @staticmethod
    def update_recurring_payment(user, reference_order=None, next_date=None, new_amount=None, last_date=None,
                                 annual_payment=None):
        # store recurring payment record
        actual_payment = RecurringPayment.objects.filter(user=user)
        if actual_payment:
            # update recurring payment record
            obj = actual_payment.get()
            if reference_order:
                obj.reference_order = reference_order
                obj.payment_month_day = reference_order.update_time.strftime("%d")
            if next_date:
                obj.next_payment_date = next_date
            if new_amount:
                obj.next_payment_amount = new_amount
            if last_date:
                obj.last_payment_date = last_date
            if isinstance(annual_payment, bool):
                obj.annual_payment = annual_payment
            obj.save()
        else:
            # create recurring payment record
            query_dict: dict = {'user': user}
            if reference_order:
                query_dict['reference_order'] = reference_order
                query_dict['payment_month_day'] = reference_order.update_time.strftime("%d")
            if next_date:
                query_dict['next_payment_date'] = next_date
            if new_amount:
                query_dict['next_payment_amount'] = new_amount
            if last_date:
                query_dict['last_payment_date'] = last_date
            if isinstance(annual_payment, bool):
                query_dict['annual_payment'] = annual_payment
            RecurringPayment.objects.create(**query_dict)

    @staticmethod
    def has_active_subscription(user) -> bool:
        events_query = RecurringPayment.objects.filter(user=user, next_payment_date__isnull=False)
        if events_query:
            return True
        return False

    def get_subscription_details(self, user) -> tuple:

        order_obj = None
        current_type = None
        current_amount = None
        current_credits = None
        start = None
        operator = None

        if self.has_active_subscription(user):

            # get initial data
            recurring_payment = RecurringPayment.objects.filter(user=user)
            if recurring_payment:
                recurring_payment_obj = recurring_payment.get()
                order_obj = Order.objects.get(pk=recurring_payment_obj.reference_order_id)
                items_obj = OrderItem.objects.get(order=order_obj)
                product_obj = Product.objects.get(pk=items_obj.product_id)
                if product_obj.type == 'YSU':
                    current_type = 'annual'
                else:
                    current_type = 'monthly'
                current_amount = product_obj.price
                current_credits = product_obj.value
                # start = order_obj.update_time.strftime('%Y-%m-%d')
                start = order_obj.update_time
                operator = order_obj.operator

            # update with last succeeded change
            events = SubscriptionEvent.objects.filter(user=user, status='COMPLETED', event_type='CHG',
                                                      event_time__gte=order_obj.update_time).order_by('-event_time')
            if events:
                event_obj = events.first()
                product_obj = event_obj.product
                if product_obj.type == 'YSU':
                    current_type = 'annual'
                else:
                    current_type = 'monthly'
                current_amount = product_obj.price
                current_credits = product_obj.value

        return order_obj, current_type, current_amount, current_credits, start, operator
