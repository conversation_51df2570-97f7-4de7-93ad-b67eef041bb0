{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_segments' %}">{% trans "Segments" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{{ title }}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fa fa-layer-group me-2"></i>{{ title }}</h2>
                <a href="{% url 'profiles_newsletter_segments' %}" class="newsletter-btn-secondary btn">
                    <i class="fa fa-arrow-left"></i> {% trans "Back to Segments" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="newsletter-card">
                <div class="newsletter-card-body">
                    <form method="post" action="{{ action_url }}">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.registration_date_from.id_for_label }}" class="form-label">{{ form.registration_date_from.label }}</label>
                                    {{ form.registration_date_from }}
                                    {% if form.registration_date_from.errors %}
                                        <div class="invalid-feedback d-block">{{ form.registration_date_from.errors.0 }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">{% trans "Leave empty for no date restriction" %}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.registration_date_to.id_for_label }}" class="form-label">{{ form.registration_date_to.label }}</label>
                                    {{ form.registration_date_to }}
                                    {% if form.registration_date_to.errors %}
                                        <div class="invalid-feedback d-block">{{ form.registration_date_to.errors.0 }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">{% trans "Leave empty for no date restriction" %}</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.confirmed_only }}
                                        <label class="form-check-label" for="{{ form.confirmed_only.id_for_label }}">
                                            {{ form.confirmed_only.label }}
                                        </label>
                                    </div>
                                    {% if form.confirmed_only.errors %}
                                        <div class="invalid-feedback d-block">{{ form.confirmed_only.errors.0 }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">{% trans "Include only confirmed subscriptions" %}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            {{ form.is_active.label }}
                                        </label>
                                    </div>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">{% trans "Active segments can be used in campaigns" %}</small>
                                </div>
                            </div>
                        </div>

                        {% if segment %}
                        <div class="newsletter-card mt-4" style="background-color: var(--muted);">
                            <div class="newsletter-card-body">
                                <h6><i class="fa fa-info-circle me-2"></i>{% trans "Segment Preview" %}</h6>
                                <p class="mb-2">
                                    <strong>{% trans "Current subscribers:" %}</strong> 
                                    <span class="newsletter-badge newsletter-badge-primary">{{ segment.get_subscribers.count }}</span>
                                </p>
                                <small class="text-muted">
                                    {% trans "This shows how many subscribers currently match this segment's criteria." %}
                                </small>
                            </div>
                        </div>
                        {% endif %}

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'profiles_newsletter_segments' %}" class="newsletter-btn-secondary btn">
                                <i class="fa fa-times"></i> {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="newsletter-btn-primary btn">
                                <i class="fa fa-save"></i> {% trans "Save Segment" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
