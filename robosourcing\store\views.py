import base64
import uuid, json

import pytz
from django.db.models import Q
from django.http import HttpResponse, HttpResponseRedirect, Http404, JsonResponse
from django.urls import reverse
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings

from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt

from itertools import chain
from datetime import datetime
from dateutil.relativedelta import relativedelta
import calendar

from db.models import UserSubscription
from profiles.models import Address, Contact, OrganizationUUID
from store.models import Product, Basket, Order, OrderItem, SubsequentOrder, Invoice
from robosourcing.utils import TimeManager
from .store_utils import StoreUtils
from .payu_connector import PayUConnector
from .systim_connector import SystimConnector
from .viva_connector import VivaConnector

from profiles.forms import OrganizationDetailsForm, PhoneNumberForm, UserNameForm, AddressForm
import logging

logger = logging.getLogger(__name__)

su = StoreUtils()


@login_required
def store(request):
    return render(request, 'front/dashboard/store.html')


@login_required
def store_credits(request):
    products = Product.objects.filter(type='PAC', active=True)
    context = {
        'products': products
    }
    return render(request, 'front/dashboard/store_credits.html', context)


@login_required
def store_abo(request, period):

    # lock for entering this subpage having active subscription
    if su.has_active_subscription(request.user):
        messages.warning(request, _('You currently have an active subscription. You can buy a new one after this ends. You can make changes to the existing one at any time.'))
        return http_referer_or(request, 'store')
    elif su.has_pending_subscription_event(request.user):
        messages.warning(request, _('You already bought a subscription and it is about to activate. However your order is still pending, please be patient a little more.'))
        return http_referer_or(request, 'store')

    if period == 'year':
        products = Product.objects.filter(type='YSU', active=True)
    else:
        products = Product.objects.filter(type='SUB', active=True)
    context = {
        'period': period,
        'products': products
    }
    return render(request, 'front/dashboard/store_abo.html', context)


def http_referer_or(request, name) -> str:
    if 'HTTP_REFERER' in request.META.keys():
        return redirect(request.META['HTTP_REFERER'])
    return redirect(name)


@login_required
def store_switch(request):

    # TODO: read current subscription data to show remaining credits
    # get user subscription data
    # user_subscription = UserSubscription.objects.get(user=request.user)

    # get user's subscription details
    order_obj, current_type, current_amount, current_credits, start, operator = \
        su.get_subscription_details(request.user)

    # TODO: remove accepting url params added for development (commenting the following few lines)
    # overwrite initial data with url params (development only!)
    if 'type' in request.GET.keys() and 'amount' in request.GET.keys() and 'credits' in request.GET.keys() \
            and 'start' in request.GET.keys():
        current_type = request.GET['type']
        current_amount = request.GET['amount']
        current_credits = request.GET['credits']
        start = datetime.strptime(request.GET['start'], "%Y-%m-%d")

    # lock for entering this subpage without active subscription
    if not su.has_active_subscription(request.user):
        messages.warning(
            request, _('You currently do not have an active subscription. You can purchase one at any time.'))
        return http_referer_or(request, 'store')

    # get user time zone
    if request.user.userprofile.address and request.user.userprofile.address.timezone:
        user_tz = pytz.timezone(request.user.userprofile.address.timezone)
    else:
        user_tz = pytz.timezone(settings.TIME_ZONE)

    # get now date
    now = timezone.localtime(timezone.now(), timezone=user_tz).date()

    # handle submit button
    if 'operation' in request.POST:
        if request.POST['operation'] == 'unsubscribe':

            # handle subscription (cancel)
            su.handle_subscription_cancel(request.user)

            # store subscription event in db (unsubscribe) - DIAGRAM SE1
            su.store_subscription_event(request, 'UNS', 'PENDING', change_mode='END')

            messages.success(request, _('Your unsubscription request has been accepted.'))
            return redirect('store_switch')  # needed to not duplicate event requests with F5

        elif request.POST['operation'] == 'change' and order_obj:

            # verify change possibility (double check javascript)
            ok = True
            # disable credits top-ups equal or lower for immediate change from annual to annual or monthly to monthly
            if request.POST['change_mode'] == 'immediate' and request.POST['subscription_type'] == current_type:
                if int(request.POST['credits_number']) <= current_credits:
                    ok = False
            # disable equal credits top-ups for change with period end from annual to annual or monthly to monthly
            elif request.POST['change_mode'] == 'end' and request.POST['subscription_type'] == current_type:
                if int(request.POST['credits_number']) == current_credits:
                    ok = False

            if not ok:

                messages.error(request, _('Your subscription change request has not been accepted!'))
                return redirect('store_switch')  # needed to not duplicate event requests with F5

            else:

                # get product price for given credits number (double check javascript)
                if request.POST['subscription_type'] == 'monthly':
                    product = Product.objects.get(type='SUB', value=request.POST['credits_number'],  active=True)
                else:
                    product = Product.objects.get(type='YSU', value=request.POST['credits_number'], active=True)
                new_amount = product.price

                # calculate supplement (double check javascript)
                supplement = None
                if request.POST['change_mode'] == 'immediate':
                    if request.POST['subscription_type'] == 'monthly':
                        # calculate supplement (new amount is monthly one)
                        supplement = new_amount - current_amount
                    else:
                        if current_type == 'monthly':
                            # calculate supplement (new amount is annual one)
                            supplement = new_amount - current_amount
                        else:
                            start = timezone.localtime(start, timezone=user_tz).date()
                            # calculate time diff
                            diff = relativedelta(now, start)
                            # calculate proportional supplement (both amounts are annual ones)
                            supplement = float(new_amount - current_amount) * (1 - diff.months/12)

                if supplement:

                    if order_obj.operator:

                        # create recurring unattended payment - DIAGRAM UP1 / UP2
                        order_id, msg = su.handle_recurring_payment(request.user, order_obj, supplement,
                                                                    is_supplement=True)
                        if order_id:

                            # store subscription event in db (change) - DIAGRAM SE2 / SE4
                            su.store_subscription_event(request, 'CHG', 'PENDING', product=product, change_mode='IME',
                                                        amount=supplement, payment_id=order_id)

                            # go to user orders
                            messages.success(request, _('Your subscription change request has been accepted.'))
                            su.update_payment_statuses((None,), user=request.user, single=order_id)
                            return redirect('user_orders')

                        else:

                            messages.error(request, _('Error occured during processing your subscription change request!')
                                           + f' {msg} ' + _('Please try again and contact us if the problem persists.'))
                            return redirect('store_switch')  # needed to not duplicate event requests with F5

                    else:

                        # TODO: development only - comment out following lines
                        # create unique order id
                        order_id = str(uuid.uuid4()).split('-')[-1]  # last part of uuid
                        # store payment in db
                        __, total_price, tax, total_price_incl_tax, desc = su.prepare_payment_data(supplement,
                                                                                                   is_supplement=True)
                        sub = SubsequentOrder.objects.create(order_id=order_id, reference_order=order_obj,
                                                             description=desc, price=total_price, tax=tax,
                                                             price_incl_tax=total_price_incl_tax, status='COMPLETED')
                        # store subscription event in db (change) - DIAGRAM SE2 / SE4
                        su.store_subscription_event(request, 'CHG', 'PENDING', product=product, change_mode='IME',
                                                    amount=supplement, payment_id=order_id)
                        # perform after payment actions
                        su.handle_transaction_after_payment(request.user, sub, 'COMPLETED')
                        # redirect
                        messages.success(request, _('Your subscription change request has been accepted.'))
                        return redirect('store_switch')  # needed to not duplicate event requests with F5

                else:

                    # handle subscription change
                    su.handle_subscription_change(request.user, product, False)

                    # store subscription event in db (change) - DIAGRAM SE3 / SE4
                    su.store_subscription_event(request, 'CHG', 'PENDING', product=product, change_mode='END')

                    messages.success(request, _('Your subscription change request has been accepted.'))
                    return redirect('store_switch')  # needed to not duplicate event requests with F5

    # parse start date
    # start = datetime.strptime(start, "%Y-%m-%d")
    start = timezone.localtime(start, timezone=user_tz).date()
    start_month = start.month
    start_day = start.day
    # parse current date
    # now = datetime.now()
    current_year = now.year
    current_month = now.month
    current_day = now.day
    # calculate time diff
    diff = relativedelta(now, start)
    diff_months_float = diff.months + diff.days / calendar.monthrange(current_year, current_month)[1]
    # calculate time diff from period end
    end = None

    if settings.SCHEDULER_TEST_MODE:
        if settings.SHORT_PERIOD == 'hour':
            short_delta = relativedelta(hours=1)
        elif settings.SHORT_PERIOD == 'day':
            short_delta = relativedelta(days=1)
        if settings.LONG_PERIOD == 'day':
            long_delta = relativedelta(days=1)
        elif settings.LONG_PERIOD == 'week':
            long_delta = relativedelta(weeks=1)
    else:
        short_delta = relativedelta(months=1)
        long_delta = relativedelta(years=1)

    if current_type == 'monthly':
        end = start + short_delta
    else:
        end = start + long_delta
    diff_from_end = relativedelta(end, now)
    # provide a chart bar with a height corresponding to at least 1 day
    if diff_months_float < 1/31:
        diff_months_float = 1/31
    # calculate next top-up date
    next_topup = start
    if settings.SCHEDULER_TEST_MODE and settings.SHORT_PERIOD == 'hour':
        next_topup += relativedelta(years=diff.years, months=diff.months, days=diff.days, hours=diff.hours + 1)
    elif settings.SCHEDULER_TEST_MODE and settings.SHORT_PERIOD == 'day':
        next_topup += relativedelta(years=diff.years, months=diff.months, days=diff.days + 1)
    else:
        next_topup += relativedelta(years=diff.years, months=diff.months+1)
        # if current_day == start_day:
        #     next_topup += relativedelta(months=-1)
    # calculate next payment date
    if current_type == 'monthly':
        next_payment = next_topup
    else:
        next_payment = start
        if settings.SCHEDULER_TEST_MODE and settings.LONG_PERIOD == 'day':
            next_payment += relativedelta(days=diff.days + 1)
        elif settings.SCHEDULER_TEST_MODE and settings.SHORT_PERIOD == 'week':
            next_payment += relativedelta(weeks=diff.weeks + 1)
        else:
            next_payment += relativedelta(years=diff.years+1)
            # if current_month == start_month and current_day == start_day:
            #     next_payment += relativedelta(years=-1)

    # getting card used for subscription purchase - card info stored in user subscription record is preferred
    # as it may be the one provided later (updated by user himself or on demand sent in email notification)
    payment_details = None
    user_subscription = UserSubscription.objects.filter(user=request.user).first()
    if user_subscription:
        payment_method = user_subscription.payment_method
        payment_details = user_subscription.payment_details
    if not payment_details:
        payment_method = order_obj.payment_method
        payment_details = order_obj.payment_details

    # verify payment card
    card_expired = False
    card_expires_soon = False
    if payment_method == 'CARD_TOKEN':
        # initialize connector
        conn = None
        if operator == 'PAYU':
            conn = PayUConnector()
        elif operator == 'VIVA':
            conn = VivaConnector()
        if conn:
            # log in
            conn.login()
            # get token
            weeks_earlier = 2
            token, expires_soon = conn.get_card_token(request.user, details=payment_details, expire_weeks=weeks_earlier)
            if not token or token['status'] != 'ACTIVE':
                card_expired = True
            elif expires_soon:
                card_expires_soon = True

    # prepare subscription data
    actual_sub = {
        'type': current_type,
        'amount': str(current_amount).replace(',', '.'),
        'credits': current_credits,
        'start': start,
        'next_topup': next_topup,
        'next_payment': next_payment,
        'payment_method': payment_method,
        'payment_details': payment_details,
        'card_expired': card_expired,
        'card_expires_soon': card_expires_soon,
        'chart_start': start + relativedelta(years=diff.years),
        'diff_months': diff.months,
        'diff_months_float': str(diff_months_float).replace(',', '.'),
        'diff_months_from_end': diff_from_end.years * 12 + diff_from_end.months,
        'locked_for_changes': su.has_pending_subscription_event(request.user),
    }
    # prepare context
    events = su.read_subscription_event(request.user)
    products = Product.objects.filter(active=True).filter(Q(type='SUB') | Q(type='YSU'))
    context = {
        'actual_sub': actual_sub,
        'events': events,
        'products': products
    }
    return render(request, 'front/dashboard/store_switch.html', context)


@login_required
def store_update(request):

    conn = PayUConnector()

    # log in
    conn.login()

    # payu_link = conn.get_card_update_link(request.user)
    # payu_html = conn.get_card_update_link(request.user)
    # return redirect(payu_link)

    # prepare context
    context = {
        "merchant_pos_id": conn._PAYU_CLIENT_ID,
        # 'payu_link': payu_link,
        # 'payu_html': payu_html
    }
    return render(request, 'front/dashboard/store_update.html', context)


def save_token(request):
    token = request.POST.get('token', '')
    tm = TimeManager()
    now = tm.current_time()
    user_subscription = UserSubscription.objects.filter(user=request.user).first()
    user_subscription.payment_method = 'CARD_TOKEN'
    user_subscription.payment_details = token
    user_subscription.payment_last_update = now
    user_subscription.save()
    return HttpResponse(token)
    # if 'theme' not in request.session:
    #     request.session['theme'] = 'auto'
    # query = request.POST.get('name', '')
    # if query:
    #     request.session['theme'] = query
    #     response = HttpResponse('theme changed')
    #     response.set_cookie(
    #         'theme',
    #         query,
    #         max_age=settings.LANGUAGE_COOKIE_AGE,
    #         path=settings.LANGUAGE_COOKIE_PATH,
    #         domain=settings.LANGUAGE_COOKIE_DOMAIN,
    #         secure=settings.LANGUAGE_COOKIE_SECURE,
    #         httponly=settings.LANGUAGE_COOKIE_HTTPONLY,
    #         samesite=settings.LANGUAGE_COOKIE_SAMESITE,
    #     )
    #     return response
    # return HttpResponse('error occured')


@login_required
def add_to_basket(request):
    # Znajdź produkt w bazie danych
    product = None
    if "pricelist_item_id" in request.POST:
        product_id = request.POST["pricelist_item_id"]
        product = Product.objects.get(id=product_id, active=True)

    if not product:
        messages.error(request, _('Unknown pricelist item!'))
        return http_referer_or(request, 'store')

    if product.type in ('SUB', 'YSU'):
        # Usuń wszystkie elementy koszyka jeśli zamawiasz subskrypcję
        subs = Basket.objects.filter(user=request.user)
        if subs.count():
            subs.delete()
            messages.warning(request, _("All items have been removed from the cart as a subscription should be ordered separately"))
    elif product.type == 'PAC':
        # Usuń subskrypcję z koszyka, jeśli kupujesz paczki
        subs = Basket.objects.filter(user=request.user).filter(Q(product__type='SUB') | Q(product__type='YSU'))
        if subs.count():
            subs.delete()
            messages.warning(request, _("A subscription has been removed from the cart as they can't be ordered simultaneously with packages."))

    # Dodaj produkt do koszyka lub zwiększ jego ilość
    basket_obj, created = Basket.objects.get_or_create(user=request.user, product=product)
    if not created:
        basket_obj.quantity += 1
        basket_obj.save()

    # Zaktualizuj licznik elementów (dla paska w dashboardzie)
    counter = 0
    for item in Basket.objects.filter(user=request.user):
        counter += item.quantity
    request.session['basket_counter'] = counter

    # Sprawdź, który przycisk został kliknięty
    if 'add_to_basket' in request.POST:
        # Pokaż wiadomość o dodaniu do koszyka bez przekierowania
        messages.success(request,
                         _('"%(item)s" has been added to the cart! You have %(qty)s "%(item)s" in your shopping cart!') % {
                             'item': product.name, 'qty': basket_obj.quantity})
        return http_referer_or(request, 'store')

    # Jeśli został kliknięty "Buy Now" przekieruj do koszyka
    return redirect('basket')

@login_required
def update_basket(request, product, mode):

    # find basket item in DB
    item = Basket.objects.get(user=request.user, product=product)

    if not item:
        # show an error message and bo back to basket
        messages.error(request, _('Unknown basket item!'))
        return redirect('basket')

    # find product in DB
    product = Product.objects.get(id=item.product.id, active=True)

    if not product:
        # show an error message and go back to basket
        messages.error(request, _('Unknown pricelist item!'))
        return redirect('basket')

    # update item quantity or delete it
    if mode == 'decrement':
        item.quantity -= 1
        item.save()
    elif mode == 'increment':
        item.quantity += 1
        item.save()
    if mode == 'remove' or item.quantity <= 0:
        item.delete()

    # update items counter (for dashboard top bar only)
    counter = 0
    for item in Basket.objects.filter(user=request.user):
        counter += item.quantity
    request.session['basket_counter'] = counter

    # show a message and go back to basket
    messages.success(request, _('Your shopping cart has been updated!'))
    return redirect('basket')


@login_required
def basket(request):
    # Pobranie produktów w koszyku
    items_query = Basket.objects.filter(user=request.user)
    items_obj = items_query.all() if items_query else None
    total_price = sum((item.product.price * item.quantity) for item in items_obj) if items_obj else 0

    # Obliczenie podatku i całkowitej ceny z podatkiem
    tax_rate = 23
    tax = round(total_price * tax_rate / 100, 2)
    total_price_incl_tax = total_price + tax

    # Pobranie danych użytkownika
    user_profile = request.user.userprofile
    address_instance = user_profile.address or Address()
    contact_instance = user_profile.contact or Contact()
    organization_instance = user_profile.organization_uuid or OrganizationUUID()

    # Upewnij się, że instancja organizacji ma wartość kraju
    if not organization_instance.country:
        organization_instance.country = 'PL'
    
    # Inicjalizacja formularzy dla adresu, kontaktu i organizacji użytkownika
    # Zaktualizowano inicjalizację formularza name_form
    name_form = UserNameForm(instance=request.user)
    address_form = AddressForm(instance=address_instance)
    phone_number_form = PhoneNumberForm(instance=contact_instance)
    organization_form = OrganizationDetailsForm(instance=organization_instance)

    # Pobranie 'want_invoice' z sesji
    want_invoice = request.session.get('want_invoice', False)
    data_confirmed = request.session.get('data_confirmed', False)
    cart_confirmed = request.session.get('cart_confirmed', False)
    payment_confirmed = request.session.get('payment_confirmed', False)

    operators = Order.OPERATOR_LIST

    if request.method == 'POST':
        # Obsługa formularzy personalnych, adresowych, organizacyjnych itd.
        if 'submit_name' in request.POST:
            # Zaktualizowano inicjalizację formularza name_form podczas POST
            name_form = UserNameForm(request.POST, instance=request.user)
            if name_form.is_valid():
                name_form.save()
                messages.success(request, 'Your personal details have been updated.')

        elif 'submit_phone_number' in request.POST:
            phone_number_form = PhoneNumberForm(request.POST, instance=contact_instance)
            if phone_number_form.is_valid():
                phone_number = phone_number_form.save()
                user_profile.contact = phone_number
                user_profile.save()
                messages.success(request, 'Your phone number has been updated.')

        elif 'submit_address' in request.POST:
            address_form = AddressForm(request.POST, instance=address_instance)
            if address_form.is_valid():
                address = address_form.save()
                user_profile.address = address
                user_profile.save()
                messages.success(request, 'Your address has been updated.')

        elif 'submit_organization' in request.POST:
            organization_form = OrganizationDetailsForm(request.POST, instance=organization_instance)
            if organization_form.is_valid():
                organization = organization_form.save()
                user_profile.organization_uuid = organization
                user_profile.save()
                messages.success(request, 'Your organization details have been updated.')
            else:
                messages.error(request, 'Please correct the errors in the form.')

        return redirect('basket')  # Przekierowanie, aby uniknąć ponownego przesłania formularza

    context = {
        'basket': items_obj,
        'total_price': total_price,
        'tax_rate': tax_rate,
        'tax': tax,
        'total_price_incl_tax': total_price_incl_tax,
        'user_profile': user_profile,
        'name_form': name_form,
        'phone_number_form': phone_number_form,
        'address_form': address_form,
        'organization_form': organization_form,
        'want_invoice': want_invoice,
        'data_confirmed': data_confirmed,
        'cart_confirmed': cart_confirmed,
        'payment_confirmed': payment_confirmed, 
        'operators': operators,
    }

    return render(request, 'front/dashboard/basket.html', context)

@csrf_exempt
@require_POST
def save_cart_confirmation(request):
    data = json.loads(request.body)
    request.session['cart_confirmed'] = data.get('cart_confirmed', False)
    return JsonResponse({'status': 'success'})

@csrf_exempt
@require_POST
def save_data_confirmation(request):
    data = json.loads(request.body)
    request.session['data_confirmed'] = data.get('data_confirmed', False)
    return JsonResponse({'status': 'success'})


@csrf_exempt
@require_POST
def update_want_invoice(request):
    data = json.loads(request.body)
    want_invoice = data.get('want_invoice', False)
    request.session['want_invoice'] = want_invoice
    return JsonResponse({'status': 'success'})

@csrf_exempt
def save_payment_confirmation(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        payment_confirmed = data.get('payment_confirmed', False)
        request.session['payment_confirmed'] = payment_confirmed
        return JsonResponse({'status': 'success'})
    return JsonResponse({'status': 'failed'}, status=400)


# @login_required
# def purchase(request):
#
#     items_query = Basket.objects.filter(user=request.user)
#     if items_query:
#         items_obj = items_query.all()
#         total_value = sum((item.product.value * item.quantity) for item in items_obj)
#         # total_price = sum((item.product.price * item.quantity) for item in items_obj)
#         credits_obj, created = Credit.objects.get_or_create(user=request.user)
#         if purchase_process_completed():
#             subscription = items_query.filter(product__type='SUB')
#             if subscription:
#                 basket_item = subscription.get()
#                 new_sub_obj = Product.objects.get(pk=basket_item.product_id)
#                 actual_sub = UserSubscription.objects.filter(user=request.user)
#                 if actual_sub:
#                     actual_sub_obj = actual_sub.get()
#                     actual_sub_obj.value = new_sub_obj.value
#                     actual_sub_obj.price = new_sub_obj.price
#                     actual_sub_obj.renew_date = timezone.now() + timezone.timedelta(days=30)
#                     actual_sub_obj.save()
#                 else:
#                     UserSubscription.objects.create(user=request.user, value=new_sub_obj.value,
#                     price=new_sub_obj.price)
#             credits_obj.credits += total_value
#             credits_obj.save()
#             items_query.delete()
#             messages.success(request, _('Transaction complete!'))
#         else:
#             messages.error(request, _('Payment process error!'))
#     else:
#         messages.info(request, _('Your shopping cart is empty!'))
#
#     # empty user basket & update items counter
#     items_query = Basket.objects.filter(user=request.user)
#     items_query.delete()
#     request.session['basket_counter'] = 0#
#     return redirect('kredyty')
#
#
# def purchase_process_completed() -> bool:
#     return True


@login_required
def choose_operator(request):

    # prepare context and load page template
    context = {
        'operators': Order.OPERATOR_LIST,
    }
    return render(request, 'front/home_page/choose_operator.html', context)


@login_required
def make_order(request):

    def skip_purchase():
        # prepare order data
        products, num, total_price, tax, total_price_incl_tax, desc, vdesc = su.prepare_order_data(basket_items)
        # create unique order id
        order_id = str(uuid.uuid4()).split('-')[-1]  # last part of uuid
        # handle order before payment
        order = su.handle_order_before_payment(request, True if order_type == 'subscription' else False, None,
                                               order_id, basket_items, total_price, tax, total_price_incl_tax, desc)
        # handle order after payment
        su.handle_order_after_payment(request.user, order, order.status)
        # empty user basket & update items counter
        items_query = Basket.objects.filter(user=request.user)
        items_query.delete()
        request.session['basket_counter'] = 0
        # go to user orders
        return redirect('user_orders')

    # verify purchase mode
    if "mode" not in request.POST:
        messages.error(request, _('Purchase mode not set!'))
        return HttpResponseRedirect(reverse('index', args=()))

    # verify operator
    if "operator" not in request.POST:
        messages.error(request, _('Purchase operator not set!'))
        return http_referer_or(request, 'store')

    # prepare basket items as list
    basket_items: list = []
    if request.POST["mode"] == 'single':

        # verify pricelist item
        if "pricelist_item_id" not in request.POST:
            messages.error(request, _('Pricelist item not set!'))
            return http_referer_or(request, 'store')

        # find product in DB
        product = Product.objects.get(id=request.POST["pricelist_item_id"], active=True)
        if not product:
            messages.error(request, _('Unknown pricelist item!'))
            return http_referer_or(request, 'store')

        # append product to basket items
        basket_items.append({'id': product.id, 'name': product.name, 'price': product.price, 'quantity': 1})

    elif request.POST["mode"] == 'basket':

        # verify shopping cart
        query_items = Basket.objects.filter(user=request.user)
        if not query_items:
            messages.error(request, _('Your shopping cart is empty!'))
            return http_referer_or(request, 'store')

        # append basket products to basket items
        for item in query_items:
            basket_items.append({'id': item.product.id, 'name': item.product.name, 'price': item.product.price,
                                 'quantity': item.quantity})

    else:

        messages.error(request, _('Unknown purchase mode!'))
        return http_referer_or(request, 'store')

    order_type = None
    subs = ('SUB', 'YSU')
    for item in basket_items:

        # find product in DB
        product = Product.objects.get(id=item['id'], active=True)
        if not product:
            messages.error(request, _('Unknown pricelist item!'))
            return http_referer_or(request, 'store')

        # double check basket and determine order type
        if product.type in subs:
            if order_type:
                if order_type == 'purchase':
                    messages.error(request, _("Products in your cart can't be ordered at once!"))
                    return http_referer_or(request, 'store')
                else:
                    messages.error(request, _("You can order a single subscription only!"))
                    return http_referer_or(request, 'store')
            order_type = 'subscription'
        else:
            order_type = 'purchase'

    # initialize connector
    if request.POST['operator'] == 'VIVA':
        conn = VivaConnector()
    else:
        conn = PayUConnector()

    # log in
    conn.login()

    # conn.get_payment_methods(request)
    # html = conn.create_order()
    # return HttpResponse(html.decode('utf-8'))

    # TODO: development only - remove following lines on production
    if request.POST['operator'] == 'SKIP':
        # skip purchase
        return skip_purchase()

    # create order (redirect to operator page)
    if order_type == 'subscription':

        # triple check basket when subscribing
        if len(basket_items) > 1:
            messages.error(request, _('Your cart should contain a single item only!') + ' '
                           + _('Please try again and contact us if the problem persists.'))
            return http_referer_or(request, 'store')

        # create subscription order
        uri = conn.create_subscription(request, basket_items)

    else:

        # create normal order
        uri = conn.create_order(request, basket_items)

    # verify external URI
    if not uri:
        messages.error(request, _('Preparing order form failed!') + ' '
                       + _('Please try again and contact us if the problem persists.'))
        return http_referer_or(request, 'store')

    # go to operator's site
    return redirect(uri)


@login_required
def handle_continue(request):

    # VIVA only
    if 't' in request.GET and 's' in request.GET:
        su.insert_order_payment_id(request, request.GET['t'], request.GET['s'])

    su.update_orders_statuses((None,), user=request.user)

    # empty user basket & update items counter
    items_query = Basket.objects.filter(user=request.user)
    items_query.delete()
    request.session['basket_counter'] = 0

    # go to user orders
    return redirect('user_orders')


def handle_notify(request):

    # TODO: comment following lines after the notify URL is verified by Viva
    # authorize Viva webhook (sending request from production server as a response on verify request)
    # viva = VivaConnector()
    # return JsonResponse(viva.authorize_webhook())

    # initialize connector (check PayU first)
    payu = PayUConnector()
    if payu.goes_from_authorized_ip(request):
        # handle PayU notification
        if payu.handle_notification(request):
            # inform payU server (code 200 is enough) that we received and handled notification successfully
            return HttpResponse('ok')
    else:
        # initialize connector (Viva after that)
        viva = VivaConnector()
        if viva.goes_from_authorized_ip(request):
            # handle Viva notification
            if viva.handle_notification(request):
                # inform viva server (code 200 is enough) that we received and handled notification successfully
                return HttpResponse('ok')

    # inform server (returning code different from 200) that someting went wrong with handling it
    # in case when hackers try to access our script code 404 seems to be optimal :)
    raise Http404


@login_required
def user_orders(request):

    # handle refresh button
    if 'refresh' in request.POST:
        su.update_orders_statuses((None, 'NEW', 'PENDING', 'WAITING_FOR_CONFIRMATION'), user=request.user)

    # get user orders
    orders = Order.objects.filter(user=request.user).order_by('-create_time')

    # collect all user orders items
    items: list = []
    for order in orders:
        items.append(OrderItem.objects.filter(order=order))
    items = list(chain(*items))
    # collect all user subsequent orders
    subs: list = []
    for order in orders:
        subs.append(SubsequentOrder.objects.filter(reference_order=order))
    subs = list(chain(*subs))

    # add paginator
    paginator = Paginator(orders, 10)
    page = request.GET.get('page')
    try:
        orders = paginator.page(page)
    except PageNotAnInteger:
        orders = paginator.page(1)
    except EmptyPage:
        orders = paginator.page(paginator.num_pages)

    # prepare context and load orders template
    context = {
        'orders': orders,
        'items': items,
        'subs': subs
    }
    return render(request, 'front/dashboard/orders.html', context)


@login_required
def user_invoices(request):

    # handle refresh button
    if 'refresh' in request.POST:
        # TODO: issuing invoices for signle user on demand
        pass

    # get user invoices
    orders = Order.objects.filter(user=request.user, invoice_id__isnull=False)
    subsequent_orders = SubsequentOrder.objects.filter(reference_order__user=request.user, invoice_id__isnull=False)
    multi_query = Q(id__in=orders.values('invoice_id')) | Q(id__in=subsequent_orders.values('invoice_id'))
    invoices = Invoice.objects.filter(multi_query).order_by('-issue_time')

    # add paginator
    paginator = Paginator(invoices, 10)
    page = request.GET.get('page')
    try:
        invoices = paginator.page(page)
    except PageNotAnInteger:
        invoices = paginator.page(1)
    except EmptyPage:
        invoices = paginator.page(paginator.num_pages)

    # prepare context and load orders template
    context = {
        'invoices': invoices,
    }
    if not settings.ISSUE_INVOICES:
        messages.warning(request, _('Invoicing has not been activated. Contact the site administrator.'))
    elif not request.user.userprofile.organization_uuid:
        messages.warning(request, _('Invoices may not be issued because you either did not submit a request or did not enter the necessary data.'))
    return render(request, 'front/dashboard/invoices.html', context)


def invoice_get(request, id):

    # get invoice object
    try:
        invoice = Invoice.objects.get(pk=id)
    except (Exception, ):
        messages.error(request, f'Invoice not found')
        return HttpResponseRedirect(reverse('user_invoices'))

    # verify user
    orders = Order.objects.filter(user=request.user, invoice_id=invoice)
    if not orders:
        orders = SubsequentOrder.objects.filter(reference_order__user=request.user, invoice_id=invoice)
    if not orders:
        messages.error(request, f'Operation not allowed')
        return HttpResponseRedirect(reverse('user_invoices'))

    if invoice.system == 'SYS':
        systim = SystimConnector()
        # systim.login()
        obj = systim.get_invoice_pdf(invoice.ext_id)
        if obj['error']['code'] == 0:
            content = base64.b64decode(obj['result']['file'])
            filename = obj['result']['name']
            response = HttpResponse(content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename={filename}'
            # store event
            invoice.downloaded = True
            invoice.save()
            # return file
            return response

    messages.error(request, f'Operation failed')
    return HttpResponseRedirect(reverse('user_invoices'))
