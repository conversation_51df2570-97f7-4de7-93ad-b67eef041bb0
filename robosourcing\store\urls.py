from django.urls import path
from . import views

urlpatterns = [
    path('store/', views.store, name='store'),
    path('add-to-basket/', views.add_to_basket, name='add_to_basket'),
    path('update-basket/<str:mode>/<int:product>/', views.update_basket, name='update_basket'),
    path('basket/', views.basket, name='basket'),
    path('credits/', views.store_credits, name='store_credits'),
    path('abo/<str:period>/', views.store_abo, name='store_abo'),
    path('switch-subscription/', views.store_switch, name='store_switch'),
    path('update-card/', views.store_update, name='store_update'),
    path('save-token/', views.save_token, name='save_token'),
    # path('purchase/', views.purchase, name='purchase'),
    path('order/', views.make_order, name="make_order"),
    path('continue/', views.handle_continue),
    path('notify/', views.handle_notify),
    path('orders/', views.user_orders, name='user_orders'),
    path('invoices/', views.user_invoices, name='user_invoices'),
    path('invoices/<str:id>/get/', views.invoice_get, name='invoice_get'),
    path('choose-operator/', views.choose_operator, name="choose_operator"),
    path('update-want-invoice/', views.update_want_invoice, name='update_want_invoice'),
    path('save-data-confirmation/', views.save_data_confirmation, name='save_data_confirmation'),
    path('save-cart-confirmation/', views.save_cart_confirmation, name='save_cart_confirmation'),
    path('save-payment-confirmation/', views.save_payment_confirmation, name='save_payment_confirmation'),
]
