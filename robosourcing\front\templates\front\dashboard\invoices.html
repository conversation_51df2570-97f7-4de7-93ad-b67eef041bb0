{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load crispy_forms_filters %}
{% load split %}

{% block title %}{{ block.super }} - {% trans "Invoices" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{%url 'store'%}">{% trans "Store" %}</a></li>
          <li class="breadcrumb-item site-name active">{% trans "My invoices" %}<i class="fa fa-refresh text-muted ms-3" role="button" onclick="$(this).addClass('fa-spin'); document.refreshform.submit()"></i></li>
        </ol>
    </nav>
    
    <form name="refreshform" action="{% url 'user_invoices' %}" method="post">
        {% csrf_token %}
        <input type="hidden" name="refresh" value="1">
    </form>

    <div class="dashboard-element">
        <h3 class="db-section-title">{% blocktrans %}List of issued invoices{% endblocktrans %}:</h3>
        <hr>
        <div class="table-responsive">
            <table class="table my-custom-table">
                <thead>
                <tr>
                    <th>{% trans "Issue time" %}</th>
                    <th>{% trans "Invoice number" %}</th>
                    <th class="text-end"></th>
                </tr>
                </thead>
                <tbody>
                {% for invoice in invoices %}
                <tr>
                    <td>{{ invoice.issue_time|date:"DATETIME_FORMAT" }}</td>
                    <td>{{ invoice.number }}</td>
                    <td class="text-end"><a href="{% url 'invoice_get' invoice.pk %}" onclick="markDownloaded(this)" class="btn {% if invoice.downloaded %}btn-secondary{% else %}btn-primary{% endif %}"><i class="far fa-file-pdf"></i> {% trans "Get invoice" %}</a></td>
                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if invoices %}
        <nav aria-label="...">
            {% include "front/home_page/pagination.html" with objects=invoices %}
        </nav>
        {% endif %}

    </div>
</div>

<script>
function markDownloaded(el) {
    $(el).removeClass('btn-primary').addClass('btn-secondary');
}
</script>
{% endblock extra_content_1 %}

{% block extra_content_2 %}
{% endblock %}