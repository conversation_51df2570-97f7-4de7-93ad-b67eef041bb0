from apscheduler.schedulers.background import BackgroundScheduler

from .jobs import subscription_updater, freebies, monthly_payment_launcher, annual_payment_launcher, order_retriever, \
    payment_card_checker, invoice_issuer, create_monthly_withdrawals
from .jobs import time_checker
from django.conf import settings


def start_scheduler():
    scheduler = BackgroundScheduler()

    if settings.SCHEDULER_TEST_MODE or settings.ACCELERATE_TIME:

        if settings.ACCELERATE_TIME:
            time_checker()
            scheduler.add_job(time_checker, 'interval', minutes=1, misfire_grace_time=None)

        # subscription_updater()
        # scheduler.add_job(subscription_updater, 'interval', minutes=1, misfire_grace_time=None)
        # freebies()
        # scheduler.add_job(freebies, 'interval', minutes=1, misfire_grace_time=None)
        # order_retriever()
        # scheduler.add_job(order_retriever, 'interval', minutes=1, misfire_grace_time=None)
        monthly_payment_launcher()
        scheduler.add_job(monthly_payment_launcher, 'interval', minutes=1, misfire_grace_time=None)
        # annual_payment_launcher()
        # scheduler.add_job(annual_payment_launcher, 'interval', minutes=1, misfire_grace_time=None)
        # payment_card_checker()
        # scheduler.add_job(payment_card_checker, 'interval', minutes=1, misfire_grace_time=None)
        if settings.ISSUE_INVOICES:
            invoice_issuer()
            scheduler.add_job(invoice_issuer, 'interval', minutes=1, misfire_grace_time=None)
        
        # scheduler.add_job(create_monthly_withdrawals, 'interval', minutes=1, misfire_grace_time=None) 

    else:

        scheduler.add_job(subscription_updater, 'interval', minutes=5, misfire_grace_time=None)
        scheduler.add_job(freebies, 'interval', minutes=5, misfire_grace_time=None)
        scheduler.add_job(order_retriever, 'interval', minutes=5, misfire_grace_time=None)
        scheduler.add_job(monthly_payment_launcher, 'interval', minutes=5, misfire_grace_time=None)
        scheduler.add_job(annual_payment_launcher, 'interval', minutes=5, misfire_grace_time=None)
        scheduler.add_job(payment_card_checker, 'interval', minutes=5, misfire_grace_time=None)
        if settings.ISSUE_INVOICES:
            scheduler.add_job(invoice_issuer, 'interval', minutes=5, misfire_grace_time=None)
        scheduler.add_job(create_monthly_withdrawals, 'cron', day=1, hour=0, minute=0, misfire_grace_time=None)

    scheduler.start()
