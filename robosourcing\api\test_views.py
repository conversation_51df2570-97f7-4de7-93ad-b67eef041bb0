from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from db.models import Robot, User, UserProfile, Scenario, UserStorage

class ScenarioUploadTests(TestCase):
    def setUp(self):
        # Create a user
        self.user = User.objects.create_user(username='testuser', password='12345')
        self.user_profile, created = UserProfile.objects.get_or_create(user=self.user, defaults={'role': 'ADM'})

        # Create a robot owned by the user
        self.robot = Robot.objects.create(owner=self.user, name='Test Robot')

        # Authenticate the user for this session
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # The correct URL name from your urls.py, assuming it's set up correctly
        self.url = reverse('robo_scenario_upload')

    def test_upload_scenario(self):
        data = {
            'name': 'New Scenario',
            'description': 'Description for new scenario',
            'scenario': '{}',  # JSON format data
            'author': 'Author Name',
            'rid': str(self.robot.rid)  # Ensure this field is expected as a string
        }

        # Make the POST request
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the creation of a UserStorage entry
        self.assertTrue(UserStorage.objects.filter(user=self.user).exists(), "UserStorage entry was not created")

    def test_upload_scenario_with_robot(self):
        data = {
            'name': 'New Scenario',
            'description': 'Description for new scenario',
            'scenario': '{}',
            'author': 'Author Name',
            'rid': str(self.robot.rid)
        }

        # Make the POST request
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify response data
        self.assertIn('sid', response.data)
        self.assertEqual(response.data['detail'], 'Scenario uploaded!')

        # Verify the creation of a UserStorage entry
        user_storage = UserStorage.objects.filter(user=self.user, sid=response.data['sid'])
        self.assertTrue(user_storage.exists())
        self.assertEqual(user_storage.first().rid, self.robot)

        # Ensure the robot ID is correctly linked
        self.assertEqual(str(user_storage.first().rid.rid), data['rid'])
