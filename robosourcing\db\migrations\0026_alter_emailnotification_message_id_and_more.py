# Generated by Django 4.2.13 on 2024-10-16 17:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('db', '0025_merge_0023_merge_20240926_1748_0024_creditevent_rid'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emailnotification',
            name='message_id',
            field=models.IntegerField(choices=[(0, 'Thank you for registering an account'), (1, 'Welcome to our web service'), (2, 'Thank you for making your purchases'), (3, 'Your subscription event completed successfully'), (4, 'Your subscription event has failed'), (5, 'Your payment card expires soon'), (6, 'Your payment card has expired'), (7, 'Contact form submitted')]),
        ),
        migrations.AlterField(
            model_name='emailnotification',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
