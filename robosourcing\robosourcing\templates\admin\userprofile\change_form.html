{% extends "admin/change_form.html" %}
{% load static %}

{% block content %}
    {{ block.super }}
    
    <h2>Related profile data:</h2>
    <h3>Address data</h3>
    {% if addresses %}
        <table>
            <thead>
                <tr>
                    <th>Street</th>
                    <th>Street number</th>
                    <th>Home number</th>
                    <th>City</th>
                    <th>Postal code</th>
                    <th>Country</th>
                    <th>Timezone</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
        {% for address in addresses %}
                <tr>
                    <td>{{ address.street }}</td>
                    <td>{{ address.street_number }}</td>
                    <td>{{ address.home_number }}</td>
                    <td>{{ address.city }}</td>
                    <td>{{ address.postal_code }}</td>
                    <td>{{ address.country }}</td>
                    <td>{{ address.timezone }}</td>
                    <td><a href="{% url 'admin:profiles_address_change' address.pk %}" class="button">Edit</a></td>
                </tr>
        {% endfor %}
            </tbody>
        </table>
    {% else %}
    <p>No address data found</p>
    {% endif %}


    <h3>Contact data</h3>
    {% if contacts %}
        <table>
            <thead>
                <tr>
                    <th>Area code</th>
                    <th>Phone number</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
        {% for contact in contacts %}
                <tr>
                    <td>{{ contact.area_code }}</td>
                    <td>{{ contact.phone_number }}</td>
                    <td><a href="{% url 'admin:profiles_contact_change' contact.pk %}" class="button">Edit</a></td>
                </tr>
        {% endfor %}
            </tbody>
        </table>
    {% else %}
    <p>No contact data found</p>
    {% endif %}

    <h3>Organization data</h3>
    {% if organizations %}
        <table>
            <thead>
                <tr>
                    <th>Tax ID</th>
                    <th>Name 1</th>
                    <th>Name 2</th>
                    <th>Street</th>
                    <th>Street number</th>
                    <th>Home number</th>
                    <th>City</th>
                    <th>Postal code</th>
                    <th>Country</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
        {% for organization in organizations %}
                <tr>
                    <td>{{ organization.tax_id }}</td>
                    <td>{{ organization.name_1 }}</td>
                    <td>{{ organization.name_2 }}</td>
                    <td>{{ organization.street }}</td>
                    <td>{{ organization.street_number }}</td>
                    <td>{{ organization.home_number }}</td>
                    <td>{{ organization.city }}</td>
                    <td>{{ organization.postal_code }}</td>
                    <td>{{ organization.country }}</td>
                    <td><a href="{% url 'admin:profiles_organization_change' organization.pk %}" class="button">Edit</a></td>
                </tr>
        {% endfor %}
            </tbody>
        </table>
    {% else %}
    <p>No organization data found</p>
    {% endif %}

    <h3>OrganizationUUID data</h3>
    {% if organizations_uuid %}
        <table>
            <thead>
                <tr>
                    <th>Tax ID</th>
                    <th>Name 1</th>
                    <th>Name 2</th>
                    <th>Street</th>
                    <th>Street number</th>
                    <th>Home number</th>
                    <th>City</th>
                    <th>Postal code</th>
                    <th>Country</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
        {% for organization in organizations_uuid %}
                <tr>
                    <td>{{ organization.tax_id }}</td>
                    <td>{{ organization.name_1 }}</td>
                    <td>{{ organization.name_2 }}</td>
                    <td>{{ organization.street }}</td>
                    <td>{{ organization.street_number }}</td>
                    <td>{{ organization.home_number }}</td>
                    <td>{{ organization.city }}</td>
                    <td>{{ organization.postal_code }}</td>
                    <td>{{ organization.country }}</td>
                    <td><a href="{% url 'admin:profiles_organization_change' organization.pk %}" class="button">Edit</a></td>
                </tr>
        {% endfor %}
            </tbody>
        </table>
    {% else %}
    <p>No organization data found</p>
    {% endif %}

{% endblock %}