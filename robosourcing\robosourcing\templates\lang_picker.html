{% load i18n %}
{% load togb %}
{% get_current_language as LANGUAGE_CODE %}
{% get_available_languages as LANGUAGES %}
{% get_language_info_list for LANGUAGES as languages %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
      <span class="fi fi-{{ LANGUAGE_CODE|togb }}"></span>
    </a>
    <ul class="dropdown-menu dropdown-menu-dark" id="language-list">
    {% for language in languages %}
        <li>
            <a class="dropdown-item" href="{% url 'set_language' %}" data-language-code="{{ language.code }}">
                <i class="fi fi-{{ language.code|togb }}"></i>
                <span> &nbsp; {{ language.name_local }}</span>
            </a>
        </li>
    {% endfor %}
    </ul>
</li>