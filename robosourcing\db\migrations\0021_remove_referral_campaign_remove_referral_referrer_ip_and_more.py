# Generated by Django 4.2.13 on 2024-09-17 19:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0020_marketingcampaign_referral'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='referral',
            name='campaign',
        ),
        migrations.RemoveField(
            model_name='referral',
            name='referrer_ip',
        ),
        migrations.AddField(
            model_name='referral',
            name='path',
            field=models.CharField(default=1, max_length=255),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='Visit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('referrer_ip', models.GenericIPAddressField()),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='visits', to='db.marketingcampaign')),
            ],
        ),
        migrations.AddField(
            model_name='referral',
            name='visit',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='referrals', to='db.visit'),
            preserve_default=False,
        ),
    ]
