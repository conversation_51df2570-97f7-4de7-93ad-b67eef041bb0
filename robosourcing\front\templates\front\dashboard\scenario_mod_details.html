{% extends "front/dashboard/base.html" %}

{% load static %}
{% load i18n %}
{% load form_filters %}
{% block title %}{{ block.super }} - {% trans "Scenadio details" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{%url 'scenario_mod'%}">{% trans "Moderation Panel" %}</a></li>
          <li class="breadcrumb-item site-name active">
                {{ scenario.name }} - {% trans "Details" %}
          </li>
        </ol>
    </nav>
    <div class="row">
        
        <div class="col-md-2">
            <div class="dashboard-element">
                <h2 class="dashboard-header">{% trans "View" %}</h2>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <a href="{% url 'scenario_mod_details' scenario.sid %}" class="btn btn-sm btn-outline-primary">{% trans "Details" %}</a>
                </div>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <a href="{% url 'scenario_change_history' scenario.sid %}" class="btn btn-sm btn-primary">{% trans "Change History" %}</a>
                </div>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <a href="" class="btn btn-sm btn-primary">{% trans "Comments" %}</a>
                </div>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <a href="" class="btn btn-sm btn-primary">{% trans "Documents" %}</a>
                </div>
            </div>
        </div>

        <div class="col-md-10">
            <div class="dashboard-element">
                <h2 class="dashboard-header">{{ scenario.name }}<span class="fw-lighter">- {% trans "Details" %}</span></h2>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <div class="dashboard-body-section mb-5">
                        <h6>{% trans "GENERAL INFORMATION" %}</h6>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <p><span class="fw-bold">{% trans "version" %}:</span> {{ scenario.scenario_version }}</p>
                                {% if scenario.version_changelog %}
                                  <p><span class="fw-bold">{% trans "Changelog" %}:</span> {{ scenario.version_changelog|linebreaksbr }}</p>
                                {% endif %}
                                <p><span class="fw-bold">{% trans "author" %}:</span> {{ scenario.author }}</p>
                            </div>
                            <div class="col-6">
                                <p><span class="fw-bold">{% trans "owner" %}:</span> {{ scenario.owner }}</p>
                                <p><span class="fw-bold">{% trans "cost" %}:</span> {{ scenario.price }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-body-section mb-5">
                        <h6>{% trans "DESCRIPTION" %}</h6>
                        <hr>
                        <p>{{ scenario.description }}</p>
                    </div>
                    <div class="dashboard-body-section mb-5">
                        <h6>{% trans "SYSTEM INFORMATION" %}</h6>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <p><span class="fw-bold">{% trans "created at" %}:</span> {{ scenario.created_at }}</p>
                                <p><span class="fw-bold">{% trans "updated at" %}:</span> {{ scenario.updated_at }}</p>
                                <p><span class="fw-bold">{% trans "published at" %}:</span> {{ scenario.publish_time }}</p>
                            </div>
                            <div class="col-6">
                                <p><span class="fw-bold">{% trans "availability" %}: </span> {{ scenario.get_available_display }}</p>
                                <p><span class="fw-bold">{% trans "editable" %}: </span> {{ scenario.get_editable_display }}</p>
                                <p><span class="fw-bold">{% trans "updatable" %}: </span> {{ scenario.get_updatable_display }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-body-section mb-5">
                        <h6>{% trans "SEO Metadata" %}</h6>
                        <hr>
                        <form method="post" enctype="multipart/form-data" class="w-100" style="max-width: 600px;">
                            {% csrf_token %}
                            <input type="hidden" name="seo_form" value="1">

                            <div class="mb-3">
                                <label for="{{ seo_form.title.id_for_label }}" class="form-label fw-bold">{% trans "Title" %}</label>
                                {{ seo_form.title|add_class:"form-control" }}
                                {% if seo_form.title.errors %}
                                    <div class="text-danger small">{{ seo_form.title.errors }}</div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ seo_form.description.id_for_label }}" class="form-label fw-bold">{% trans "Description" %}</label>
                                {{ seo_form.description|add_class:"form-control" }}
                                {% if seo_form.description.errors %}
                                    <div class="text-danger small">{{ seo_form.description.errors }}</div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ seo_form.keywords.id_for_label }}" class="form-label fw-bold">{% trans "Keywords" %}</label>
                                {{ seo_form.keywords|add_class:"form-control" }}
                                {% if seo_form.keywords.errors %}
                                    <div class="text-danger small">{{ seo_form.keywords.errors }}</div>
                                {% endif %}
                                <div class="form-text">{% trans "Enter comma-separated keywords" %}</div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ seo_form.image.id_for_label }}" class="form-label fw-bold">{% trans "OpenGraph Image" %}</label>
                                {{ seo_form.image|add_class:"form-control" }}
                                {% if seo_form.image.errors %}
                                    <div class="text-danger small">{{ seo_form.image.errors }}</div>
                                {% endif %}
                            </div>

                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> {% trans "Save metadata" %}
                            </button>
                        </form>
                    </div>



                    <div class="dashboard-body-section mb-5">
                      <h6>{% trans "TAGS" %}</h6>
                      <hr>
                      <div class="row">
                        <div class="col">
                          <form method="POST">
                            {% csrf_token %}
                            <input type="hidden" name="assign_groups_form" value="1">
                            {% for group in all_groups %}
                              <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="groups" value="{{ group.id }}" id="group_{{ group.id }}" {% if group in assigned_groups %}checked{% endif %}>
                                <label class="form-check-label" for="group_{{ group.id }}">
                                  {{ group.name }}
                                </label>
                              </div>
                            {% endfor %}
                            <button type="submit" class="btn btn-primary mt-3">Zapisz grupy tagów</button>
                          </form>
                        </div>
                        <div class="col">
                          <ul>
                          {%  for tag in scenario_tags %}
                          <li style="display:inline; margin-right:10px;">
                              {{ tag.name }}
                              <form method="post"
                                    action="{% url 'detach_tag' scenario.sid tag.name %}"
                                    style="display:inline;">
                                  {% csrf_token %}
                                  <button type="submit" class="btn btn-sm btn-danger">
                                      <i class="fas fa-times"></i>
                                  </button>
                              </form>
                          </li>
                      {% empty %}
                          <li class="text-muted">{% trans "No tags yet" %}</li>
                      {% endfor %}
                        </ul>

                        </div>
                      </div>
                    </div>
                  
                  <div class="dashboard-body-section mb-5">
                      <h6>{% trans "CATEGORIES" %}</h6>
                      <hr>
                      <form method="POST">
                          {% csrf_token %}
                          <input type="hidden" name="assign_categories_form" value="1">
                          <div class="row">
                              {% for cat in available_categories %}
                              <div class="col-md-4">
                                  <div class="form-check">
                                      <input class="form-check-input" type="checkbox" name="categories" value="{{ cat.name }}" id="cat_{{ cat.name }}"
                                            {% if cat in scenario_cats %}checked{% endif %}>
                                      <label class="form-check-label" for="cat_{{ cat.name }}">{{ cat.name }}</label>
                                  </div>
                              </div>
                              {% endfor %}
                          </div>
                          <button type="submit" class="btn btn-primary mt-3">{% trans "Save categories" %}</button>
                      </form>
                  </div>


                  <div class="dashboard-body-section mb-5">
                    <h6>{% trans "OPERACJE MODERATORSKIE" %}</h6>
                    <hr>
                  
                    {% for act in actions %}
                      {% if act.status == scenario.ScenarioStatus.REJECTED %}
                        {# Formularz odrzucenia – wyświetl blokowo, z szerokim textarea #}
                        <form method="post" style="display:block; margin-bottom:1rem; margin-top:3rem; max-width:600px;">
                          {% csrf_token %}
                          <div class="mb-2">
                            <textarea
                              name="message"
                              class="form-control w-100"
                              rows="3"
                              placeholder="{% trans "Powód odrzucenia" %}"
                              required
                            ></textarea>
                          </div>
                          <input type="hidden" name="status" value="{{ act.status }}">
                          <button type="submit" class="btn {{ act.class }}">
                            <i class="{{ act.icon }}"></i> {{ act.label }}
                          </button>
                        </form>
                      {% else %}
                        {# Pozostałe akcje – inline #}
                        <form method="post" style="display:inline-block; margin-right:10px;">
                          {% csrf_token %}
                          <input type="hidden" name="status" value="{{ act.status }}">
                          <button type="submit" class="btn {{ act.class }}">
                            <i class="{{ act.icon }}"></i> {{ act.label }}
                          </button>
                        </form>
                      {% endif %}
                    {% endfor %}
                  
                    {% if not actions %}
                      <p class="text-muted">{% trans "Brak dostępnych akcji dla tego statusu." %}</p>
                    {% endif %}
                  </div>
                    <div class="dashboard-body-section mb-5">
                        <a href="{% url 'download_scenario_botie' scenario.sid %}" class="btn btn-primary" download>
                            <i class="fas fa-download"></i> {% trans "Download" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>

</script>

{% endblock %}
