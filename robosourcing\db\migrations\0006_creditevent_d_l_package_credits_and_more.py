# Generated by Django 4.1.9 on 2024-06-12 14:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0005_creditevent'),
    ]

    operations = [
        migrations.AddField(
            model_name='creditevent',
            name='d_l_package_credits',
            field=models.BigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='creditevent',
            name='d_l_subscription_credits',
            field=models.BigIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='creditevent',
            name='d_package_credits',
            field=models.BigIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='creditevent',
            name='d_subscription_credits',
            field=models.BigIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='creditevent',
            name='event_type',
            field=models.CharField(choices=[('BUY', 'BUY'), ('CHG', 'SUBSCRIPTION CHANGE'), ('TOP', 'TOP-UP'), ('FRE', 'FREEBIE'), ('USE', 'USAGE')], default='BUY', max_length=3),
        ),
    ]
