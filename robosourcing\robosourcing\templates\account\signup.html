{% extends "account/base.html" %}
{% load crispy_forms_filters %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block head_title %}{% trans "Signup" %}{% endblock %}

{% block title %}
    {{ block.super }} - {% trans "Signup" %}
{% endblock title %}

{% block content %}
<div class="container">
    <div class="row justify-content-center _text-light">
        <section class="col-md-8 col-lg-6 col-xxl-5">
            <form class="signup" id="signup_form" method="post" action="{% url 'account_signup' %}">
            {% csrf_token %}
            {% if redirect_field_value %}
                <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
            {% endif %}
                <div class="row align-items-center">
                    <div class="col-3 text-end px-4 mt-3">
                    </div>
                    <div class="col-9 mt-3">
                        <h1>{% trans "Sign Up" %}</h1>
                    </div>

                    {{ form|crispy }}

                    <div class="mt-3 text-end">
                        <button class="btn btn-primary login-button" type="submit">{% trans "Sign Up" %}</button>
                    </div>

                </div>
            </form>
            <div class="mt-5">
                <hr>
                <p>{% blocktrans %}Already have an account? Then please <a href="{{ login_url }}">sign in</a>.{% endblocktrans %}</p>
            </div>
        </section>
    </div>
</div>
{% endblock %}