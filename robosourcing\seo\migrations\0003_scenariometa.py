# Generated by Django 4.2.13 on 2025-06-05 09:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0049_alter_scenario_event_type_id'),
        ('seo', '0002_alter_staticpagemeta_page_objectmeta'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScenarioMeta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('keywords', models.CharField(blank=True, max_length=255, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='seo_images/')),
                ('scenario', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='seo_meta', to='db.scenario')),
            ],
        ),
    ]
