from django.contrib.auth.models import User
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics
from rest_framework import permissions
from rest_framework.pagination import PageNumberPagination
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON>enticated, AllowAny


from db.models import Scenario, ScenarioTag, UserStorage, Robot
from .permissions import IsOwnerOrReadOnly
from .serializers import ScenarioSerializer, UserSerializer
import logging

LOGGER = logging.getLogger('logger')


class UserList(generics.ListAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer


class UserDetail(generics.RetrieveAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer


class ScenarioListPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'size'
    max_page_size = 1000


@permission_classes([AllowAny])
class ScenarioList(generics.ListCreateAPIView):
    queryset = Scenario.objects.all()
    serializer_class = ScenarioSerializer
    pagination_class = ScenarioListPagination

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter(name='tags', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, description="Comma separated list of tags for filtering scenarios", required=False),
        openapi.Parameter(name='status', in_=openapi.IN_QUERY, type=openapi.TYPE_NUMBER, description="Scenario status", required=False, default=0)
    ])
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        queryset = Scenario.objects.all()
        return self.filtered(queryset)

    def filtered_by_tags(self, queryset):
        tags = self.request.query_params.get('tags', None)
        if tags is not None:
            tags = tags.split(',')
            queryset = queryset.filter(tags__name__in=tags).distinct()
        return queryset
    
    def filtered(self, queryset):
        status  = self.request.query_params.get('status', None)
        tags    = self.request.query_params.get('tags', None)
        if status is not None:
            queryset = queryset.filter(status=status)
        if tags is not None:
            tags = tags.split(',')
            tags_sid = ScenarioTag.objects.filter(tag__name__in=tags).values_list('sid__sid', flat=True)
            if tags:
                queryset = queryset.filter(sid__in=tags_sid).distinct()
                
        return queryset

    def perform_create(self, serializer):
        user = self.request.user
        scenario = serializer.save(owner=user)
        rid = self.request.data.get('rid')

        if rid:
            try:
                robot = Robot.objects.get(rid=rid)
                UserStorage.objects.create(user=user, sid=scenario, rid=robot)
            except Robot.DoesNotExist:
                LOGGER.error(f"Robot with ID {rid} not found.")



class ScenarioDetail(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly, IsOwnerOrReadOnly]
    queryset = Scenario.objects.all()
    serializer_class = ScenarioSerializer


