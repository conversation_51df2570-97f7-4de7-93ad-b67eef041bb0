import os
import uuid
from wsgiref.util import FileWrapper

from django.http import HttpResponse, Http404
from rest_framework.exceptions import NotFound
from rest_framework.generics import ListCreateAPIView, GenericAPIView, RetrieveAPIView, CreateAPIView
from rest_framework.mixins import UpdateModelMixin
from rest_framework.parsers import FileUploadParser

from .models import RoboMap, MapUsage
from .serializers import RoboMapFileSerializer, RoboMapSerializer, RoboMapUsageSerializer


class RoboMapList(ListCreateAPIView):
    queryset = RoboMap.objects.all()
    serializer_class = RoboMapSerializer

    def perform_create(self, serializer):
        uuid_part = str(uuid.uuid4()).split('-')[-1]  # last part of uuid
        version = "{0}-{1}".format(serializer.validated_data['app'], uuid_part)
        serializer.save(map_version=version)


class RoboMapFile(GenericAPIView, UpdateModelMixin):
    parser_classes = [FileUploadParser]
    serializer_class = RoboMapFileSerializer
    queryset = RoboMap.objects.all()
    lookup_field = 'map_version'

    def get_parser_context(self, http_request):
        self.kwargs['filename'] = "{0}.rmap".format(str(uuid.uuid4()))
        return super().get_parser_context(http_request)

    def perform_update(self, serializer):
        serializer.save(map_size=serializer.validated_data['map_file'].size, status=RoboMap.MapStatus.ACTIVE)

    def put(self, request, *args, **kwargs):
        request.data['map_file'] = request.data['file']
        return self.partial_update(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.status != RoboMap.MapStatus.ACTIVE:
            raise NotFound

        file_handle = instance.map_file.path
        filename = os.path.split(instance.map_file.name)[1]
        map_file = open(file_handle, 'rb')
        response = HttpResponse(FileWrapper(map_file), content_type='application/octet-stream')
        response['Content-Disposition'] = 'attachment; filename="{0}"'.format(filename)
        return response


class RoboMapFilter(RetrieveAPIView):
    serializer_class = RoboMapSerializer
    queryset = RoboMap.objects.all()
    known_operations = ['latest', 'biggest']

    def get(self, request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def get_object(self):
        queryset = RoboMap.objects.all().filter(app=self.kwargs['app']).filter(status=RoboMap.MapStatus.ACTIVE)
        robo_map = None

        try:
            if self.kwargs['map_version'] in self.known_operations:
                match self.kwargs['map_version']:
                    case 'latest':
                        robo_map = queryset.latest('created_at')
                    case 'biggest':
                        robo_map = queryset.latest('map_size')
            else:
                robo_map = queryset.filter(map_version=self.kwargs['map_version']).get()
        except queryset.model.DoesNotExist:
            raise NotFound("No map found for given criteria")

        return robo_map


class RoboMapReportSubmit(CreateAPIView):
    queryset = MapUsage.objects.all()
    serializer_class = RoboMapUsageSerializer

    def perform_create(self, serializer):
        robo_map = RoboMap.objects.get(map_version=self.kwargs['map_version'])
        serializer.save(robo_map=robo_map, app=self.kwargs['app'])
