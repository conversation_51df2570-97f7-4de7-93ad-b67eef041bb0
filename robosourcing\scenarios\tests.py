from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status
from db.models import Scenario, Robot, UserStorage, Hid, UserProfile
from django.urls import reverse

class PerformCreateTests(TestCase):
    def setUp(self):
        # Create a user
        self.user = User.objects.create_user(username='testuser', password='12345')
        self.user_profile, created = UserProfile.objects.get_or_create(user=self.user, defaults={'role': 'ADM'})

        # Create a robot owned by the user
        self.robot = Robot.objects.create(owner=self.user, name='Test Robot')

        # Authenticate the user for this session
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # URL do testowania
        self.url = reverse('scenario-list')  

    def test_create_user_storage_with_valid_data(self):
        data = {
            'name': 'Test Scenario',
            'description': 'Test Description',
            'author': 'Test Author',
            'scenario': '{}',
            'rid': str(self.robot.rid)  # Użyj rid zamiast id
        }

        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(UserStorage.objects.filter(user=self.user, rid=self.robot).exists())

    def test_create_user_storage_without_robot(self):
        data = {
            'name': 'Test Scenario',
            'description': 'Test Description',
            'author': 'Test Author',
            'scenario': '{}'
        }

        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertFalse(UserStorage.objects.filter(user=self.user).exists())

# Uruchomienie testów
if __name__ == '__main__':
    TestCase.main()