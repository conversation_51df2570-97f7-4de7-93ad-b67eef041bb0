from django.db import transaction
from django.core.files.storage import default_storage
import base64
from django.core.files.base import ContentFile
from api.utils.exceptions import robo_exception_handler
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from db.models import File, FileCategory, FileKeyword, KeywordsHash, Scenario
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from api.serializers import AttachmentMetadataSerializer, AttachmentSerializer, DetailedResponseSerializer, FileHashSerializer, KeywordHashSerializer, ListAttachmentMetadataSerializer, ListAttachmentSerializer, ListKeywordsHashSerializer, ProgramSerializer, UploadScenarioAttachmentSerializer




@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['post'],
                     request_body=UploadScenarioAttachmentSerializer,
                     responses={status.HTTP_200_OK: DetailedResponseSerializer, '>=400': DetailedResponseSerializer},)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def upload_attachment(request, *args, **kwargs):
    serializer = UploadScenarioAttachmentSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    file_content = ContentFile(base64.b64decode(serializer.validated_data['file']))
    file_hash = serializer.validated_data['file_hash']
    if not default_storage.exists(file_hash):
        file_name = default_storage.save(file_hash, file_content)

    file, created = File.objects.update_or_create(
        file_hash       = serializer.validated_data['file_hash'],
        scenario        = Scenario.objects.get(pk=serializer.validated_data['scenario'])
    )
    file.export_name     = serializer.validated_data['export_name']
    file.file_type       = serializer.validated_data['file_type']
    if serializer.validated_data.get('keywords_hash', None):
        kw_hash_obj, created = KeywordsHash.objects.get_or_create(pk=serializer.validated_data['keywords_hash'])
        file.keywords_hash = kw_hash_obj
    file.save()
    if not created: #TODO
        file.categories.clear()
        file.keywords.clear()

    if serializer.validated_data.get('categories', None):
        for category in serializer.validated_data['categories']:
            category_obj, created = FileCategory.objects.get_or_create(pk=category)
            file.categories.add(category_obj)
    if serializer.validated_data.get('keywords', None):
        for keyword in serializer.validated_data['keywords']:
            keyword_obj, created = FileKeyword.objects.get_or_create(pk=keyword)
            file.keywords.add(keyword_obj)  

    response = DetailedResponseSerializer(detail='Attachment uploaded!')
    return Response(response.data, status=status.HTTP_200_OK)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['get'],
                     query_serializer=ListKeywordsHashSerializer,
                     responses={status.HTTP_200_OK: ListAttachmentMetadataSerializer, '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def get_attachment_list(request, *args, **kwargs):
    keywords_hashes = []
    querry_keywords_hashes = request.GET.getlist('keywords_hashes')
    for k in querry_keywords_hashes:    #in case '...?keywords_hashes=123,345...' instead of proper '...?keywords_hashes=123&keywords_hashes=345...'
        k_list = k.split(',')
        keywords_hashes.extend(k_list)

    print(keywords_hashes)

    metadatas = File.objects.filter(keywords_hash__in=keywords_hashes)
    if not metadatas.exists():
        response = DetailedResponseSerializer(detail='File does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    else:          
        response = ListAttachmentMetadataSerializer(metadatas)
        return Response(response.data, status=status.HTTP_200_OK)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['get'],
                     responses={status.HTTP_200_OK: ListAttachmentMetadataSerializer, '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def get_attachment_list_by_sid(request, sid, *args, **kwargs):
    
    if not Scenario.objects.filter(pk=sid).exists():
         response = DetailedResponseSerializer(detail='Scenario does not exists!')
         return Response(response.data, status=status.HTTP_409_CONFLICT)
    files = File.objects.filter(scenario=sid)
    if not files.exists():
        response = DetailedResponseSerializer(detail='Attachment does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    else:
        print(files)
        response = AttachmentMetadataSerializer(files, many=True)
        return Response(response.data, status=status.HTTP_200_OK)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['get'],
                     query_serializer=ListAttachmentSerializer,
                     responses={status.HTTP_200_OK: ListAttachmentSerializer, '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def get_file_list(request, *args, **kwargs):
    file_hashes = []
    querry_file_hashes = request.GET.getlist('file_hashes')
    for f in querry_file_hashes:    #in case '...?file_hashes=123,345...' instead of proper '...?file_hashes=123&file_hashes=345...'
        f_list = f.split(',')
        file_hashes.extend(f_list)
    files = File.objects.filter(file_hash__in=file_hashes)
    if not files.exists():
        response = DetailedResponseSerializer(detail='File does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    else:
        for f in files:
            print('@@@')
            f_data = default_storage.open(f.file_hash)
            f.file = base64.b64encode(f_data.read()).decode('ascii')
            f_data.close()           
        response = AttachmentSerializer(files, many=True)
        return Response(response.data, status=status.HTTP_200_OK)


@swagger_auto_schema(tags=["Robots", "BotieApp"], methods=['get'],
                     responses={status.HTTP_200_OK: AttachmentSerializer, '>=400': DetailedResponseSerializer},)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@robo_exception_handler
@transaction.atomic
def get_file(request, file_hash, *args, **kwargs):

    file = File.objects.filter(file_hash=file_hash).first()
    if not file:
        response = DetailedResponseSerializer(detail='File does not exists!')
        return Response(response.data, status=status.HTTP_404_NOT_FOUND)
    else:
        f_data = default_storage.open(file_hash)
        file.file = base64.b64encode(f_data.read()).decode('ascii')
        f_data.close()                     
        response = AttachmentSerializer(file)
        return Response(response.data, status=status.HTTP_200_OK)