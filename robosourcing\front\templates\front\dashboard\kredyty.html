{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load crispy_forms_filters %}
{% load theme %}
{% load split %}

{% block title %}{{ block.super }} - {% trans "Credits" %}{% endblock %}

{% block extra_content_1 %}

<div class="container">

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item site-name active">{% trans "Credits" %}</li>
        </ol>
    </nav>

    <div class="dashboard-element">
        <div class="row">
            <div class="col-12 col-xxl-5">
                {% if credit %}
                <h2 class="db-section-title">{% trans 'Your Credits' %} ({% trans 'last update' %}:
                    {{ credit.update_time|date:"DATETIME_FORMAT" }})</h2>
                <hr>
                <div class="table-responsive">
                    <table class="table my-custom-table">
                        <thead>
                            <tr>
                                <th></th>
                                <th class="text-center">{% trans "Bought" %}<div data-bs-toggle="popover" data-bs-trigger="hover" data-bs-placement="bottom" title="{% trans 'Hint' %}" data-bs-content="{% trans 'Credits received as part of the purchased subscription and purchased in packages' %}"><i class="fa fa-info-circle"></i></div></th>
                                <th class="text-center">{% trans "Assigned" %}<div data-bs-toggle="popover" data-bs-trigger="hover" data-bs-placement="bottom" title="{% trans 'Hint' %}" data-bs-content="{% trans 'The amount of credits assigned to robots to enable them to work offline' %}"><i class="fa fa-info-circle"></i></div></th>
                                <th class="text-center">{% trans "Locked" %}<div data-bs-toggle="popover" data-bs-trigger="hover" data-bs-placement="bottom" title="{% trans 'Hint' %}" data-bs-content="{% trans 'The amount of credits locked by robots to perform current tasks according to scenario' %}"><i class="fa fa-info-circle"></i></div></th>
                                <th class="text-center">{% trans "Available" %}<div data-bs-toggle="popover" data-bs-trigger="hover" data-bs-placement="bottom" title="{% trans 'Hint' %}" data-bs-content="{% trans 'The remaining amount of credits available to all robots' %}"><i class="fa fa-info-circle"></i></div></th>
                            </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{% trans "From subscription" %}</td>
                            <td class="text-center">{{ credit.subscription_credits|add:credit.l_subscription_credits|add:robot_credit.cached_credits|add:robot_credit.l_cached_credits|floatformat:"0g" }}</td>
                            <td class="text-center">{{ robot_credit.cached_credits|add:robot_credit.l_cached_credits }}</td>
                            <td class="text-center">{{ credit.l_subscription_credits|add:robot_credit.l_cached_credits|floatformat:"0g" }}</td>
                            <td class="text-center">{{ credit.subscription_credits|floatformat:"0g" }}</td>
                        </tr>
                        <tr>
                            <td>{% trans "From packages" %}</td>
                            <td class="text-center">{{ credit.package_credits|add:credit.l_package_credits|floatformat:"0g" }}</td>
                            <td class="text-center">-</td>
                            <td class="text-center">{{ credit.l_package_credits|floatformat:"0g" }}</td>
                            <td class="text-center">{{ credit.package_credits|floatformat:"0g" }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "TOTAL" %}</th>
                            <th class="text-center">{{ credit.credits|add:credit.locked_credits|add:robot_credit.cached_credits|add:robot_credit.l_cached_credits|floatformat:"0g" }}</th>
                            <th class="text-center">{{ robot_credit.cached_credits|add:robot_credit.l_cached_credits }}</th>
                            <th class="text-center">{{ credit.locked_credits|add:robot_credit.l_cached_credits|floatformat:"0g" }}</th>
                            <th class="text-center">{{ credit.credits|floatformat:"0g" }}</th>
                        </tr>
                        </tbody>
                    </table>
                </div>
                {% else %}
                <h2 class="db-section-title">{% trans 'Your Credits' %}</h2>
                <hr>
                <div class="table-responsive">
                    <table class="table my-custom-table">
                        <thead>
                            <tr>
                                <th>{% trans 'Your Credits' %}</th>
                                <th>{% trans "Bought" %}</th>
                                <th>{% trans "Locked" %}</th>
                                <th>{% trans "Available" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{% trans "TOTAL" %}</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                {% endif %}

                <hr>

                <div class="row">
                    <div class="col-auto">
                        <h5>{% trans 'Chart period' %}:</h5>
                    </div>
                    <div class="col">
                        <form name="changeform" action="{% url 'credits' %}" method="post">
                        {% csrf_token %}
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="chart_period" id="period_week" value="week"
                                 onclick="document.changeform.submit()">
                          <label class="form-check-label" for="period_week">{% trans 'week' %}</label>
                        </div>
                         <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="chart_period" id="period_month" value="month"
                                 onclick="document.changeform.submit()">
                          <label class="form-check-label" for="period_month">{% trans 'month' %}</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="chart_period" id="period_quarter" value="quarter"
                                 onclick="document.changeform.submit()">
                          <label class="form-check-label" for="period_quarter">{% trans '3 months' %}</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="chart_period" id="period_half" value="half"
                                 onclick="document.changeform.submit()">
                          <label class="form-check-label" for="period_half">{% trans 'half a year' %}</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="chart_period" id="period_year" value="year"
                                 onclick="document.changeform.submit()">
                          <label class="form-check-label" for="period_year">{% trans 'year' %}</label>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-12 col-xxl-7">
                <canvas id="myChart"></canvas>
            </div>
        </div>
    </div>



</div>

<script>
var chart_period = '{{ chart_period }}';

{% if request|theme in 'dark auto-dark'|split:' ' %}
var balance_color = '#84b436'; //green
var top_up_color = '#a580b2';  //purple
var buy_pkg_color = '#666666'; //gray
var buy_sub_color = '#fbab18'; //yellow
var chg_sub_color = '#c07e03'; //yellow dimmed
{% else %}
var balance_color = '#84b436'; //green
var top_up_color = '#0b8bcc'; //blue
var buy_pkg_color = '#666666'; //gray
var buy_sub_color = '#b72f2f'; //red
var chg_sub_color = '#7D2020'; //red dimmed
{% endif %}

const Utils = new ChartJSUtils();
const DATA_COUNT = {{ days }};
const NUMBER_CFG = {count: DATA_COUNT, min: 0};

const labels = [];

for (let i = 0; i < DATA_COUNT; ++i) {
  labels.push(Utils.newDate(i - DATA_COUNT + 1));
}

var point_rad, categ_perc, bar_perc;
if (chart_period == 'week') {
    line_width = 2;
    point_rad = 3;
    categ_perc = 0.2;
    bar_perc = 1;
} else if (chart_period == 'month') {
    line_width = 2;
    point_rad = 3;
    categ_perc = 0.5;
    bar_perc = 1;
} else if (chart_period == 'quarter') {
    line_width = 2;
    point_rad = 0;
    categ_perc = 0.8;
    bar_perc = 1;
} else if (chart_period == 'half') {
    line_width = 1;
    point_rad = 0;
    categ_perc = 1;
    bar_perc = 1;
} else {
    line_width = 1;
    point_rad = 0;
    categ_perc = 1;
    bar_perc = 1;
}

const data = {
  labels: labels,
  datasets: [
  {
    type: 'line',
    label: '{% trans 'Credits balance' %}',
    backgroundColor: balance_color,
    borderColor: balance_color,
    fill: false,
    borderWidth: line_width,
    pointRadius: point_rad,
    pointHoverRadius: 5,
    data: {{ chart_data.balance|safe }},
  },
  {
    type: 'bar',
    label: '{% trans 'Subscription purchases' %}',
    backgroundColor: buy_sub_color,
    borderColor: buy_sub_color,
    categoryPercentage: categ_perc,
    barPercentage: bar_perc,
    data: {{ chart_data.buy_sub|safe }},
  },
  {
    type: 'bar',
    label: '{% trans 'Subscription changes' %}',
    backgroundColor: chg_sub_color,
    borderColor: chg_sub_color,
    categoryPercentage: categ_perc,
    barPercentage: bar_perc,
    data: {{ chart_data.chg_sub|safe }},
  },
  {
    type: 'bar',
    label: '{% trans 'Top-ups from subscriptions' %}',
    backgroundColor: top_up_color,
    borderColor: top_up_color,
    categoryPercentage: categ_perc,
    barPercentage: bar_perc,
    data: {{ chart_data.top_up|safe }},
  },
  {
    type: 'bar',
    label: '{% trans 'One-time package purchases' %}',
    backgroundColor: buy_pkg_color,
    borderColor: buy_pkg_color,
    categoryPercentage: categ_perc,
    barPercentage: bar_perc,
    data: {{ chart_data.buy_pkg|safe }},
  }
  ]
};

const config = {
  type: 'line',
  data: data,
  options: {
    plugins: {
      title: {
        text: 'Chart.js Combo Time Scale',
        display: false
      }
    },
    /*scales: {
      x: {
        type: 'time',
        display: true,
        offset: true,
        ticks: {
          source: 'data'
        },
        time: {
          unit: 'day'
        },
      },
      //adapters: {
      //  date: {
      //    locale: enUS,
      // },
      //},
    },*/
  },
};

function init_form() {
    if (chart_period == 'week') $('#period_week').attr('checked','checked');
    else if (chart_period == 'month') $('#period_month').attr('checked','checked');
    else if (chart_period == 'quarter') $('#period_quarter').attr('checked','checked');
    else if (chart_period == 'half') $('#period_half').attr('checked','checked');
    else $('#period_year').attr('checked','checked');
}

init_form();
var ctx = document.getElementById('myChart');
var chart = new Chart(ctx, config);

document.addEventListener("DOMContentLoaded", function () {

    //init popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
      return new bootstrap.Popover(popoverTriggerEl)
    });

});
</script>
{% endblock %}
