# Naprawa problemu nieskończonych pętli przekierowań SEO

## Problem
Google Search Console pokazywał 145,000 niezidentyfikowanych stron z malformowanymi URL-ami zawierającymi nieskończone pętle przekierowań w systemie uwierzytelniania Django.

### Przykład problematycznego URL:
```
/accounts/github/login/?process=login&next=%2Faccounts%2Flogin%2F%3Fnext%3D%2Faccounts%2Fsignup%2F%253Fnext%253D%25252Faccounts%25252Flogin%25252F...
```

## Przyczyny problemu

1. **Brak ustawienia `LOGIN_URL`** w settings.py - Django używał domyślnego `/accounts/login/`
2. **Niespójne parametry `login_url`** w dekoratorach `@login_required` i `@group_required`
3. **Potencjalne pętle w menu.html** z parametrem `?next={{ request.get_full_path|urlencode }}`

## Rozwiązanie

### 1. Dodanie LOGIN_URL do settings.py
```python
# robosourcing/settings.py
LOGIN_URL = 'account_login'  # Ustawienie domyślnego URL logowania
```

### 2. Utworzenie middleware zapobiegającego pętlom
```python
# robosourcing/middleware.py
class RedirectLoopPreventionMiddleware(MiddlewareMixin):
    """Middleware zapobiegający nieskończonym pętlom przekierowań w URL-ach logowania"""
    
    def process_request(self, request):
        # Sprawdź czy to URL logowania z parametrem next
        if '/accounts/' in request.path and 'next=' in request.GET.get('next', ''):
            next_url = request.GET.get('next', '')
            
            # Dekoduj URL aby sprawdzić głębokość zagnieżdżenia
            decoded_url = unquote(next_url)
            
            # Policz ile razy występuje 'accounts' w URL
            accounts_count = decoded_url.count('accounts')
            
            # Jeśli więcej niż 3 wystąpienia, przekieruj na stronę główną
            if accounts_count > 3:
                return HttpResponseRedirect(reverse('index'))
            
            # Sprawdź długość URL - jeśli przekracza 500 znaków, przekieruj
            if len(request.get_full_path()) > 500:
                return HttpResponseRedirect(reverse('index'))
        
        return None
```

### 3. Dodanie middleware do MIDDLEWARE w settings.py
```python
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'robosourcing.middleware.RedirectLoopPreventionMiddleware',  # Dodane na początku
    # ... pozostałe middleware
]
```

### 4. Usunięcie niespójnych parametrów login_url
Usunięto wszystkie wystąpienia `login_url='login'` i `login_url='home'` z dekoratorów, polegając na globalnym ustawieniu `LOGIN_URL`.

### 5. Utworzenie robots.txt
```
# static/robots.txt
User-agent: *

# Blokuj wszystkie URL-e z parametrami next zawierającymi przekierowania
Disallow: /accounts/*/login/?*next=*accounts*
Disallow: /accounts/*/signup/?*next=*accounts*

# Blokuj długie URL-e z wielokrotnymi przekierowaniami
Disallow: /*?*next=*%252F*
Disallow: /*?*next=*%2525*

# Blokuj wszystkie URL-e z wielokrotnymi parametrami next
Disallow: /*next=*next=*

# Blokuj URL-e z zakodowanymi parametrami
Disallow: /*%253F*
Disallow: /*%2525*

# Blokuj prywatne sekcje
Disallow: /admin/
Disallow: /profiles/
Disallow: /api/
Disallow: /accounts/password/
Disallow: /accounts/email/

# Pozwól na główne strony
Allow: /
Allow: /botbook/
Allow: /store/
Allow: /scenarios/
Allow: /maps/

# Sitemap
Sitemap: https://www.botie.pl/sitemap.xml
```

### 6. Dodanie URL dla robots.txt
```python
# robosourcing/urls.py
def robots_txt(request):
    """Serwuje robots.txt z katalogu static"""
    robots_path = os.path.join(settings.BASE_DIR, 'static', 'robots.txt')
    try:
        with open(robots_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return HttpResponse(content, content_type='text/plain')
    except FileNotFoundError:
        return HttpResponse('User-agent: *\nDisallow:', content_type='text/plain')

urlpatterns = [
    # ... inne URL-e
    path('robots.txt', robots_txt, name='robots_txt'),
]
```

## Następne kroki

1. **Wdrożenie zmian** na serwer produkcyjny
2. **Monitorowanie** Google Search Console przez następne tygodnie
3. **Zgłoszenie URL-i do usunięcia** w Google Search Console dla problematycznych stron
4. **Sprawdzenie logów serwera** pod kątem podobnych problemów

## Testowanie

Po wdrożeniu należy przetestować:
- Czy logowanie działa poprawnie
- Czy nie ma pętli przekierowań
- Czy robots.txt jest dostępny pod `/robots.txt`
- Czy middleware poprawnie blokuje długie URL-e

## Monitoring

Regularnie sprawdzać:
- Google Search Console - liczba niezidentyfikowanych stron
- Logi serwera - błędy 500/404 związane z URL-ami logowania
- Performance - czy middleware nie wpływa negatywnie na wydajność
