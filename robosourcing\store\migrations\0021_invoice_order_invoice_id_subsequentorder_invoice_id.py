# Generated by Django 4.1.9 on 2024-08-04 21:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0020_alter_recurringpayment_last_payment_date_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('issue_time', models.DateTimeField(auto_now_add=True)),
                ('number', models.CharField(max_length=50)),
                ('system', models.CharField(choices=[('SYS', 'Systim')], default='SYS', max_length=3)),
                ('ext_id', models.PositiveIntegerField()),
            ],
        ),
        migrations.AddField(
            model_name='order',
            name='invoice_id',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='store.invoice'),
        ),
        migrations.AddField(
            model_name='subsequentorder',
            name='invoice_id',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='store.invoice'),
        ),
    ]
