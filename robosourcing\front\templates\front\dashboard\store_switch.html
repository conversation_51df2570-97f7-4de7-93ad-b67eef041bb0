{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load divide %}
{% load theme %}
{% load split %}
{% load reldelta %}

{% block title %}{{ block.super }} - {% trans "Change Subscriptons" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{%url 'store'%}">{% trans "Store" %}</a></li>
          <li class="breadcrumb-item site-name active">{% trans "Subscription change" %}</li>
        </ol>
    </nav>
    <div class="dashboard-element">

        <!-- <h1>{% trans "Subscription change" %}</h1> -->
        <h3 class="db-section-title">{% blocktrans %}You can adjust the number of credits topped up each month to your needs.
            We offer the following options for increasing it for an additional fee.{% endblocktrans %}</h3>

        <hr>

        <form name="changeform" action="{% url 'store_switch' %}" method="post">
        {% csrf_token %}
        <input type="hidden" name="operation" value="change">

            <div class="row">

                <div class="col-md-4">
                    <h3>{% trans "Current subscription" %}</h3>
                    <p>{% trans "Type" %}: {% trans actual_sub.type %} <br>
                    {% trans "Monthly top-up" %}: {{ actual_sub.credits }}<br>
                    {% trans "Subscription started" %}: {{ actual_sub.start|date:"DATE_FORMAT" }}<br>
                    {% trans "Next top-up" %}: {{ actual_sub.next_topup|date:"DATE_FORMAT" }}<br>
                    {% trans "Next payment" %}: {{ actual_sub.next_payment|date:"DATE_FORMAT" }}<br>
                    {% trans "To pay" %}: {{ actual_sub.amount|floatformat:"2g" }} zł<br>
                    {% trans "Payment method" %}: {{ actual_sub.payment_method }}<br>
                    {% trans "Payment details" %}: {{ actual_sub.payment_details }}</p>
                    {% if actual_sub.card_expired or actual_sub.card_expires_soon or True %}
                    <div class="card mb-3" style="max-width: 25rem;">
                      <div class="card-header"><i class="fa fa-warning text-danger"></i> {% trans "Warning" %}</div>
                      <div class="card-body">
                        <h5 class="card-title text-primary">{% if actual_sub.card_expired %}{% trans "Your payment card expired" %}{% else %}{% trans "Your payment card expires soon" %}{% endif %}</h5>
                        <p class="card-text">{% trans "Please provide new card details" %} <a href="{% url 'store_update' %}">{% trans "here" %}</a>.</p>
                      </div>
                    </div>
                    {% endif %}
                </div>

                <div class="col-md-8">
                    {% if not actual_sub.locked_for_changes %}
                    <h3>{% trans "Requested change" %}</h3>
                    <label class="form-label">{% trans "Subscription type" %}:</label>
                    <div>
                        <select class="form-select form-select-sm w-auto" name="subscription_type" onchange="update_form(true)">
                            <option value="monthly">{% trans "monthly" %}</option>
                            <option value="annual">{% trans "annual" %}</option>
                        </select>
                    </div>
                    <label class="form-label mt-3">{% trans "Monthly top-ups" %}:</label>
                    <div>
                        <select class="form-select form-select-sm w-auto" id="credits_number" name="credits_number" onchange="update_form()"></select>
                    </div>
                    <label class="form-label mt-3">{% trans "Change mode" %}:</label>
                    <div class="col-auto">
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="change_mode" id="change_mode_with_end" value="end" checked onclick="update_form()">
                          <label class="form-check-label" for="change_mode_with_end">{% trans "with the end of current period" %}</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="change_mode" id="change_mode_immediate" value="immediate" onclick="update_form()">
                          <label class="form-check-label" for="change_mode_immediate">{% trans "immediate" %}</label>
                        </div>
                    </div>
                    {% else %}
                    <div class="card mb-3" style="max-width:25rem;">
                      <div class="card-header"><i class="fa fa-info text-info"></i> {% trans "Hint" %}</div>
                      <div class="card-body">
                        <h5 class="card-title text-primary">{% trans "Subscription change is in progress" %}</h5>
                        <p class="card-text">{% trans "You can't make any changes until the process is completed." %}</p>
                      </div>
                    </div>
                    {% endif %}
                </div>

            </div>

            <hr class="mt-3">

            {% if not actual_sub.locked_for_changes %}
            <div class="custom_switch row align-items-center">
                <div class="col text-end text-muted">{% trans "current subscription" %}</div>
                <div class="col col-auto">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault" style="transform: scale(1.5); margin-left: -2.3em" onchange="toggleChart()">
                    </div>
                </div>
                <div class="col text-muted">{% trans "after change" %}</div>
            </div>
            {% endif %}

            <div class="chart-container mt-3">
                <canvas id="myChart"></canvas>
            </div>

            <hr class="mt-3">

            {% if not actual_sub.locked_for_changes %}
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <input type="button" class="btn btn-secondary w-auto ms-auto" onclick="unsubscribe()" value="{% trans 'Unsubscribe' %}"> ({% trans 'with end of period' %})
                </div>
                <div class="col-lg-6 text-end">
                    (<span id="supplement_info"></span><span id="supplement_amount"></span>) <input type="submit" id="btn_submit" class="btn btn-primary w-auto ms-auto" value="{% trans 'Request a change' %}">
                </div>
            </div>
            {% endif %}

        </form>

    </div>

    <div class="row mt-5">
        <div class="col">
            <h3>{% trans "Changes history" %}</h3>
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Event time" %}</th>
                        <th class="text-center">{% trans "Event type" %}</th>
                        <th class="text-center">{% trans "Product" %}</th>
                        <th class="text-center">{% trans "Mode" %}</th>
                        <th class="text-center">{% trans "Status" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for event in events %}
                    <tr>
                        <td>{{ event.event_time|date:"DATETIME_FORMAT" }}</td>
                        <td class="text-center">{{ event.get_event_type_display }}</td>
                        <td class="text-center">{{ event.product.get_type_display }} {{ event.product.value }}</td>
                        <td class="text-center">{% if event.change_mode %}{{ event.get_change_mode_display }}{% endif %}</td>
                        <td class="text-center{% if sub.status == 'COMPLETED' %} text-success{% elif sub.status == 'CANCELED' %} text-danger{% else %} text-primary{% endif %}">{{ event.status }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

</div>

<script>
var locked_for_changes = '{{ actual_sub.locked_for_changes }}';
var current_type = '{{ actual_sub.type }}';
var current_amount = {{ actual_sub.amount }};
var credits_number = {{ actual_sub.credits }};
var year = parseInt('{{ actual_sub.chart_start|date:"Y" }}');
var month = parseInt('{{ actual_sub.chart_start|date:"n" }}');
var day = parseInt('{{ actual_sub.chart_start|date:"d" }}');
var current_month = {% now "n" %};
var current_day = {% now "d" %};
var diff_months = {{ actual_sub.diff_months }};
var diff_months_float = {{ actual_sub.diff_months_float }};
var diff_months_from_end = {{ actual_sub.diff_months_from_end }};

var products = new Array();
{% for prod in products %}
if (!products['{{ prod.value }}']) products['{{ prod.value }}'] = {'monthly': null, 'annual': null};
products['{{ prod.value }}']['{% if prod.type == 'SUB' %}monthly{% else %}annual{% endif %}'] = parseFloat('{{ prod.price }}'.replace(',','.'));
{% endfor %}

{% if request.LANGUAGE_CODE == 'pl' %}
var months = ['Sty', 'Lut', 'Mar', 'Kwi', 'Maj', 'Cze', 'Lip', 'Sie', 'Wrz', 'Paź', 'Lis', 'Gru'];
{% else %}
var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
{% endif %}

var prev = null;

{% if request|theme in 'dark auto-dark'|split:' ' %}
var bar_color = '#84b436';
var hl_color_top_axis = '#d0d0d0';
var hl_color_bottom_axis = '#a580b2';
var ticks_color = '#303030';
var hl_color_change_on_axis = '#fbab18';
{% else %}
var bar_color = '#84b436';
var hl_color_top_axis = '#303030';
var hl_color_bottom_axis = '#0b8bcc';
var ticks_color = '#d0d0d0';
var hl_color_change_on_axis = '#b72f2f';
{% endif %}

function trim_last_month_day(day, month, year) {
    nums = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    if (year%4 == 0) nums[1] = 29;
    if (day > nums[month]) day = nums[month];
    return day;
}

function calculate_supplement(type, change=null, new_monthly_amount=0, new_annual_amount=0) {
    supplement = 0;
    if (change == 'immediate') {
        if (type == 'monthly') {
            supplement = new_monthly_amount - current_amount;
        } else {
            if (current_type == 'monthly') {
                supplement = new_annual_amount - current_amount;
            } else {
                supplement = Math.round((new_annual_amount - current_amount) * (1 - diff_months/12) * 100) / 100;
            }
        }
    }
}

function update_chart(type, change=null, new_credits_number=0, new_monthly_amount=0, new_annual_amount=0) {

    var ticks_callback_dates = function(value, index, ticks) {
        var year_annotation = year+Math.floor(value/12);
        if (year_annotation != prev) {
            prev=year_annotation;
            year_annotation=' '+year_annotation;
        } else year_annotation='';
        return trim_last_month_day(day, (value+month-1)%12, year+Math.floor(value/12))+' '+months[(value+month-1)%12]+year_annotation;
    }
    if (type == 'monthly') {
        if (current_type == 'annual') {
            var ticks_max = 12;
            var top_step_size = 12;
        } else {
            var ticks_max = 12;
            var top_step_size = 1;
        }
        var top_ticks_callback_amounts = function(value, index, ticks) {
            var first_change = (12 + current_month - month)%12 - (current_day < day ? 1 : 0);
            if (change == 'immediate') {
                if (value == first_change) {
                    return current_amount+(supplement>0 ? ' + ' + supplement : '')+' '+'zł';
                } else if (value > first_change) {
                    return new_monthly_amount+' '+'zł';
                }
            } else if (change == 'end') {
                if (current_type == 'annual') {
                    //change from annual to monthly with period end
                    if (value == 12) {
                        return new_monthly_amount+' '+'zł';
                    } else if (value > 0) {
                        return '';
                    }
                } else {
                    if (value > first_change) {
                        return new_monthly_amount+' '+'zł';
                    }
                }
            }
            return current_amount+' '+'zł';
        }
        //show new values of credit top-ups
        var bottom_ticks_callback_credits = function(value, index, ticks) {
            var first_change = (12 + current_month - month)%12 - (current_day < day ? 1 : 0);
            if (change == 'immediate') {
                if (value == first_change) {
                    return credits_number+((new_credits_number-credits_number)>0 ? ' + ' + (new_credits_number-credits_number) : '');
                } else if (value > first_change) {
                    return new_credits_number;
                }
            } else if (change == 'end') {
                 if (current_type == 'annual') {
                    //change from annual to monthly with period end
                    if (value == 12) {
                        return new_credits_number;
                    }
                } else {
                    if (value > first_change) {
                        return new_credits_number;
                    }
                }
            }
            return credits_number;
        }
        //highlite changed credit top-ups and amounts
        var first_change = null;
        if (change == 'immediate') {
            first_change = (12 + current_month - month)%12 - (current_day < day ? 1 : 0);
        } else if (change == 'end') {
            if (current_type == 'annual') {
                //change from annual to monthly with period end
                first_change = 12;
            } else {
                first_change = (12 + current_month - month)%12 - (current_day < day ? 1 : 0) + 1;
            }
        }
        var coloring_function_amounts = (c) => {if(first_change !== null && c['tick']['value'] >= first_change) return hl_color_change_on_axis; else return hl_color_top_axis;}
        var coloring_function_credits = (c) => {if(first_change !== null && c['tick']['value'] >= first_change) return hl_color_change_on_axis; else return hl_color_bottom_axis;}
    } else {
        if (current_type == 'monthly') {
            var ticks_max = 12;
            var top_step_size = 1;
        } else {
            var ticks_max = 12;
            var top_step_size = 12;
        }
        var top_ticks_callback_amounts = function(value, index, ticks) {
            if (change == 'immediate') {
                var first_change = (12 + current_month - month)%12 - (current_day < day ? 1 : 0);
                if (current_type == 'monthly') {
                    //change from monthly to annual immediatelly
                    if (value == first_change) {
                        return current_amount+(supplement>0 ? ' + ' + supplement : '')+' '+'zł';
                    } else if (value > first_change) {
                        return '';
                    }
                } else {
                    if (value == 0) {
                        return current_amount+(supplement>0 ? ' + ' + supplement : '')+' '+'zł';
                    } else if (value == 12) {
                        return new_annual_amount+' '+'zł';
                    }
                }
            } else if (change == 'end') {
                var first_change = (12 + current_month - month)%12 - (current_day < day ? 1 : 0) + 1;
                if (current_type == 'monthly') {
                    //change from monthly to annual with period end
                    if (value == first_change) {
                        return new_annual_amount+' '+'zł';
                    } else if (value > first_change) {
                        return '';
                    }
                } else {
                    if (value == 12) {
                        return new_annual_amount+' '+'zł';
                    }
                }
            }
            return current_amount+' '+'zł';
        }
        var bottom_ticks_callback_credits = function(value, index, ticks) {
            if (change == 'immediate') {
                var first_change = (12 + current_month - month)%12 - (current_day < day ? 1 : 0);
                if (current_type == 'monthly') {
                    //change from monthly to annual immediatelly
                    if (value == first_change) {
                        return credits_number+((new_credits_number-credits_number)>0 ? ' + ' + (new_credits_number-credits_number) : '');
                    } else if (value > first_change) {
                        return new_credits_number;
                    }
                } else {
                    if (value == first_change) {
                        return credits_number+((new_credits_number-credits_number)>0 ? ' + ' + (new_credits_number-credits_number) : '');
                    } else if (value > first_change) {
                        return new_credits_number;
                    }
                }
            } else if (change == 'end') {
                var first_change = (12 + current_month - month)%12 - (current_day < day ? 1 : 0) + 1;
                if (current_type == 'monthly') {
                    //change from monthly to annual with period end
                    if (value >= first_change) {
                        return new_credits_number;
                    }
                } else {
                    if (value == 12)
                        return new_credits_number;
                }
            }
            return credits_number;
        }
        //highlite changed credit top-ups and amounts
        var first_change_amounts = null;
        var first_change_credits = null;
        if (change == 'immediate') {
            //change from monthly to annual immediatelly
            if (current_type == 'monthly') {
                first_change_amounts = (12 + current_month - month)%12 - (current_day < day ? 1 : 0);
                first_change_credits = (12 + current_month - month)%12 - (current_day < day ? 1 : 0);
            } else {
                first_change_amounts = 0;
                first_change_credits = (12 + current_month - month)%12 - (current_day < day ? 1 : 0);
            }
        } else if (change == 'end') {
            //change from monthly to annual with period end
            if (current_type == 'monthly') {
                first_change_amounts = (12 + current_month - month)%12 - (current_day < day ? 1 : 0) + 1;
                first_change_credits = (12 + current_month - month)%12 - (current_day < day ? 1 : 0) + 1;
            } else {
                first_change_amounts = 12;
                first_change_credits = 12;
            }
        }
        var coloring_function_amounts = (c) => {if(first_change_amounts !== null && c['tick']['value'] >= first_change_amounts) return hl_color_change_on_axis; else return hl_color_top_axis;}
        var coloring_function_credits = (c) => {if(first_change_credits !== null && c['tick']['value'] >= first_change_credits) return hl_color_change_on_axis; else return hl_color_bottom_axis;}
    }
    var ctx = document.getElementById('myChart');
    var labels = [''];
    var data = {
        labels: labels,
        datasets: [{
            label: labels,
            xAxisID: 'A',
            data: [diff_months_float],
            backgroundColor: [
              bar_color,
            ],
            borderColor: [
              'rgba(255, 255, 255, 0)',
            ],
            borderWidth: 1,
            barThickness: 10,
            maxBarThickness: 10
        }/*,{
            label: labels,
            xAxisID: 'B',
            data: [diff_months_float],
            backgroundColor: [
              bar_color,
            ],
            borderColor: [
              'rgba(255, 255, 255, 0)',
            ],
            borderWidth: 1
        }*/],
    };
    var scales = {};
    scales['A'] = {
        type: 'linear',
        position: 'top',
        min: 0,
        max: ticks_max,
        grid: {
            drawOnChartArea: true,
            color: ticks_color,
        },
        ticks: {
            stepSize: top_step_size,
            autoSkip: false,
            color: hl_color_top_axis,
            color: coloring_function_amounts,
            callback: top_ticks_callback_amounts,
        },
    }
    scales['B'] = {
        type: 'linear',
        position: 'top',
        title: {
            text: '{% trans "recurring payments" %}',
            color: hl_color_top_axis,
            font: {
                size: 16,
            },
            display: true,
        },
        min: 0,
        max: ticks_max,
        grid: {
            drawOnChartArea: true,
            color: ticks_color,
        },
        ticks: {
            stepSize: top_step_size,
            autoSkip: false,
            color: hl_color_top_axis,
            callback: ticks_callback_dates,
        },
    }
    scales['C'] = {
        type: 'linear',
        position: 'bottom',
        /*title: {
            text: 'credit top-ups',
            color: hl_color_bottom_axis,
            font: {
                size: 16,
            },
            display: true,
        },*/
        min: 0,
        max: ticks_max,
        grid: {
            drawOnChartArea: true,
            color: ticks_color,
        },
        ticks: {
            stepSize: 1,
            autoSkip: false,
            color: hl_color_bottom_axis,
            callback: ticks_callback_dates,
        },
    }
    scales['D'] = {
        type: 'linear',
        position: 'bottom',
        title: {
            text: '{% trans "credit top-ups" %}',
            color: hl_color_bottom_axis,
            font: {
                size: 16,
            },
            display: true,
        },
        min: 0,
        max: ticks_max,
        grid: {
            drawOnChartArea: true,
            color: ticks_color,
        },
        ticks: {
            stepSize: 1,
            autoSkip: false,
            color: hl_color_bottom_axis,
            color: coloring_function_credits,
            callback: bottom_ticks_callback_credits,
        },
    }
    var chart = new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            scales: scales,
            animation: false,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                enabled: false
              }
            }
        }
    });

    return chart;
}

function disable_credit_options() {
    // disable credits top-ups equal or lower for immediate change from annual to annual or monthly to monthly
    if ($('#change_mode_immediate').prop('checked') && $('select[name=subscription_type]').val() == current_type) {
        $('option', 'select[name=credits_number]').each(function() {
            if (parseInt($(this).val()) <= credits_number) $(this).prop('disabled', true);
            else $(this).prop('disabled', false);
        });
    // disable equal credits top-ups for change with period end from annual to annual or monthly to monthly
    } else if ($('#change_mode_with_end').prop('checked') && $('select[name=subscription_type]').val() == current_type) {
        $('option', 'select[name=credits_number]').each(function() {
            if (parseInt($(this).val()) == credits_number) $(this).prop('disabled', true);
            else $(this).prop('disabled', false);
        });
    // enable all otherwise
    } else {
        $('option', 'select[name=credits_number]').prop('disabled', false);
    }
}

function update_form(period_change=false) {
    var msg, msg2;
    // update list of available credits number
    if (period_change) {
        var selected_type = $('select[name=subscription_type]').val();
        var elem = document.getElementById('credits_number');
        for (var i = elem.options.length - 1 ; i >= 0 ; i--) elem.remove(i);
        for (var key in products) if (products[key][selected_type]) {
            elem.options[elem.options.length] = new Option(key,key);
        }
    }
    // disable immediate change from annual to monthly
    if (current_type == 'annual') {
        if ($('select[name=subscription_type]').val() == 'monthly') {
            $('#change_mode_immediate').prop('disabled', true);
            $('#change_mode_with_end').prop('checked', true);
        } else {
            $('#change_mode_immediate').prop('disabled', false);
        }
    }
    // disable credits top-ups
    disable_credit_options();
    //select first enabled option in case selected has been disabled
    var disabled = false;
    var selected = false;
    $('option', 'select[name=credits_number]').each(function() {
        if (!selected) {
            if ($(this).prop('selected') && $(this).prop('disabled')) disabled = true;
            if (disabled && !$(this).prop('disabled')) {
                $(this).prop('selected', true);
                selected = true;
            }
        }
    });
    // disable switch and submit button if change is not possible
    if ($('option:selected', 'select[name=credits_number]').prop('disabled')) {
        //disbale slider
        $('.form-switch .form-check-input').prop('checked', false);
        revert_changes();
        $('.form-switch .form-check-input').addClass('disabled');
        $('.form-switch .form-check-input').attr('disabled', true);
        //disable button
        $('#btn_submit').addClass('disabled');
        $('#btn_submit').attr('disabled', true);
        //set messages
        msg = '{% trans 'the requested change is not allowed' %}';
        msg2 = '';
    } else {
        //enable slider
        $('.form-switch .form-check-input').removeClass('disabled');
        $('.form-switch .form-check-input').attr('disabled', false);
        //enable button
        $('#btn_submit').removeClass('disabled');
        $('#btn_submit').attr('disabled', false);
        //clear messages
        msg = '';
        msg2 = '';
    }
    // disable change from annual earlier than a month before period ends
    if (current_type == 'annual') {
        if ($('#change_mode_with_end').prop('checked')) {
            if (diff_months_from_end > 0) {
                //disbale slider
                $('.form-switch .form-check-input').prop('checked', false);
                revert_changes();
                $('.form-switch .form-check-input').addClass('disabled');
                $('.form-switch .form-check-input').attr('disabled', true);
                //disable button
                $('#btn_submit').addClass('disabled');
                $('#btn_submit').attr('disabled', true);
                //set messages
                msg = '{% trans 'the requested change is allowed in the last month of the subscription period only' %}';
                msg2 = '';
            }
        }
    }
    //calculate supplement
    var num = $('option:selected', 'select[name=credits_number]').val();
    calculate_supplement(
        $('select[name=subscription_type]').val(),
        $('input[name=change_mode]:checked').val(),
        products[num]['monthly'],
        products[num]['annual']
    );
    //update info + button
    if (!msg) {
        if (supplement>0) {
            msg = '{% trans 'immediate extra payment' %}: ';
            msg2 = supplement + ' zł';
        } else {
            msg = '{% trans 'no extra payment now' %}';
            msg2 = '';
        }
    }
    $('#supplement_info').text(msg);
    $('#supplement_amount').text(msg2);
    //update chart
    if (chart_mode == 'after') updateChart();
}

function show_changes() {
    if (chart) chart.destroy();
    var num = $('option:selected', 'select[name=credits_number]').val();
    chart = update_chart(
        $('select[name=subscription_type]').val(),
        $('input[name=change_mode]:checked').val(),
        $('select[name=credits_number]').val(),
        products[num]['monthly'],
        products[num]['annual']
    );
    chart_mode = 'after';
}

function revert_changes() {
    if (chart) chart.destroy();
    chart = update_chart(current_type);
    chart_mode = 'before';
}

function updateChart() {
    if (chart_mode == 'after') show_changes();
    else revert_changes();
}

function toggleChart() {
    if (chart_mode == 'after') revert_changes();
    else show_changes();
}

function unsubscribe() {
    document.changeform.operation.value = 'unsubscribe';
    document.changeform.submit();
}

var chart, chart_mode, supplement;
revert_changes();
if (locked_for_changes == 'False') update_form(true);
</script>

{% endblock %}