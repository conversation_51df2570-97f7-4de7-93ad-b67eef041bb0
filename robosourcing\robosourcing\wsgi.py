"""
WSGI config for robosourcing project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/howto/deployment/wsgi/
"""

import os
from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'robosourcing.settings')

application = get_wsgi_application()

# lock_file_path = '/tmp/scheduler.lock'
lock_file_path = os.path.join(os.path.dirname(__file__), 'scheduler.lock')

# start scheduler if not yet running
if not os.path.exists(lock_file_path):

    # create lock file
    with open(lock_file_path, 'w') as f:
        f.write('Scheduler is running')

    # start scheduler
    from robosourcing.jobs import scheduler
    scheduler.start_scheduler()

    # delete lock on scheduler exit
    import atexit
    atexit.register(lambda: os.remove(lock_file_path))
