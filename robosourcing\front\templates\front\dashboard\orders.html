{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load crispy_forms_filters %}
{% load split %}

{% block title %}{{ block.super }} - {% trans "Orders" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item site-name"><a href="{%url 'home'%}">{% trans "Dashboard" %}</a></li>
          <li class="breadcrumb-item site-name"><a href="{%url 'store'%}">{% trans "Store" %}</a></li>
          <li class="breadcrumb-item site-name active">{% trans "My orders" %}<i class="fa fa-refresh text-muted ms-3" role="button" onclick="$(this).addClass('fa-spin'); document.refreshform.submit()"></i></li>
        </ol>
    </nav>
    
    <form name="refreshform" action="{% url 'user_orders' %}" method="post">
        {% csrf_token %}
        <input type="hidden" name="refresh" value="1">
    </form>

<div class="dashboard-element">
    <h3 class="db-section-title">{% blocktrans %}List of orders{% endblocktrans %}:</h3>
    <hr>
    <div class="table-responsive">
        <table class="table my-custom-table">
            <thead>
            <tr>
                <th style="width: 1%"></th>
                <th>{% trans "Order time" %}</th>
                <th class="text-center d-none d-md-table-cell">{% trans "Order ID" %}</th>
                <th class="text-center d-none d-lg-table-cell">{% trans "Description" %}</th>
                <th class="text-center">{% trans "Total price" %}</th>
                <th class="text-center">{% trans "Status" %}</th>
            </tr>
            </thead>
            <tbody>
            {% for order in orders %}
            <tr>
                <td>
                    <div class="btn btn-sm no-outline" onclick="expandDetailsRow(this)"><i class="fa-regular fa-lg fa-square-plus"></i></div>
                </td>
                <td class="{% if not order.status %}text-muted{% endif %}">{{ order.create_time|date:"DATETIME_FORMAT" }}</td>
                <td class="text-center{% if not order.status %} text-muted{% endif %} d-none d-md-table-cell">{{ order.order_id }}</td>
                <td class="text-center{% if not order.status %} text-muted{% endif %} d-none d-lg-table-cell">{{ order.description }}</td>
                <td class="text-center{% if not order.status %} text-muted{% endif %}">{{ order.price_incl_tax|floatformat:"2g" }} zł</td>
                <td class="text-center{% if not order.status %} text-muted{% elif order.status in 'PAID COMPLETED'|split:' ' %} text-success{% elif order.status == 'WAITING_FOR_CONFIRMATION' %} text-warning{% elif order.status in 'CANCELED EXPIRED'|split:' ' %} text-danger{% elif order.status in 'NEW PENDING'|split:' ' %} text-primary{% endif %}">
                    {% if order.status %}
                        {{ order.status }}
                    {% endif %}
                </td>
            </tr>
            <tr class="details" style="display: none">
                <td></td>
                <td colspan="5">
                    <div class="row">
                        <div class="col-lg-3">
                            <h5>{% trans "Order details" %}:</h5>
                            <p>
                                {% trans "Operator" %}: {{ order.get_operator_display }}<br>
                                {% if order.payment_method %}
                                    {% trans "Payment method" %}: {{ order.payment_method }}<br>
                                {% endif %}
                                {% if order.payment_id %}
                                    {% trans "Payment ID" %}: {{ order.payment_id }}<br>
                                {% endif %}
                                {% if order.payment_details %}
                                    {% trans "Payment details" %}: {{ order.payment_details }}<br>
                                {% endif %}
                                {% trans "Price" %}: {{ order.price|floatformat:"2g" }} zł<br>
                                {% trans "VAT" %}: {{ order.tax|floatformat:"2g" }} zł<br>
                                {% trans "TOTAL" %}: {{ order.price_incl_tax|floatformat:"2g" }} zł<br>
                                {% if order.update_time %}
                                    {% trans "Last updated" %}: {{ order.update_time|date:"DATETIME_FORMAT" }}<br>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-lg-9">
                            <h5>{% trans "Order items" %}:</h5>
                            <table class="table table-sm table-hover my-custom-table-level-2 rounded-3 overflow-hidden">
                                <thead>
                                    <tr>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Name" %}</th>
                                        <th class="text-center">{% trans "Credits" %}</th>
                                        <th class="text-center">{% trans "Price" %}</th>
                                        <th class="text-center">{% trans "Quantity" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in items %}
                                        {% if item.order == order %}
                                    <tr>
                                        <td>{{ item.product.get_type_display }}</td>
                                        <td>{{ item.product.name }}</td>
                                        {% if item.product.type == 'SUB' or item.product.type == 'YSU' %}
                                        <td class="text-center">{{ item.product.value }}{% trans "/mo" %}</td>
                                        {% elif item.product.type == 'PAC' %}
                                        <td class="text-center">{{ item.product.value }}</td>
                                        {% else %}
                                        <td class="text-center">-</td>
                                        {% endif %}
                                        <td class="text-center">{{ item.product.price|floatformat:"2g" }} zł</td>
                                        <td class="text-center">{{ item.quantity }}</td>
                                    </tr>
                                        {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                        <h5>{% trans "Subsequent charges to account" %}:</h5>
                            <table class="table table-sm table-hover my-custom-table-level-2 rounded-3 overflow-hidden">
                                <thead>
                                    <tr>
                                        <th>{% trans "Charge time" %}</th>
                                        <th class="text-center d-none d-md-table-cell">{% trans "Transaction ID" %}</th>
                                        <th class="text-center d-none d-lg-table-cell">{% trans "Description" %}</th>
                                        <th class="text-center">{% trans "Price" %}</th>
                                        <th class="text-center">{% trans "VAT" %}</th>
                                        <th class="text-center">{% trans "TOTAL" %}</th>
                                        <th class="text-center">{% trans "Status" %}</th>
                                        <th class="text-center">{% trans "Completed" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sub in subs %}
                                        {% if sub.reference_order == order %}
                                    <tr>
                                        <td class="{% if not sub.status %}text-muted{% endif %}">{{ sub.create_time|date:"DATETIME_FORMAT" }}</td>
                                        <td class="text-center{% if not sub.status %} text-muted{% endif %} d-none d-md-table-cell">{{ sub.order_id }}</td>
                                        <td class="text-center{% if not sub.status %} text-muted{% endif %} d-none d-lg-table-cell">{{ sub.description }}</td>
                                        <td class="text-center{% if not sub.status %} text-muted{% endif %}">{{ sub.price|floatformat:"2g" }} zł</td>
                                        <td class="text-center{% if not sub.status %} text-muted{% endif %}">{{ sub.tax|floatformat:"2g" }} zł</td>
                                        <td class="text-center{% if not sub.status %} text-muted{% endif %}">{{ sub.price_incl_tax|floatformat:"2g" }} zł</td>
                                        <td class="text-center{% if not sub.status %} text-muted{% elif sub.status in 'PAID COMPLETED F'|split:' ' %} text-success{% elif sub.status == 'WAITING_FOR_CONFIRMATION' %} text-warning{% elif sub.status in 'CANCELED EXPIRED'|split:' ' %} text-danger{% elif sub.status in 'NEW PENDING'|split:' ' %} text-primary{% endif %}">
                                            {% if sub.status %}
                                                {{ sub.status }}
                                            {% endif %}
                                        </td>
                                        <td class="text-center{% if not sub.status %} text-muted{% endif %}">{% if order.status in 'PAID COMPLETED'|split:' ' %}{{ sub.update_time|date:"DATETIME_FORMAT" }}{% endif %}</td>
                                    </tr>
                                        {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if orders %}
    <nav aria-label="...">
        {% include "front/home_page/pagination.html" with objects=orders %}
    </nav>
    {% endif %}

</div>
</div>

<script>
function expandDetailsRow(el) {
    row = $(el).parents('tr');
    var visible = $(row).next().is(':visible');
    $(row).siblings(':not(.details)').find('td:first-child i').removeClass('fa-square-minus').addClass('fa-square-plus');
    $(row).siblings('.details').hide();
    if (visible) {
        $(row).find('td:first-child i').removeClass('fa-square-minus').addClass('fa-square-plus');
    } else {
        $(row).find('td:first-child i').removeClass('fa-square-plus').addClass('fa-square-minus');
        $(row).next().show();
    }
}
</script>
{% endblock extra_content_1 %}

{% block extra_content_2 %}
{% endblock %}