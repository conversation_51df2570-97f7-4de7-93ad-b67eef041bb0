<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="64" height="64" viewBox="0 0 64 64" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(Infinity NaN NaN Infinity 0 0)" id="7e877b39-0ee7-46c5-9e4c-8112861e32b8"  >
</g>
<g transform="matrix(1 0 0 1 32 32)" id="e758eee2-a169-49f7-aca7-b6ecdf7cdbb0"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-32" y="-32" rx="0" ry="0" width="64" height="64" />
</g>
<g transform="matrix(0.88 0 0 0.88 32 32)" id="b22ccbda-c87e-4d75-9d3a-760b37645a00"  >
<circle style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  cx="0" cy="0" r="35" />
</g>
<g transform="matrix(1.34 0 0 1.37 32 31.55)"  >
<path style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-20.42, -21)" d="M 20.4389 0.5 C 9.13672 0.5 0 9.89583 0 21.5198 C 0 30.8114 5.85421 38.6766 13.9756 41.4603 C 14.9909 41.6696 15.3629 41.008 15.3629 40.4515 C 15.3629 39.9642 15.3294 38.2939 15.3294 36.5535 C 9.64378 37.8066 8.45981 34.0478 8.45981 34.0478 C 7.54609 31.6117 6.19225 30.9856 6.19225 30.9856 C 4.33136 29.698 6.32781 29.698 6.32781 29.698 C 8.39203 29.8372 9.47518 31.8556 9.47518 31.8556 C 11.3022 35.057 14.2462 34.1525 15.4306 33.5955 C 15.5997 32.2383 16.1414 31.2987 16.7167 30.7768 C 12.182 30.2895 7.41096 28.4799 7.41096 20.4059 C 7.41096 18.1091 8.22259 16.2299 9.50865 14.7684 C 9.30575 14.2465 8.59494 12.0885 9.71198 9.20012 C 9.71198 9.20012 11.4377 8.6432 15.329 11.3577 C 16.995 10.8976 18.713 10.6636 20.4389 10.6616 C 22.1647 10.6616 23.9239 10.9055 25.5484 11.3577 C 29.4401 8.6432 31.1659 9.20012 31.1659 9.20012 C 32.2829 12.0885 31.5717 14.2465 31.3688 14.7684 C 32.6887 16.2299 33.4669 18.1091 33.4669 20.4059 C 33.4669 28.4799 28.6958 30.2545 24.1272 30.7768 C 24.8719 31.4379 25.5145 32.6906 25.5145 34.6744 C 25.5145 37.4931 25.4811 39.7554 25.4811 40.4511 C 25.4811 41.008 25.8534 41.6696 26.8684 41.4607 C 34.9897 38.6761 40.8439 30.8114 40.8439 21.5198 C 40.8774 9.89583 31.7072 0.5 20.4389 0.5 Z" stroke-linecap="round" />
</g>
</svg>