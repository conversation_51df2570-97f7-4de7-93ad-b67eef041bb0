# Generated by Django 4.2.13 on 2025-02-06 17:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('front', '0010_alter_carouselitem_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='carouselitem',
            name='button_color',
            field=models.CharField(blank=True, choices=[('#fbab18', 'Brand Yellow'), ('#84b436', 'Brand Green'), ('#b72f2f', 'Brand Red'), ('#a580b2', 'Brand Purple'), ('#0077E4', 'Brand Blue'), ('#ffffff', 'Brand White'), ('#000000', 'Brand Black'), ('#666666', 'Brand Gray')], default='#0077E4', max_length=7, null=True, verbose_name='Button Color'),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='description_en',
            field=models.TextField(blank=True, help_text='<PERSON><PERSON><PERSON><PERSON> HTML, np. <strong>pogrubienie</strong>', null=True, verbose_name='Description (English)'),
        ),
        migrations.AlterField(
            model_name='carouselitem',
            name='description_pl',
            field=models.TextField(blank=True, help_text='Możesz używać HTML, np. <strong>pogrubienie</strong>', null=True, verbose_name='Description (Polish)'),
        ),
    ]
