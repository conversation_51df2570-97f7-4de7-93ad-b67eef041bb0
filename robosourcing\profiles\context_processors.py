import pytz
from datetime import datetime


def timezones(request):
    tz_list = pytz.all_timezones
    timezones_with_offsets = []
    now = datetime.now()
    for tz_name in tz_list:
        tz = pytz.timezone(tz_name)
        offset = tz.utcoffset(now)
        hours, remainder = divmod(offset.total_seconds(), 3600)
        minutes, _ = divmod(remainder, 60)
        sign = '+' if hours >= 0 else '-'
        formatted_offset = f"UTC{sign}{abs(int(hours)):02}:{int(abs(minutes)):02}"
        timezones_with_offsets.append({
            'name': tz_name,
            'offset': formatted_offset,
            'offset_hours': hours + minutes / 60
        })
    timezones_with_offsets.sort(key=lambda x: x['offset_hours'])
    return {
        'timezones': timezones_with_offsets
    }
