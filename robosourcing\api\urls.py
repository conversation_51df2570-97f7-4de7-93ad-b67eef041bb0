from django.urls import path, re_path

from api.token import RoboObtainTokenView
from . import views
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView, TokenVerifyView
from api.endpoints import attachments

# swagger
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
schema_view = get_schema_view(
   openapi.Info(
      title="Snippets API",
      default_version='v1',
      description="Test description",
      terms_of_service="https://www.google.com/policies/terms/",
      contact=openapi.Contact(email="<EMAIL>"),
      license=openapi.License(name="BSD License"),
   ),
   public=True,
   permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    # swagger patterns
    re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    re_path(r'^swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    re_path(r'^redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

    path('elements/', views.get_elements),

    #robot functions
    path('robo_check/', views.robot_action_availble),
   #  path('robo_scenario_upload/', views.robot_action_upload_scenario, name='robo_scenario_upload'),
    path('scenarios/', views.robot_action_create_scenario, name='create_scenario'),
    path('scenarios/<uuid:sid>/', views.scenario_detail, name='scenario_detail'),
    path('scenarios/<uuid:sid>/update_scenario/', views.robot_action_update_scenario),
    path('scenarios/<uuid:sid>/related_scenario/', views.robot_action_create_related_scenario, name='create_related_scenario'),
    path('scenarios/changelog/<uuid:sid>/', views.get_scenario_changelog, name='get_scenario_changelog'),
    path('scenarios/check_updates/', views.check_scenarios_updates, name='check_scenarios_updat'),
    path('robo_lock/', views.robot_action_lock_credits),
    path('robo_payment/', views.robot_action_payment),
    path('create_robot/', views.create_robot, name='create_robot'),
    path('create_anonymous_robot/', views.create_anonymous_robot, name='create_anonymous_robot'),
    path('get_connect_key/', views.get_connect_key, name='get_connect_key'),
    path('add_favorite_scenario/', views.add_favorite_scenario, name='add_favorite_scenario'),
    path('connect_robot/', views.connect_robot, name='connect_robot'),
    path('disconnect_robot/', views.disconnect_robot, name='disconnect_robot'),
    path('delete_robot/', views.delete_robot, name='delete_robot'),
    path('update_robot/', views.update_robot, name='update_robot'),
    path('info_robot/', views.info_robot, name='info_robot'),
    path('request_reset_key/', views.request_reset_key, name='request_reset_key'),
    path('check_reset_key/', views.check_reset_key, name='check_reset_key'),
    path('register_scenario_event/', views.register_scenario_event, name='register_scenario_event'),
    #JWT_auth
   #  path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/', RoboObtainTokenView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    # Download related paths
    path('download/platforms/', views.get_download_platforms, name='get_download_platforms'),
    path('download/latest-version/<int:platform_id>/', views.get_latest_version, name='get_latest_version'),
    path('download/versions/<int:platform_id>/', views.get_download_versions, name='get_download_versions'),
    path('download/version-info/<int:version_id>/', views.get_download_version_info, name='get_download_version_info'),
    path('download/register-event/', views.register_download_event, name='register_download_event'),
    path('download/add-item/', views.add_download_item, name='add_download_item'),
    path('download/update-item/<int:version_id>/', views.update_download_item, name='update_download_item'),
    # Scenarios    
    path('scenario/detail/<uuid:sid>/', views.get_scenario_detail, name='get_scenario_detail'),
    path('scenario/detail/<uuid:sid>/change_description', views.change_description, name='change_description'),
    path('botie/my_scenarios/', views.get_my_scenarios, name='get_my_scenarios'),
    path('botie/last_ver_info/<int:platform_id>/', views.get_last_ver_info, name='get_last_ver_info'),
   # scenario attachments
   path('upload/scenario_attachment/', attachments.upload_attachment, name='upload_attachment'),
   path('attachment/get/list/<uuid:sid>', attachments.get_attachment_list_by_sid, name='get_attachment_list_by_sid'),
   path('attachment/get/list/', attachments.get_attachment_list, name='get_attachment_list'),
   path('attachment/get/file/<str:file_hash>', attachments.get_file, name='get_attachment'),
   path('attachment/get/file/', attachments.get_file_list, name='get_attachments'),
]
