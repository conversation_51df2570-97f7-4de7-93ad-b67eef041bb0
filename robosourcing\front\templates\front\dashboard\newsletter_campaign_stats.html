{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Campaign Statistics" %} - {{ campaign.name }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_campaigns' %}">{% trans "Campaigns" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{% trans "Statistics" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fa fa-chart-bar me-2"></i>{% trans "Campaign Statistics" %}</h2>
                    <p style="color: var(--muted-foreground);" class="mb-0">{{ campaign.name }}</p>
                </div>
                <a href="{% url 'profiles_newsletter_campaigns' %}" class="newsletter-btn-secondary btn">
                    <i class="fa fa-arrow-left"></i> {% trans "Back to Campaigns" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <!-- Campaign Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="newsletter-card">
                <div class="newsletter-card-header">
                    <h5 class="mb-0">{% trans "Campaign Information" %}</h5>
                </div>
                <div class="newsletter-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>{% trans "Name" %}:</strong> {{ campaign.name }}</p>
                            <p><strong>{% trans "Template" %}:</strong> {{ campaign.template.name }}</p>
                            <p><strong>{% trans "Segment" %}:</strong> {{ campaign.segment.name }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Status" %}:</strong> 
                                <span class="newsletter-badge newsletter-badge-{% if campaign.status == 'sent' %}success{% elif campaign.status == 'draft' %}secondary{% elif campaign.status == 'scheduled' %}warning{% else %}primary{% endif %}">
                                    {{ campaign.get_status_display }}
                                </span>
                            </p>
                            <p><strong>{% trans "Created" %}:</strong> {{ campaign.created_at|date:"d.m.Y H:i" }}</p>
                            {% if campaign.sent_at %}
                                <p><strong>{% trans "Sent" %}:</strong> {{ campaign.sent_at|date:"d.m.Y H:i" }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    {% if campaign.status == 'sent' %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="newsletter-stats-grid">
                <div class="newsletter-stat-card">
                    <div class="newsletter-stat-icon">
                        <i class="fa fa-paper-plane"></i>
                    </div>
                    <div class="newsletter-stat-content">
                        <div class="newsletter-stat-number">{{ total_sent }}</div>
                        <div class="newsletter-stat-label">{% trans "Emails Sent" %}</div>
                    </div>
                </div>

                <div class="newsletter-stat-card">
                    <div class="newsletter-stat-icon">
                        <i class="fa fa-envelope-open"></i>
                    </div>
                    <div class="newsletter-stat-content">
                        <div class="newsletter-stat-number">{{ total_opens }}</div>
                        <div class="newsletter-stat-label">{% trans "Opens" %}</div>
                    </div>
                </div>

                <div class="newsletter-stat-card">
                    <div class="newsletter-stat-icon">
                        <i class="fa fa-mouse-pointer"></i>
                    </div>
                    <div class="newsletter-stat-content">
                        <div class="newsletter-stat-number">{{ total_clicks }}</div>
                        <div class="newsletter-stat-label">{% trans "Clicks" %}</div>
                    </div>
                </div>

                <div class="newsletter-stat-card">
                    <div class="newsletter-stat-icon">
                        <i class="fa fa-percentage"></i>
                    </div>
                    <div class="newsletter-stat-content">
                        <div class="newsletter-stat-number">{{ open_rate|floatformat:1 }}%</div>
                        <div class="newsletter-stat-label">{% trans "Open Rate" %}</div>
                    </div>
                </div>

                <div class="newsletter-stat-card">
                    <div class="newsletter-stat-icon">
                        <i class="fa fa-chart-line"></i>
                    </div>
                    <div class="newsletter-stat-content">
                        <div class="newsletter-stat-number">{{ click_rate|floatformat:1 }}%</div>
                        <div class="newsletter-stat-label">{% trans "Click Rate" %}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Analysis -->
    <div class="row">
        <div class="col-md-6">
            <div class="newsletter-card">
                <div class="newsletter-card-header">
                    <h5 class="mb-0">{% trans "Performance Analysis" %}</h5>
                </div>
                <div class="newsletter-card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Delivery Rate" %}</span>
                            <span class="newsletter-text-success">100%</span>
                        </div>
                        <div class="progress mt-1" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Open Rate" %}</span>
                            <span class="newsletter-text-primary">{{ open_rate|floatformat:1 }}%</span>
                        </div>
                        <div class="progress mt-1" style="height: 6px;">
                            <div class="progress-bar" style="width: {{ open_rate }}%; background-color: var(--primary);"></div>
                        </div>
                    </div>
                    
                    <div class="mb-0">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Click Rate" %}</span>
                            <span class="newsletter-text-info">{{ click_rate|floatformat:1 }}%</span>
                        </div>
                        <div class="progress mt-1" style="height: 6px;">
                            <div class="progress-bar" style="width: {{ click_rate }}%; background-color: var(--primary);"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="newsletter-card">
                <div class="newsletter-card-header">
                    <h5 class="mb-0">{% trans "Benchmarks" %}</h5>
                </div>
                <div class="newsletter-card-body">
                    <div class="mb-3">
                        <small style="color: var(--muted-foreground);">{% trans "Industry Average Open Rate" %}</small>
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Technology" %}</span>
                            <span>21.5%</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <small style="color: var(--muted-foreground);">{% trans "Industry Average Click Rate" %}</small>
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Technology" %}</span>
                            <span>2.6%</span>
                        </div>
                    </div>
                    
                    <div class="alert alert-info" style="background-color: var(--muted); border: 1px solid var(--border);">
                        <small>
                            {% if open_rate > 21.5 %}
                                <i class="fa fa-thumbs-up text-success"></i> {% trans "Your open rate is above industry average!" %}
                            {% else %}
                                <i class="fa fa-info-circle"></i> {% trans "Consider optimizing your subject line to improve open rates." %}
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="newsletter-card">
                <div class="newsletter-card-body text-center py-5">
                    <i class="fa fa-clock fa-3x" style="color: var(--muted-foreground);" class="mb-3"></i>
                    <h5 style="color: var(--muted-foreground);">{% trans "No Statistics Available" %}</h5>
                    <p style="color: var(--muted-foreground);">{% trans "Statistics will be available after the campaign is sent." %}</p>
                    {% if campaign.status == 'draft' %}
                        <a href="{% url 'profiles_newsletter_campaign_edit' campaign.id %}" class="newsletter-btn-primary btn">
                            <i class="fa fa-edit"></i> {% trans "Edit Campaign" %}
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
