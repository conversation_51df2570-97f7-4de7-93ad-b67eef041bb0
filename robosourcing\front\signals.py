import os
import shutil
from django.conf import settings
from django.db.models.signals import post_migrate
from django.dispatch import receiver

@receiver(post_migrate)
def copy_default_image(sender, **kwargs):
    # Ścieżka do pliku domyślnego w katalogu static
    static_default = os.path.join(settings.BASE_DIR, 'static', 'default.png')
    
    # Docelowy katalog i ścieżka w MEDIA_ROOT (dla modelu Tag)
    media_dir = os.path.join(settings.MEDIA_ROOT, 'tags')
    media_default = os.path.join(media_dir, 'default.png')
    
    # Jeśli plik nie istnieje w MEDIA_ROOT i istnieje w static, kopiujemy go
    if not os.path.exists(media_default) and os.path.exists(static_default):
        os.makedirs(media_dir, exist_ok=True)
        shutil.copy(static_default, media_default)
        print("Skopiowano default.png do MEDIA_ROOT/tags/")

