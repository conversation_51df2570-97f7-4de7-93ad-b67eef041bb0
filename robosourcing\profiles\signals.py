from allauth.account.signals import user_logged_in, user_signed_up
# from allauth.account.signals import email_confirmation_sent, email_confirmed
from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from db.mailer import Mailer
# from store.models import Order, Invoice, SubsequentOrder, SubscriptionEvent
# from store.models import Schedule as StoreSchedule
# from db.models import Schedule as DBSchedule, EmailNotification, CreditEvent
from .models import UserProfile
# from db.models import Robot
from db.models import Credit, UserSubscription, DownloadEvent
from django.contrib.auth.models import User
# from robosourcing.utils import TimeManager
from .models import UserProfile
from db.models import AffiliateLink, WithdrawalCycle


mailer = Mailer()

# @receiver(user_signed_up)
# def user_signed_up_receiver(request, user, **kwargs):
#     ref_code = request.GET.get('ref')
#     if ref_code:
#         try:
#             affiliate_link = AffiliateLink.objects.get(code=ref_code)
#             # Upewnij się, że profil użytkownika istnieje
#             user_profile, created = UserProfile.objects.get_or_create(user=user)
#             user_profile.referred_by = affiliate_link.user
#             user_profile.save()
#         except AffiliateLink.DoesNotExist:
#             pass

@receiver(post_save, sender=User)
def create_or_update_user_profile(sender, instance, created, **kwargs):
    if created:
        initial_subscription_value = 100
        UserProfile.objects.create(user=instance)
        UserSubscription.objects.create(user=instance, value=initial_subscription_value)
        Credit.objects.create(user=instance, subscription_credits=initial_subscription_value)

        # Wyślij powiadomienie e-mail
        mailer.after_registration_email(instance)



@receiver(pre_save, sender=User)
def save_previous_last_login(sender, instance, **kwargs):
    if instance.pk:
        try:
            previous_instance = User.objects.get(pk=instance.pk)
            instance.previous_last_login = previous_instance.last_login
        except User.DoesNotExist:
            instance.previous_last_login = None


@receiver(user_logged_in)
def user_logged_in_handler(sender, request, user, **kwargs):

    # disabled here because is performed before every view processing
    # credits = Credit.objects.filter(user=request.user)
    # if credits:
    #     request.session['credits_balance'] = credits[0].credits

    # send email notification
    if request.user.previous_last_login:
        diff = request.user.last_login - request.user.previous_last_login
        if diff.days > 7:
            mailer.after_login_email(request.user)


# @receiver(pre_save, sender=Order)
# @receiver(pre_save, sender=SubsequentOrder)
# @receiver(pre_save, sender=Credit)
# def set_accelerated_time(sender, instance, **kwargs):
#     tm = TimeManager()
#     now = tm.current_time()
#     if instance._state.adding and not instance.create_time:
#         instance.create_time = now
#     instance.update_time = now
#
#
# @receiver(pre_save, sender=Invoice)
# def set_accelerated_time(sender, instance, **kwargs):
#     tm = TimeManager()
#     now = tm.current_time()
#     if instance._state.adding and not instance.issue_time:
#         instance.issue_time = now
#
#
# @receiver(pre_save, sender=DBSchedule)
# @receiver(pre_save, sender=StoreSchedule)
# def set_accelerated_time(sender, instance, **kwargs):
#     tm = TimeManager()
#     now = tm.current_time()
#     if instance._state.adding and not instance.create_time:
#         instance.create_time = now
#     instance.last_update = now
#
#
# @receiver(pre_save, sender=SubscriptionEvent)
# @receiver(pre_save, sender=DownloadEvent)
# @receiver(pre_save, sender=CreditEvent)
# def set_accelerated_time(sender, instance, **kwargs):
#     tm = TimeManager()
#     now = tm.current_time()
#     if instance._state.adding and not instance.event_time:
#         instance.event_time = now
#
#
# @receiver(pre_save, sender=EmailNotification)
# def set_accelerated_time(sender, instance, **kwargs):
#     tm = TimeManager()
#     now = tm.current_time()
#     if instance._state.adding and not instance.sending_time:
#         instance.sending_time = now

@receiver(post_save, sender=AffiliateLink)
def create_initial_withdrawal_cycle(sender, instance, created, **kwargs):
    if created:
        # Tworzymy pierwszy cykl wypłat od momentu utworzenia AffiliateLink
        start_date = instance.created_at  # Załóżmy, że AffiliateLink ma pole created_at
        end_date = start_date + timedelta(days=30)
        
        WithdrawalCycle.objects.create(
            partner=instance.user,
            start_date=start_date,
            end_date=end_date,
            total_withdrawn=Decimal('0.00')
        )