import os, logging

from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
# from django.core.cache import cache
from django.db.models import Count, CharField, QuerySet
from django.db.models.functions import Lower
from django.shortcuts import render, redirect, get_object_or_404
from django.core.mail import send_mail
from django.core.mail import EmailMultiAlternatives
from django.http import JsonResponse, Http404, FileResponse, HttpResponseForbidden
from django.db.models import Q, Prefetch
from django.http import HttpResponseRedirect, HttpResponse
from django.contrib import messages
from django.conf import settings
from django.urls import reverse
from django.template.loader import render_to_string
import mimetypes

import json
from collections import Counter
from itertools import chain
from slugify import slugify
import requests
# hashing timestamp
from time import time
import basehash

from store.store_utils import StoreUtils
from .models import CarouselItem, ContactRequest, ContactCounter, ContactFile, CategoryCard, NewsletterSubscription
from db.models import Robot, Scenario, DownloadItem, Tag, DownloadPlatform, DownloadPackage, DownloadServer, \
    ScenarioTag, Category, Scenario_Category, UserStorage, EmailNotification, TagGroup
from store.models import Product
from .forms import ContactForm, NewsletterForm

from django.views import View
from bs4 import BeautifulSoup

from seo.models import StaticPageMeta, ObjectMeta, ScenarioMeta

CharField.register_lookup(Lower)
su = StoreUtils()

def get_static_meta(page_name):
    try:
        return StaticPageMeta.objects.get(page=page_name)
    except StaticPageMeta.DoesNotExist:
        return None


@login_required
def dashboard_home(request):
    return render(request, 'front/dashboard/index.html')


def index(request):
    current_datetime = timezone.now()
    carousel_items = CarouselItem.objects.filter(
        Q(is_displayed=True) &
        (Q(display_start_date__lte=current_datetime) | Q(display_start_date__isnull=True)) &
        (Q(display_end_date__gte=current_datetime) | Q(display_end_date__isnull=True))
    ).order_by('order')
    cards = CategoryCard.objects.all()
    
    context = {
        'carousel_items': carousel_items,
        'cards': cards,
        'meta': get_static_meta('home'),
    }
    return render(request, 'front/home_page/index.html', context)


def how_it_work(request):  
    context = {
       'meta': get_static_meta('how_it_works'),
    }
    return render(request, 'front/home_page/how_it_works.html', context)


def get_hashing_base():
    hash_fn = basehash.base36()  # you can initialize a 36, 52, 56, 58, 62 and 94 base fn
    ts = int(time())  # get timestamp without millis
    return hash_fn, ts


def get_secret():
    hash_fn, ts = get_hashing_base()
    secret = str(ts)
    return hash_fn.hash(secret)


def verify_secret(secret):
    hash_fn, ts = get_hashing_base()
    if ts - int(hash_fn.unhash(secret)) < 60*60*1:  # give 1 hour to download file from link
        return True
    return False


def download_direct(_, filename):
    return FileResponse(open(f'{settings.LOCAL_REPOSITORY_PATH}{filename}', 'rb'))


def get_server_url(req):
    protocol = 'http'
    if req.is_secure():
        protocol += 's'
    return f'{protocol}://{req.get_host()}'


def get_client_ip(req):
    """
    Extract the client IP address from the request.

    :param req: The HTTP request object.
    :return: IP address as a string.
    """
    x_forwarded_for = req.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = req.META.get('REMOTE_ADDR')
    return ip


def get_current_theme(req):
    if 'theme' in req.session.keys():
        return req.session['theme']
    theme = req.get_signed_cookie("theme")
    if theme:
        return theme
    return None


def download_installer(request, platform_name=None, package_name=None, version_id=None, command=None):
    # Zablokuj pobieranie przez boty RPA (przy próbie wysłania formularza bez aktualnego kodu secret)
    if command == 'get' and "secret" in request.POST and verify_secret(request.POST["secret"]):
        # Znajdź i sprawdź, czy istnieje DownloadItem
        download_item = get_object_or_404(DownloadItem, id=version_id)
        # url = settings.SCENARIOS_API_URL + 'download/register-event/'
        # url = settings.SCENARIOS_API_URL_LOCAL + 'download/register-event/'
        url = get_server_url(request) + '/api/download/register-event/'
        headers = settings.SCENARIOS_API_HEADERS
        # Rejestruj wydarzenie pobrania przez API
        event_registered = True
        try:
            response = requests.post(url, headers=headers, data={'version_id': version_id,
                                                                 'ip_address': get_client_ip(request)})
            response.raise_for_status()  # Spowoduje zgłoszenie wyjątku dla kodów błędu HTTP
        except requests.exceptions.HTTPError as err:
            # Obsługa błędów odpowiedzi HTTP (np. 4xx, 5xx)
            messages.error(request, f'Błąd serwera API: {err}')
            event_registered = False
        except requests.exceptions.ConnectionError as err:
            # Obsługa błędów połączenia
            messages.error(request, f'Błąd połączenia z serwerem API: {err}')
            event_registered = False
        except requests.exceptions.Timeout as err:
            # Obsługa błędów timeoutu
            messages.error(request, f'Przekroczono limit czasu żądania do serwera API: {err}')
            event_registered = False
        except (requests.exceptions.RequestException, Exception) as err:
            # Obsługa innych wyjątków
            messages.error(request, f'Błąd żądania API: {err}')
            event_registered = False
        if event_registered:
            # Przekieruj do URL-a pobierania
            download_url = download_item.get_download_url()
            if download_url:
                # Sprawdź odpowiedź serwera bez pobierania całego pliku
                response = requests.head(download_url)
                if response.status_code in (200, 301, 302):
                    return HttpResponseRedirect(download_url)
            # Obsługa braku URL-a
            messages.error(request, 'Plik aktualnie niedostępny. Prosimy wybrać inny serwer lub ewentualnie inną wersję programu.')
    else:
        # Komunikat dla botów
        messages.error(request, 'Pobranie pliku za pomocą wygenerowanego odnośnika nie jest możliwe.')
    # Przekierowanie w przypadku dowolnego błędu
    return HttpResponseRedirect(reverse('download', args=(platform_name, package_name, version_id)))


def download(request, platform_name=None, package_name=None, version_id=None):

    def verify_download_items(check_items:QuerySet, check_mode:str, check_qs:QuerySet) -> QuerySet:
        if check_qs:
            check_qs = check_qs.order_by('id')
            check_id = check_qs.values_list('id', flat=True)[0]
            filtered_items = None
            match check_mode:
                case 'platform':
                    filtered_items = check_items.filter(platform_id_id=check_id)
                case 'package':
                    filtered_items = check_items.filter(package_id_id=check_id)
                case 'server':
                    filtered_items = check_items.filter(server_id_id=check_id)
            if filtered_items:
                return True, filtered_items
        return False, check_items

    # get all active download items
    download_items = DownloadItem.objects.filter(
        is_active=True,
    )

    # get platform ID and filter download items by platform
    chosen_platform_name = platform_name
    if not platform_name:
        # get default platform
        qs = DownloadPlatform.objects.filter(default=True, active=True)
    else:
        qs = DownloadPlatform.objects.filter(name__lower=platform_name, active=True)
    ok, download_items = verify_download_items(download_items, 'platform', qs)
    if not ok:
        # get first active platform
        qs = DownloadPlatform.objects.filter(active=True)
        ok, download_items = verify_download_items(download_items, 'platform', qs)
        if not ok:
            raise Http404("Administrator nie aktywował żadnej platformy lub nie udostępnia dla niej żadnego pliku!")
    qs = qs.order_by('id')
    platform_id = qs.values_list('id', flat=True)[0]
    platform_name = qs.values_list('name', flat=True)[0]
    # show message
    if chosen_platform_name and platform_name.lower() != chosen_platform_name:
        messages.error(request, 'Żaden z serwerów nie udostępnia aktualnie plików dla wybranej platformy.')

    # get package ID and filter download items by package
    chosen_package_name = package_name
    if not package_name:
        # get default package for chosen platform
        qs = DownloadPackage.objects.filter(platform_id=platform_id, default=True, active=True)
    else:
        qs = DownloadPackage.objects.filter(name__lower=package_name, platform_id=platform_id, active=True)
    ok, download_items = verify_download_items(download_items, 'package', qs)
    if not ok:
        # get first active package for chosen platform
        qs = DownloadPackage.objects.filter(platform_id=platform_id, active=True)
        ok, download_items = verify_download_items(download_items, 'package', qs)
        if not ok:
            raise Http404("Administrator nie aktywował żadnego pakietu lub nie udostępnia żadnego pliku w wybranym pakiecie!")
    qs = qs.order_by('id')
    package_id = qs.values_list('id', flat=True)[0]
    package_name = qs.values_list('name', flat=True)[0]
    # show message
    if chosen_package_name and package_name.lower() != chosen_package_name:
        messages.error(request, 'Żaden z serwerów nie udostępnia aktualnie plików w wybranym pakiecie.')

    # get server ID and filter download items by server
    server_id = None
    if "server_id" in request.POST:
        server_id = int(request.POST["server_id"])
    if not server_id:
        # get default server
        qs = DownloadServer.objects.filter(default=True, active=True)
    else:
        qs = DownloadServer.objects.filter(id=server_id, active=True)
    ok, download_items = verify_download_items(download_items, 'server', qs)
    if not ok:
        # get first active server
        qs = DownloadServer.objects.filter(active=True)
        ok, download_items = verify_download_items(download_items, 'server', qs)
        if not ok:
            raise Http404("Administrator nie aktywował żadnego serwera lub nie udostępnia na nim żadnego pliku w wybranym pakiecie!")
    qs = qs.order_by('id')
    server_id = qs.values_list('id', flat=True)[0]
    server_name = qs.values_list('name', flat=True)[0]
    # show message
    if "server_id" in request.POST:
        if server_id == int(request.POST["server_id"]):
            messages.info(request, f'Przełączono na serwer {server_name}.')
        else:
            messages.error(request, 'Wybrany serwer nie udostępnia aktualnie plików w wybranym pakiecie.')

    # get download items for platform in right package
    if version_id:
        # verify if platform, package and server match
        qs = download_items.filter(id=version_id)
        if qs:
            chosen_item = qs.first()
        else:
            version_id = None
    if not version_id:
        # get the default item
        qs = download_items.filter(is_default=True)
        if not qs:
            qs = download_items.order_by('-release_version')
        if qs:
            chosen_item = qs.first()
        else:
            raise Http404("Wersja nie istnieje!")
    # print(platform_id, platform_name, package_id, package_name, version_id, server_id)
    # print(download_items)
    # print(chosen_item.release_version)
    # print(chosen_item.is_active)
    # print(chosen_item.server_id_id)
    # print(chosen_item.id)
    context = {
        'platforms': DownloadPlatform.objects.filter(active=True).order_by('id'),
        'packages': DownloadPackage.objects.filter(platform_id=platform_id, active=True).order_by('id'),
        'servers': DownloadServer.objects.filter(active=True).order_by('id'),
        'download_items': download_items.order_by('-release_version'),
        'chosen_item': chosen_item,
        'platform_name': platform_name,
        'package_name': package_name,
        'secret': get_secret(),
        'meta': get_static_meta('download'),
    }
    return render(request, 'front/home_page/download.html', context)



def scenarios_page(request):
    # ------------------------------------------------------------------ #
    # 1.  Odczyt parametrów GET  +  zapamiętywanie w sesji
    # ------------------------------------------------------------------ #
    query = request.GET.get('q', '').strip()
    if 'q' in request.GET:
        request.session['scenarios_filter_query'] = query
    else:
        query = request.session.get('scenarios_filter_query', '')

    selected_tags = [t for t in request.GET.getlist('tags') if t]
    if 'tags' in request.GET:
        request.session['scenarios_filter_tags'] = selected_tags
    else:
        selected_tags = request.session.get('scenarios_filter_tags', [])

    cat_param          = request.GET.get('cat', '').strip()
    current_category   = None
    parent_category    = None
    if cat_param:
        try:
            current_category = Category.objects.get(name=cat_param)
            parent_category  = current_category.parent
        except Category.DoesNotExist:
            pass

    # ------------------------------------------------------------------ #
    # 2.  Bazowy queryset  (publiczne, zaakceptowane scenariusze)
    # ------------------------------------------------------------------ #
    qs = (Scenario.objects
          .filter(status=Scenario.ScenarioStatus.ACCEPTED,
                  available=Scenario.AvailabilityStatus.PUBLIC)
          .prefetch_related(
              Prefetch('tags',
                       queryset=Tag.objects.select_related('group'),
                       to_attr='prefetched_tags'))
          )

    # -- wyszukiwarka pełnotekstowa ------------------------------------ #
    if query:
        qs = qs.filter(Q(name__icontains=query) | Q(description__icontains=query))

    # -- filtr po tagach / grupach tagów ------------------------------- #
    if selected_tags:
        qs = qs.filter(
                Q(tags__name__in=selected_tags) |          # konkretny tag
                Q(tags__group__name__in=selected_tags)     # lub dowolny tag danej grupy
             ).distinct()

    # -- filtr po kategorii ------------------------------------------- #
    if current_category:
        qs = qs.filter(categories=current_category)

    # ------------------------------------------------------------------ #
    # 3.  Paginacja
    # ------------------------------------------------------------------ #
    paginator = Paginator(qs, 15)
    page      = request.GET.get('page')
    try:
        scenarios = paginator.page(page)
    except PageNotAnInteger:
        scenarios = paginator.page(1)
    except EmptyPage:
        scenarios = paginator.page(paginator.num_pages)

    # ------------------------------------------------------------------ #
    # 4.  Dołączenie ikon grup tagów do każdego scenariusza
    # ------------------------------------------------------------------ #
    for scen in scenarios:
        seen, groups = set(), []
        for t in getattr(scen, 'prefetched_tags', []):
            g = t.group
            if g and g.pk not in seen:
                seen.add(g.pk)
                groups.append(g)
        scen.tag_groups = groups

    # ------------------------------------------------------------------ #
    # 5.  Lewy panel kategorii / breadcrumbs
    # ------------------------------------------------------------------ #
    sidebar_categories = (
        Category.objects.filter(parent=current_category)
        if current_category else
        Category.objects.filter(parent__isnull=True)
    )

    sidebar_categories_with_counts = []
    for cat in sidebar_categories:
        cnt = Scenario.objects.filter(categories=cat,
                                      status=Scenario.ScenarioStatus.ACCEPTED,
                                      available=Scenario.AvailabilityStatus.PUBLIC).count()
        sidebar_categories_with_counts.append((cat, str(cnt) if cnt < 10000 else "9999+"))

    category_header = "Podkategorie" if (current_category and parent_category) else "Kategorie"

    breadcrumbs = []
    if current_category:
        c = current_category
        while c:
            breadcrumbs.append(c)
            c = c.parent
        breadcrumbs.reverse()

    # ------------------------------------------------------------------ #
    # 6.  Render
    # ------------------------------------------------------------------ #
    return render(request, 'front/home_page/scenariusze.html', {
        'scenarios'                    : scenarios,
        'query'                        : query,
        'selected_tags'                : selected_tags,
        'current_category'             : current_category,
        'parent_category'              : parent_category,
        'sidebar_categories_with_counts': sidebar_categories_with_counts,
        'category_header'              : category_header,
        'breadcrumbs'                  : breadcrumbs,
        'meta'                         : get_static_meta('scenariusze'),
    })

def get_categories_paths() -> dict:

    def get_category_path(qs: QuerySet, name: str):
        if name:
            for i in qs:
                if i.name == name:
                    path.append(name)
                    get_category_path(qs, i.parent_id)
                    break

    paths: dict = {}
    path: list
    parents = Category.objects.all()
    for category in parents:
        path = []
        get_category_path(parents, category.parent_id)
        path.reverse()
        paths[category.name] = path

    return paths


def scenario_details(request, sid):
    try:
        scenario = Scenario.objects.get(sid=sid)
    except (Scenario.DoesNotExist, Exception):
        return redirect('scenarios_page')

    tags = scenario.tags.select_related('group').all()
    groups = []
    seen = set()
    for tag in tags:
        if tag.group and tag.group.pk not in seen:
            groups.append(tag.group)
            seen.add(tag.group.pk)
    categories = scenario.categories.all()
    programs = scenario.apps.all()
    paths: dict = get_categories_paths()

    user_robots = Robot.objects.filter(owner=request.user) if request.user.is_authenticated else Robot.objects.none()
    assigned_robots = [str(obj.rid_id) for obj in UserStorage.objects.filter(sid=scenario)]

    # Pobierz metadane powiązane z tym scenariuszem (jeśli istnieją)
    try:
        meta = scenario.seo_meta
    except ScenarioMeta.DoesNotExist:
        meta = None

    return render(request, 'front/home_page/scenario_detail.html', {
        'scenario': scenario,
        'tags': tags,
        'tag_groups': groups,
        'categories': categories,
        'programs': programs,
        'paths': paths,
        'user_robots': user_robots,
        'assigned_robots': assigned_robots,
        'meta': meta,  # dodane do kontekstu
    })


def tags_autocomplete(request):
    query         = request.GET.get('q', '').strip()
    selected_tags = request.GET.getlist('selected_tags', [])

    # -------- 1.  wyszukujemy TYLKO te tagi, których nazwa *zawiera* frazę ------------
    keywords   = query.lower().split()
    tag_filter = Q()
    for kw in keywords:
        tag_filter |= Q(name__icontains=kw)

    matched_tags = Tag.objects.filter(tag_filter).select_related('group')

    # -------- 2.  wyciągamy unikalne GRUPY tagów -------------------------------------
    groups = {}
    for t in matched_tags:
        if t.group and t.group.name not in groups:
            groups[t.group.name] = t.group          # zapamiętaj obiekt grupy

    if not groups:
        return JsonResponse([], safe=False)

    # -------- 3.  policzmy scenariusze z dowolnym tagiem z tych grup ------------------
    counts = (
        Scenario.objects
                .filter(status=Scenario.ScenarioStatus.ACCEPTED,
                        available=Scenario.AvailabilityStatus.PUBLIC,
                        tags__group__in=groups.values())
                .values('tags__group__name')
                .annotate(cnt=Count('sid', distinct=True))
    )
    count_map = {row['tags__group__name']: row['cnt'] for row in counts}

    # -------- 4.  wynik ---------------------------------------------------------------
    result = [{
        "name":  g.name,
        "count": count_map.get(g.name, 0),
        "icon":  g.icon.url if g.icon else None,
    } for g in groups.values() if g.name not in selected_tags]

    return JsonResponse(result, safe=False)



def scenario_get(request, sid):
    try:
        scenario = Scenario.objects.get(sid=sid)
    except Scenario.DoesNotExist as err:
        messages.error(request, f'Błąd odczytu z bazy danych: {err}')
        return HttpResponseRedirect(reverse('scenario_details', args=(sid,)))

    try:
        content = json.dumps(scenario.scenario, indent=4)
    except (Exception,) as err:
        messages.error(request, f'Błąd parsowania JSON: {err}')
        return HttpResponseRedirect(reverse('scenario_details', args=(sid,)))

    response = HttpResponse(content, content_type='application/json')
    response['Content-Disposition'] = f'attachment; filename={slugify(scenario.name)}.botie'
    return response


def set_theme(request):
    if 'theme' not in request.session:
        request.session['theme'] = 'auto'
    query = request.POST.get('name', '')
    if query:
        request.session['theme'] = query
        response = HttpResponse('theme changed')
        response.set_cookie(
            'theme',
            query,
            max_age=settings.LANGUAGE_COOKIE_AGE,
            path=settings.LANGUAGE_COOKIE_PATH,
            domain=settings.LANGUAGE_COOKIE_DOMAIN,
            secure=settings.LANGUAGE_COOKIE_SECURE,
            httponly=settings.LANGUAGE_COOKIE_HTTPONLY,
            samesite=settings.LANGUAGE_COOKIE_SAMESITE,
        )
        return response
    return HttpResponse('error occured')


def pricelist(request):
    context = {
        'pricelist_package': Product.objects.filter(type='PAC', active=True).order_by('value'),
        'pricelist_month': Product.objects.filter(type='SUB', active=True).order_by('value'),
        'pricelist_year': Product.objects.filter(type='YSU', active=True).order_by('value'),
        'has_active_subscription': su.has_active_subscription(request.user) if request.user.is_authenticated else False,
        'locked_for_changes': su.has_pending_subscription_event(request.user) if request.user.is_authenticated else False,
        'meta': get_static_meta('pricelist'),
    }
    return render(request, 'front/home_page/pricelist.html', context)


def features(request):
    return render(request, 'front/home_page/features.html')


def how_it_works(request):
    context = {
        'meta': get_static_meta('how_it_works'),
    }
    return render(request, 'front/home_page/how_it_works.html', context)


def store_regulations(request):
    return render(request, 'front/home_page/store_regulations.html')


def privacy_policy(request):
    return render(request, 'front/home_page/privacy_policy.html')


def app_download_guide(request):
    return render(request, 'front/home_page/app_download_guide.html')


def app_install_guide(request):
    return render(request, 'front/home_page/app_install_guide.html')


def app_launch_guide(request):
    return render(request, 'front/home_page/app_launch_guide.html')


# webalizer (direct access - reports in static folder)
# def webalizer_report(request):
#     return redirect('/static/webalizer/index.html')


# webalizer (access from admin site - reports in restricted media folder)
@staff_member_required
def serve_webalizer_report(request, filename):
    report_path = os.path.join(settings.WEBALIZER_REPORTS_DIR, filename)

    if not os.path.exists(report_path):
        return HttpResponseForbidden("File not found")

    return FileResponse(open(report_path, 'rb'))


def contact(request):
    if request.method == 'POST':
        form = ContactForm(request.POST, request.FILES)
        if form.is_valid():
            contact_request = form.save(commit=False)
            contact_request.save()

            # Obsługa przesyłania wielu plików i generowanie linków do pobrania
            files = request.FILES.getlist('files')
            file_urls = []
            for f in files:
                contact_file = ContactFile.objects.create(contact_request=contact_request, file=f)
                download_link = request.build_absolute_uri(reverse('download_file', args=[contact_file.id]))
                file_urls.append(download_link)
                print(f"Generated download link: {download_link}")

            send_contact_email(contact_request, to_customer=False, file_urls=file_urls)
            send_contact_email(contact_request, to_customer=True, file_urls=file_urls)

            messages.success(request, f'Wiadomość została wysłana. Numer sprawy: {contact_request.case_number}')
            return redirect('contact')
        else:
            print('Form is invalid')
            print(form.errors)
            messages.error(request, 'Wiadomość nie została wysłana')
            return render(request, 'front/home_page/contact.html', {'form': form})
    else:
        # Przygotowanie danych początkowych dla formularza
        initial = {}
        scenario_name = request.GET.get('scenario_name')
        scenario_sid = request.GET.get('scenario_sid')

        # Domyślnie wybierz "custom_scenario"
        initial['message_type'] = 'custom_scenario'

        # Jeśli przesłano dane o scenariuszu z botbook
        if scenario_name and scenario_sid:
            initial['scenario_name'] = scenario_name
            initial['description'] = f"SID: {scenario_sid}\n\n"

        form = ContactForm(initial=initial)

        # Przekaż dodatkowe dane do szablonu
        context = {
            'form': form,
            'scenario_name': scenario_name,
            'scenario_sid': scenario_sid,
            'default_selection': 'custom_scenario'
        }

    return render(request, 'front/home_page/contact.html', context)


def download_file(request, file_id):
    # Pobierz plik na podstawie jego ID
    contact_file = get_object_or_404(ContactFile, id=file_id)

    # Ścieżka do pliku na serwerze
    file_path = os.path.join(settings.MEDIA_ROOT, contact_file.file.name)

    # Ustal typ pliku (mimetype)
    mime_type, _ = mimetypes.guess_type(file_path)

    # Otwórz plik do odczytu w trybie binarnym
    with open(file_path, 'rb') as f:
        response = HttpResponse(f.read(), content_type=mime_type)
        response['Content-Disposition'] = f'attachment; filename={os.path.basename(file_path)}'
        return response

def send_contact_email(contact_request, to_customer=False, file_urls=None):
    # Lista odbiorców
    recipients = [contact_request.email] if to_customer else ['<EMAIL>']

    # Kontekst do renderowania szablonów
    context = {
        'contact_request': contact_request,
        'file_urls': file_urls,
        'to_customer': to_customer
    }

    # Renderowanie treści e-maila
    if to_customer:
        subject = render_to_string('account/email/contact_message_subject_customer.txt', context).strip()
        text_template = "account/email/contact_message_customer.txt"
        text_content = render_to_string(text_template, context).strip()
        html_template = "account/email/contact_message_customer.html"
        html_content = render_to_string(html_template, context).strip()
    else:
        subject = render_to_string('account/email/contact_message_subject_office.txt', context).strip()
        text_template = "account/email/contact_message_office.txt"
        text_content = render_to_string(text_template, context).strip()
        html_template = "account/email/contact_message_office.html"
        html_content = render_to_string(html_template, context).strip()

    # Wysyłanie wiadomości e-mail za pomocą EmailMultiAlternatives
    email_from = settings.EMAIL_HOST_USER
    msg = EmailMultiAlternatives(subject, text_content, email_from, recipients)
    msg.attach_alternative(html_content, "text/html")

    try:
        result = msg.send()
    except Exception as e:
        logging.error(f"Error sending email: {e}")
        result = False

    # Sprawdzenie, czy użytkownik jest zalogowany
    user = None
    if hasattr(contact_request, 'user') and contact_request.user.is_authenticated:
        user = contact_request.user

    # Zapisz powiadomienie o wysyłce wiadomości e-mail w bazie danych
    EmailNotification.objects.create(
        user=user,  # Zalogowany użytkownik lub None
        status_id=EmailNotification.Status.SENT if result else EmailNotification.Status.FAILED,
        message_id=EmailNotification.Message.CONTACT_FORM_SUBMITTED
    )

    return result


# user guide
class UserGuideView(View):
    @staticmethod
    def get(request, path='index.html'):
        user_guide_path = os.path.join(settings.BASE_DIR, 'user_guide', path)
        if os.path.exists(user_guide_path):
            content_type = 'text/html'
            if user_guide_path.endswith('.jpg'):
                content_type = 'image/jpeg'
            elif user_guide_path.endswith('.png'):
                content_type = 'image/png'
            elif user_guide_path.endswith('.gif'):
                content_type = 'image/gif'
            elif user_guide_path.endswith('.css'):
                content_type = 'text/css'
            elif user_guide_path.endswith('.js'):
                content_type = 'text/javascript'
            with open(user_guide_path, 'rb') as file:
                return HttpResponse(file.read(), content_type=content_type)
        else:
            raise Http404("File not found")


def find_html_files(request):
    keyword = request.GET.get('keyword', '')
    root_folder = os.path.join(settings.BASE_DIR, 'user_guide', 'content', 'pages')
    matching_files = []
    for foldername, subfolders, filenames in os.walk(root_folder):
        for filename in filenames:
            if filename.endswith('.html'):
                file_path = os.path.join(foldername, filename)
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    # strip tages
                    soup = BeautifulSoup(content, 'html.parser')
                    # lxml is more tolerant for bugs in DOM but needs to be installed
                    # soup = BeautifulSoup(content, 'lxml')
                    text = soup.get_text()
                    # check if page contains a keyword
                    if keyword.lower() in text.lower():
                        # get relative path
                        relative_path = os.path.relpath(file_path, root_folder)
                        # convert backslashes na slashes and add a base path
                        matching_files.append('/content/pages/'+relative_path.replace(os.sep, '/'))
    # return matching files as JSON dict
    return JsonResponse({"keyword": keyword, 'found_files': matching_files})


def newsletter_subscribe(request):
    """
    Widok do zapisywania się do newslettera
    """
    if request.method == 'POST':
        # Sprawdź czy to żądanie AJAX
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or 'application/json' in request.headers.get('Accept', ''):
            form = NewsletterForm(request.POST)
            if form.is_valid():
                subscription = form.save(commit=False)

                # Zapisz dodatkowe informacje
                subscription.ip_address = request.META.get('REMOTE_ADDR')
                subscription.user_agent = request.META.get('HTTP_USER_AGENT', '')
                subscription.save()

                # Wyślij e-mail potwierdzający
                from db.mailer import Mailer
                mailer = Mailer()
                mailer.newsletter_confirmation(subscription)

                return JsonResponse({
                    'success': True,
                    'message': 'Dziękujemy za zapisanie się do newslettera! Sprawdź swoją skrzynkę e-mail i kliknij link potwierdzający.'
                })
            else:
                errors = []
                for field, field_errors in form.errors.items():
                    for error in field_errors:
                        errors.append(f"{field}: {error}")
                return JsonResponse({
                    'success': False,
                    'message': 'Wystąpił błąd: ' + '; '.join(errors)
                })
        else:
            # Standardowe żądanie POST
            form = NewsletterForm(request.POST)
            if form.is_valid():
                subscription = form.save(commit=False)

                # Zapisz dodatkowe informacje
                subscription.ip_address = request.META.get('REMOTE_ADDR')
                subscription.user_agent = request.META.get('HTTP_USER_AGENT', '')
                subscription.save()

                # Wyślij e-mail potwierdzający
                from db.mailer import Mailer
                mailer = Mailer()
                mailer.newsletter_confirmation(subscription)

                messages.success(request,
                    'Dziękujemy za zapisanie się do newslettera! '
                    'Sprawdź swoją skrzynkę e-mail i kliknij link potwierdzający.')
                return redirect('index')
            else:
                messages.error(request, 'Wystąpił błąd podczas zapisywania do newslettera. Sprawdź wprowadzone dane.')
    else:
        form = NewsletterForm()

    return render(request, 'front/newsletter/subscribe.html', {'form': form})


def newsletter_confirm(request, token):
    """
    Widok do potwierdzania subskrypcji newslettera
    """
    try:
        subscription = NewsletterSubscription.objects.get(confirmation_token=token)

        if subscription.is_confirmed:
            messages.info(request, 'Ta subskrypcja została już wcześniej potwierdzona.')
        else:
            subscription.is_confirmed = True
            subscription.confirmed_at = timezone.now()
            subscription.save()

            # Wyślij e-mail powitalny
            from db.mailer import Mailer
            mailer = Mailer()
            mailer.newsletter_welcome(subscription)

            messages.success(request,
                'Dziękujemy! Twoja subskrypcja newslettera została potwierdzona. '
                'Wkrótce otrzymasz e-mail powitalny.')

    except NewsletterSubscription.DoesNotExist:
        messages.error(request, 'Nieprawidłowy link potwierdzający.')

    return redirect('index')


def newsletter_unsubscribe(request, token):
    """
    Widok do wypisywania się z newslettera
    """
    try:
        subscription = NewsletterSubscription.objects.get(confirmation_token=token)
        subscription.delete()
        messages.success(request, 'Zostałeś wypisany z newslettera.')
    except NewsletterSubscription.DoesNotExist:
        messages.error(request, 'Nieprawidłowy link.')

    return redirect('index')
