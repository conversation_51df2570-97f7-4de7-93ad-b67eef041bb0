# Generated by Django 4.2.13 on 2024-09-26 16:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('front', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('message_type', models.Char<PERSON>ield(max_length=50)),
                ('description', models.TextField()),
                ('base_scenario', models.CharField(blank=True, max_length=200, null=True)),
                ('contact_preference', models.CharField(max_length=50)),
                ('contact_info', models.Char<PERSON><PERSON>(max_length=100)),
            ],
        ),
    ]
