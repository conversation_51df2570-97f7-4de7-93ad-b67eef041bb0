"""
Django settings for robosourcing project.
Generated by 'django-admin startproject' using Django 4.1.5.
For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/
For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

import os
from datetime import timedelta, datetime

import pytz
from django.conf import settings
from dotenv import dotenv_values
from pathlib import Path
from django.contrib.messages import constants as messages
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent

PROJECT_DIR = os.path.abspath(os.path.dirname(__file__))
FIXTURE_DIRS = (
   os.path.join(PROJECT_DIR, 'fixtures'),
)

# Load .env variables as dict
dotenv_path = os.path.join(os.path.dirname(PROJECT_DIR), '.env')
config = dotenv_values(dotenv_path)

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-7zez6o9=64ku7mbw%9r@y%jux#t_y(u0#)$lrw8gb56(o#+x=e'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    'rest_framework',
    'api',
    'db',
    'store',
    'seo',
    'profiles',
    'scenarios',
    'robo_map',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'allauth.socialaccount.providers.github',
    'allauth.socialaccount.providers.facebook',
    'crispy_forms',
    'crispy_bootstrap5',
    'phonenumber_field',
    'django_extensions',
    'bootstrap5',
    'simple_history',
    'rest_framework_simplejwt',
    'drf_yasg',
    'front.apps.FrontConfig',
    'admin_reorder',
    'webalizer',
    'meta',
    # 'sass_processor',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'simple_history.middleware.HistoryRequestMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'admin_reorder.middleware.ModelAdminReorder',
    'robosourcing.middleware.CustomMiddleware',
    'robosourcing.middleware.ReferralTrackingMiddleware',
]

ROOT_URLCONF = 'robosourcing.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        # 'DIRS': ['robosourcing/templates'],
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'profiles.context_processors.timezones',
            ],
            'libraries': {
                # 'divide': 'front.templatetags.divide',
            }
        },
    },
]

WSGI_APPLICATION = 'robosourcing.wsgi.application'

MESSAGE_TAGS = {
        messages.DEBUG: 'alert-secondary',
        messages.INFO: 'alert-info',
        messages.SUCCESS: 'alert-success',
        messages.WARNING: 'alert-warning',
        messages.ERROR: 'alert-danger',
 }

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

if config['DB_MODE'] == 'SRV':
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': config['DB_NAME'],
            'USER': config['DB_USER'],
            'PASSWORD': config['DB_PASSWORD'],
            'HOST': config['DB_HOST'],
            'PORT': config['DB_PORT'],
        }
    }
    ALLOWED_HOSTS = config['ALLOWED_HOSTS'].split(',')
    if 'DEBUG' in config:
        if config['DEBUG'] == 'False':
            DEBUG = False
            SESSION_COOKIE_SECURE = True
#            CSRF_COOKIE_SECURE = True
            SECURE_SSL_REDIRECT = True
            SECURE_HSTS_SECONDS = 31536000
            SECURE_HSTS_PRELOAD = True
            SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    # INSTALLED_APPS += (
    # 	'mod_wsgi.server',
    # )
    # SESSION_COOKIE_SECURE = True
    # CSRF_COOKIE_SECURE = True
    # SECURE_SSL_REDIRECT = True
    # SECURE_HSTS_SECONDS = 31536000
    # SECURE_HSTS_PRELOAD = True
    # SECURE_HSTS_INCLUDE_SUBDOMAINS = True
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR.parent / 'db.sqlite3',
        }
    }


# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

# LANGUAGE_CODE = 'en-us'
LANGUAGE_CODE = 'pl'
# TIME_ZONE = 'UTC'
TIME_ZONE = 'Europe/Warsaw'
USE_I18N = True
USE_L10N = True
USE_TZ = True

LANGUAGES = [
#    ('en-us', _('English')),
    ('pl', _('Polish')),

]

LOCALE_PATHS = (BASE_DIR / 'locale', )

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'sass_processor.finders.CssFinder',
]

STATIC_URL = 'static/'
if config['DB_MODE'] == 'SRV':
    STATICFILES_DIRS = [
       BASE_DIR / "static",
    ]
    # STATIC_ROOT = os.path.join(BASE_DIR, 'static')
else:
    STATICFILES_DIRS = []
    STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# because of constantly changing static by developers use config to steer this
if 'USE_STATIC_ROOT' in config:
    if config['USE_STATIC_ROOT'] == 'True':
        STATICFILES_DIRS = []
        STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# webalizer reports path (access from admin)
# WEBALIZER_DIR = BASE_DIR / "static" / "webalizer"
WEBALIZER_DIR = BASE_DIR / "media" / "webalizer"

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.BasicAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        # 'rest_framework.permissions.IsAuthenticated',
        'rest_framework.permissions.AllowAny',
    ],
    # 'DEFAULT_RENDERER_CLASSES': [
    #     'rest_framework.renderers.BrowsableAPIRenderer',
    #     # 'rest_framework.renderers.JSONRenderer',
    # ],
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=5),
    "REFRESH_TOKEN_LIFETIME": timedelta(minutes=15),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": False,

    "ALGORITHM": "HS256",
    "SIGNING_KEY": settings.SECRET_KEY,
    "VERIFYING_KEY": "",
    "AUDIENCE": None,
    "ISSUER": None,
    "JSON_ENCODER": None,
    "JWK_URL": None,
    "LEEWAY": 0,

    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",

    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "TOKEN_USER_CLASS": "rest_framework_simplejwt.models.TokenUser",

    "JTI_CLAIM": "jti",

    "SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
    "SLIDING_TOKEN_LIFETIME": timedelta(minutes=5),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),

    "TOKEN_OBTAIN_SERIALIZER": "api.token.RoboTokenObtainPairSerializer",
    # "TOKEN_OBTAIN_SERIALIZER": "rest_framework_simplejwt.serializers.TokenObtainPairSerializer",
    "TOKEN_REFRESH_SERIALIZER": "rest_framework_simplejwt.serializers.TokenRefreshSerializer",
    "TOKEN_VERIFY_SERIALIZER": "rest_framework_simplejwt.serializers.TokenVerifySerializer",
    "TOKEN_BLACKLIST_SERIALIZER": "rest_framework_simplejwt.serializers.TokenBlacklistSerializer",
    "SLIDING_TOKEN_OBTAIN_SERIALIZER": "rest_framework_simplejwt.serializers.TokenObtainSlidingSerializer",
    "SLIDING_TOKEN_REFRESH_SERIALIZER": "rest_framework_simplejwt.serializers.TokenRefreshSlidingSerializer",
}

AUTHENTICTION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    # `allauth` specific authentication methods, such as login by e-mail
    'allauth.account.auth_backends.AuthenticationBackend',
]

ACCOUNT_FORMS = {
    'signup': 'profiles.forms.CustomSignupForm',
}

ACCOUNT_ADAPTER = 'profiles.adapters.MyAccountAdapter'


SITE_ID = 1

# Registration of local accounts
# ACCOUNT_AUTHENTICATION_METHOD = 'email'
# ACCOUNT_AUTHENTICATION_METHOD = 'username_email'
ACCOUNT_EMAIL_REQUIRED = True
# ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_UNIQUE_EMAIL = False
# ACCOUNT_EMAIL_VERIFICATION = 'none'
# ACCOUNT_EMAIL_VERIFICATION = 'optional'
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_CONFIRM_EMAIL_ON_GET = False
# LOGIN_REDIRECT_URL = 'home'  # dashboard index
LOGIN_REDIRECT_URL = 'index'  # main page index

# Log in with social accounts
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
        },
        'OAUTH_PKCE_ENABLED': True,
    },
    'facebook': {
        'METHOD': 'oauth2',
        'SCOPE': ['email', 'public_profile'],
        'AUTH_PARAMS': {'auth_type': 'reauthenticate'},
        'INIT_PARAMS': {'cookie': True},
        'FIELDS': [
            'id',
            'first_name',
            'last_name',
            'middle_name',
            'name',
            'name_format',
            'picture',
            'short_name'
        ],
        'EXCHANGE_TOKEN': True,
        # 'VERIFIED_EMAIL': False,
        'VERIFIED_EMAIL': True,
        'VERSION': 'v13.0',
        'GRAPH_API_URL': 'https://graph.facebook.com/v13.0',
    }
}
SOCIALACCOUNT_LOGIN_ON_GET = False
SOCIALACCOUNT_EMAIL_AUTHENTICATION = True
SOCIALACCOUNT_EMAIL_AUTHENTICATION_AUTO_CONNECT = True
ACCOUNT_LOGOUT_REDIRECT_URL = 'index'

# Crispy forms Bootstrap5 styled
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

GRAPH_MODELS = {
    # 'all_applications': True,
    'group_models': True,
    'app_labels': ["profiles", "db"],
}

# Django Logging Information
LOGGING = {
    # Define the logging version
    'version': 1,
    # Enable the existing loggers
    'disable_existing_loggers': False,

    # Define the handlers
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'djangoapp.log',
        },

        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
        },
    },

    # Define the loggers
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,

        },
    },
}

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'uploads')

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

CSRF_USE_SESSIONS = False
CSRF_COOKIE_HTTPONLY = False 

SCENARIOS_API_URL = 'https://***************:8000/api/'
SCENARIOS_API_URL_LOCAL = 'http://127.0.0.1:8000/api/'
SCENARIOS_API_HEADERS = {
    'accept': 'application/json',
    'X-CSRFToken': 'xhorHyohHXk9GdqtAEa6E2mGNA2h1M5EEjNJ9YJhfEuRsU4przW13yxOjmG2G5xk',
}

# Django Mail
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_BACKEND = "django.core.mail.backends.filebased.EmailBackend"
EMAIL_HOST = 'mail-serwer64472.lh.pl'
EMAIL_PORT = 587
# EMAIL_PORT = 465
EMAIL_TIMEOUT = 30
EMAIL_USE_TLS = False
EMAIL_USE_SSL = False
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'Kt%Q3Fuo'
# EMAIL_FILE_PATH = BASE_DIR / "sent_emails"
DEFAULT_FROM_EMAIL = 'BotieWeb <<EMAIL>>'
ACCOUNT_ADAPTER = 'robosourcing.adapters.CustomAccountAdapter'

# Download Section
# server settings
LOCAL_REPOSITORY_PATH = '/opt/instalki/'  # Django based file server repo
GEOIP2_CITY_DB_PATH = '/opt/geoip2/GeoLite2-City.mmdb'  # database used in Djangp administration
# dev settings
# LOCAL_REPOSITORY_PATH = 'g:\\Botie Installer\\'  # Django based file server repo
# GEOIP2_CITY_DB_PATH = 'g:\\RBS\\GeoLite2-City.mmdb'  # database used in Djangp administration

SESSION_EXPIRE_AT_BROWSER_CLOSE = False

ADMIN_REORDER = (
    # Cross-linked model
    {'app': 'store', 'label': 'Site settings', 'models': ('sites.Site', 'front.CarouselItem', 'front.CategoryCard',
                                                          'socialaccount.SocialApp')},

    # Cross-linked model
    {'app': 'store', 'label': 'Store manager', 'models': ('store.Product', 'store.Order',
                                                          'store.SubsequentOrder', 'store.SubscriptionEvent',
                                                          'store.RecurringPayment', 'db.UserSubscription',
                                                          'db.Credit', 'db.CreditEvent', 'store.Invoice')},

    # Cross-linked model
    {'app': 'store', 'label': 'Referral codes manager', 'models': ('db.MarketingCampaign', 'db.Visit')},

    # Cross-linked model
    {'app': 'store', 'label': 'Users manager', 'models': ('auth.Group', 'auth.User', 'profiles.UserProfile',
                                                          'db.AffiliateLink', 'db.CommissionRateHistory', 'db.PartnerCommission', 'db.CommissionChangeEvent', 'db.AffiliateWithdrawalRequest',
                                                          'db.WithdrawalCycle', 'profiles.Address', 'profiles.Contact',
                                                          'profiles.Organization', 'profiles.OrganizationUUID',
                                                          'profiles.Consent',
                                                          'account.EmailAddress', 'db.EmailNotification',
                                                          'front.NewsletterSubscription', 'front.NewsletterSegment',
                                                          'front.NewsletterTemplate', 'front.NewsletterCampaign',
                                                          'front.NewsletterDelivery', 'front.NewsletterOpen',
                                                          'front.NewsletterClick', 'front.ExternalMailingService',
                                                          'socialaccount.SocialAccount', 'socialaccount.SocialToken')},

    # Cross-linked model
    {'app': 'store', 'label': 'Downloads manager', 'models': ('db.DownloadItem', 'db.DownloadPackage',
                                                              'db.DownloadPlatform', 'db.DownloadRequirement',
                                                              'db.DownloadServer', 'db.DownloadEvent')},

    # Cross-linked model
    {'app': 'store', 'label': 'Scenarios manager', 'models': ('db.Scenario', 'db.StartScenario', 'db.Category',
                                                              'db.Tag', 'db.TagGroup', 'db.Program', 'db.Scenario_Event', 'db.File')},

    # Cross-linked model
    {'app': 'store', 'label': 'Robots manager', 'models': ('db.Robot', )},

    #Seo app
    {'app': 'seo', 'label': 'SEO manager', 'models': ('seo.StaticPageMeta', )},

    # Original label and models
    # 'sites',
    # 'db',
    # 'store',
    # 'auth',
    # 'front',
    # 'account',
    # 'socialaccount',
    # 'profiles',
)

# Store settings
if 'STORE_URL' in config:
    STORE_URL = config['STORE_URL']
else:
    STORE_URL = 'https://s1.robosourcing.pl:8001'
if 'ISSUE_INVOICES' in config:
    ISSUE_INVOICES = True if config['ISSUE_INVOICES'] == 'True' else False
else:
    ISSUE_INVOICES = False
if 'PAYU_ENABLED' in config:
    PAYU_ENABLED = True if config['PAYU_ENABLED'] == 'True' else False
else:
    PAYU_ENABLED = True
if 'PAYU_USE_SANDBOX' in config:
    PAYU_USE_SANDBOX = True if config['PAYU_USE_SANDBOX'] == 'True' else False
else:
    PAYU_USE_SANDBOX = True
if 'VIVA_ENABLED' in config:
    VIVA_ENABLED = True if config['VIVA_ENABLED'] == 'True' else False
else:
    VIVA_ENABLED = True
if 'SKIP_PAYMENT_ENABLED' in config:
    SKIP_PAYMENT_ENABLED = True if config['SKIP_PAYMENT_ENABLED'] == 'True' else False
else:
    SKIP_PAYMENT_ENABLED = True

# Scheduler settings
SCHEDULER_TEST_MODE = False
SHORT_PERIOD = 'day'
LONG_PERIOD = 'week'
if 'SCHEDULER_TEST_MODE' in config and config['SCHEDULER_TEST_MODE'] == 'True':
    SCHEDULER_TEST_MODE = True
    if 'SHORT_PERIOD' in config:
        SHORT_PERIOD = config['SHORT_PERIOD']
    if 'LONG_PERIOD' in config:
        LONG_PERIOD = config['LONG_PERIOD']

# Server time accelerator
ACCELERATE_TIME = False
if 'ACCELERATE_TIME' in config and config['ACCELERATE_TIME'] == 'True':
    ACCELERATE_TIME = True
    BASE_TIME = datetime(2024, 8, 15, 0, 0, 0, tzinfo=timezone.utc)
    SPEED_FACTOR = 1
    if 'BASE_TIME' in config and 'SPEED_FACTOR' in config:
        timezone = pytz.timezone(TIME_ZONE)
        BASE_TIME = timezone.localize(datetime.strptime(config['BASE_TIME'], "%Y-%m-%d %H:%M:%S"))
        SPEED_FACTOR = int(config['SPEED_FACTOR'])

# Upload files settings
MAX_BYTES_SIZE = 10000000   #10MB

# Allow loadind page urls in frames (user guide)
X_FRAME_OPTIONS = 'SAMEORIGIN'
