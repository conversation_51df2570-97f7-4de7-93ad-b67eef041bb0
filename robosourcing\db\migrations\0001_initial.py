# Generated by Django 4.1.7 on 2024-05-06 20:49

import db.models
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='App',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('version_number', models.CharField(max_length=50)),
                ('build_number', models.CharField(default=None, max_length=50, null=True)),
                ('producer', models.CharField(default=None, max_length=100, null=True)),
            ],
            options={
                'ordering': ['name', '-version_number', '-build_number', 'producer'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('name', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('parent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='db.category')),
            ],
        ),
        migrations.CreateModel(
            name='DownloadPlatform',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nazwa platformy (np. Windows XP)', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('default', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='DownloadRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('os', models.TextField(default='Windows 7 / 10 / 11')),
                ('processor', models.TextField(default='Intel Core i3 lub nowszy')),
                ('ram', models.TextField(default='2GB lub więcej')),
                ('disk', models.TextField(default='200MB lub więcej')),
                ('graphic', models.TextField(default='Zgodna z DirectX 12 / OpenGL 4.5')),
            ],
        ),
        migrations.CreateModel(
            name='DownloadServer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nazwa serwera (np. Serwer Europa Wschodnia)', max_length=255)),
                ('id_based_url', models.URLField(help_text='Adres IP (np. https://***************)')),
                ('domain_name_based_url', models.URLField(help_text='Adres domenowy (np. https://s1.robosourcing.pl)')),
                ('repository_path', models.CharField(help_text='Lokalizacja plików na danym serwerze (np. /instalki)', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('default', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Hash',
            fields=[
                ('hash', models.CharField(max_length=64, primary_key=True, serialize=False, validators=[django.core.validators.MinLengthValidator(64)])),
                ('info', models.TextField(blank=True, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['hash'],
            },
        ),
        migrations.CreateModel(
            name='Hid',
            fields=[
                ('hid', models.CharField(max_length=64, primary_key=True, serialize=False, validators=[django.core.validators.MinLengthValidator(64)])),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('update_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Map',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('info', models.TextField(blank=True, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_source', to='db.hash')),
                ('target', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_target', to='db.hash')),
            ],
            options={
                'ordering': ['source', 'target'],
            },
        ),
        migrations.CreateModel(
            name='Pid',
            fields=[
                ('pid', models.UUIDField(editable=False, primary_key=True, serialize=False)),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('update_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Program',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('version_number', models.CharField(max_length=50)),
                ('build_number', models.CharField(default=None, max_length=50, null=True)),
                ('producer', models.CharField(default=None, max_length=100, null=True)),
            ],
            options={
                'ordering': ['name', '-version_number', '-build_number', 'producer'],
            },
        ),
        migrations.CreateModel(
            name='Robot',
            fields=[
                ('rid', models.UUIDField(auto_created=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('last_activ_time', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('description', models.CharField(blank=True, max_length=200, null=True)),
                ('locked', models.BooleanField(default=False)),
                ('lock_reason', models.CharField(choices=[('USR', 'Locked by User'), ('ADM', 'Locked by Admin'), ('JOB', 'Locked by active job')], max_length=3, null=True)),
                ('cached_credits', models.PositiveBigIntegerField(default=0)),
                ('locked_credits', models.PositiveBigIntegerField(default=0)),
                ('pool_credits', models.PositiveBigIntegerField(default=0)),
                ('min_buffer', models.PositiveBigIntegerField(default=0)),
                ('hid', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='db.hid')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('payer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_payer', to=settings.AUTH_USER_MODEL)),
                ('pid', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='db.pid')),
            ],
            options={
                'ordering': ['owner', 'last_activ_time'],
            },
        ),
        migrations.CreateModel(
            name='Scenario',
            fields=[
                ('sid', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=150)),
                ('scenario_version', models.IntegerField(default=0)),
                ('description', models.CharField(max_length=500)),
                ('author', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last update date')),
                ('accepted', models.BooleanField(default=False)),
                ('scenario', models.JSONField()),
                ('price', models.PositiveBigIntegerField(default=0)),
                ('status', models.IntegerField(choices=[(0, 'Sent to moderation'), (1, 'Assigned to moderator'), (2, 'Scenario in moderation'), (3, 'Scenario rejected'), (4, 'Scenario accepted'), (5, 'Scenario suspended'), (6, 'Scenario canceled')], default=0)),
                ('publish_time', models.DateTimeField(null=True, verbose_name='First publication date')),
                ('is_fixed_cost', models.BooleanField(default=False)),
                ('available', models.IntegerField(choices=[(10, 'Private'), (20, 'Limited to account'), (50, 'limited to group'), (90, 'Public')], default=10)),
                ('editable', models.IntegerField(choices=[(10, 'Private'), (20, 'Limited to account'), (50, 'limited to group'), (90, 'Public')], default=10)),
                ('updatable', models.IntegerField(choices=[(10, 'Private'), (20, 'Limited to account'), (50, 'limited to group'), (90, 'Public')], default=10)),
                ('moderator', models.ForeignKey(blank=True, limit_choices_to={'userprofile__role': 'MOD'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_scenarios', to=settings.AUTH_USER_MODEL, verbose_name='Assigned moderator')),
                ('owner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scenarios', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['sid'],
            },
        ),
        migrations.CreateModel(
            name='Scenario_Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_time', models.DateTimeField(auto_now_add=True)),
                ('type_id', models.IntegerField(choices=[(0, 'Sent to moderation'), (1, 'Assigned to moderator'), (2, 'Scenario in moderation'), (3, 'Scenario rejected'), (4, 'Scenario accepted'), (5, 'Scenario added to favorites'), (6, 'Scenario file dawnloaded'), (7, 'Scenario suspended'), (8, 'Scenario restored'), (9, 'Scenario canceled'), (10, 'Return Scenario to pool'), (11, 'Scenario removed from favorites')], default=0)),
                ('rid', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='db.robot')),
                ('sid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.scenario')),
                ('user_id', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('name', models.CharField(max_length=50, primary_key=True, serialize=False)),
            ],
        ),
        migrations.CreateModel(
            name='UserSubscription',
            fields=[
                ('active', models.BooleanField(default=True)),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('update_counter', models.PositiveIntegerField(default=0)),
                ('user', models.OneToOneField(editable=False, on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('value', models.PositiveIntegerField()),
                ('price', models.FloatField()),
                ('renew_date', models.DateTimeField(default=db.models.UserSubscription.one_month_later)),
            ],
            options={
                'ordering': ['user_id'],
            },
        ),
        migrations.CreateModel(
            name='UserStorage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('update_time', models.DateTimeField(auto_now=True)),
                ('rid', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='db.robot')),
                ('sid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.scenario')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user', 'sid', 'rid', 'update_time', 'create_time'],
            },
        ),
        migrations.CreateModel(
            name='Step',
            fields=[
                ('step', models.PositiveIntegerField(primary_key=True, serialize=False)),
                ('job', models.TextField(blank=True, null=True)),
                ('info', models.TextField(blank=True, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.map')),
            ],
            options={
                'ordering': ['id', 'step'],
            },
        ),
        migrations.CreateModel(
            name='Scenario_Event_Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.CharField(max_length=2000)),
                ('event_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.scenario_event')),
            ],
        ),
        migrations.CreateModel(
            name='FavoriteScenario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('scenario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='db.scenario')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorite_scenarios', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Favorite Scenario',
                'verbose_name_plural': 'Favorite Scenarios',
                'ordering': ['-added_at'],
            },
        ),
        migrations.CreateModel(
            name='Element',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tree_index', models.TextField()),
                ('hash_txt', models.TextField()),
                ('label', models.TextField(blank=True, null=True)),
                ('typedef_val', models.TextField(blank=True, null=True)),
                ('text', models.TextField(blank=True, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('hash', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.hash')),
            ],
            options={
                'ordering': ['hash', 'tree_index'],
            },
        ),
        migrations.CreateModel(
            name='DownloadPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='MSI, EXE, ZIP, etc.', max_length=10)),
                ('active', models.BooleanField(default=True)),
                ('default', models.BooleanField(default=False)),
                ('platform_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='db.downloadplatform')),
            ],
        ),
        migrations.CreateModel(
            name='DownloadItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('release_version', models.CharField(max_length=10)),
                ('release_date', models.DateField()),
                ('release_date_time', models.DateTimeField(auto_now_add=True, null=True)),
                ('publish_time', models.DateTimeField(blank=True, help_text='Data i czas początku wyświetlania', null=True)),
                ('end_time', models.DateTimeField(blank=True, help_text='Data i czas zakończenia wyświetlania', null=True)),
                ('is_default', models.BooleanField(default=False, help_text='Czy ma być wersją domyślną dla platformy i pakietu?')),
                ('is_active', models.BooleanField(default=True)),
                ('filename', models.TextField()),
                ('short_description', models.TextField(blank=True, null=True)),
                ('full_description', models.TextField(blank=True, null=True)),
                ('package_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='db.downloadpackage')),
                ('platform_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='db.downloadplatform')),
                ('requirements', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='db.downloadrequirement')),
                ('server_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='db.downloadserver')),
            ],
        ),
        migrations.CreateModel(
            name='DownloadEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_time', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('region', models.CharField(max_length=255)),
                ('version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='download_events', to='db.downloaditem')),
            ],
        ),
        migrations.CreateModel(
            name='Credit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('credits', models.PositiveBigIntegerField(default=0)),
                ('locked_credits', models.PositiveBigIntegerField(default=0)),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('update_time', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user', 'update_time', 'create_time'],
            },
        ),
        migrations.CreateModel(
            name='Arch_Robot',
            fields=[
                ('rid', models.UUIDField(auto_created=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('arch_time', models.DateTimeField(auto_now_add=True)),
                ('create_time', models.DateTimeField()),
                ('last_activ_time', models.DateTimeField()),
                ('hid', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='db.hid')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('pid', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='db.pid')),
            ],
            options={
                'ordering': ['owner', 'hid', 'pid', 'arch_time', 'last_activ_time'],
            },
        ),
        migrations.CreateModel(
            name='ScenarioTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.scenario')),
                ('tag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.tag')),
            ],
            options={
                'ordering': ['sid', 'tag'],
                'unique_together': {('sid', 'tag')},
            },
        ),
        migrations.CreateModel(
            name='Scenario_Program',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.program')),
                ('sid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.scenario')),
            ],
            options={
                'ordering': ['sid', 'program'],
                'unique_together': {('sid', 'program')},
            },
        ),
        migrations.CreateModel(
            name='Scenario_Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.category')),
                ('sid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='db.scenario')),
            ],
            options={
                'ordering': ['sid', 'category'],
                'unique_together': {('sid', 'category')},
            },
        ),
        migrations.AddConstraint(
            model_name='robot',
            constraint=models.CheckConstraint(check=models.Q(('hid__isnull', False), ('owner__isnull', False), _connector='OR'), name='robot_not_both_null'),
        ),
        migrations.AlterUniqueTogether(
            name='robot',
            unique_together={('hid', 'pid')},
        ),
        migrations.AlterUniqueTogether(
            name='favoritescenario',
            unique_together={('user', 'scenario')},
        ),
        migrations.AlterUniqueTogether(
            name='element',
            unique_together={('hash', 'tree_index')},
        ),
    ]
