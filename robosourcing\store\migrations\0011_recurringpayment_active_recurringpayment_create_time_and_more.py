# Generated by Django 4.1.9 on 2024-06-10 08:10

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0010_recurringpayment_annual_payment'),
    ]

    operations = [
        migrations.AddField(
            model_name='recurringpayment',
            name='active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='recurringpayment',
            name='create_time',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='recurringpayment',
            name='last_update',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
