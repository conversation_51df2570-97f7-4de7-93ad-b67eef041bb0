{% extends 'main.html' %}
{% load i18n %}
{% load static %}
{% load key %}
{% load gettype %}

{% block head_title %}{% trans "Scenarios" %}{% endblock head_title %}

{% block content %}
<div id="botbook" class="botbook section">
    <div class="container section-title fade-in pb-2">
        <h2>{% trans "Skills" %}</h2>
        <p>{% trans "Find skill in BotBoook and automate your work" %}</p>
    </div>

    <div class="container">

        <!-- Breadcrumbs -->
        
        <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item botbook-breadcrumb-item">
                <a href="{% url 'scenarios_page' %}" class="botbook-breadcrumb-item">{% trans "All Categories" %}</a>
            </li>
            {% for cat in breadcrumbs %}
            {% if forloop.last %}
                <li class="breadcrumb-item active  botbook-breadcrumb-item" aria-current="page">{{ cat.name }}</li>
            {% else %}
                <li class="breadcrumb-item  botbook-breadcrumb-item">
                    <a href="?cat={{ cat.name }}" class="botbook-breadcrumb-item">{{ cat.name }}</a>
                </li>
            {% endif %}
            {% endfor %}
        </ol>
        </nav>
        

        <!-- GÓRNY WIERSZ - pełnowymiarowy formularz wyszukiwania -->
        <div class="row mb-3">
            <div class="col-12">
                <form method="GET" action="{% url 'scenarios_page' %}">
                    {% for tag in selected_tags %}
                        <input type="hidden" name="tags" value="{{ tag }}">
                    {% endfor %}
                    {% if current_category %}
                        <input type="hidden" name="cat" value="{{ current_category.name }}">
                    {% endif %}

                    <!-- Wiersz: Pole wyszukiwania -->
                    <div class="input-group mb-3">
                        <input
                            type="text"
                            id="tag-input"
                            name="q"
                            placeholder="{% trans 'Search by a keyword or a tag' %}"
                            value="{{ query }}"
                            class="form-control py-2"
                            autocomplete="off"
                        >
                        <button type="button" class="btn btn-secondary" id="clear-search">
                            <i class="fa fa-xmark"></i>
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>

                    <!-- Wiersz: Autouzupełnianie tagów (wyświetlane dynamicznie) -->
                    <div class="mb-3">
                        <div id="tags-to-add-header" style="display: none;">
                            <p class="text-muted">{% trans "Tags to add:" %}</p>
                        </div>
                        <div id="live-search-results" class="tags-autocomplete-list"></div>
                    </div>

                    <!-- Wiersz: Already selected tags -->
                    {% if selected_tags %}
                        <div class="mb-3">
                            <div>
                                <p class="text-muted">{% trans "Selected tags:" %}</p>
                            </div>
                            <div class="selected-tags">
                                {% for tag in selected_tags %}
                                    <span class="btn btn-sm btn-outline-secondary selected-tag mt-1 me-1">
                                        #{{ tag }}
                                        <a
                                            href="?tags=&{% for t in selected_tags %}{% if not t == tag %}tags={{ t }}&{% endif %}{% endfor %}{% if current_category %}cat={{ current_category.name }}{% endif %}"
                                            class="remove-tag"
                                        >
                                            <i class="fa fa-xmark text-danger"></i>
                                        </a>
                                    </span>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- DOLNY WIERSZ - Lewa kolumna kategorie, prawa kolumna scenariusze -->
        <div class="row">
            <!-- LEWA KOLUMNA: PANEL KATEGORII -->
            <div class="col-md-3 mb-3">
                <div class="sidebar-categories">
                    <div class="section-title pb-1">
                        <h2 class="mb-3">{% trans "Categories" %}</h2>
                    </div>
                    
                    {% if current_category %}
                        {% if parent_category %}
                            <div class="mb-2">
                                <a href="?cat={{ parent_category.name }}" class="btn btn-link p-0 back-link">
                                    &larr; {% trans "Back to" %} {{ parent_category.name }}
                                </a>
                            </div>
                        {% else %}
                            <div class="mb-2">
                                <a href="{% url 'scenarios_page' %}" class="btn btn-link p-0 back-link">
                                    &larr; {% trans "All Categories" %}
                                </a>
                            </div>
                        {% endif %}
                    {% endif %}
            
                    {% if current_category %}
                        <p><strong>{{ current_category.name }}</strong></p>
                    {% endif %}
            
                    <!-- Zamiast list-group używamy własnych klas -->
                    <ul class="category-list">
                        {% for cat, scenario_count in sidebar_categories_with_counts %}
                            <li class="category-list-item d-flex justify-content-between align-items-center">
                                <a href="?cat={{ cat.name }}" class="category-link text-decoration-none">
                                    {{ cat.name }}
                                </a>
                                <span class="category-scenario-count">
                                    {{ scenario_count }}
                                </span>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            

            <!-- PRAWA KOLUMNA: SCENARIUSZE -->
            <div class="col-md-9">
                <div class="row row-cols-1 row-cols-lg-2 row-cols-xxl-3 g-3">
                    {% for scenario in scenarios %}
                        <div class="col">
                            <div class="ratio ratio-16x9">
                                <div class="botbook-card shadow-sm">
                                    <div class="card-header pb-0">
                                        <h2>{{ scenario.name|truncatechars:150 }}</h2>
                                    </div>
                                    <div class="card-body p-0 mt-3">
                                        <p>{{ scenario.description|truncatechars:500|linebreaksbr }}</p>
                                    </div>
                                    <div class="card-footer d-flex justify-content-between align-items-center">
                                        <div class="tag-icons">
                                            {% for group in scenario.tag_groups %}
                                              {% if group.icon %}
                                                <img
                                                  src="{{ group.icon.url }}"
                                                  alt="{{ group.name }}"
                                                  title="{{ group.name }}"
                                                  class="tag-icon"
                                                  style="width:24px; height:24px; margin-right:5px;"
                                                />
                                              {% endif %}
                                            {% endfor %}
                                          </div>
                                        <a href="{% url 'scenario_details' scenario.sid %}" class="btn btn-sm btn-outline-primary">
                                            {% trans 'Details' %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Paginacja -->
                {% if scenarios.has_other_pages %}
                    <nav aria-label="..." class="mt-3">
                        {% include "front/home_page/pagination.html" with objects=scenarios %}
                    </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="mt-5 mb-5 divider"></div>

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[action="{% url 'scenarios_page' %}"]');

    // -------- TAGS LOGIC --------
    function fetchTags() {
        const input = document.getElementById('tag-input').value;
        const selectedTags = [...form.querySelectorAll('input[name="tags"]')].map(i => i.value);
        const headerElem = document.getElementById('tags-to-add-header');

        if (input.length > 1) {
            const searchParams = new URLSearchParams();
            selectedTags.forEach(tag => searchParams.append('selected_tags', tag));

            fetch(`{% url 'tags_autocomplete' %}?q=${input}&${searchParams.toString()}`)
                .then(response => response.json())
                .then(data => {
                    const tagsList = document.getElementById('live-search-results');
                    tagsList.innerHTML = "";

                    if (data.length > 0) {
                        headerElem.style.display = "block";
                    } else {
                        headerElem.style.display = "none";
                    }

                    data.forEach(group => {
                        if (!selectedTags.includes(group.name)) {
                            const tagLink = document.createElement("div");
                            tagLink.textContent = `#${group.name} `;
                            tagLink.dataset.tagName = group.name;
                            tagLink.classList.add(
                                "tag-suggestion",
                                "btn",
                                "btn-sm",
                                "mt-1",
                                "me-1",
                                "btn-outline-secondary",
                                "position-relative"
                            );

                            const applyIcon = document.createElement("i");
                            applyIcon.classList.add("fa-regular", "fa-square-plus", "text-primary");
                            tagLink.appendChild(applyIcon);

                            const tagBadge = document.createElement("span");
                            tagBadge.textContent = `${group.count}`;
                            tagBadge.classList.add(
                                "badge",
                                "position-absolute",
                                "top-0",
                                "start-100",
                                "translate-middle",
                                "rounded-pill",
                                "bg-secondary"
                            );
                            tagLink.appendChild(tagBadge);

                            tagLink.addEventListener("click", function(ev) {
                                ev.preventDefault();
                                addTag(group.name);
                            });

                            tagsList.appendChild(tagLink);
                        }
                    });
                });
        } else {
            document.getElementById("live-search-results").innerHTML = "";
            headerElem.style.display = "none";
        }
    }

    function addTag(tagName) {
        const hiddenInput = document.createElement("input");
        hiddenInput.type = "hidden";
        hiddenInput.name = "tags";
        hiddenInput.value = tagName;
        form.appendChild(hiddenInput);

        document.getElementById("tag-input").value = "";
        form.submit();
    }

    document.getElementById("clear-search").addEventListener("click", function() {
        document.getElementById("tag-input").value = "";
        form.submit();
    });

    fetchTags();
    document.getElementById("tag-input").addEventListener("input", fetchTags);
});
</script>
{% endblock %}
