<!-- front/templates/front/dashboard/partner_dashboard_commission.html -->

{% extends "front/dashboard/base.html" %}

{% load static %}
{% load i18n %}

{% block title %}{{ block.super }} - {% trans "Commission Details" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <div class="row">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
              <li class="breadcrumb-item site-name"><a href="{% url 'partner_dashboard_menu' %}">{% trans "Partner Panel" %}</a></li>
              <li class="breadcrumb-item site-name active" aria-current="page">{% trans "Commission Details" %}</li>
            </ol>
        </nav>

        <!-- <PERSON><PERSON> filtrów -->
        <div class="col-md-3 col-lg-2 mb-4">
            <div class="dashboard-element">
                <h2 class="dashboard-header">{% trans "Filters" %}</h2>
                <div class="dashboard-body mb-4 ms-4 me-4">
                    <form method="get" action="{% url 'partner_dashboard_commission' %}">
                        <!-- Pola filtrów -->

                        <div class="mb-3">
                            <label for="start_date" class="form-label">{% trans "Start date" %}:</label>
                            <input type="date" name="start_date" id="start_date" value="{{ start_date }}" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label for="end_date" class="form-label">{% trans "End date" %}:</label>
                            <input type="date" name="end_date" id="end_date" value="{{ end_date }}" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label for="min_commission_rate" class="form-label">{% trans "Min Commission Rate (%)" %}:</label>
                            <input type="number" step="0.01" name="min_commission_rate" id="min_commission_rate" value="{{ min_commission_rate }}" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label for="max_commission_rate" class="form-label">{% trans "Max Commission Rate (%)" %}:</label>
                            <input type="number" step="0.01" name="max_commission_rate" id="max_commission_rate" value="{{ max_commission_rate }}" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label for="min_amount" class="form-label">{% trans "Min Amount" %}:</label>
                            <input type="number" step="0.01" name="min_amount" id="min_amount" value="{{ min_amount }}" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label for="max_amount" class="form-label">{% trans "Max Amount" %}:</label>
                            <input type="number" step="0.01" name="max_amount" id="max_amount" value="{{ max_amount }}" class="form-control">
                        </div>

                        <!-- Wybór liczby rekordów na stronie -->
                        <div class="mb-3">
                            <label for="records_per_page" class="form-label">{% trans "Show" %}</label>
                            <select name="records_per_page" id="records_per_page" class="form-select">
                                <option value="5" {% if records_per_page == 5 %} selected {% endif %}>5</option>
                                <option value="10" {% if records_per_page == 10 %} selected {% endif %}>10</option>
                                <option value="20" {% if records_per_page == 20 %} selected {% endif %}>20</option>
                                <option value="50" {% if records_per_page == 50 %} selected {% endif %}>50</option>
                                <option value="100" {% if records_per_page == 100 %} selected {% endif %}>100</option>
                            </select>
                            {% trans "commissions per page" %}
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" type="submit">{% trans "Apply" %}</button>
                            <!-- Przycisk do czyszczenia filtrów -->
                            <button class="btn btn-secondary" type="button" onclick="clearFilters();">{% trans "Clear filters" %}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
            <!-- Tabela z prowizjami -->
            <div class="col-md-9 col-lg-10">
                <div class="dashboard-element">

                    <div class="table-responsive">
                        <table class="table table-striped my-custom-table">
                            <thead>
                                <tr>
                                    <th>
                                        <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=created_at&direction={% if order_by == 'created_at' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                            {% trans "Date" %}
                                        </a>
                                    </th>
                                    <th class="d-none d-md-table-cell">
                                        <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=related_order__id&direction={% if order_by == 'related_order__id' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                            {% trans "Purchased Products" %}
                                        </a>
                                    </th>
                                    <th>
                                        <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=related_order__price&direction={% if order_by == 'related_order__price' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                            {% trans "Net Order Value (PLN)" %}
                                        </a>
                                    </th>
                                    <th class="d-none d-md-table-cell">
                                        <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=related_order__price_incl_tax&direction={% if order_by == 'related_order__price_incl_tax' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                            {% trans "Gross Order Value (PLN)" %}
                                        </a>
                                    </th>
                                    <th class="d-none d-md-table-cell">
                                        <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=commission_rate&direction={% if order_by == 'commission_rate' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                            {% trans "Commission Rate (%)" %}
                                        </a>
                                    </th>
                                    <th>
                                        <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=amount&direction={% if order_by == 'amount' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                            {% trans "Commission Amount (PLN)" %}
                                        </a>
                                    </th>
                                    <th>{% trans "Description" %}</th>
                                    <!-- <th>
                                        <a class="sort-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}order_by=description&direction={% if order_by == 'description' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                            {% trans "Description" %}
                                        </a>
                                    </th> -->
                                </tr>
                            </thead>
                            <tbody>
                                {% for commission in page_obj %}
                                <tr>
                                    <td>{{ commission.created_at|date:"Y-m-d H:i" }}</td>
                                    <td class="d-none d-md-table-cell">
                                        {% for item in commission.related_order.items.all %}
                                            {{ item.product.name }}{% if not forloop.last %}, {% endif %}
                                        {% empty %}
                                            {% trans "No products" %}
                                        {% endfor %}
                                    </td>
                                    <td>{{ commission.related_order.price }}</td>
                                    <td class="d-none d-md-table-cell">{{ commission.related_order.price_incl_tax }}</td>
                                    <td class="d-none d-md-table-cell">{{ commission.commission_rate }}</td>
                                    <td>{{ commission.amount }}</td>
                                    <td>{% trans "Purchased by" %} {{ commission.related_event.user.username }}</td>
                                    <!-- <td>
                                        {% if commission.description %}
                                            {{ commission.description }}
                                        {% else %}
                                            {{ commission.related_order.description }}
                                        {% endif %}
                                    </td> -->
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7">{% trans "No commissions available." %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Paginacja -->
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">
                            {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}.
                        </span>
                        <nav>
                            <ul class="pagination">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page=1" aria-label="{% trans 'First' %}">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" aria-label="{% trans 'Previous' %}">
                                            <span aria-hidden="true">&lsaquo;</span>
                                        </a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item"><a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a></li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'order_by' and key != 'direction' and key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" aria-label="{% trans 'Next' %}">
                                            <span aria-hidden="true">&rsaquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}" aria-label="{% trans 'Last' %}">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function clearFilters() {
        const url = new URL(window.location.href);
        url.searchParams.delete('affiliate_link');
        url.searchParams.delete('start_date');
        url.searchParams.delete('end_date');
        url.searchParams.delete('min_commission_rate');
        url.searchParams.delete('max_commission_rate');
        url.searchParams.delete('min_amount');
        url.searchParams.delete('max_amount');
        url.searchParams.delete('related_event');
        url.searchParams.delete('related_order');
        url.searchParams.delete('records_per_page');
        url.searchParams.delete('page');
        url.searchParams.delete('order_by');
        url.searchParams.delete('direction');
        window.location.href = url.toString();
    }
    </script>
</div>
{% endblock %}
