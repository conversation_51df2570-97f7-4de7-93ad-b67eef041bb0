import os
import logging

from email.mime.image import MIMEImage
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from django.core.mail import send_mail
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _

from robosourcing.settings import BASE_DIR, STORE_URL, EMAIL_HOST_USER
from db.models import EmailNotification


class SingletonMeta(type):
    """
    The Singleton metaclass.
    """

    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]


class Mailer(metaclass=SingletonMeta):
    """
    A class containing methods for sending e-mail notifications to users
    """

    _sender_email: str
    _anchor_css: str
    _logger = None
    _use_templates: bool

    # ********************************************
    def __init__(self):

        self._use_templates = True

        # init logger
        self._logger = logging.getLogger('MAILER')
        self._logger.setLevel(logging.DEBUG)
        self._file_handler = logging.FileHandler('mailer.log')
        self._file_handler.setLevel(logging.INFO)
        self._file_handler.setFormatter(logging.Formatter('%(asctime)s | %(levelname)s | %(message)s'))
        self._logger.addHandler(self._file_handler)

        # mail commons
        self._sender_email = EMAIL_HOST_USER
        self._anchor_css = 'style="font-weight: bold; color: #0b8bcc; text-decoration:none;"'

    # ********************************************

    @staticmethod
    def _text_header(user):

        # IF UPDATING THE HEADER DO SAME SETTINGS IN:
        # robosourcing\templates\account\email\base_message.txt
        # FOR ALLAUTH EMAILS

        if user.profil.first_name:
            result = f'Witaj {user.profil.first_name},\n\n'
        else:
            result = f'Witaj,\n\n'

        return result

    # ********************************************

    @staticmethod
    def _html_header(user):

        # IF UPDATING THE HEADER DO SAME SETTINGS IN:
        # robosourcing\templates\account\email\base_message.html
        # FOR ALLAUTH EMAILS

        result = f'<div style="display: inline-block; width: 600px; max-width:100%; background-color: white; padding: 25px;">' \
                 f'<img src="{STORE_URL}/static/images/brandbook/logo_red_text_100.png">' \
                 f'<img src="{STORE_URL}/static/images/brandbook/logo_red_icon_100.png" style="float: right">' \
                 f'<hr style="border-top: 1px solid lightgrey;"><br>'
        if user.first_name:
            result += f'<strong>Witaj {user.profil.first_name},</strong><br><br>'
        else:
            result += '<strong>Witaj,</strong><br><br>'

        return result

    # ********************************************

    @staticmethod
    def _text_footer(user):

        # IF UPDATING THE FOOTER DO SAME SETTINGS IN:
        # robosourcing\templates\account\email\base_message.txt
        # FOR ALLAUTH EMAILS

        result = f'\n\nPozdrawiamy,\nZespół Botie' \
                 f'\n\n--\nJeśli nie chcesz otrzymywać wiadomości tego typu, możesz zmienić swoje preferencje tutaj: {STORE_URL}/profiles/profile'

        return result

    # ********************************************

    @staticmethod
    def _html_footer(user):

        # IF UPDATING THE FOOTER DO SAME SETTINGS IN:
        # robosourcing\templates\account\email\base_message.html
        # FOR ALLAUTH EMAILS

        result = f'<br><br><hr style="border-top: 1px solid lightgrey;">' \
                 f'Pozdrawiamy,<br>Zespół Botie' \
                 f'<br><br><div style="color: lightgrey;">--<br>Jeśli nie chcesz otrzymywać wiadomości tego typu, możesz zmienić swoje preferencje <a style="color: darkgrey;" href="{STORE_URL}/profiles/profile">tutaj</a>.</div>' \
                 f'</div>'

        return result

    # ********************************************

    @staticmethod
    def embed_logotypes(msg) -> MIMEMultipart:
        with open(os.path.join(BASE_DIR, 'static', 'images', 'brandbook', 'logo_red_text_100.png'), 'rb') as img:
            mime_image = MIMEImage(img.read())
            mime_image.add_header('Content-ID', '<image1>')
            msg.attach(mime_image)
        with open(os.path.join(BASE_DIR, 'static', 'images', 'brandbook', 'logo_red_icon_100.png'), 'rb') as img:
            mime_image2 = MIMEImage(img.read())
            mime_image2.add_header('Content-ID', '<image2>')
            msg.attach(mime_image2)
        return msg

    # ********************************************

    def send_html_mail(self, user, subject, text, html, recipients) -> bool:

        if self._use_templates:

            # result = send_mail(subject,
            #                    text,
            #                    self._sender_email,
            #                    recipients,
            #                    fail_silently=False,
            #                    html_message=html)

            msg = MIMEMultipart('related')
            msg['Subject'] = subject
            msg['From'] = self._sender_email
            msg['To'] = recipients[0]
            msg.attach(MIMEText(html, 'html'))
            msg = self.embed_logotypes(msg)
            email = EmailMessage(subject, None, self._sender_email, recipients)
            email.attach(msg)
            result = email.send()

        else:

            result = send_mail(subject,
                               self._text_header(user) + text + self._text_footer(user),
                               self._sender_email,
                               recipients,
                               fail_silently=False,
                               html_message=self._html_header(user)
                               + html
                               # + html_body.replace('<a ', '<a '+self._anchor_css+' ')
                               + self._html_footer(user))

        return result

    def after_registration_email(self, user):

        # recipients list
        recipients = user.email,

        # render content
        if self._use_templates:
            context = {'user': user}
            # subject
            sub = render_to_string("account/email/extra/after_registration_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/after_registration_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/after_registration_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            # subject
            sub = _('Thank you for registering an account')
            # text body
            text = f'Naprawdę się cieszymy, że do nas dołączyłeś!\n'
            text += f'Gdy pobierzesz i zainstalujesz aplikację Botie ({STORE_URL}/download/), otrzymasz 100 kredytów za darmo, więc możesz przetestować Botie bez żadnych przeszkód.\n'
            text += f'Od teraz będziemy odnawiać Ci co miesiąc pakiet do 100 kredytów bez żadnych opłat.\n'
            text += f'Aby zwiększyć abonament miesięczny sprawdź CENNIK ({STORE_URL}/pricelist/).\n'
            text += f'Zaloguj się, użyj kolejnych scenariuszy z BotBooka ({STORE_URL}/scenarios/), odkryj nowe inspiracje i moc Botie.\n'
            text += f'Zapraszamy do sekcji FAQ - Najczęściej Zadawanych Pytań ({STORE_URL}/faq/).\n'
            text += f'W razie pytań możesz kontaktować się z nami również drogą mailową: <EMAIL>.\n'
            # html body
            html = f'Naprawdę się cieszymy, że do nas dołączyłeś!<br>'
            html += f'Gdy pobierzesz i zainstalujesz aplikację <a href="{STORE_URL}/download/">Botie</a>, otrzymasz 100 kredytów za darmo, więc możesz przetestować Botie bez żadnych przeszkód.<br>'
            html += f'Od teraz będziemy odnawiać Ci co miesiąc pakiet do 100 kredytów bez żadnych opłat.<br>'
            html += f'Aby zwiększyć abonament miesięczny sprawdź <a href="{STORE_URL}/pricelist/">Botie</a>CENNIK</a>.<br>'
            html += f'Zaloguj się, użyj kolejnych scenariuszy z <a href="{STORE_URL}/scenarios/">BotBooka</a>, odkryj nowe inspiracje i moc Botie.<br>'
            html += f'Zapraszamy do sekcji <a href="{STORE_URL}/faq/">FAQ</a> (Najczęściej Zadawanych Pytań).<br>'
            html += f'W razie pytań możesz kontaktować się z nami również drogą mailową: <a href="mailto:<EMAIL>"><EMAIL></a>.<br>'

        # send mail
        result = self.send_html_mail(user, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=user, status_id=result,
                                         message_id=EmailNotification.Message.AFTER_REGISTRATION)

        return result

    def after_login_email(self, user):

        # recipients list
        recipients = user.email,

        # render content
        if self._use_templates:
            context = {'user': user}
            # subject
            sub = render_to_string("account/email/extra/after_login_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/after_login_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/after_login_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            # subject
            sub = _('Welcome to our web service')
            # text body
            text = f'Cieszymy się, że wciąż powracasz!\n\n'
            text += f'Ostatnie logowanie odnotowaliśmy: {user.last_login}.\n\n'
            text += f'Zapraszamy do sekcji FAQ - Najczęściej Zadawanych Pytań ({STORE_URL}/faq/).\n'
            text += f'W razie pytań możesz kontaktować się z nami również drogą mailową: <EMAIL>.\n'
            # html body
            html = f'Cieszymy się, że wciąż powracasz!<br><br>'
            html += f'Ostatnie logowanie odnotowaliśmy: {user.last_login}.<br><br>'
            html += f'Zapraszamy do sekcji <a href="{STORE_URL}/faq/">FAQ</a> (Najczęściej Zadawanych Pytań).<br>'
            html += f'W razie pytań możesz kontaktować się z nami również drogą mailową: <a href="mailto:<EMAIL>"><EMAIL></a>.<br>'

        # send mail
        result = self.send_html_mail(user, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=user, status_id=result,
                                         message_id=EmailNotification.Message.AFTER_LOGIN)

        return result

    def after_purchase_email(self, user, order):

        # recipients list
        recipients = user.email,

        # render content
        if self._use_templates:
            context = {'user': user, 'order': order}
            # subject
            sub = render_to_string("account/email/extra/after_purchase_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/after_purchase_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/after_purchase_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            return False

        # send mail
        result = self.send_html_mail(user, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=user, status_id=result,
                                         message_id=EmailNotification.Message.AFTER_PURCHASE)

        return result

    def subscription_event_completed(self, user, event):

        # recipients list
        recipients = user.email,

        # render content
        if self._use_templates:
            context = {'user': user, 'event': event}
            # subject
            sub = render_to_string("account/email/extra/subscription_event_completed_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/subscription_event_completed_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/subscription_event_completed_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            return False

        # send mail
        result = self.send_html_mail(user, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=user, status_id=result,
                                         message_id=EmailNotification.Message.SUBSCRIPTION_EVENT_COMPLETED)

        return result

    def subscription_event_canceled(self, user, event):

        # recipients list
        recipients = user.email,

        # render content
        if self._use_templates:
            context = {'user': user, 'event': event}
            # subject
            sub = render_to_string("account/email/extra/subscription_event_canceled_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/subscription_event_canceled_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/subscription_event_canceled_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            return False

        # send mail
        result = self.send_html_mail(user, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=user, status_id=result,
                                         message_id=EmailNotification.Message.SUBSCRIPTION_EVENT_CANCELED)

        return result

    def payment_card_expire_soon(self, user, order, token):

        # recipients list
        recipients = user.email,

        # render content
        if self._use_templates:
            context = {'user': user, 'order': order, 'token': token}
            # subject
            sub = render_to_string("account/email/extra/payment_card_expire_soon_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/payment_card_expire_soon_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/payment_card_expire_soon_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            return False

        # send mail
        result = self.send_html_mail(user, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=user, status_id=result,
                                         message_id=EmailNotification.Message.PAYMENT_CARD_EXPIRE_SOON)

        return result

    def payment_card_expired(self, user, order, token):

        # recipients list
        recipients = user.email,

        # render content
        if self._use_templates:
            context = {'user': user, 'order': order, 'token': token}
            # subject
            sub = render_to_string("account/email/extra/payment_card_expired_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/payment_card_expired_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/payment_card_expired_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            return False

        # send mail
        result = self.send_html_mail(user, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=user, status_id=result,
                                         message_id=EmailNotification.Message.PAYMENT_CARD_EXPIRED)

        return result

    def send_withdrawal_request_email(self, user, withdrawal_request):
        """
        Wysyła e-mail do użytkownika potwierdzający zgłoszenie wypłaty,
        oraz e-mail do biura informujący o nowym żądaniu wypłaty.
        """

        # Kontekst do szablonów
        context = {
            'user': user,
            'withdrawal_request': withdrawal_request
        }

        # ---------------------------------------
        # 1. Wiadomość do użytkownika
        # ---------------------------------------
        if self._use_templates:
            # Renderowanie szablonów dla użytkownika
            sub_user = render_to_string("account/email/extra/withdrawal_request_subject_user.txt", context).strip()
            text_user = render_to_string("account/email/extra/withdrawal_request_message_user.txt", context).strip()
            html_user = render_to_string("account/email/extra/withdrawal_request_message_user.html", context).strip()
        else:
            # Jeśli nie korzystasz z szablonów, możesz tu zbudować treść wiadomości ręcznie
            sub_user = "Żądanie wypłaty środków"
            text_user = f"Twoje żądanie wypłaty środków w kwocie {withdrawal_request.amount} zostało zarejestrowane."
            html_user = f"Twoje żądanie wypłaty środków w kwocie {withdrawal_request.amount} zostało zarejestrowane."

        # Wysyłamy mail do użytkownika
        recipients_user = [user.email]
        result_user = self.send_html_mail(user, sub_user, text_user, html_user, recipients_user)

        # Zapis do EmailNotification (wysyłka do użytkownika)
        EmailNotification.objects.create(
            user=user,
            status_id=EmailNotification.Status.SENT if result_user else EmailNotification.Status.FAILED,
            message_id=EmailNotification.Message.WITHDRAWAL_REQUEST_SUBMITTED
        )

        # ---------------------------------------
        # 2. Wiadomość do biura
        # ---------------------------------------
        if self._use_templates:
            # Renderowanie szablonów dla biura
            sub_office = render_to_string("account/email/extra/withdrawal_request_subject_office.txt", context).strip()
            text_office = render_to_string("account/email/extra/withdrawal_request_message_office.txt", context).strip()
            html_office = render_to_string("account/email/extra/withdrawal_request_message_office.html", context).strip()
        else:
            sub_office = "Nowe żądanie wypłaty środków"
            text_office = f"Użytkownik {user.username} zgłosił wypłatę środków w kwocie {withdrawal_request.amount}."
            html_office = f"Użytkownik {user.username} zgłosił wypłatę środków w kwocie {withdrawal_request.amount}."

        recipients_office = ['<EMAIL>', '<EMAIL>']
        result_office = self.send_html_mail(user, sub_office, text_office, html_office, recipients_office)

        # Zapis do EmailNotification (wysyłka do biura)
        EmailNotification.objects.create(
            user=user,
            status_id=EmailNotification.Status.SENT if result_office else EmailNotification.Status.FAILED,
            message_id=EmailNotification.Message.WITHDRAWAL_REQUEST_SUBMITTED
        )

        return result_user and result_office
    
    def send_monthly_withdrawal_summary(self, withdrawal_requests):
        """
        Wysyła miesięczne podsumowanie nowych żądań wypłat do administratora.
        """
        context = {
            'withdrawal_requests': withdrawal_requests
        }

        # Renderowanie szablonów
        subject = render_to_string("account/email/extra/monthly_withdrawal_summary_subject.txt", context).strip()
        text_message = render_to_string("account/email/extra/monthly_withdrawal_summary_message.txt", context).strip()
        html_message = render_to_string("account/email/extra/monthly_withdrawal_summary_message.html", context).strip()

        recipients = ['<EMAIL>']

        # Wysyłanie wiadomości
        result = self.send_html_mail(None, subject, text_message, html_message, recipients)

        # Zapis powiadomienia
        EmailNotification.objects.create(
            user=None,
            status_id=EmailNotification.Status.SENT if result else EmailNotification.Status.FAILED,
            message_id=EmailNotification.Message.MONTHLY_WITHDRAWAL_SUMMARY
        )

        return result

    def password_reset_key(self, user, reset_key):

        # recipients list
        recipients = user.email,

        # render content
        if self._use_templates:
            context = {'user': user, 'reset_key': reset_key}
            # subject
            sub = render_to_string("account/email/extra/password_reset_key_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/password_reset_key_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/password_reset_key_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            return False

        # send mail
        result = self.send_html_mail(user, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=user, status_id=result,
                                         message_id=EmailNotification.Message.PASSWORD_RESET_KEY)

        return result

    def scenario_accepted(self, scenario):

        # recipients list
        recipients = scenario.owner.email,

        # render content
        if self._use_templates:
            context = {'scenario': scenario}
            # subject
            sub = render_to_string("account/email/extra/scenario_accepted_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/scenario_accepted_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/scenario_accepted_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            return False

        # send mail
        result = self.send_html_mail(scenario.owner, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=scenario.owner, status_id=result,
                                         message_id=EmailNotification.Message.SCENARIO_ACCEPTED)

        return result

    def scenario_rejected(self, scenario, message):

        # recipients list
        recipients = scenario.owner.email,

        # render content
        if self._use_templates:
            context = {'scenario': scenario, 'message': message}
            # subject
            sub = render_to_string("account/email/extra/scenario_rejected_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/scenario_rejected_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/scenario_rejected_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            return False

        # send mail
        result = self.send_html_mail(scenario.owner, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=scenario.owner, status_id=result,
                                         message_id=EmailNotification.Message.SCENARIO_REJECTED)

        return result

    def newsletter_confirmation(self, subscription):
        """
        Wysyła e-mail z prośbą o potwierdzenie subskrypcji newslettera
        """
        # recipients list
        recipients = [subscription.email]

        # render content
        if self._use_templates:
            context = {'subscription': subscription}
            # subject
            sub = render_to_string("account/email/extra/newsletter_confirmation_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/newsletter_confirmation_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/newsletter_confirmation_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            # subject
            sub = 'Potwierdź subskrypcję newslettera Botie'
            # text body
            text = f'Dziękujemy za zapisanie się do newslettera Botie!\n\n'
            text += f'Aby potwierdzić subskrypcję, kliknij w poniższy link:\n'
            text += f'{STORE_URL}/newsletter/confirm/{subscription.confirmation_token}/\n\n'
            text += f'Jeśli to nie Ty zapisałeś się do newslettera, zignoruj tę wiadomość.\n'
            # html body
            html = f'Dziękujemy za zapisanie się do newslettera Botie!<br><br>'
            html += f'Aby potwierdzić subskrypcję, kliknij w poniższy link:<br>'
            html += f'<a href="{STORE_URL}/newsletter/confirm/{subscription.confirmation_token}/">Potwierdź subskrypcję</a><br><br>'
            html += f'Jeśli to nie Ty zapisałeś się do newslettera, zignoruj tę wiadomość.<br>'

        # send mail (bez użytkownika, bo to może być niezarejestrowany email)
        result = self.send_html_mail(None, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=None, status_id=result,
                                       message_id=EmailNotification.Message.NEWSLETTER_CONFIRMATION)

        return result

    def newsletter_welcome(self, subscription):
        """
        Wysyła e-mail powitalny po potwierdzeniu subskrypcji newslettera
        """
        # recipients list
        recipients = [subscription.email]

        # render content
        if self._use_templates:
            context = {'subscription': subscription}
            # subject
            sub = render_to_string("account/email/extra/newsletter_welcome_subject.txt", context).strip()
            # text body from template
            template_name = "account/email/extra/newsletter_welcome_message.txt"
            text = render_to_string(template_name, context).strip()
            # html body from template
            template_name = "account/email/extra/newsletter_welcome_message.html"
            html = render_to_string(template_name, context).strip()
        else:
            # subject
            sub = 'Witaj w newsletterze Botie!'
            # text body
            text = f'Dziękujemy za potwierdzenie subskrypcji newslettera Botie!\n\n'
            text += f'Od teraz będziesz otrzymywać najważniejsze informacje o świecie automatyzacji.\n'
            text += f'Sprawdź nasze najnowsze scenariusze: {STORE_URL}/botbook/\n'
            text += f'Pobierz aplikację Botie: {STORE_URL}/download/\n\n'
            # html body
            html = f'Dziękujemy za potwierdzenie subskrypcji newslettera Botie!<br><br>'
            html += f'Od teraz będziesz otrzymywać najważniejsze informacje o świecie automatyzacji.<br>'
            html += f'<a href="{STORE_URL}/botbook/">Sprawdź nasze najnowsze scenariusze</a><br>'
            html += f'<a href="{STORE_URL}/download/">Pobierz aplikację Botie</a><br><br>'

        # send mail (bez użytkownika, bo to może być niezarejestrowany email)
        result = self.send_html_mail(None, sub, text, html, recipients)

        # store event
        EmailNotification.objects.create(user=None, status_id=result,
                                       message_id=EmailNotification.Message.NEWSLETTER_WELCOME)

        return result
