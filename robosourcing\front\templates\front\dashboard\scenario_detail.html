{% extends "front/dashboard/base.html" %}
{% load i18n %}

{% block title %}{{ block.super }} - {{ scenario.name }}{% endblock %}

{% block extra_content_1 %}
<div class="container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
      <li class="breadcrumb-item site-name"><a href="{% url 'robot_scenarios_view' %}">{% trans "Scenario Library" %}</a></li>
      <li class="breadcrumb-item site-name active">{{ scenario.name }}</li>
    </ol>
  </nav>

  <div class="row g-4">
    <!-- Main info -->
    <div class="col-lg-12">
      <div class="dashboard-element h-100 d-flex flex-column">
        <h2 class="db-section-title">{{ scenario.name }}</h2>
        <hr>
        <div class="mb-4 ms-4 me-4">
          <p class="lead">{{ scenario.description }}</p>
        </div>
      </div>
    </div>

    <!-- Meta panels -->
    <div class="col-md-6 col-lg-4">
      <div class="dashboard-element h-100 d-flex flex-column">
        <h3 class="db-section-title">{% trans "General" %}</h3>
        <hr>
        <div class="ms-4 me-4 mb-3">
        <p><strong>{% trans "Author:" %}</strong> {{ scenario.author }}</p>
        <p><strong>{% trans "Created:" %}</strong> {{ scenario.created_at|date:"Y-m-d" }}</p>
        <p><strong>{% trans "Version:" %}</strong> {{ scenario.scenario_version }}</p>
        {% if scenario.version_changelog %}
          <p><strong>{% trans "Changelog:" %}</strong> {{ scenario.version_changelog }}</p>
        {% endif %}
      </div>
      </div>
    </div>
    <div class="col-md-6 col-lg-4">
      <div class="dashboard-element h-100 d-flex flex-column">
        <h3 class="db-section-title">{% trans "Cost & Availability" %}</h3>
        <hr>
        <div class="ms-4 me-4 mb-3">
          <p><strong>{% trans "Cost:" %}</strong> {{ scenario.price }}</p>
          <p><strong>{% trans "Availability:" %}</strong> {{ scenario.get_available_display }}</p>
          <p><strong>{% trans "Editable:" %}</strong> {{ scenario.get_editable_display }}</p>
        </div>
      </div>
    </div>
    <div class="col-md-6 col-lg-4">
      <div class="dashboard-element h-100 d-flex flex-column">
        <h3 class="db-section-title">{% trans "System Info" %}</h3>
        <hr>
        <div class="ms-4 me-4 mb-3">
          <p><strong>{% trans "Updated:" %}</strong> {{ scenario.updated_at|date:"Y-m-d" }}</p>
          <p><strong>{% trans "Published:" %}</strong> {{ scenario.publish_time|date:"Y-m-d" }}</p>
          <p><strong>{% trans "Accepted:" %}</strong> {{ scenario.accepted|yesno:"Yes,No" }}</p>
        </div>
      </div>
    </div>

    <!-- Tags and Categories -->
    <div class="col-md-6">
      <div class="dashboard-element h-100 d-flex flex-column">
        <h3 class="db-section-title">{% trans "Tags" %}</h3>
        <hr>
        <div class="ms-4 me-4 mb-3">
          {% if tags %}
            <ul class="list-inline">
              {% for st in tags %}
                <li class="list-inline-item badge bg-primary me-1 mb-1">{{ st.tag.name }}</li>
              {% endfor %}
            </ul>
          {% else %}
            <p class="text-muted">{% trans "No tags" %}</p>
          {% endif %}
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="dashboard-element h-100 d-flex flex-column">
        <h3 class="db-section-title">{% trans "Categories" %}</h3>
        <hr>
        <div class="ms-4 me-4 mb-3">
          {% if categories %}
            <ul class="list-inline">
              {% for cat in categories %}
                <li class="list-inline-item badge bg-secondary me-1 mb-1">{{ cat.name }}</li>
              {% endfor %}
            </ul>
          {% else %}
            <p class="text-muted">{% trans "No categories" %}</p>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="col-12">
      <div class="dashboard-element h-100 d-flex flex-column">
        <h3 class="db-section-title">{% trans "Actions" %}</h3>
        <hr>
        <div class="ms-4 me-4 mb-3 d-flex gap-2">
          <button id="fav-btn" class="btn btn-sm {% if is_favorited %}btn-danger{% else %}btn-outline-danger{% endif %}">
            <i class="fas fa-heart"></i>
            {% if is_favorited %}{% trans "Remove Favourite" %}{% else %}{% trans "Add Favourite" %}{% endif %}
          </button>
          <button class="btn btn-sm btn-secondary"
                  data-bs-toggle="modal"
                  data-bs-target="#assignModal"
                  data-scenario-id="{{ scenario.sid }}"
                  data-assigned-robots="{{ assigned_robot_ids|join:',' }}">
            <i class="fas fa-thumbtack"></i> {% trans "Pin to robot" %}
          </button>
        </div>
      </div>
    </div>

  </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<script>
// ---------------------
// CSRF Helper
// ---------------------
function getCookie(name) {
  const m = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
  return m ? decodeURIComponent(m[2]) : null;
}

// ---------------------
// Favourites Toggle
// ---------------------
document.addEventListener('DOMContentLoaded', function () {
  const favBtn = document.getElementById('fav-btn');
  if (favBtn) {
    favBtn.addEventListener('click', function () {
      const isFav = favBtn.classList.contains('btn-danger');
      const url = isFav
        ? '{% url "remove_scenario_from_favorites" %}'
        : '{% url "add_scenario_to_favorites" %}';
      fetch(url, {
        method: 'POST',
        headers: {
          'X-CSRFToken': getCookie('csrftoken'),
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `scenario_id={{ scenario.sid }}`
      }).then(() => location.reload());
    });
  }
});

// ---------------------
// Assign Modal Handling
// ---------------------
document.addEventListener('DOMContentLoaded', function () {
  const assignModal = document.getElementById('assignModal');
  const assignForm = document.getElementById('assignForm');

  // Prefill scenario + assigned checkboxes
  assignModal.addEventListener('show.bs.modal', function (event) {
    const button = event.relatedTarget;
    const scenarioId = button.getAttribute('data-scenario-id');
    const assignedRobots = (button.getAttribute('data-assigned-robots') || '').split(',').map(id => id.trim());

    const scenarioInput = assignModal.querySelector('input[name="scenario"]');
    if (scenarioInput) scenarioInput.value = scenarioId;

    document.querySelectorAll('#assignModal .robot-checkbox').forEach(checkbox => {
      checkbox.checked = assignedRobots.includes(checkbox.value);
    });
  });

  // Select/Deselect all
  const selectAll = document.getElementById('select_all_robots');
  if (selectAll) {
    selectAll.addEventListener('change', function () {
      const checked = this.checked;
      document.querySelectorAll('#assignModal .robot-checkbox').forEach(cb => cb.checked = checked);
    });
  }

  // Submit form with AJAX
  if (assignForm) {
    assignForm.addEventListener('submit', function (e) {
      e.preventDefault();

      const formData = new FormData(assignForm);
      document.querySelectorAll('#assignModal .robot-checkbox').forEach(checkbox => {
        if (!checkbox.checked) {
          formData.append('remove[]', checkbox.value);
        }
      });

      fetch(assignForm.action, {
        method: 'POST',
        headers: { 'X-CSRFToken': getCookie('csrftoken') },
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'OK') {
          // Close modal
          const modalInstance = bootstrap.Modal.getInstance(assignModal);
          if (modalInstance) modalInstance.hide();
          // Reload the page or redirect
          window.location.reload();
        } else {
          alert('{% trans "There was an issue assigning the scenario." %}');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('{% trans "An error occurred while submitting data." %}');
      });
    });
  }
});
</script>
{% endblock %}
