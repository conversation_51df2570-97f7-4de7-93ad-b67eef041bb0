import logging
from itertools import chain

from dateutil.relativedelta import relativedelta
from django.db import transaction, IntegrityError
from django.db.models import Q
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from django.db.models import Sum

from db.mailer import Mailer
from db.models import ScheduledFreebies, UserSubscription, EmailNotification, User, PartnerCommission, AffiliateWithdrawalRequest, WithdrawalCycle
from store.models import RecurringPayment, SubsequentOrder, Order, OrderItem, Invoice
from store.payu_connector import PayUConnector
from store.store_utils import StoreUtils
from store.systim_connector import SystimConnector
from django.conf import settings

from robosourcing.utils import TimeManager

logger = logging.getLogger('django')
su = StoreUtils()
mailer = Mailer()

if settings.SCHEDULER_TEST_MODE:
    if settings.SHORT_PERIOD == 'hour':
        short_delta = relativedelta(hours=-1)
    elif settings.SHORT_PERIOD == 'day':
        short_delta = relativedelta(days=-1)
    if settings.LONG_PERIOD == 'day':
        long_delta = relativedelta(days=-1)
    elif settings.LONG_PERIOD == 'week':
        long_delta = relativedelta(weeks=-1)
else:
    short_delta = relativedelta(months=-1)
    long_delta = relativedelta(years=-1)

subscription_updater_delta      = short_delta
freebies_delta                  = short_delta
monthly_payment_launcher_delta  = short_delta
annual_payment_launcher_delta   = long_delta


def subscription_updater():
    # FIXME: subscription should be processed (credits top-up) for some conditions only
    # - in case of monthly subscription when recurring payment succeeded
    # - in case of annual one when it's not the payment month or annual recurring payment succeeded
    # so maybe it could be launched from here but should rely on flags update_counter and max_update_counter?

    now = timezone.now()
    delta = now + subscription_updater_delta
    # tm = TimeManager()
    # now = tm.current_time()
    # delta = tm.shifted_time(months=-1)

    subscritions = UserSubscription.objects.filter(active=True, last_update__lte=delta)
    logger.info(f'[{now}] subscriptions to process: {subscritions.count()}')
    for sub in subscritions:
        # DIAGRAM - US6 / US7
        sub.process_subscription()


def freebies():
    now = timezone.now()
    delta = now + freebies_delta
    # tm = TimeManager()
    # now = tm.current_time()
    # delta = tm.shifted_time(months=-1)

    freebies = ScheduledFreebies.objects.filter(active=True, last_update__lte=delta)
    logger.info(f'[{now}] freebies to process: {freebies.count()}')
    for freebie in freebies:
        freebie.add_package_credits()
        # freebie.refill_subscription_credits()


def order_retriever():
    su.update_orders_statuses((None, 'NEW', 'PENDING', 'WAITING_FOR_CONFIRMATION'), subsequent=True)


def monthly_payment_launcher():
    now = timezone.now()
    delta = now + monthly_payment_launcher_delta
    # tm = TimeManager()
    # now = tm.current_time()
    # delta = tm.shifted_time(months=-1)

    payments = RecurringPayment.objects.filter(Q(annual_payment=False) & Q(active=True) &
                                               Q(pending_payment__isnull=True) &
                                               Q(next_payment_date__isnull=False) & Q(next_payment_date__lte=now) &
                                               (Q(last_update__lte=delta) | Q(last_update__isnull=True)))
    logger.info(f'[{now}] monthly payments to process: {payments.count()}')
    for index, payment in enumerate(payments):
        logger.info(f'[{now}] {index=} processing payment {payment.reference_order=}')
        if payment.reference_order.operator:
            # create recurring unattended payment - DIAGRAM UP3 / UP4
            order_id, msg = su.handle_recurring_payment(payment.reference_order.user, payment.reference_order,
                                                        payment.next_payment_amount)
            if order_id:
                logger.info(f'[{now}] launching payment done: {msg}')
                payment.pending_payment = SubsequentOrder.objects.get(order_id=order_id)
                payment.save()
                # check order status (and process after payment actions if successfull)
                su.update_payment_statuses((None,), single=order_id)
            else:
                logger.error(f'[{now}] launching payment failed: {msg}')
        else:
            logger.info(f'[{now}] launching payment skipped - test payment')


def annual_payment_launcher():
    now = timezone.now()
    delta = now + annual_payment_launcher_delta
    # tm = TimeManager()
    # now = tm.current_time()
    # delta = tm.shifted_time(years=-1)

    payments = RecurringPayment.objects.filter(Q(annual_payment=True) & Q(active=True) &
                                               Q(pending_payment__isnull=True) &
                                               Q(next_payment_date__isnull=False) & Q(next_payment_date__lte=now) &
                                               (Q(last_update__lte=delta) | Q(last_update__isnull=True)))
    logger.info(f'[{now}] annual payments to process: {payments.count()}')
    for payment in payments:
        if payment.reference_order.operator:
            # create recurring unattended payment - DIAGRAM UP3 / UP4
            order_id, msg = su.handle_recurring_payment(payment.reference_order.user, payment.reference_order,
                                                        payment.next_payment_amount)
            if order_id:
                payment.pending_payment = SubsequentOrder.objects.get(order_id=order_id)
                payment.save()
                # check order status (and process after payment actions if successfull)
                su.update_payment_statuses((None,), single=order_id)
            else:
                logger.error(f'[{now}] launching payment failed: {msg}')
        else:
            pass


def payment_card_checker():

    weeks_earlier = 2

    now = timezone.now()
    delta = now + relativedelta(weeks=weeks_earlier)
    delta2 = now - relativedelta(weeks=weeks_earlier)
    # tm = TimeManager()
    # now = tm.current_time()
    # delta = tm.shifted_time(weeks=weeks_earlier)
    # delta2 = tm.shifted_time(weeks=-weeks_earlier)

    payments = RecurringPayment.objects.filter(next_payment_date__lte=delta)
    logger.info(f'[{now}] payment cards to check: {payments.count()}')

    for payment in payments:

        notifications = EmailNotification.objects.filter(user=payment.user, status_id=EmailNotification.Status.SENT,
                                                         message_id=EmailNotification.Message.PAYMENT_CARD_EXPIRE_SOON,
                                                         sending_time__gte=delta2)

        if notifications:
            logger.info(f'[{now}] Skipped - notification already sent to: {payment.user.email}')
            continue

        # initialize connector
        if payment.reference_order.operator == 'PAYU':

            conn = PayUConnector()

            # log in
            conn.login()

            # find card used in a payment for a subscription and check if it expires in x weeks
            token, expires_soon = conn.get_card_token(payment.user, details=payment.reference_order.payment_details,
                                                      expire_weeks=weeks_earlier)

            if not token or expires_soon:
                # send email notification
                logger.info(f'[{now}] Sending a notification to: {payment.user.email}')
                if not mailer.payment_card_expire_soon(payment.user, payment.reference_order, token):
                    logger.error(f'[{now}] Sending a notification failed: {payment.user.email}')

        elif payment.reference_order.operator == 'VIVA':

            # TODO: prepare card checker for VIVA
            # conn = VivaConnector()
            pass


def invoice_issuer():

    systim = SystimConnector()
    # systim.login()

    now = timezone.now()
    # tm = TimeManager()
    # now = tm.current_time()

    # get completed orders with no invoice issued
    multi_query = Q()
    for s in ('COMPLETED', 'PAID'):
        multi_query = multi_query | Q(status=s)

    orders = Order.objects.filter(invoice_id__isnull=True).filter(multi_query)
    subsequent_orders = SubsequentOrder.objects.filter(invoice_id__isnull=True).filter(multi_query)
    combined_orders = list(chain(orders, subsequent_orders))
    logger.info(f'[{now}] orders to issue invoice for: {len(combined_orders)}')

    for order in combined_orders:

        # check company (add if not existing or update its data)
        invoice_data = order.user.userprofile.organization_uuid
        if not invoice_data:
            logger.info(f'[{now}] skipped - user "{order.user.username}" has not provided data for invoice')
            continue
        name = f'{invoice_data.name_1} {invoice_data.name_2}'.strip()
        street = f'{invoice_data.street} {invoice_data.street_number}'.strip()
        if invoice_data.home_number:
            street += '/'+invoice_data.home_number
        city = invoice_data.city.strip()
        postal_code = invoice_data.postal_code.strip()
        nip = invoice_data.tax_id.strip()
        # if not nip:
        #     logger.info(f'[{now}] skipped - user "{order.user.username}" has not provided a tax payer identification number')
        #     continue
        # if not (name and street and city and postal_code):
        #     logger.info(f'[{now}] skipped - user "{order.user.username}" has not provided company name neither the address')
        #     continue
        obj = systim.list_companies(nip=nip)
        if obj['error']['code'] == 0:
            # update company data
            if len(obj['result'].keys()) != 1:
                logger.info(f'[{now}] skipped - more than one company with NIP: {nip}')
                continue
            company_id = list(obj['result'].keys())[0]
            obj = systim.update_company(company_id, name, street, city, postal_code, nip=nip)
            if obj['error']['code'] != 0:
                logger.error(f'[{now}] updating company failed: {nip}')
                continue
        else:
            # add company
            obj = systim.add_company(name, street, city, postal_code, nip=nip)
            if obj['error']['code'] != 0:
                logger.error(f'[{now}] adding company failed: {nip}')
                continue
            company_id = obj['result']

        # prepare data for invoice items
        data = []
        if isinstance(order, Order):
            items = OrderItem.objects.filter(order=order)
            for item in items:
                netto = item.quantity * float(item.product.price)
                tax_rate = 23
                tax, brutto = su.calculate_tax(netto, tax_rate)
                data.append({
                    'id_produktu': None,
                    'ilosc': item.quantity,
                    'cena_netto': float(item.product.price),
                    'kwota_netto': netto,
                    'kwota_vat': tax,
                    'kwota_brutto': brutto,
                    'jednostka': 'szt.',
                    'opis': item.product.name,
                    'stawka_vat': 1,  # 23%
                })
        else:
            netto = float(order.price)
            tax_rate = 23
            tax, brutto = su.calculate_tax(netto, tax_rate)
            data.append({
                'id_produktu': None,
                'ilosc': 1,
                'cena_netto': order.price,
                'kwota_netto': netto,
                'kwota_vat': tax,
                'kwota_brutto': brutto,
                'jednostka': 'szt.',
                'opis': order.description,
                'stawka_vat': 1,  # 23%
            })

        # issue an invoce
        amount_paid = order.price_incl_tax
        if isinstance(order, Order):
            payment_method = order.payment_method
        else:
            payment_method = order.reference_order.payment_method
        obj = systim.add_invoice(company_id, data, payment_method, amount_paid)
        if obj['error']['code'] != 0:
            logger.error(f'[{now}] issuing an invoice failed: {nip}')
            continue
        invoice_id = obj['result']['id']
        invoice_number = obj['result']['numer_faktury']
        try:
            with transaction.atomic():
                invoice = Invoice.objects.create(system='SYS', ext_id=invoice_id, number=invoice_number)
                order.invoice_id = invoice
                order.save()
        except IntegrityError:
            logger.error(f'[{now}] storing invoice data in database failed: {invoice_number}')
            continue


def create_monthly_withdrawals():
    """
    Tworzy żądania wypłat dla wszystkich partnerów co miesiąc i wysyła podsumowanie do administratora.
    """
    now = timezone.now()

    mailer = Mailer()
    new_withdrawal_requests = []

    # Pobierz wszystkich partnerów
    partners = User.objects.filter(groups__name='Partner').select_related('userprofile', 'affiliate_link')

    for partner in partners:
        affiliate_link = getattr(partner, 'affiliate_link', None)
        if not affiliate_link:
            continue  # Brak linku afiliacyjnego

        # Znajdź ostatni cykl wypłat
        last_cycle = WithdrawalCycle.objects.filter(partner=partner).order_by('-end_date').first()

        if last_cycle:
            cycle_start = last_cycle.end_date
        else:
            cycle_start = affiliate_link.created_at

        cycle_end = cycle_start + timedelta(days=30)

        # Upewnij się, że cykl nie przekracza aktualnej daty
        if cycle_end > now:
            cycle_end = now

        # Utwórz nowy cykl
        new_cycle = WithdrawalCycle.objects.create(
            partner=partner,
            start_date=cycle_start,
            end_date=cycle_end
        )

        # Oblicz całkowitą prowizję w cyklu
        total_commission = PartnerCommission.objects.filter(
            partner=partner,
            created_at__gte=cycle_start,
            created_at__lt=cycle_end
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        if total_commission > Decimal('0.00'):
            # Stwórz żądanie wypłaty
            withdrawal_request = AffiliateWithdrawalRequest.objects.create(
                user=partner,
                amount=total_commission,
                cycle=new_cycle
            )

            new_withdrawal_requests.append(withdrawal_request)

    # Wysyłanie podsumowania do administratora
    if new_withdrawal_requests:
        mail_result = mailer.send_monthly_withdrawal_summary(new_withdrawal_requests)



def time_checker():
    tm = TimeManager()
    print(tm.current_time())
    print(tm.shifted_time(weeks=1))
    print(tm.shifted_time(weeks=-1))
