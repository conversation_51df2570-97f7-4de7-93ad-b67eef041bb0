:root {

/* <PERSON><PERSON>D<PERSON>OK COLORS */
--brand-yellow: #fbab18;
--brand-green: #84b436;
--brand-red: #b72f2f;
--brand-purple: #a580b2;
--brand-blue: #0077E4;
--brand-white: #ffffff;
--brand-black: #000000;
--brand-gray: #666666;

--default-color: #161B21;
--accent-color: #0077E4;
--heading-color: #161B21;
--contrast-color: #ffffff;

/* GRAYSCALE COLORS */
--brand-lightgray: #f0f0f0;
--brand-semilightgray: #d0d0d0;
--brand-darkgray: #101010;
--brand-semidarkgray: #303030;

/* MARCIN DB COLORS */
--primary-700: #0077E4;
--primary-600: #0088FF;
--primary-500: #33A8FF;
--primary-400: #66C2FF;
--primary-300: #99DBFF;
--primary-200: #CCE6FF;
--primary-100: #E5F1FF;

--brand-red-600: #b72f2f;
--brand-red-500: #d04a4a;
--brand-red-400: #e47070;
--brand-red-300: #f29b9b;
--brand-red-200: #f9cfcf;
--brand-red-100: #fde4e4;

--gray-100: #EEF0F4;  /*238, 240, 244*/
--gray-200: #DDE1E8;
--gray-300: #CBD3DD;
--gray-400: #BAC4D1;
--gray-500: #A9B5C6;
--gray-600: #7D8FA9;
--gray-700: #586A84;
--gray-800: #313131;
--gray-900: #1D232C;
--gray-0: #161B21;  /*22,27,33*/

/*--light-gray-color: #d4d1d1;
--light-gray-color-25: rgba(212, 209, 209, .25);
--platinum: #e5e5e5;
--black: #2b2d42;
--white: #fff;
--thumb: #edf2f4;*/

/* MAIN COLORS */
/* BG-DARK */
/*--bs-dark-rgb: rgb(51, 51, 51);
--main-bg-gray: #262626;
--dark-bg: #1E1E1E;*/

/* LIGHT */
/*--orange-lg: #FFA927;
--green-lg: #00C844;
--red-lg: #FF2055;
--blue-lg: #0085FF;*/

/* STANDARD */
/*--orange: #FF9A02;
--green: #00AA3A;
--red: #FF003D;
--blue: #0071DA;*/

/* DARK */
/*--orange-dk: #E68A00;
--green-dk: #028F32;
--green-bg-dk: rgb(51, 56, 51);
--red-dk: #DA0034;
--blue-dk: #015AAC;*/
}

:root {
    --default-font: "Roboto",  system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --heading-font: "Roboto",  sans-serif;
    --nav-font: "Lato",  sans-serif;
}

:root {
    scroll-behavior: smooth;
}

body {
/*background-color: var(--green-bg-dk);*/
font-family: var(--default-font);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

.link-info {
  color: var(--primary-600)!important;
  text-decoration: none!important;
  font-weight: bold;
}
/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 100px;
  overflow: clip;
}

@media (max-width: 1199px) {

  section,
  .section {
    scroll-margin-top: 66px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center!important;
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 0;
}

.section-title p {
  font-size: 15px;
  margin-bottom: 10px;
}

/*--------------------------------------------------------------
# Top Slider
--------------------------------------------------------------*/
.top-slider {
  padding: 0!important;
}

/* Wysokość slidera: 600px dla desktop/tablet, 550px dla mobilnych */
.top-slider .carousel-item {
  position: relative;
  width: 100%;
  height: 600px;
}

/* @media (max-width: 767px) {
  .top-slider .carousel-item {
    height: 550px;
  }
} */

/* Tło w layout C – wykorzystuje zmienne CSS z fallbackiem do domyślnych kolorów */
.slider-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-light, var(--gray-100));
  background-position: center;
  background-size: cover;
  z-index: 1;
}

body.dark-mode .slider-bg {
  background: var(--bg-dark, var(--gray-800));
}

/* Tło responsywne w layoutach A/B */
.responsive-bg img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.content-title {
  font-size: 22px;
  color: var(--heading-color);
  margin-bottom: 20px;
}

.lead {
  font-size: 18px;
  line-height: 1.6;
  color: var(--default-color);
  margin-bottom: 20px;
}

.link-info {
  color: var(--accent-color);
  text-decoration: none;
}

.btn-lg {
  padding: 12px 30px;
  font-size: 1.25rem;
}

.typed-word::after {
  content: '|';
  animation: blink 1.3s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0; }
  100% { opacity: 1; }
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  /* Usunięcie domyślnego obrazu tła */
  background-image: none;
  filter: none!important;
  height: auto;
}

/* Strzałka w lewo */
.carousel-control-prev-icon::after {
  content: '‹'; 
  font-size: 5rem; 
  color: var(--brand-blue);
}

/* Strzałka w prawo */
.carousel-control-next-icon::after {
  content: '›'; 
  font-size: 5rem;
  color: var(--brand-blue);
}

.image-col {
  display: flex;
  flex-direction: column;      /* Ustaw w pionie */
  justify-content: flex-end;   /* „Przyklejenie” do dołu */
}

@media (max-width: 767px) {
  .top-slider .carousel-item {
    height: auto !important; 
    min-height: 600px;
    padding-top: 5px;
  }
}

.my-text-col {
  text-align: center;         
  max-width: 1140px;
  margin: 0 auto; 
}

.my-text-col {
  max-width: 1140px;
  margin: 0 auto;   
  text-align: center;
}

@media (min-width: 768px) and (max-width: 991px) {
  .layout-img-col img {
    width: 160% !important; 
  }
}


/*--------------------------------------------------------------
# About 2 Section
--------------------------------------------------------------*/
.about-2 .content {
    background-color: var(--gray-100);
    padding-top: 50px;
    padding-bottom: 50px;
  }
  
  .about-2 .content-subtitle {
    font-size: 15px;
    margin-bottom: 10px;
    display: block;
    color: var(--default-color);
  }
  
  .about-2 .content-title {
    color: var(--heading-color);
    font-size: 22px;
    margin-bottom: 30px;
  }
  
  .about-2 p {
    line-height: 1.7;
    color: var(--default-color);
  }
  
  .about-2 .btn-get-started {
    background-color: var(--primary-600);
    color: var(--contrast-color);
    border-radius: 30px;
    padding: 8px 30px;
    border: 2px solid transparent;
    transition: 0.3s all ease-in-out;
    font-size: 14px;
  }
  
  .about-2 .btn-get-started:hover {
    border-color: var(--accent-color);
    background-color: transparent;
    color: var(--accent-color);
  }
  
  .about-2 .lead {
    line-height: 1.6;
    font-size: 18px;
    font-weight: normal;
    color: var(--default-color);
  }

/*--------------------------------------------------------------
# Steps Section
--------------------------------------------------------------*/
.steps .swiper-wrapper {
    /* Ustawiamy wysokość na 100% i flexbox */
    display: flex;
    height: 100%;
  }
  
  .steps .swiper-slide {
    /* Karta wypełnia całą wysokość rodzica */
    height: auto;
    display: flex;
    align-items: stretch;
  }
  
  .steps .step {
    background-color: var(--gray-100);
    padding: 20px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .steps .step h3 {
    font-size: 20px;
    margin-top: 15px;
    text-align: left;
  }
  
  .steps .step h3 a {
    color: var(--heading-color);
  }
  
  .steps .step .pic {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
  }
  
  .steps .step .pic img {
    border-radius: 4px;
    max-width: 100%;
    height: auto;
  }
  
  .steps p {
    line-height: 1.7;
    color: var(--default-color);
    text-align: left;
  }
  
  .steps .more {
    position: relative;
    padding-right: 30px;
    display: inline-block;
    text-align: left;
  }
  
  .steps .more span {
    position: absolute;
    top: 50%;
    right: 0;
    font-size: 16px;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: transparent;
    color: var(--contrast-color);
    line-height: 1.6;
    text-align: center;
    transition: 0.3s all ease;
  }
  
  .steps .more:hover {
    color: var(--contrast-color);
  }
  
  .steps .more:hover span {
    background: var(--accent-color);
  }
  
  .steps .more.dark {
    color: var(--default-color);
  }
  
  .steps .more.dark > span {
    color: var(--default-color);
  }
  
  .steps .more.dark:hover {
    color: var(--accent-color);
  }
  
  .steps .more.dark:hover span {
    color: var(--contrast-color);
    background: var(--accent-color);
  }
  
  /* Stylizacja nawigacji */
  .steps .slider-nav a {
    width: 40px;
    height: 40px;
    position: relative;
    background: var(--accent-color);
    border-radius: 50%;
    transition: 0.2s all ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
  }
  
  .steps .slider-nav a i {
    color: var(--gray-100);
    font-size: 24px;
  }
  
  /* Tło pod strzałkami nawigacyjnymi */
  .steps .slider-nav i {
    background-color: var(--primary-600);
    padding: 5px 10px;
    border-radius: 8px;
  }
  
  .steps .slider-nav a:hover {
    background-color: color-mix(in srgb, var(--accent-color) 90%, white 15%);
  }
  
  .steps .slider-nav a:first-child {
    margin-left: 0;
  }
  
  /* Przesunięcie paginacji niżej */
  .steps .swiper-pagination {
    position: relative;
    margin-top: 20px;
  }
  
  .steps .swiper-pagination .swiper-pagination-bullet {
    background-color: var(--accent-color);
    opacity: 1;
  }
  
  .steps .swiper-pagination .swiper-pagination-bullet-active {
    background-color: var(--heading-color);
  }


/*--------------------------------------------------------------
# Robotyzacja Section
--------------------------------------------------------------*/
.robotyzacja {
    background-color: var(--gray-100);
}

.robotyzacja .service-item .service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    flex: 0 0 50px;
    border-radius: 50%;
    position: relative;
    color: var(--gray-900); /* Poprawa czytelności ikony */
    background-color: var(--light-gray); /* Lekkie tło dla ikon */
    font-size: 24px;
    margin-right: 20px;
    transition: all 0.3s ease; /* Dodaje płynność zmian */
}

/* Styl dla aktywnego elementu oraz przy hover */
.robotyzacja .service-item.link.active,
.robotyzacja .service-item.link:hover {
    background-color: var(--brand-white); /* Zielone tło dla aktywnego/hover elementu */
    color: #fff; /* Biały tekst */
}

/* Styl dla ikony w aktywnym elemencie oraz przy hover */
.robotyzacja .service-item.link.active .service-icon,
.robotyzacja .service-item.link:hover .service-icon {
    background-color: var(--primary-700); /* Ciemnozielone tło dla ikony */
    color: #fff; /* Biały tekst ikony */
}
  
.robotyzacja .service-item .service-contents h3 {
    font-size: 18px;
    color: var(--gray-900);
}

.robotyzacja .service-item.link {
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 7px;
    text-decoration: none;
    background: var(--surface-color);
    transition: background 0.3s ease;
}

.robotyzacja .service-item.link .service-contents {
    color: var(--gray-900);
}

.robotyzacja .service-item.link:hover {
    color: #fff; /* Biały tekst przy hover */
}

.robotyzacja .swiper-wrapper {
    height: auto;
}

.robotyzacja .swiper-slide img {
    width: 100%;
    border-radius: 8px;
}

.robotyzacja .swiper-slide .p-4 {
    padding: 20px;
}

.robotyzacja .list-check {
    padding-left: 20px;
}

.robotyzacja .list-check li {
    position: relative;
    margin-bottom: 10px;
    padding-left: 20px;
    color: var(--default-color);
}

.robotyzacja .list-check li::before {
    content: "\f00c"; /* FontAwesome check icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 0;
    color: var(--accent-color);
}

/*--------------------------------------------------------------
# Services 2 Section
--------------------------------------------------------------*/
.services-2 .content-subtitle {
    font-size: 15px;
    margin-bottom: 10px;
    display: block;
    color: var(--default-color);
  }
  
  .services-2 .content-title {
    color: var(--heading-color);
    font-size: 22px;
    margin-bottom: 30px;
  }
  
  .services-2 p {
    line-height: 1.7;
    color: var(--default-color);
  }
  
  .services-2 .lead {
    line-height: 1.6;
    font-size: 18px;
    font-weight: normal;
    color: var(--default-color);
  }
  
  .services-2 .btn-get-started {
    background-color: var(--primary-700);
    color: var(--brand-white);
    border-radius: 30px;
    padding: 8px 30px;
    border: 2px solid transparent;
    transition: 0.3s all ease-in-out;
    font-size: 14px;
  }
  
  .services-2 .btn-get-started:hover {
    border-color: var(--accent-color);
    background-color: transparent;
    color: var(--accent-color);
  }
  
  .services-2 .services-item .services-icon {
    color: var(--accent-color);
    margin-bottom: 20px;
  }
  
  .services-2 .services-item .services-icon i {
    font-size: 48px;
  }
  
  .services-2 .services-item h3 {
    font-size: 17px;
    font-weight: 400;
    color: var(--heading-color);
  }



/*--------------------------------------------------------------
# Skill Section
--------------------------------------------------------------*/
.skill .content {
  background-color: var(--gray-100);
  padding-top: 50px;
  padding-bottom: 50px;
}

.skill .content-subtitle {
  font-size: 15px;
  margin-bottom: 10px;
  display: block;
  color: var(--default-color);
}

.skill .content-title {
  color: var(--heading-color);
  font-size: 22px;
  margin-bottom: 30px;
}

.skill p {
  line-height: 1.7;
  color: var(--default-color);
}

.skill .btn-get-started {
  background-color: var(--primary-600);
  color: var(--contrast-color);
  border-radius: 30px;
  padding: 8px 30px;
  border: 2px solid transparent;
  transition: 0.3s all ease-in-out;
  font-size: 14px;
}

.skill .btn-get-started:hover {
  border-color: var(--accent-color);
  background-color: transparent;
  color: var(--accent-color);
}

.skill .lead {
  line-height: 1.6;
  font-size: 18px;
  font-weight: normal;
  color: var(--default-color);
}

/*--------------------------------------------------------------
# BotBook Section
--------------------------------------------------------------*/

.botbook-card {
  display: flex;
  flex-direction: column;
  height: 100%; 
  background-color: var(--surface-color);
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 0px;
  transition: all 0.3s ease;
  border-color: var(--gray-700);
  border-width: 1px;
  border-style: solid;
}

.botbook-card h2 {
  font-size: 16px;          
  font-weight: bold;
  line-height: 1.2em;       
  height: calc(1.2em * 2);    
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;     
  overflow: hidden;
  text-overflow: ellipsis;  
}


.botbook-card p {
  line-height: 1.2em;                 
  height: calc(1.2em * 2);              
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.botbook-card .card-footer {
  margin-top: auto;
}
  
.sidebar-categories { 
  height: 100%; 
  background-color: var(--gray-100);
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 0px;
  transition: all 0.3s ease;
}

.back-link {
  color: var(--brand-black);
  text-decoration: none;
  font-size: 14px;
  margin-bottom: 15px;
}

/* Lista bez wypunktowania */
.sidebar-categories .category-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

/* Każdy element listy z białym tłem i dolną krawędzią */
.sidebar-categories .category-list-item {
  padding: 0.5rem 0;
}


/* Link do kategorii – czarny tekst, bez podkreślenia */
.sidebar-categories .category-link {
  text-decoration: none;
  color: var(--brand-black);
}

.botbook-breadcrumb-item {
  display: inline-block;
  margin-right: 5px;
  font-size: 14px;
  color: var(--brand-black)!important;
}

/*--------------------------------------------------------------
# Faq Section
--------------------------------------------------------------*/
.faq .content-subtitle {
  font-size: 15px;
  margin-bottom: 10px;
  display: block;
  color: var(--default-color);
}

.faq .content-title {
  color: var(--heading-color);
  font-size: 22px;
  margin-bottom: 30px;
}

.faq p {
  line-height: 1.7;
  color: var(--default-color);
}

.faq .custom-accordion .accordion-item {
  background-color: var(--surface-color);
  margin-bottom: 0px;
  position: relative;
  border-radius: 0px;
  overflow: hidden;
}

.faq .custom-accordion .accordion-item .btn-link {
  display: block;
  width: 100%;
  padding: 15px 0;
  text-decoration: none;
  text-align: left;
  color: var(--default-color);
  border: none;
  padding-left: 40px;
  border-radius: 0;
  position: relative;
  background-color: color-mix(in srgb, var(--default-color), transparent 94%);
}

.faq .custom-accordion .accordion-item .btn-link:before {
  content: "\f282";
  display: inline-block;
  font-family: "bootstrap-icons" !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -0.125em;
  -webkit-font-smoothing: antialiased;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 15px;
}

.faq .custom-accordion .accordion-item .btn-link[aria-expanded=true] {
  color: var(--accent-color);
}

.faq .custom-accordion .accordion-item .btn-link[aria-expanded=true]:before {
  font-family: "bootstrap-icons" !important;
  content: "\f286";
  position: absolute;
  color: var(--accent-color);
  top: 50%;
  transform: translateY(-50%);
}

.faq .custom-accordion .accordion-item .accordion-body {
  padding: 20px 20px 20px 20px;
  color: var(--default-color);
}

.light-background {
background-color: var(--gray-100);
}

.bg-dark {
/*background-color: var(--bs-dark-rgb)!important;*/
}

.bg-dark-content {
/*background-color: var(--main-bg-gray);*/
height: 100%;
}


/* Style for the fixed navbar */
.navbar-home {
padding-top: 0px;
padding-bottom: 0px;
min-height: 80px !important;
}

.navbar-dashboard {
    margin: .6rem 1rem 0rem 2.5rem;
    /* top: 10px; */
    /*left: 200px; */
    /* right: 18px; */
    border-radius: 10px;
}
@media (min-width: 992px) {
    .navbar-dashboard {
        margin: .6rem 1rem 0rem 12.5rem;
    }
}


  .sidebar-logo {
    margin-bottom: 50px;
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
  }

  .site-name {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--gray-100)!important;
  }

.db-section-title {
  font-size: 1rem;
  font-weight: 400;
}

.double-menu {
/*border: 1px solid var(--light-gray-color-25);*/
border: 1px solid var(--bs-secondary);
border-radius: .3rem;
display: inline-flex;
}

.double-menu .nav-link {
padding: .3rem .5rem !important;
}

.main-content {
margin-top: 80px;
min-height: 50vh;
}

.main-index-content {
/*color: white;*/
height: 100%;
padding: 10%;
}

.main-login-content {
/*color: white;*/
height: 100%;
}

.main-dashboard-content {
padding-top: 3.2rem;
padding-bottom: 1rem;
/*color: white;*/
min-height: 99vh;
}

.header-dashboard {
    position:fixed;
    width: 100%;
    height: 85px;
    z-index: 1030;
}

.footer-dashboard {
    /* background-color: var(--gray-900); */
    /* height: 30px; */
}

/* CAROUSEL */
.index-carousel {
/*height: 96vh;*/
/*height: 50vh;*/
overflow: hidden;
}
.carousel-caption {
/*top: 10%;*/
}
.carousel-caption h2, .carousel-caption h4 {
    color: white;
    text-shadow: 0px 0px 15px black;
}
.carousel-caption p {
    color: #e7e7e7;
    text-decoration: italic;
    text-shadow: 0px 0px 15px black;
}
.carousel-caption .btn {
    box-shadow: 0px 0px 15px #909090;
}
.carousel-img {
/*display: flex;
justify-content: center;
align-items: center;*/
width: 100%;
height: 300px;
}
.carousel-img img {
/*max-height: 100%;*/
}

/*.login_form {
padding: 5% 30% 0;
}*/

.footer {
/*background-color: var(--green-bg-dk);*/
}

#page-container {
position: relative;
/*min-height: 100vh;*/
}

/* DASHBOARD */
.header-dashboard div.gradient {
    height: 15px;
}
.footer-dashboard div.gradient {
    height: 15px;
}

/* DASHBOARD CONTENT */
.dashboard-content {
    margin: 2rem 1rem 1rem 2.5rem;
}
@media (min-width: 992px) {
    .dashboard-content {
        margin: 2rem 1rem 1rem 12.5rem;
    }
}
.dashboard-element {
    /* border-style: solid;
    border-width: 3px; */
    /*border-color: var(--green-dk);*/
    /* border-color: var(--brand-green); */
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 20px;
}

.dashboard-header {
    padding: 15px;
}

/* DASHBOARD CARDS */
.dashboard-card {
/*background-color: var(--bs-dark-rgb);*/
padding: 1rem;
min-height: 200px;
height: 100%;
/*border-radius: 1rem;*/
word-wrap: break-word;
/*box-shadow: 5px 5px var(--light-gray-color);*/
border-style: solid;
border-width: 3px;
/*border-color: var(--green-dk);*/
}

.dashboard-card-sm {
width: 23%;
}

.dashboard-card-md {
width: 48%;
}

.dashboard-card-lg {
width: 73%;
}

.dashboard-card-full {
width: 98%;
}

/* SIDE-NAV */
#sidebar-wrapper {
    z-index: 1031;
    position: fixed;
    /* width: 5.2rem; */
    /* left: 178px; */
    top: 10px;
    /*left: 10px;*/
    height: 97.5%;
    border-radius: 0 10px 10px 0;
    /* margin-left: -178px; */
    /* -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease; */
    width: 25px;
    overflow: hidden;
}
.sidebar-expanded {
    width: auto !important;
}
#sidebar-wrapper .toggler {
    height: 100%;
    width: 25px !important;
    /* background-color: red; */
    position: absolute;
    right: 0;
    display: block;
    background-color: inherit;
}
#sidebar-wrapper .toggler div > div {
    cursor: pointer;
}
#sidebar-wrapper.sidebar-expanded .toggler {
    background-color: transparent;
}
@media (min-width: 992px) {
    #sidebar-wrapper {
        left: 10px;
        border-radius: 10px 10px 10px 10px;
        width: auto;
        overflow: auto;
    }
    #sidebar-wrapper .toggler {
        display: none !important;
    }
    .sidebar-menu {
        margin-right: 2px !important;
    }
}

 .logo-sidebar {
    width: 120px;
    height: 88px;
    background-size: cover;
}


  .sidebar-menu {
    /* width: 85%;
    margin: 3rem auto; */
    margin-top: 5px;
    margin-left: 2px;
    margin-right: 17px;
    padding: 10px;
    border-radius: 10px;
  }

  .sidebar-menu .nav-link {
    padding: 0px;
  }

  .sidebar-menu a {
    font-size: 12px;
    color: var(--brand-white);
  }

  .menu-chosen-bg {
    padding: 2px 10px 2px 2px;
    /* width: 40px;
    height: 40px; */
    border-radius: 10px;
    /* margin: auto; */
  }

  .menu-chosen-bg:hover {
    opacity: 0.9;
  }

  .menu-unchosen-bg {
    padding: 2px 5px;
    color: var(--gray-700);
    /* width: 40px;
    height: 40px;
    border-radius: 25px;
    margin: auto; */
  }

  .menu-unchosen-bg:hover {
    /*background-color: var(--light-gray-color);*/
    opacity: 0.5;
  }

  .menu-chosen-bg, .menu-unchosen-bg {
    display: flex;
    align-items: center;
    }

/* .row {
--bs-gutter-x: 0;
} */

/* HOME PAGE */

/* .container {
max-width: 1400px;
padding: 0 15px;
margin: 0 auto;
}

h2 {
font-size: 32px;
margin-bottom: 1em;
} */

/* .cards {
display: grid;
grid-auto-columns: 100%;
grid-column-gap: 10px;
grid-auto-flow: column;
padding: 25px 0px;
list-style: none;
overflow-x: scroll;
scroll-snap-type: x mandatory;
}

.card {
color: var(--blue);
display: flex;
flex-direction: column;
padding: 20px;
background: var(--dark-bg);
border-radius: 12px;
box-shadow: 0 5px 15px rgba(0, 0, 0, 15%);
scroll-snap-align: start;
transition: all 0.2s;
}

.first-card {
background: var(--blue);
color: white;
}

.card:hover {
color: var(--white);
background: var(--blue);
}

.card .card-title {
font-size: 20px;
}

.card .card-content {
margin: 20px 0;
max-width: 85%;
}

.card .card-link-wrapper {
margin-top: auto;
}

.card .card-link {
display: inline-block;
text-decoration: none;
color: white;
background: var(--blue);
padding: 6px 12px;
border-radius: 8px;
transition: background 0.2s;
}

.card:hover .card-link {
background: var(--blue-dk);
}

.cards::-webkit-scrollbar {
height: 12px;
}

.cards::-webkit-scrollbar-thumb,
.cards::-webkit-scrollbar-track {
border-radius: 92px;
}

.cards::-webkit-scrollbar-thumb {
background: var(--blue-dk);
}

.cards::-webkit-scrollbar-track {
background: var(--dark-bg);
}

@media (min-width: 500px) {
.cards {
grid-auto-columns: calc(50% - 10px);
grid-column-gap: 20px;
}
}

@media (min-width: 700px) {
.cards {
grid-auto-columns: calc(calc(100% / 3) - 20px);
grid-column-gap: 30px;
}
}

@media (min-width: 1100px) {
.cards {
grid-auto-columns: calc(25% - 30px);
grid-column-gap: 40px;
}
} */

/* AKORDEONY */

.accordion {
margin: auto;
}

.accordion input {
display: none;
}
.box {
position: relative;
background: var(--bs-dark-rgb);
height: 64px;
transition: all .15s ease-in-out;
}
.box::before {
content: '';
position: absolute;
display: block;
top: 0;
bottom: 0;
left: 0;
right: 0;
pointer-events: none;
box-shadow: 0 -1px 0 var(--dark-bg),0 0 2px rgba(0,0,0,.12),0 2px 4px rgba(0,0,0,.24);
}
header.box {
background: var(--blue);
z-index: 100;
cursor: initial;
box-shadow: 0 -1px 0 var(--dark-bg),0 0 2px -2px rgba(0,0,0,.12),0 2px 4px -4px rgba(0,0,0,.24);
}
header .box-title {
margin: 0;
font-weight: normal;
font-size: 16pt;
/*color: white;*/
cursor: initial;
}
.box-title {
width: calc(100% - 40px);
color: var(--blue-lg);
height: 64px;
line-height: 64px;
padding: 0 20px;
display: inline-block;
cursor: pointer;
-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;
}
.box-content {
width: calc(100% - 40px);
padding: 30px 20px;
font-size: 11pt;
/*color: white;*/
display: none;
}
.box-close {
position: absolute;
height: 64px;
width: 100%;
top: 0;
left: 0;
cursor: pointer;
display: none;
}

input:checked + .box {
height: auto;
margin: 16px 0;
box-shadow: 0 0 6px rgba(0,0,0,.16),0 6px 12px rgba(0,0,0,.32);
}
input:checked + .box .box-title {
border-bottom: 1px solid rgba(0,0,0,.18);
}
input:checked + .box .box-content,
input:checked + .box .box-close {
display: inline-block;
}
.arrows section .box-title {
padding-left: 44px;
width: calc(100% - 64px);
}
.arrows section .box-title:before {
position: absolute;
display: block;
content: '\203a';
font-size: 18pt;
left: 20px;
top: -2px;
transition: transform .15s ease-in-out;
color: rgba(0,0,0,.54);
}
input:checked + section.box .box-title:before {
transform: rotate(90deg);
}

/* HOME PAGE - LOGIN PAGE */
.home_page-login {
height: 900px;
}

.socialaccount_images {
max-height: 100%;
max-width: 300%;
margin: auto;
}


/* ROZWIJANE MENU */
/* .menu {
display: inline-block;
position: relative;
}

.menu a {
color: var(--light-gray-color);
text-decoration: none;
}

.menu a:hover {
text-decoration: underline;
} */
/* W PRAWO */
/* .menu .dropdown-right {
display: none;
position: absolute;
top: -15px;
left: 100%;
padding: 10px;
white-space: nowrap;
} */
/* W LEWO */
/* .menu .dropdown-left {
display: none;
position: absolute;
top: -15px;
right: 100%;
padding: 10px;
white-space: nowrap;
}

.menu:hover .dropdown {
display: block;
}

.menu .dropdown a {
display: inline-block;
text-decoration: none;
color: var(--light-gray-color);
padding: 5px;
}

.menu .dropdown a:not(:last-child) {
margin-right: 10px;
}

.menu .dropdown a:hover {
/*background-color: var(--main-bg-gray);*/
}

.form-control {
width: 70%;
border-radius: 20px;
}

/*.login-button {
width: 70%;
border-radius: 20px;
}*/

.button {
border-radius: 20px;
}

.baner-header {
font-weight: 100;
font-size: 3rem;
text-align: center;
margin: auto;
width: 70%;
height: inherit;
display: table-cell;
vertical-align: middle;
color: white;
}

.baner {
-webkit-background-size: cover;
-moz-background-size: cover;
-o-background-size: cover;
background-size: cover;
height: 300px;
display: table;
}

.login-baner {
background-image: url("../images/baner/octo.png");
}

.index-baner {
background-image: url("../images/baner/1.png");
height: 550px;
}

/* ROZDZIELNIK SEKCJI */
.divider {
height: 3rem;
background-color: rgba(0, 0, 0, .1);
border: solid rgba(0, 0, 0, .15);
border-width: 1px 0;
box-shadow: inset 0 .5em 1.5em rgba(0, 0, 0, .1), inset 0 .125em .5em rgba(0, 0, 0, .15);
}

@media (min-width: 992px) {
.rounded-lg-3 { border-radius: .3rem; }
}



.container-border {
position: relative!important;
}

.section-with-border {
z-index: 1;
position: relative;
}

.index-robotyzacja-border {
border-width: 0 0 0 3px!important;
/*border-color: var(--green-dk)!important;*/
position: absolute;
width: 100%;
height: 50%;
bottom: -50px;
left: 0;
}

.index-botie-border {
border-width: 0 0 0 3px!important;
/*border-color: var(--green-dk)!important;*/
position: absolute;
width: 100%;
height: 150%;
bottom: -300px;
left: 0;
}

.index-botieweb-border {
border-width: 0 3px 3px 3px!important;
/*border-color: var(--green-dk)!important;*/
position: absolute;
width: 100%;
height: 80%;
bottom: 0px;
left: 0;
}

.border-lead {
border-width: 3px!important;
/*border-color: var(--green-dk)!important;*/
}

.section-robotyzacja, .section-botie-web {
/*background-color: var(--green-bg-dk);*/
}

.robotyzacja-header {
position: relative;
top: -30px;
display: inline-block;
/*background-color: var(--green-bg-dk);*/
padding: 0 60px 60px;
margin-left: -60px;
}

.robotyzacja-first-paragraph {
margin-top: -40px;
}

.botie-description {
height: 100%;
position: relative;
}

.botie-section-button {
bottom: -25px;
right: 50px;
/*background-color: var(--bs-dark-rgb);*/
position: absolute;
width: 40%;
}

.botie-web-section-button {
bottom: -25px;
right: 0px;
width: 45%;
padding-right: 15%;
/*background-color: var(--green-bg-dk);*/
position: absolute;
}

.steps-container {
position: relative;
/*background-color: var(--green-bg-dk);*/
}

.steps-container .number {
font-size: 8rem;
font-weight: bold;
font-style: italic;
/* color: var(--green-dk); */
display: inline-block;
position: absolute;
bottom: -34px;
right: 32px;
}

.description-box {
position: relative;
z-index: 1;
}

.fade-in {
opacity: 0;
transform: translateY(20px);
transition: opacity 1s ease, transform 1s ease;
}

.fade-in-animate {
opacity: 1;
transform: translateY(0);
}

.steps-container {
/* Add the following styles to set the initial transition */
transition: transform 0.3s ease;
}

/* Add the following rule to apply the scale transform on hover */
.steps-container:hover {
transform: scale(1.05);
}
/* CARDS LOOP */

.slide-container-categ{
width: 100%;
padding: 40px 0!important;
}
.slide-content-categ{
margin: 0 60px;
overflow: hidden;
}

.card-categ {
/*background-color: var(--green-bg-dk);*/
}


.image-content,
.card-content {
display: flex;
flex-direction: column;
align-items: center;
padding: 10px 14px;
}

.image-content{
position: relative;
row-gap: 5px;
padding: 25px 0;
}

.overlay {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    /*background-color: var(--green-dk);*/
    border-radius: 25px 25px 0 25px;
    }

.overlay::before,
.overlay::after{
content: '';
position: absolute;
right: 0;
bottom: -40px;
height: 40px;
width: 40px;
/*background-color: var(--green-dk);*/
}

.overlay::after{
border-radius: 0 25px 0 0;
/*background-color: var(--green-bg-dk);*/
}


.card-image {
position: relative;
height: 150px;
width: 150px;
border-radius: 50%;
/* background: var(--green-bg-dk); */
background-color: var(--brand-white);
padding: 3px;
}

#how-to .card-image{
height: 250px;
width: 250px;
background-color: var(--gray-100);
}

.card-image .card-img{
height: 100%;
width: 100%;
object-fit: cover;
border-radius: 50%;
/*border: 4px solid #4070F4;*/
}

.description{
min-height: 200px;
}

.swiper-navBtn{
color: var(--blue);
transition: color 0.3s ease;
}
.swiper-navBtn:hover{
color: var(--blue-dk);
}
.swiper-navBtn::before,
.swiper-navBtn::after{
font-size: 35px;
}
.swiper-button-next{
right: 0;
}
.swiper-button-prev{
left: 0;
}
.swiper-pagination- {
margin-bottom: 0!important;
}
.swiper-pagination-bullet{
/*background-color: #6E93f7;*/
opacity: 1;
}
.swiper-pagination-bullet-active{
/*background-color: #4070F4;*/
}
@media screen and (max-width: 768px) {
.slide-content{
margin: 0 10px!important;
}
.swiper-navBtn{
display: none!important;
}
}

/* DOWNLOAD PAGE */

.logo-download-image {
position: relative;
height: 150px;
width: 150px;
/*background-color: var(--green-dk);*/
}

.switch {
position: relative;
display: inline-block;
width: 60px;
height: 34px;
}

.switch input { 
opacity: 0;
width: 0;
height: 0;
}

.slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: #ccc;
-webkit-transition: .4s;
transition: .4s;
}

.slider:before {
position: absolute;
content: "";
height: 26px;
width: 26px;
left: 4px;
bottom: 4px;
background-color: white;
-webkit-transition: .4s;
transition: .4s;
}

input:checked + .slider {
background-color: #2196F3;
}

input:focus + .slider {
box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
-webkit-transform: translateX(26px);
-ms-transform: translateX(26px);
transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
border-radius: 34px;
}

.slider.round:before {
border-radius: 50%;
}

/* FLOATING LABELS FOR MODALS */
.floating-label-group {
position: relative;
}

.floating-label-group-dk {
position: relative;
}

.floating-label-group .form-control {
width: 100%;
padding: 0.8rem;
/*background-color: var(--bs-dark-rgb);*/
/*color: white;*/
}

.floating-label-group-dk .form-control {
width: 100%;
padding: 0.8rem;
/*background-color: var(--main-bg-gray);*/
/*color: white;*/
}

.form-select {
/*background-color: var(--bs-dark-rgb);*/
/*color: white;*/
}

.form-select-dk {
/*background-color: var(--main-bg-gray);*/
/*color: white;*/
}

.floating-label {
font-size: small;
/*color: white;*/
/*background-color: var(--bs-dark-rgb);*/
position: relative;
left: 0.1rem;
top: 0px;
padding: 0 10px;
}

.floating-label-dk {
font-size: small;
/*color: white;*/
/*background-color: var(--main-bg-gray);*/
position: relative;
left: 1rem;
top: 11px;
padding: 0 10px;
}

.modal-header,
.modal-body,
.modal-footer {
border: none;
}

.modal-footer {
display: flex;
justify-content: space-between;
}

/*.invalid-feedback {
position: absolute;
bottom: -20px;
right: 0;
left: 0;
}*/


/* TAGS */

.selected-tags {
display: flex;
flex-wrap: wrap;
gap: 10px;
}

/*.selected-tag {
background: var(--blue);
color: var(--white);
padding: 5px 10px;
border-radius: 15px;
display: flex;
align-items: center;
}

.selected-tag .remove-tag {
margin-left: 5px;
color: var(--white);
text-decoration: none;
cursor: pointer;
}*/

/* Autocomplete List */
.tags-autocomplete-list {
/*color: var(--white);
z-index: 1000;*/
display: flex;
flex-wrap: wrap;
gap: 10px;
}

.tags-autocomplete-list a {
/*border: 1px solid var(--white);
padding: 5px 10px;
text-decoration: none;
color: var(--white);
border-radius: 15px;*/
white-space: nowrap;
}

.tags-autocomplete-list a:hover {
/*background-color: var(--blue-dk);*/
}

.menu-chosen-bg, .menu-unchosen-bg {
display: flex;
align-items: center;
}

.scenario-description td {
border-top: none;
}

.custom-accordion-header {
/*background-color: var(--main-bg-gray)!important;*/
color: white!important;
}

.custom-accordion-item {
/*border: 1px solid var(--green-dk)!important;*/
border: 1px solid var(--brand-green) !important;
/*background-color: var(--main-bg-gray);*/
/*color: white;*/
}

.scenario-mod-table {
/*color: white;*/
/*background-color: var(--main-bg-gray)*/
}

#tag-list {
list-style-type: none;
padding: 0;
border: 1px solid #ddd;
max-height: 150px;
overflow-y: auto;
}

#tag-list li {
padding: 10px;
background-color: #f9f9f9;
border-bottom: 1px solid #ddd;
}

#tag-list li:hover {
background-color: #e9e9e9;
cursor: pointer;
}

/* CUSTOM TABLES */
.my-custom-table {
    border-radius: 15px;
}

.my-custom-table th {
    font-weight: bold;
    font-size: 1.2em;
}

.my-custom-table td, .my-custom-table th {
    border: none;
    padding: 1rem;
    vertical-align: middle;
    text-align: left;
}

.my-custom-table tbody td {
    font-weight: 400;
}

.my-custom-table>:not(first-child) {
    border-top: none!important;
}

.my-custom-table-level-2 {
    border-radius: 15px;
}

.my-custom-table-level-2 th {
    font-weight: bold;
    font-size: 1.1em;
}

.my-custom-table-level-2 td, .my-custom-table-level-2 th {
    border: none;
    padding: .5rem;
    vertical-align: middle;
    text-align: left;
}

.max-width-column {
    max-width: 340px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* MODERATOR */
.sent-to-moderation { background-color: #ffc107; color: black; }
.assigned-to-moderator { background-color: #17a2b8; color: white; }
.scenario-in-moderation { background-color: #28a745; color: white; }
.scenario-rejected { background-color: #dc3545; color: white; }
.scenario-accepted { background-color: #c3e6cb; color: #155724; }

.status-badge {
padding: 0.3em 0.5em;
border-radius: 0.25rem;
font-weight: 500;
display: inline-block;
}

/* SMALL BUTTON FOR DOWNLOAD SECTION */
.btn-xs {
margin-top: -1px;
height: 21px;
display: inline-block;
padding: 0.35em 0.65em;
font-size: .75em;
font-weight: 700;
line-height: .75;
/*  color: #fff;*/
text-align: center;
white-space: nowrap;
vertical-align: baseline;
border-radius: 0.25rem;
}

/* HIDE DEFAULT LIST-VIEW LOGIN ALERTS */
form.login > div.alert.alert-block.alert-danger {
display: none !important;
}

/* PRICE LIST */
.accordion.pricelist-regulations {
	--bs-accordion-btn-bg: transparent;
	--bs-accordion-active-bg: transparent;
	--bs-accordion-btn-focus-box-shadow: none;
}
.accordion.pricelist-regulations .accordion-body p {
    line-height: 14pt;
    margin-bottom: 0;
}

.pricingTable {
text-align: center;
background: var(--bs-light);
/*margin: 0 -15px;*/
box-shadow: 0 0 5px #999999;
padding-bottom: 40px;
border-radius: 15px;
transform: scale(1);
transition: all .5s ease 0s
}

.pricingTable:hover {
transform: scale(1.05);
z-index: 1
}

.pricingTable .pricingTable-header {
padding: 40px 0;
background: var(--brand-gray);
border-radius: 15px 15px 50% 50%;
transition: all .5s ease 0s
}

.pricingTable .pricingTable-header i {
font-size: 50px;
margin-bottom: 10px;
transition: all .5s ease 0s
}

.pricingTable .heading-lines {
font-size: 35px;
transition: all .5s ease 0s
}

.pricingTable .heading-lines > span {
display: block;
font-size: 14px;
color: var(--bs-light);
}

.pricingTable:hover .month,
.pricingTable:hover .heading-lines,
.pricingTable:hover .pricingTable-header i {
color: #fff
}

.pricingTable .amount {
font-size: 40px;
line-height: 40px;
color: #ff9624;
margin-bottom: 20px;
}

.pricingTable .amount-desc {
font-size: 20px;
line-height: 20px;
padding-top: 17px;
}

.pricingTable .price {
font-size: 24px;
font-weight: bold;
}

.pricingTable .price-desc {
font-size: 14px;
}

.pricingTable .bottom-info {
font-size: 14px;
line-height: 16px;
height: 68px;
}

.pricingTable .pricingTable-signup input.btn {
/*display: inline-block;*/
/*font-size: 15px;*/
color: #fff;
/*padding: 10px 35px;*/
/*border-radius: 20px;*/
/*background: #ffa442;*/
text-transform: uppercase;
transition: all .3s ease 0s
}

.pricingTable .pricingTable-signup a.btn:hover {
box-shadow: 0 0 10px #666666
}

.pricingTable .pricingTable-signup input.btn:hover {
box-shadow: 0 0 10px #ffa442
}

.pricingTable .btn.disabled {
    border: 0;
}

.pricingTable .dropdown-menu .row {
    cursor: pointer;
}

/* box 1 : year */
.pricingTable.year .amount,
.pricingTable.year .heading-lines {
color: var(--brand-yellow);
}

.pricingTable.year .pricingTable-signup input.btn,
.pricingTable.year:hover .pricingTable-header {
background: var(--brand-yellow);
}

.pricingTable.year .pricingTable-signup input.btn:hover {
box-shadow: 0 0 10px var(--brand-yellow);
}

/* box 2 : month */
.pricingTable.month .amount,
.pricingTable.month .heading-lines {
color: var(--brand-blue);
}

.pricingTable.month .pricingTable-signup input.btn,
.pricingTable.month:hover .pricingTable-header {
background: var(--brand-blue);
}

.pricingTable.month .pricingTable-signup input.btn:hover {
box-shadow: 0 0 10px var(--brand-blue);
}

/* box 3 : package */
.pricingTable.package .amount,
.pricingTable.package .heading-lines {
color: var(--brand-green);
}

.pricingTable.package .pricingTable-signup input.btn,
.pricingTable.package:hover .pricingTable-header {
background: var(--brand-green);
}

.pricingTable.package .pricingTable-signup input.btn:hover {
box-shadow: 0 0 10px var(--brand-green);
}

/* boxes 1-3 : whitening on mouse over */
.pricingTable.package:hover .heading-lines,
.pricingTable.month:hover .heading-lines,
.pricingTable.year:hover .heading-lines {
color: #fff
}

@media screen and (max-width:990px) {
.pricingTable {
margin: 0 0 20px
}
}

/* DYMKI */
.dymek {
width: 178px;
height: 97px;
display: flex;
justify-content: center;
align-items: center;
padding-bottom: 20px;
line-height: 16px;
font-size: 14px;
color: white;
background-repeat: no-repeat;
}
.dymek-purple {
background-image: url('/static/images/brandbook/dymek_purple_178.png');
}
.dymek-blue {
background-image: url('/static/images/brandbook/dymek_blue_178.png');
}
.dymek-green {
background-image: url('/static/images/brandbook/dymek_green_178.png');
}
.dymek-yellow {
background-image: url('/static/images/brandbook/dymek_yellow_178.png');
}
.dymek-darkgray {
background-image: url('/static/images/brandbook/dymek_darkgray_178.png');
}
.dymek-lightgray {
background-image: url('/static/images/brandbook/dymek_lightgray_178.png');
color: black;
}
.dymek-value {
background-image: url('/static/images/brandbook/1x/Asset 14.png');
}

/* KATEGORIE SCENARIUSZY */
#category-filter {
background-color: transparent !important;
border: solid 1px var(--bs-secondary) !important;
border-radius: 5px;
padding-top: 0 !important;
padding-bottom: 0 !important;
margin-bottom: 0 !important;
}
#category-filter .dropdown-menu {
border-radius: 0 0 5px 5px;
border-left: 0;
border-right: 0;
}
#category-filter .dropdown-menu .dropdown-menu {
border-radius: 0;
border-left: 0;
border-right: 0;
border-bottom: 0 !important;
}
#category-filter .dropdown-menu li a {
cursor: pointer;
}
#chosen-category {
padding-left: 15px;
}
#category-cleaner {
border-color: transparent;
display: none;
}
#category-filter .nav-item .dropdown-toggle, #category-filter .dropdown-item .dropdown-toggle {
cursor: pointer;
text-align: center;
width: 30px !important;
height: 20px !important;
}

/* BOXY SCENARIUSZY */
.card-text {
    font-size: 12pt;
    line-height: 14pt;
}

/* WELCOME DASHBOARD TILE */
.welcome-tile-menu {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 10px;
    width: 250px;
    height: 250px;
    background-color: var(--gray-900);
    border-radius: 10px;
    font-weight: bold;
    text-decoration: none;
    text-align: center;
    color: var(--gray-700);
    font-size: 20px;
  }

  .tile-icon {
    font-size: 60px;
    display: inline;

  }

  .welcome-tile-menu:hover {
    background-color: var(--gray-800);
    transform: scale(1.01);
    color: var(--brand-white);
  }

/* DASHBOARD NAV */

.breadcrumb-item + .breadcrumb-item::before {
  content: "\f054";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: var(--gray-700);
}


/* ALERTS */
.alert {
opacity: 1;
/* color: var(--bs-light); */
padding: 10px 20px;
}
.alert.alert-info {
background-color: var(--brand-blue);
}
.alert.alert-success {
background-color: var(--brand-green);
}
.alert.alert-warning {
background-color: var(--brand-yellow);
}
.alert.alert-danger {
background-color: var(--brand-red);
}
.alert .btn-close {
padding: 15px;
}

.scenarios-table {
border-collapse: collapse; /* Zapewnia, że ramki komórek się nie dublują */
width: 100%; /* Opcjonalnie, rozciąga tabelę na całą szerokość dostępnego kontenera */
background-color: var(--gray-900);
--bs-table-bg: var(--gray-900);
}

.scenarios-table th, .scenarios-table td {
border: none; /* Usuwa wszelkie domyślne obramowania */
border-bottom: 1px solid #ddd; /* Dodaje subtelne poziome linie pomiędzy wierszami */
padding: 8px; /* Dodaje padding wokół tekstu w komórkach */
}

.scenarios-table tr:last-child td {
border-bottom: none; /* Usuwa linię na dole ostatniego wiersza */
}

/* DISABLED */
*.disabled, *[disabled] {
    opacity: .3 !important;
}

/* NO-OUTLINE */
.no-outline {
    border: none !important;
}

/* CHARTS (CURRENT SUB + REQUESTED CHANGE) */
.chart-container {height:330px}
@media (min-width: 576px) {
.chart-container {height:310px}
}
@media (min-width: 768px) {
.chart-container {height:290px}
}
@media (min-width: 992px) {
.chart-container {height:270px}
}
@media (min-width: 1200px) {
.chart-container {height:250px}
}
@media (min-width: 1400px) {
.chart-container {height:230px}
}

/* COOKIE BOTTOM MODAL */
#cookieModal .modal-dialog {
   position:fixed;
   top:auto;
   right:auto;
   left:auto;
   bottom: 0;
   margin: 0;
   width: 100% !important;
   max-width: 100% !important;
}
#cookieModal .modal-content {
   border-radius: 0 !important;
}

/* HEADRES */
h4, h5, h6 {
    font-weight: normal;
}

/* ACCORDIONS */
.accordion {
	--bs-accordion-btn-bg: transparent;
	--bs-accordion-active-bg: transparent;
	--bs-accordion-btn-focus-box-shadow: none;
}

.tile-container {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
    gap: 20px;
}

.tile {
    border: 1px solid var(--tile-border-color);
    padding: 15px;
    min-width: 200px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: var(--tile-background-color);
}

.tile:hover,
.tile.selected {
    background-color: var(--accent-color);
    border-color: var(--tile-hover-border-color);
}

.card-body {
    padding: 10px;
}

.card-text {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--card-text-color);
}


.form-group {
    margin-bottom: 15px;
}


.contact-promo-section {
    position: relative;
    background: url('../images/homepage/robot_kid.jpg') no-repeat center center/cover;
    color: #fff;
    padding: 80px 0;
}

.contact-promo-section .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
}

.contact-promo-section .container {
    position: relative;
    z-index: 1;
}

.contact-promo-section .text-section h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 20px;
}

.contact-promo-section .text-section p {
    font-size: 1.2rem;
    line-height: 1.5;
}

/* New styles for logo and button */
.contact-promo-section .logo-button-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.contact-promo-section .company-logo {
    z-index: 1;
    min-width: 150px;
    max-width: 250px;
    margin-left: 110px;
    margin-bottom: -15px; 
    transform: rotate(
        12deg
    );
}

.contact-promo-section .contact-button {
    font-size: 1.2rem;
    padding: 15px 30px;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .contact-promo-section {
        padding: 60px 0;
    }
    .contact-promo-section .text-section {
        text-align: center;
        margin-bottom: 30px;
    }
    .contact-promo-section .text-section h2 {
        font-size: 2rem;
    }
    .contact-promo-section .text-section p {
        font-size: 1rem;
    }
}