{% extends "account/base.html" %}

{% block title %}{{ block.super }} - Panel moderatora{% endblock %}

{% load static %}
{% load i18n %}

{% block extra_content_1 %}
<div class="container">
<div class="dashboard-element">
    <h2 class="dashboard-header">Szczegóły Scenariusza</h2>
    <div class="mb-4 ms-4 me-4">
        <h2>{{ scenario.name }}</h2>

        <p>ID: {{ scenario.sid }}</p>
        <p>Opis: {{ scenario.description }}</p>

        <form method="post">
            {% csrf_token %}
            {{ status_form.as_p }}
            <button type="submit" class="btn btn-primary">Zapisz zmiany</button>
        </form>

<!-- Formularz do dodawania tagów -->
<form method="post">
    {% csrf_token %}
    {{ tag_form.as_p }}
    <button type="submit" name="add_tag" class="btn btn-primary">Dodaj Tag</button>
</form>

<!-- Lista tagów z możliwością usunięcia -->
<div>
    <h3>Tagi:</h3>
    <ul>
        {% for tag in scenario.tags.all %}
            <li>
                {{ tag.name }}
                <form method="post" style="display:inline;">
                    {% csrf_token %}
                    <input type="hidden" name="remove_tag" value="{{ tag.name }}">
                    <button type="submit" class="btn btn-link">Usuń</button>
                </form>
            </li>
        {% endfor %}
    </ul>
</div>

        <input type="text" id="tag-input" oninput="searchTag()" placeholder="Wpisz tag...">
        <button id="add-tag-btn" onclick="addTag()">Dodaj Tag</button>
        <ul id="tag-list" style="display:none;"></ul>

    </div>
</div>
</div>

<script>
    function searchTag() {
        var input = document.getElementById('tag-input');
        var filter = input.value.toLowerCase();
        var list = document.getElementById('tag-list');
        var xhr = new XMLHttpRequest();
    
        xhr.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                var tags = JSON.parse(this.responseText);
                list.innerHTML = '';
                tags.forEach(function(tag) {
                    if(tag.toLowerCase().indexOf(filter) > -1){
                        var listItem = document.createElement('li');
                        listItem.innerHTML = tag;
                        listItem.onclick = function() {
                            input.value = this.innerHTML;
                            list.style.display = 'none';
                        };
                        list.appendChild(listItem);
                    }
                });
                list.style.display = 'block';
            }
        };
    
        xhr.open('GET', '/profiles/tag_autocomplete?term=' + filter, true);
        xhr.send();
    }
    
    document.addEventListener('click', function (e) {
        var list = document.getElementById('tag-list');
        var input = document.getElementById('tag-input');
        if (e.target !== input) {
            list.style.display = 'none';
        }
    });
</script>


{% endblock %}

