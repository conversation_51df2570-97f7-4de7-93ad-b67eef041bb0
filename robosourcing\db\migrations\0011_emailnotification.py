# Generated by Django 4.1.9 on 2024-06-27 14:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('db', '0010_robot_offline_credits'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sending_time', models.DateTimeField(auto_now_add=True)),
                ('message_id', models.IntegerField(choices=[(0, 'Thank you for registering an account'), (1, 'Welcome to our web service'), (2, 'Thank you for making your purchases'), (3, 'Your subscription event completed successfully'), (4, 'Your subscription event has failed'), (5, 'Your payment card expires soon'), (6, 'Your payment card has expired')])),
                ('status_id', models.IntegerField(choices=[(0, 'Sending failed'), (1, 'Succesfully sent')])),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
