#UML generator
py manage.py graph_models --pydot -a -g -o my_project_visualized.png

#FIXTURES:
python manage.py dumpdata --format=json auth account socialaccount profiles db > _fixtures\{file_name}.json 
python manage.py loaddata .\_fixtures\{file_name}.json   

#DB MIGRATIONS
python manage.py makemigrations
python manage.py migrate

#VIRTUAL ENV 
python -m venv .venv

#REQUIREMENTS INSTALL 
pip install -r requirements.txt

#RUN PROJECT 
#navigate to {project_folder}/robosourcing/
python manage.py runserver