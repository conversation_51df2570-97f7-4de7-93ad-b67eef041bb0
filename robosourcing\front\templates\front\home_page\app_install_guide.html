{% extends 'main.html' %}
{% load i18n %}
{% load static %}

{% block head_title %}{% trans "App install guide" %}{% endblock %}

{% block title %}
    {{ block.super }} - {% trans "App install guide" %}
{% endblock title %}

{% block scripts %}
{% endblock %}

{% block navbar_buttons %}
    {{ block.super }}
{% endblock %}

{% block content %}
<div class="container mt-3">
    <div class="row">
        <div class="col-12 mb-5">
            <h1>Instalacja <PERSON></h1>
        </div>
        <div class="col-12">
            <p>1. Wybierz język używany podczas instalacji. Program Botie jest dostępny w języku polskim i angielskim. Natomiast sam instalator ma wbudowanych 25 języków, ale jest to wybór tylko na czas procesu instalacji programu. W przypadku wyboru nieodpowiedniego języka najlepiej anulować lub zamkn<PERSON>ć instalację i uruchomić instalatora ponownie.</p>
            <figure class="text-center">
                <img src="{% static 'images/academy/image-20240708-160401.png' %}">
                <figcaption class="figure-caption mt-2 mb-5">Ekran 2.1. Wybór języka procesu instalacji</figcaption>
            </figure>
            <p>2. Po wyborze języka pokaże się okno informacyjne o nowościach w bieżącej wersji, które możesz przejść klikając <strong>Dalej</strong>.</p>
            <figure class="text-center">
                <img src="{% static 'images/academy/image-20240708-160836.png' %}">
                <figcaption class="figure-caption mt-2 mb-5">Ekran 2.2. Okno z informacją o instalowanej wersji</figcaption>
            </figure>
            <p>3. Następnie należy wybrać tryb instalacji. Domyślnie program instaluje się tak, że jest dostępny dla wszystkich użytkowników komputera. Jeśli nikt inny z niego nie korzysta lub współdzielisz go z kimś ale ta osoba nie ma mieć dostepu do aplikacji zmień ten wybór. Proponowana jest też ścieżka domyślna (inna zależnie od dokonanego wyboru), którą można sobie w razie potrzeby dostosować. <strong>Zalecamy zostawić domyślną.</strong></p>
            <figure class="text-center">
                <img src="{% static 'images/academy/image-20240712-095725.png' %}">
                <figcaption class="figure-caption mt-2 mb-5">Ekran 2.3. Wybór trybu instalacji</figcaption>
            </figure>
            <figure class="text-center">
                <img src="{% static 'images/academy/image-20240712-095850.png' %}">
                <figcaption class="figure-caption mt-2 mb-5">Ekran 2.4. Określenie ścieżki instalacji</figcaption>
            </figure>
            <p>4. W następnym oknie Zadań dodatkowych do wyboru jest możliwość utworzenia skrótu do Botie na pulpicie użytkownika. Kliknij <strong>Dalej</strong>.</p>
            <figure class="text-center">
                <img src="{% static 'images/academy/image-20240708-164115.png' %}">
                <figcaption class="figure-caption mt-2 mb-5">Ekran 2.5. Okno zadań dodatkowych z opcją utworzenia skrótu na pulpicie</figcaption>
            </figure>
            <p>5. Ostatnim oknem przed rozpoczęciem instalacji jest okno z podsumowaniem. Kliknij <strong>Instaluj</strong> by rozpocząć instalację według ustawień.</p>
            <figure class="text-center">
                <img src="{% static 'images/academy/image-20240708-164326.png' %}">
                <figcaption class="figure-caption mt-2 mb-5">Ekran 2.6. Okno podsumowania przed instalacją</figcaption>
            </figure>
            <p>6. Po wykonaniu instalacji pojawi się okno podsumowania, w którym domyślnie ustawiona jest opcja uruchomienia aplikacji Botie. Kliknij <strong>Zakończ</strong>.</p>
            <figure class="text-center">
                <img src="{% static 'images/academy/image-20240708-164716.png' %}">
                <figcaption class="figure-caption mt-2 mb-5">Ekran 2.7. Zakończenie instalacji</figcaption>
            </figure>
        </div>
    </div>
</div>
<div class="mt-5 mb-5 divider"></div>
{% endblock %}