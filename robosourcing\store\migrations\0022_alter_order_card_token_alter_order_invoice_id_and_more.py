# Generated by Django 4.1.9 on 2024-08-05 16:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0021_invoice_order_invoice_id_subsequentorder_invoice_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='card_token',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='invoice_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.invoice'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='subsequentorder',
            name='card_token',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='subsequentorder',
            name='invoice_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.invoice'),
        ),
    ]
