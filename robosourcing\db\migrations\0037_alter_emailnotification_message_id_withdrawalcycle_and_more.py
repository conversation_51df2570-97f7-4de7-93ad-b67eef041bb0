# Generated by Django 4.2.13 on 2024-12-12 17:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('db', '0036_alter_emailnotification_message_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emailnotification',
            name='message_id',
            field=models.IntegerField(choices=[(0, 'Thank you for registering an account'), (1, 'Welcome to our web service'), (2, 'Thank you for making your purchases'), (3, 'Your subscription event completed successfully'), (4, 'Your subscription event has failed'), (5, 'Your payment card expires soon'), (6, 'Your payment card has expired'), (7, 'Contact form submitted'), (8, 'WITHDRAWAL_REQUEST_SUBMITTED'), (9, 'MONTHLY_WITHDRAWAL_SUMMARY')]),
        ),
        migrations.CreateModel(
            name='WithdrawalCycle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('total_withdrawn', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_cycles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.AddField(
            model_name='affiliatewithdrawalrequest',
            name='cycle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='withdrawals', to='db.withdrawalcycle'),
        ),
    ]
