{% load socialaccount %}
{% load static %}

{% get_providers as socialaccount_providers %}
<div class="row">
{% for provider in socialaccount_providers %}
    {% if provider.id == "openid" %}
        {% for brand in provider.get_brands %}
    <div>
        <a title="{{brand.name}}" class="socialaccount_provider {{provider.id}} {{brand.id}}" href="{% provider_login_url provider.id openid=brand.openid_url process=process %}">{{brand.name}}</a>
    </div>
        {% endfor %}
    {% endif %}
    <div class="col-3 col-md-2">
        <a title="{{provider.name}}" class="socialaccount_provider {{provider.id}}" href="{% provider_login_url provider.id process=process scope=scope auth_params=auth_params %}">
            <img height="40px" class="socialaccount_images" src="{% with 'images/'|add:provider.name|add:'.svg' as image %}{% static image %}{% endwith %}" alt="{{ provider.name }}"/>
        </a>
    </div>
{% endfor %}
</div>