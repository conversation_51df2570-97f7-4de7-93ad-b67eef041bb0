from profiles.models import UserProfile
from django.contrib.auth import get_user_model
from rest_framework import views, permissions, status, serializers
from rest_framework.response import Response

from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils.translation import gettext_lazy as _

from db.models import Robot
from django.contrib.auth import get_user_model


User = get_user_model()

        
class RoboTokenObtainSerializer(serializers.Serializer):
    rid = serializers.UUIDField()


class RoboTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, rid):
        print(cls)
        print(f'xxxxxxxxxxx{rid}xxxxxxxxxxxx')
        robot = Robot.objects.get(rid=rid)
        print(robot)
        user = robot.owner
        print(user)
        token = super().get_token(user)

        # Add custom claims
        full_name = f"{user.first_name} {user.last_name}".strip()
        token['name'] = full_name if full_name else user.username
        # ...

        return token

class RoboObtainTokenView(views.APIView):
    permission_classes = [permissions.AllowAny]
    serializer_class = RoboTokenObtainSerializer

    def get_tokens_for_user(self, user):
        refresh = RefreshToken.for_user(user)

        groups = user.groups.values_list('name', flat=True)
        role = groups[0] if groups else 'default_role'

        def _to_seconds(timestr):
            seconds= 0
            for part in timestr.split(':'):
                seconds= seconds*60 + int(part, 10)
            return seconds

        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'refresh_lifetime': str(refresh.lifetime.total_seconds()),
            'access_lifetime': str(refresh.access_token.lifetime.total_seconds()),
            'user_id': str(user.id),
            'profile_first_name': str(user.first_name),
            'profile_last_name': str(user.last_name),
            'role': role,
        }

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        rid = serializer.validated_data.get('rid')
        robot = Robot.objects.get(rid=rid)
        user = robot.owner


        if user is None:
            return Response({'message': 'Invalid credentials'}, status=status.HTTP_400_BAD_REQUEST)

        # Generate the JWT token
        return Response(self.get_tokens_for_user(user))