from django.db import models


def build_map_path(instance, filename):
    # file will be uploaded to MEDIA_ROOT/<app>/<app_version>/<filename>
    return "robo_maps/{0}/{1}/{2}".format(instance.app, instance.app_version, filename)


class RoboMap(models.Model):
    class MapStatus(models.TextChoices):
        PARTIAL = 'PARTIAL', 'Partial'  # metadata were uploaded but no map file yet
        ACTIVE = 'ACTIVE', 'Active'  # map is complete with metadata and file
        EXPIRED = 'EXPIRED', 'Expired'  # map has expired, it shouldn't be returned from api
        EXPIRING = 'EXPIRING', 'Expiring'  # map will expire soon - do not use it in scenarios
        DELETED = 'DELETED', 'Deleted'  # map was deleted. Do not return it from the api

    created_at = models.DateTimeField(auto_now_add=True, auto_now=False)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True)
    map_version = models.CharField(max_length=100, null=False, blank=False)
    map_file = models.FileField(upload_to=build_map_path, null=True)
    map_size = models.IntegerField(default=0)
    app = models.CharField(max_length=100, null=False, blank=False)
    app_version = models.CharField(max_length=100, null=False, blank=False)
    generator = models.CharField(max_length=100, null=False, blank=False)
    status = models.CharField(max_length=20, choices=MapStatus.choices, default=MapStatus.PARTIAL, null=False, blank=False)


class MapUsage(models.Model):
    class UsageResult(models.TextChoices):
        SUCCESS = 'SUCCESS', 'Success'
        FAIL = 'FAIL', 'Fail'

    reported_at = models.DateTimeField(auto_now_add=True, auto_now=False)
    status = models.CharField(max_length=20, choices=UsageResult.choices, null=False, blank=False)
    app = models.CharField(max_length=100, null=False, blank=False)
    app_version = models.CharField(max_length=100, null=False, blank=False)
    os_version = models.CharField(max_length=100, null=False, blank=False)
    screen_scale = models.CharField(max_length=10, null=False, blank=False)
    robo_map = models.ForeignKey(RoboMap, on_delete=models.CASCADE, null=False)
