{% extends "front/dashboard/base.html" %}

{% load i18n %}
{% load crispy_forms_filters %}

{% block title %}{{ block.super }} - {% trans "Robots" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item site-name active">{% trans "Robots" %}</li>
        </ol>
    </nav>

    <div class="row g-3">
        <div class="col-auto d-flex flex-column">
            <a href="{% url 'download' %}" class="welcome-tile-menu" target="_blank">
                <i class="fa fa-download tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Download New Robot" %}
            </a>
        </div>
        <div class="col-auto d-flex flex-column">
            <a class="welcome-tile-menu" type="button" id="generate-key-btn">
                <i class="fa fa-link tile-icon" aria-hidden="true"></i>
                <br>
                {% trans "Link Robot" %}
            </a>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col">
            <div class="dashboard-element">
                <h3 class="db-section-title">{% blocktrans %}Your Robots:{% endblocktrans %}</h3>
                <hr>
                <div class="table-responsive">
                    <table class="table my-custom-table">
                        <thead class="">
                            <th>{% trans "Name" %}</th>
                            <th>{% trans "Create Time" %}</th>
                            <th>{% trans "Last Active" %}</th>
                            <th>{% trans "Kredyty Offline" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </thead>
                        <!-- Data from backend -->
                        {% for robot in roboty %}
                        <tr class="">
                            <td>{{ robot.name }}</td>
                            <td>{{ robot.create_time }}</td>
                            <td>{{ robot.last_activ_time }}</td>
                            <td>{{ robot.cached_credits }}</td>
                            <td>
                                <a href="{% url 'robot_scenarios_view' %}?tab=robot&robot_id={{ robot.rid }}" class="btn btn-sm btn-secondary">{% trans "Manage Scenarios" %}</a>
                                <button class="btn btn-sm btn-secondary" data-bs-toggle="modal" data-bs-target="#blockCreditsModal" data-robot-id="{{ robot.rid }}">{% trans "Manage Credits" %}</button>
                                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{{ robot.rid }}" aria-expanded="false" aria-controls="collapse-{{ robot.rid }}">
                                    {% trans "Show Details" %}
                                </button>
                            </td>
                        </tr>
                        <tr class="collapse" id="collapse-{{ robot.rid }}">
                            <td colspan="5">
                                <div class="robot-details" id="robot-details-{{ robot.rid }}">
                                    <!-- Dynamic content will be loaded here via JavaScript -->
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock extra_content_1 %}

{% block extra_content_2 %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const blockCreditsModal = document.getElementById('blockCreditsModal');
    blockCreditsModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const robotId = button.getAttribute('data-robot-id');
        const form = blockCreditsModal.querySelector('form');
        form.querySelector('input[name="robot"]').value = robotId;

        // Set the available and offline credits
        const robotCredits = JSON.parse('{{ robot_credits|escapejs }}');
        const availableCredits = robotCredits[robotId];
        form.querySelector('#robot-blocked-credits').innerText = availableCredits;

        const totalCredits = document.getElementById('total-credits').innerText;
        console.log(`Total Credits: ${totalCredits}`);
        console.log(`Robot ID: ${robotId}, Blocked Credits: ${availableCredits}`);
    });

    const robotyData = JSON.parse('{{ roboty_data|escapejs }}');
    console.log('Robot data:', robotyData);

    // Sort robotyData by name
    robotyData.sort((a, b) => a.name.localeCompare(b.name));

    robotyData.forEach(robot => {
        const detailsContainer = document.getElementById(`robot-details-${robot.id}`);
        if (!detailsContainer) {
            console.error('Details container not found for RID:', robot.id);
            return;
        }
        console.log('Updating details container:', detailsContainer);
        detailsContainer.innerHTML = `
            <h4>Robot ${robot.name}</h4>
            <div class="row">
                <div class="col-auto">
                    <label>{% trans "Chart period:" %}</label>
                    <label><input type="radio" name="chart-period-${robot.id}" value="day" checked> {% trans "day" %}</label>
                    <label><input type="radio" name="chart-period-${robot.id}" value="week"> {% trans "week" %}</label>
                    <label><input type="radio" name="chart-period-${robot.id}" value="month"> {% trans "month" %}</label>
                </div>
                <div class="">
                <canvas id="creditUsageChart-${robot.id}"></canvas>
                </div>
            </div>
        `;

        const ctx = document.getElementById(`creditUsageChart-${robot.id}`).getContext('2d');
        let chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1', '2', '3', '4', '5', '6', '7'],
                datasets: [{
                    label: '{% trans "Credits Used" %}',
                    data: robot.credit_usage.day,
                    borderColor: 'rgba(183, 47, 47, 1)', 
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        document.querySelectorAll(`input[name="chart-period-${robot.id}"]`).forEach(radio => {
            radio.addEventListener('change', function() {
                const period = this.value;
                chart.data.datasets[0].data = robot.credit_usage[period];
                chart.update();
            });
        });
    });
});

</script>
{% endblock %}
