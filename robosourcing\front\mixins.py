# robosourcing/mixins.py


class ResetPermissionsMixin:

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class UserDisplayMixin:
    """
    Mixin to display user related information in admin panels.
    """

    def user_display(self, obj):
        """
        Displays the user's full name if available, otherwise username.
        """
        user = None
        if hasattr(obj, 'user'):
            user = obj.user
        elif hasattr(obj, 'user_id'):
            user = obj.user_id

        if not user:
            return "No user associated"

        full_name = f'{user.first_name} {user.last_name}'.strip()
        return full_name if full_name else user.username

    user_display.short_description = 'User account'


class RidDisplayMixin:

    def rid_display(self, obj):
        return obj.rid.name if obj.rid else '-'

    rid_display.short_description = 'Robot name'


class SidDisplayMixin:

    def sid_display(self, obj):
        return obj.sid.name if obj.sid else '-'

    sid_display.short_description = 'Scenario name'
