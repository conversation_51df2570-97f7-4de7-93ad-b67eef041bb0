from django.core.exceptions import ValidationError
from rest_framework import serializers

from .models import RoboMap, MapUsage

MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB


class RoboMapSerializer(serializers.ModelSerializer):
    class Meta:
        model = RoboMap
        fields = ['created_at', 'map_version', 'app', 'app_version', 'map_size', 'generator', 'status']
        read_only_fields = ['created_at', 'map_version', 'map_size', 'status']


class RoboMapFileSerializer(serializers.ModelSerializer):
    map_file = serializers.FileField(allow_empty_file=False)
    map_size = serializers.IntegerField
    status = serializers.Char<PERSON>ield

    def validate_map_file(self, value):
        filesize = value.size

        if filesize > MAX_FILE_SIZE:
            raise ValidationError("The maximum file size that can be uploaded is 10MB")

        return value

    def to_representation(self, instance):
        serializer = RoboMapSerializer(instance)
        return serializer.data

    class Meta:
        model = RoboMap
        fields = ['created_at', 'map_version', 'app', 'app_version', 'map_file', 'map_size', 'generator', 'status']


class RoboMapUsageSerializer(serializers.ModelSerializer):
    class Meta:
        model = MapUsage
        fields = ['status', 'app_version', 'os_version', 'screen_scale']
