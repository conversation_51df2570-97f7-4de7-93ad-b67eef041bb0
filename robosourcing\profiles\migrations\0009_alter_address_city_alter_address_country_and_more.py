# Generated by Django 4.2.13 on 2024-08-18 14:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('profiles', '0008_alter_contact_phone_number'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='address',
            name='city',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='address',
            name='country',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='address',
            name='home_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='address',
            name='postal_code',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='address',
            name='street',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='address',
            name='street_number',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=50, null=True),
        ),
    ]
