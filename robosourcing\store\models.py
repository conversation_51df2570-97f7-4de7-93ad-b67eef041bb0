# from datetime import timezone

from dateutil.relativedelta import relativedelta
from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
# from django.utils import timezone
# from robosourcing.utils import TimeManager
from django.conf import settings

from django.contrib.contenttypes.fields import GenericRelation
from seo.models import ObjectMeta


# Create your models here.
class Product(models.Model):

    objects = models.Manager()

    class ProductType(models.TextChoices):
        PACKAGE = "PAC", _("PACKAGE")
        MONTH_SUB = "SUB", _("MONTHLY SUBSCRIPTION")
        YEAR_SUB = "YSU", _("ANNUAL SUBSCRIPTION")

    name = models.CharField(max_length=50, null=False)
    description = models.CharField(max_length=250, null=False)
    value = models.PositiveIntegerField(null=False)
    price = models.DecimalField(max_digits=99, decimal_places=2, null=False)
    type = models.CharField(choices=ProductType.choices, default=ProductType.PACKAGE, max_length=3, null=False)
    active = models.BooleanField(default=False, null=False)
    seo = GenericRelation(ObjectMeta)


class Basket(models.Model):

    objects = models.Manager()

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(null=False, default=1)

    class Meta:
        ordering = ['user', 'product', 'quantity']
        unique_together = ['user', 'product']

    def purchase(self):
        pass


class Invoice(models.Model):

    objects = models.Manager()

    class AccountingSystem(models.TextChoices):
        SYSTIM = "SYS", _("Systim")

    issue_time = models.DateTimeField(auto_now_add=True)
    # issue_time = models.DateTimeField()
    number = models.CharField(max_length=50, null=False)
    system = models.CharField(choices=AccountingSystem.choices, default=AccountingSystem.SYSTIM, max_length=3, null=False)
    ext_id = models.PositiveIntegerField(null=False)
    downloaded = models.BooleanField(default=False)


class Order(models.Model):

    objects = models.Manager()

    OPERATOR_LIST = [
        ('PAYU', 'PayU GPO'),
        ('VIVA', 'Viva Payments'),
    ]

    create_time = models.DateTimeField(auto_now_add=True)
    # create_time = models.DateTimeField()
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=99, decimal_places=2, null=False, default=0)
    tax = models.DecimalField(max_digits=99, decimal_places=2, null=False, default=0)
    price_incl_tax = models.DecimalField(max_digits=99, decimal_places=2, null=False, default=0)
    operator = models.CharField(choices=OPERATOR_LIST, max_length=4, null=True)
    order_id = models.CharField(primary_key=True, max_length=50)
    payment_method = models.CharField(max_length=20, null=True)  # PBL, CARD_TOKEN, INSTALLMENTS
    payment_id = models.CharField(max_length=50, null=True)
    payment_details = models.CharField(max_length=50, null=True)
    description = models.CharField(max_length=250, null=False)
    card_token = models.CharField(max_length=50, null=True, blank=True)  # not used yet
    status = models.CharField(max_length=30, null=True)  # PENDING, WAITING_FOR_CONFIRMATION, COMPLETED, CANCELED
    invoice_id = models.ForeignKey(Invoice, null=True, on_delete=models.SET_NULL, blank=True)
    update_time = models.DateTimeField(auto_now=True)
    # update_time = models.DateTimeField()

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
    #     print(f'init {self.create_time}')
    #     if not self.create_time:
    #         tm = TimeManager()
    #         now = tm.current_time()
    #         self.create_time = now
    #         self.update_time = now

    # def save(self, *args, **kwargs):
    #     tm = TimeManager()
    #     now = tm.current_time()
    #     print(f'save {self.create_time}')
    #     if not self.create_time:
    #         self.create_time = now
    #     self.update_time = now
    #     super().save(*args, **kwargs)


class OrderItem(models.Model):

    objects = models.Manager()

    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(null=False, default=1)


class SubsequentOrder(models.Model):

    objects = models.Manager()

    reference_order = models.ForeignKey(Order, on_delete=models.CASCADE)
    create_time = models.DateTimeField(auto_now_add=True)
    # create_time = models.DateTimeField()
    order_id = models.CharField(primary_key=True, max_length=50)
    card_token = models.CharField(max_length=50, null=True, blank=True)  # not used yet
    price = models.DecimalField(max_digits=99, decimal_places=2, null=False, default=0)
    tax = models.DecimalField(max_digits=99, decimal_places=2, null=False, default=0)
    price_incl_tax = models.DecimalField(max_digits=99, decimal_places=2, null=False, default=0)
    description = models.CharField(max_length=250, null=False)
    status = models.CharField(max_length=30, null=True)  # PENDING, WAITING_FOR_CONFIRMATION, COMPLETED, CANCELED
    invoice_id = models.ForeignKey(Invoice, null=True, on_delete=models.SET_NULL, blank=True)
    update_time = models.DateTimeField(auto_now=True)
    # update_time = models.DateTimeField()


class Schedule(models.Model):

    objects = models.Manager()

    active = models.BooleanField(default=True)
    create_time = models.DateTimeField(auto_now_add=True)
    # create_time = models.DateTimeField()
    last_update = models.DateTimeField(auto_now=False, null=True)
    # last_update = models.DateTimeField(null=True)

    class Meta:
        abstract = True


class RecurringPayment(Schedule):

    def process_payment(self):

        if settings.SCHEDULER_TEST_MODE:
            if settings.SHORT_PERIOD == 'hour':
                short_delta = relativedelta(hours=1)
            elif settings.SHORT_PERIOD == 'day':
                short_delta = relativedelta(days=1)
            if settings.LONG_PERIOD == 'day':
                long_delta = relativedelta(days=1)
            elif settings.LONG_PERIOD == 'week':
                long_delta = relativedelta(weeks=1)
        else:
            short_delta = relativedelta(months=1)
            long_delta = relativedelta(years=1)

        if not self.last_payment_date or self.last_payment_date != self.next_payment_date:
            # set new payment date
            if self.annual_payment:
                self.next_payment_date = self.next_payment_date + long_delta
            else:
                self.next_payment_date = self.next_payment_date + short_delta
        else:
            # cancel next payment
            self.next_payment_date = None
            self.last_payment_date = None
        # reset pending payment
        self.pending_payment = None
        # update last update date (with the date the payment was launched + period)
        if self.annual_payment:
            self.last_update += long_delta
        else:
            self.last_update += short_delta
        self.save()

    objects = models.Manager()

    user = models.OneToOneField(User, on_delete=models.CASCADE, editable=False, primary_key=True)
    reference_order = models.ForeignKey(Order, on_delete=models.CASCADE)
    pending_payment = models.ForeignKey(SubsequentOrder, on_delete=models.CASCADE, default=None, null=True, blank=True)
    payment_month_day = models.PositiveIntegerField(null=False)
    annual_payment = models.BooleanField(default=False, null=False)
    next_payment_date = models.DateTimeField(null=True, auto_now=False, auto_now_add=False)
    next_payment_date = models.DateTimeField(null=True, auto_now=False, auto_now_add=False)
    next_payment_amount = models.FloatField(null=False)
    last_payment_date = models.DateTimeField(null=True, auto_now=False, auto_now_add=False, blank=True)


class SubscriptionEvent(models.Model):

    objects = models.Manager()

    class Status(models.TextChoices):
        PENDING = "PENDING", _("PENDING")
        CANCELED = "CANCELED", _("CANCELED")
        COMPLETED = "COMPLETED", _("COMPLETED")

    class EventType(models.TextChoices):
        SUBSCRIBE = "SUB", _("SUBSCRIBE")
        CHANGE = "CHG", _("CHANGE")
        UNSUBSCRIBE = "UNS", _("UNSUBSCRIBE")

    class ChangeMode(models.TextChoices):
        IMMEDIATE = "IME", _("IMMEDIATELY")
        PERIOD_END = "END", _("WITH PERIOD END")

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    event_time = models.DateTimeField(auto_now_add=True)
    # event_time = models.DateTimeField()
    event_type = models.CharField(choices=EventType.choices, default=EventType.SUBSCRIBE, max_length=3, null=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, null=True, default=None, blank=True)
    change_mode = models.CharField(choices=ChangeMode.choices, max_length=3, null=True)
    amount = models.FloatField(null=False, default=0)
    payment_id = models.CharField(max_length=50, null=True, default=None, blank=True)
    status = models.CharField(choices=Status.choices, default=Status.PENDING, max_length=9, null=False)
    update_time = models.DateTimeField(auto_now=False, null=True)

