from django.test import TestCase, Client
from django.urls import reverse
from django.core import mail
from front.models import NewsletterSubscription
from front.forms import NewsletterForm
import json


class NewsletterTestCase(TestCase):
    def setUp(self):
        self.client = Client()
        self.subscribe_url = reverse('newsletter_subscribe')
        self.test_email = '<EMAIL>'

    def test_newsletter_form_valid_data(self):
        """Test formularza z poprawnymi danymi"""
        form_data = {
            'email': self.test_email,
            'captcha': '8',
            'marketing_consent': True,
            'data_processing_consent': True,
        }
        form = NewsletterForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_newsletter_form_invalid_captcha(self):
        """Test formularza z niepoprawną captcha"""
        form_data = {
            'email': self.test_email,
            'captcha': '7',  # Niepoprawna odpowiedź
            'marketing_consent': True,
            'data_processing_consent': True,
        }
        form = NewsletterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('captcha', form.errors)

    def test_newsletter_form_missing_consents(self):
        """Test formularza bez zgód"""
        form_data = {
            'email': self.test_email,
            'captcha': '8',
            'marketing_consent': False,
            'data_processing_consent': False,
        }
        form = NewsletterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('marketing_consent', form.errors)
        self.assertIn('data_processing_consent', form.errors)

    def test_newsletter_subscription_creation(self):
        """Test tworzenia subskrypcji"""
        form_data = {
            'email': self.test_email,
            'captcha': '8',
            'marketing_consent': True,
            'data_processing_consent': True,
        }
        
        response = self.client.post(self.subscribe_url, form_data)
        
        # Sprawdź czy subskrypcja została utworzona
        self.assertTrue(NewsletterSubscription.objects.filter(email=self.test_email).exists())
        
        subscription = NewsletterSubscription.objects.get(email=self.test_email)
        self.assertFalse(subscription.is_confirmed)  # Domyślnie niepotwierdzony
        self.assertTrue(subscription.confirmation_token)  # Token został wygenerowany
        self.assertTrue(subscription.marketing_consent)
        self.assertTrue(subscription.data_processing_consent)

    def test_newsletter_ajax_subscription(self):
        """Test subskrypcji przez AJAX"""
        form_data = {
            'email': self.test_email,
            'captcha': '8',
            'marketing_consent': True,
            'data_processing_consent': True,
        }
        
        response = self.client.post(
            self.subscribe_url, 
            form_data,
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('Dziękujemy', data['message'])

    def test_newsletter_duplicate_email(self):
        """Test duplikatu email"""
        # Utwórz pierwszą subskrypcję
        NewsletterSubscription.objects.create(
            email=self.test_email,
            marketing_consent=True,
            data_processing_consent=True
        )
        
        # Spróbuj utworzyć drugą z tym samym emailem
        form_data = {
            'email': self.test_email,
            'captcha': '8',
            'marketing_consent': True,
            'data_processing_consent': True,
        }
        
        form = NewsletterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)

    def test_newsletter_confirmation(self):
        """Test potwierdzania subskrypcji"""
        # Utwórz subskrypcję
        subscription = NewsletterSubscription.objects.create(
            email=self.test_email,
            marketing_consent=True,
            data_processing_consent=True
        )
        
        # Potwierdź subskrypcję
        confirm_url = reverse('newsletter_confirm', args=[subscription.confirmation_token])
        response = self.client.get(confirm_url)
        
        # Sprawdź przekierowanie
        self.assertEqual(response.status_code, 302)
        
        # Sprawdź czy subskrypcja została potwierdzona
        subscription.refresh_from_db()
        self.assertTrue(subscription.is_confirmed)
        self.assertIsNotNone(subscription.confirmed_at)

    def test_newsletter_unsubscribe(self):
        """Test wypisywania się z newslettera"""
        # Utwórz subskrypcję
        subscription = NewsletterSubscription.objects.create(
            email=self.test_email,
            marketing_consent=True,
            data_processing_consent=True,
            is_confirmed=True
        )
        
        # Wypisz się
        unsubscribe_url = reverse('newsletter_unsubscribe', args=[subscription.confirmation_token])
        response = self.client.get(unsubscribe_url)
        
        # Sprawdź przekierowanie
        self.assertEqual(response.status_code, 302)
        
        # Sprawdź czy subskrypcja została usunięta
        self.assertFalse(NewsletterSubscription.objects.filter(email=self.test_email).exists())

    def test_newsletter_email_sending(self):
        """Test wysyłania emaili"""
        form_data = {
            'email': self.test_email,
            'captcha': '8',
            'marketing_consent': True,
            'data_processing_consent': True,
        }
        
        # Wyczyść skrzynkę testową
        mail.outbox = []
        
        response = self.client.post(self.subscribe_url, form_data)
        
        # Sprawdź czy email został wysłany
        self.assertEqual(len(mail.outbox), 1)
        
        sent_email = mail.outbox[0]
        self.assertIn(self.test_email, sent_email.to)
        self.assertIn('Potwierdź', sent_email.subject)

    def test_newsletter_pages_accessibility(self):
        """Test dostępności stron newslettera"""
        # Strona główna newslettera
        response = self.client.get(reverse('newsletter_status'))
        self.assertEqual(response.status_code, 200)
        
        # Strona subskrypcji
        response = self.client.get(reverse('newsletter_subscribe'))
        self.assertEqual(response.status_code, 200)

    def test_newsletter_token_generation(self):
        """Test generowania tokenów"""
        subscription1 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            marketing_consent=True,
            data_processing_consent=True
        )
        
        subscription2 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            marketing_consent=True,
            data_processing_consent=True
        )
        
        # Tokeny powinny być różne
        self.assertNotEqual(subscription1.confirmation_token, subscription2.confirmation_token)
        
        # Tokeny powinny mieć odpowiednią długość
        self.assertGreater(len(subscription1.confirmation_token), 20)
        self.assertGreater(len(subscription2.confirmation_token), 20)
