# Generated by Django 4.2.13 on 2024-11-06 22:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('db', '0026_alter_emailnotification_message_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='KeywordsHash',
            fields=[
                ('keywords_hash', models.CharField(max_length=32, primary_key=True, serialize=False)),
            ],
        ),
        migrations.AddField(
            model_name='scenario',
            name='apps',
            field=models.ManyToManyField(blank=True, to='db.program'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='categories',
            field=models.ManyToManyField(blank=True, to='db.category'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='origin_id',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scensrios_ancestor', to='db.scenario'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='tags',
            field=models.ManyToManyField(blank=True, to='db.tag'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='keywords_hashes',
            field=models.ManyToManyField(blank=True, to='db.keywordshash'),
        ),
        migrations.AlterField(
            model_name='file',
            name='keywords_hash',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='db.keywordshash'),
        ),
    ]
