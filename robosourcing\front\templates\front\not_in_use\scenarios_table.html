{% for scenario in scenarios %}
<tr class="">
    <td>{{ scenario.name }}</td>
    <td>{{ scenario.scenario_version }}</td>
    <td>{{ scenario.author }}</td>
    <td>{{ scenario.created_at|date:"Y-m-d H:i" }}</td>
    <td>{{ scenario.updated_at|date:"Y-m-d H:i" }}</td>
    <td>{{ scenario.get_available_display }}</td>
    <td>{{ scenario.owner }}</td>
    <td>{{ scenario.price }}</td>
    <td>
        <a href="#" class="btn btn-primary">Pobierz</a>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#changeStatusModal" data-bs-scenario-id="{{ scenario.sid }}">
            Zmień status
        </button>
        <a href="{% url 'scenario_mod_details' scenario_id=scenario.sid %}" class="btn btn-primary">Szczegóły</a>
        
    </td>
</tr>
{% empty %}
<tr>
    <td colspan="9" class="text-center">Brak scenariuszy dla tego statusu.</td>
</tr>
{% endfor %}