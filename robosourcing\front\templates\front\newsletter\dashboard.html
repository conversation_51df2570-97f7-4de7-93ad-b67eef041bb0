{% extends 'main.html' %}
{% load i18n %}
{% load static %}

{% block head_title %}{% trans "Newsletter Dashboard" %}{% endblock %}

{% block title %}
{{ block.super }} - {% trans "Newsletter Dashboard" %}
{% endblock %}

{% block content %}
<div class="container mt-3">
    <div class="container section-title fade-in pb-2">
        <h2>{% trans "Newsletter Dashboard" %}</h2>
        <p class="text-muted">{% trans "Manage your newsletter campaigns and view statistics" %}</p>
    </div>

    <!-- Statystyki główne -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ total_subscribers }}</h3>
                    <p class="card-text">{% trans "Total Subscribers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">{{ confirmed_subscribers }}</h3>
                    <p class="card-text">{% trans "Confirmed Subscribers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">{{ new_subscribers_30d }}</h3>
                    <p class="card-text">{% trans "New (30 days)" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">{{ sent_campaigns }}</h3>
                    <p class="card-text">{% trans "Sent Campaigns" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statystyki aktywności -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>{% trans "Activity (Last 30 days)" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 text-center">
                            <h4 class="text-primary">{{ recent_opens }}</h4>
                            <small>{% trans "Email Opens" %}</small>
                        </div>
                        <div class="col-6 text-center">
                            <h4 class="text-success">{{ recent_clicks }}</h4>
                            <small>{% trans "Link Clicks" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>{% trans "Quick Actions" %}</h5>
                </div>
                <div class="card-body">
                    <a href="{% url 'admin:front_newslettertemplate_add' %}" class="btn btn-primary btn-sm mb-2 d-block">
                        <i class="fas fa-plus me-2"></i>{% trans "Create Template" %}
                    </a>
                    <a href="{% url 'admin:front_newslettersegment_add' %}" class="btn btn-info btn-sm mb-2 d-block">
                        <i class="fas fa-users me-2"></i>{% trans "Create Segment" %}
                    </a>
                    <a href="{% url 'admin:front_newslettercampaign_add' %}" class="btn btn-success btn-sm d-block">
                        <i class="fas fa-paper-plane me-2"></i>{% trans "Create Campaign" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Najnowsze kampanie -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>{% trans "Recent Campaigns" %}</h5>
                </div>
                <div class="card-body">
                    {% if recent_campaigns %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Name" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Template" %}</th>
                                        <th>{% trans "Recipients" %}</th>
                                        <th>{% trans "Sent" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for campaign in recent_campaigns %}
                                    <tr>
                                        <td>{{ campaign.name }}</td>
                                        <td>
                                            <span class="badge bg-{% if campaign.status == 'sent' %}success{% elif campaign.status == 'sending' %}warning{% elif campaign.status == 'scheduled' %}info{% else %}secondary{% endif %}">
                                                {{ campaign.get_status_display }}
                                            </span>
                                        </td>
                                        <td>{{ campaign.template.name }}</td>
                                        <td>{{ campaign.total_recipients }}</td>
                                        <td>
                                            {% if campaign.sent_at %}
                                                {{ campaign.sent_at|date:"Y-m-d H:i" }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if campaign.status == 'sent' %}
                                                <a href="{% url 'campaign_stats' campaign.id %}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-chart-bar me-1"></i>{% trans "Stats" %}
                                                </a>
                                            {% elif campaign.status == 'draft' %}
                                                <a href="{% url 'send_campaign' campaign.id %}" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-paper-plane me-1"></i>{% trans "Send" %}
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">{% trans "No campaigns yet. Create your first campaign!" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
