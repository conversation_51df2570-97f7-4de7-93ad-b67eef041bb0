{% load category_tags %}

<style>

/* Kontener główny dla drzewa */
.tree, .tree ul {
  margin: 1px;
  list-style: none;
}

/* Zagnieżdżone ul */
.tree ul {
  margin-left: 1em;
  position: relative;
}

/* Dodatkowe wcięcie dla kolejnych poziomów */
.tree ul ul {
  margin-left: 0.5em;
}

/* Pionowa linia na całej wysokości listy */
.tree ul:before {
  content: "";
  display: block;
  width: 0;
  border-left: 1px solid #ccc;
  margin-left: 0;
  position: absolute;
  top: 0;
  bottom: 0;
}

/* Styl dla pojedynczego li */
.tree li {
  margin: 0;
  padding: 0 1em;
  line-height: 2rem;
  font-weight: 600;   /* pogrubiony tekst */
  position: relative;
}

/* Pozioma linia łącząca kolejne elementy */
.tree li:before {
  content: "";
  display: block;
  width: 1rem;
  height: 0;
  border-top: 1px solid #ccc;
  margin-top: -1px;
  position: absolute;
  left: 0;
  top: 1.1em;
}

/* Ostatni element w danej liście nie ciągnie linii w dół */
.tree li:last-child:before {
  background: #fff;
  height: auto;
}

/* Link do kategorii */
.category-link {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}
.category-link:hover {
  text-decoration: underline;
}
</style>

<ul class="tree">
  {% for category, data in tree %}
    <li>
      <a class="category-link" href="?cat={{ category }}">{{ category }}</a>
      {% if data.children %}
        {% render_categories categories category %}
      {% endif %}
    </li>
  {% endfor %}
</ul>
