from django.urls import path, re_path
from django.views.generic import RedirectView, TemplateView

from . import views, newsletter_views
from django.conf import settings
from django.conf.urls.static import static

from .views import serve_webalizer_report, find_html_files

urlpatterns = [
    path('', views.index, name='index'),
    path('home/', views.dashboard_home, name='home'),

    # theme picker
    path('set-theme/', views.set_theme, name='set_theme'),

    # download section
    path('download/', views.download, name='download'),
    path('download/<str:platform_name>/', views.download, name='download'),
    path('download/<str:platform_name>/<str:package_name>/', views.download, name='download'),
    path('download/<str:platform_name>/<str:package_name>/<int:version_id>/', views.download, name='download'),
    path('download/<str:platform_name>/<str:package_name>/<int:version_id>/<str:command>/', views.download_installer, name='download'),
    path('repository/<str:filename>/', views.download_direct, name='repository'),

    # public scenario search engine
    path('botbook/', views.scenarios_page, name='scenarios_page'),
    path('botbook/<uuid:sid>/', views.scenario_details, name='scenario_details'),
    path('botbook/<uuid:sid>/get/', views.scenario_get, name='scenario_get'),
    path('tags-autocomplete/', views.tags_autocomplete, name='tags_autocomplete'),

    # price list
    path('pricelist/', views.pricelist, name='pricelist'),

    # other
    path('features/', views.features, name='features'),
    path('contact/', views.contact, name='contact'),
    path('how-it-works/', views.how_it_works, name='how_it_works'),
    path('privacy-policy/', views.privacy_policy, name='privacy_policy'),
    path('store-regulations/', views.store_regulations, name='store_regulations'),
    path('download-file/<int:file_id>/', views.download_file, name='download_file'),

    # academy
    path('app-download-guide/', views.app_download_guide, name='app_download_guide'),
    path('app-install-guide/', views.app_install_guide, name='app_install_guide'),
    path('app-launch-guide/', views.app_launch_guide, name='app_launch_guide'),

    # webalizer (direct access - reports in public static folder)
    # path('webalizer/', views.webalizer_report, name='webalizer_report'),

    # webalizer (access from admin site - reports in restricted media folder)
    path('admin/webalizer/<str:filename>', serve_webalizer_report, name='serve_webalizer_report'),

    # newsletter
    path('newsletter/', TemplateView.as_view(template_name='front/newsletter/status.html'), name='newsletter_status'),
    path('newsletter/subscribe/', views.newsletter_subscribe, name='newsletter_subscribe'),
    path('newsletter/confirm/<str:token>/', views.newsletter_confirm, name='newsletter_confirm'),
    path('newsletter/unsubscribe/<str:token>/', views.newsletter_unsubscribe, name='newsletter_unsubscribe'),

    # newsletter management (admin only)
    # path('newsletter/dashboard/', newsletter_views.newsletter_dashboard, name='newsletter_dashboard'),
    path('newsletter/campaign/<int:campaign_id>/stats/', newsletter_views.campaign_stats, name='campaign_stats'),
    path('newsletter/campaign/<int:campaign_id>/send/', newsletter_views.send_campaign, name='send_campaign'),
    path('newsletter/track/open/<str:tracking_id>/', newsletter_views.track_open, name='newsletter_track_open'),
    path('newsletter/track/click/<str:tracking_id>/', newsletter_views.track_click, name='newsletter_track_click'),
    path('newsletter/export/', newsletter_views.export_subscribers, name='export_subscribers'),
    path('newsletter/export/<int:segment_id>/', newsletter_views.export_subscribers, name='export_subscribers_segment'),

    # AJAX endpoints for subscriber management
    path('newsletter/confirm-subscriber/<int:subscriber_id>/', newsletter_views.confirm_subscriber_ajax, name='confirm_subscriber_ajax'),
    path('newsletter/delete-subscriber/<int:subscriber_id>/', newsletter_views.delete_subscriber_ajax, name='delete_subscriber_ajax'),

    # user guide
    path('user_guide/find/', find_html_files, name='user_guide_find'),
    path('user_guide/<path:path>', views.UserGuideView.as_view(), name='user_guide'),
    path('user_guide/', views.UserGuideView.as_view(), name='user_guide_index'),
    re_path(r'^top.html$', RedirectView.as_view(url='/user_guide/top.html', permanent=False)),
    re_path(r'^default.html$', RedirectView.as_view(url='/user_guide/default.html', permanent=False)),
    re_path(r'^css/(?P<path>.*)$', RedirectView.as_view(url='/user_guide/css/%(path)s', permanent=False)),
    re_path(r'^js/(?P<path>.*)$', RedirectView.as_view(url='/user_guide/js/%(path)s', permanent=False)),
    re_path(r'^img/(?P<path>.*)$', RedirectView.as_view(url='/user_guide/img/%(path)s', permanent=False)),
    re_path(r'^content/(?P<path>.*)$', RedirectView.as_view(url='/user_guide/content/%(path)s', permanent=False)),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
