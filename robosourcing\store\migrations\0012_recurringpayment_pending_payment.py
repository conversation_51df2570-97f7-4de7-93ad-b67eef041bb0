# Generated by Django 4.1.9 on 2024-06-10 13:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0011_recurringpayment_active_recurringpayment_create_time_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='recurringpayment',
            name='pending_payment',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.subsequentorder'),
        ),
    ]
