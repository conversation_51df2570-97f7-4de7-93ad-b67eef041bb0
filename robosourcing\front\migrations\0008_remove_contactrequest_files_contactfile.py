# Generated by Django 4.2.13 on 2024-10-16 16:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('front', '0007_contactrequest_app_version_contactrequest_files'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='contactrequest',
            name='files',
        ),
        migrations.CreateModel(
            name='ContactFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='uploads/')),
                ('contact_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='front.contactrequest')),
            ],
        ),
    ]
