# Generated by Django 4.1.7 on 2024-05-06 20:49

from django.db import migrations, models
import django.db.models.deletion
import robo_map.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RoboMap',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('expires_at', models.DateTimeField(null=True)),
                ('map_version', models.CharField(max_length=100)),
                ('map_file', models.FileField(null=True, upload_to=robo_map.models.build_map_path)),
                ('map_size', models.IntegerField(default=0)),
                ('app', models.CharField(max_length=100)),
                ('app_version', models.Char<PERSON>ield(max_length=100)),
                ('generator', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('PARTIAL', 'Partial'), ('ACTIVE', 'Active'), ('EXPIRED', 'Expired'), ('EXPIRING', 'Expiring'), ('DELETED', 'Deleted')], default='PARTIAL', max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='MapUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reported_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('SUCCESS', 'Success'), ('FAIL', 'Fail')], max_length=20)),
                ('app', models.CharField(max_length=100)),
                ('app_version', models.CharField(max_length=100)),
                ('os_version', models.CharField(max_length=100)),
                ('screen_scale', models.CharField(max_length=10)),
                ('robo_map', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='robo_map.robomap')),
            ],
        ),
    ]
