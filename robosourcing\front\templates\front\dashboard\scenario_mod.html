{% extends "front/dashboard/base.html" %}

{% load static %}
{% load i18n %}

{% block title %}{{ block.super }} - {% trans "Moderation Panel" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <div class="row">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item site-name"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
              <li class="breadcrumb-item site-name active">{% trans "Moderation Panel" %}</li>
            </ol>
        </nav>
        <!-- Filters panel -->
<div class="col-md-3 col-lg-2 mb-4">
    <div class="dashboard-element">
      <h2 class="dashboard-header">{% trans "Filters" %}</h2>
      <div class="dashboard-body mb-4 ms-4 me-4">
        <form method="get" action="{% url 'scenario_mod' %}">
  
          <!-- Name -->
          <div class="mb-3">
            <label for="name" class="form-label">{% trans "Name" %}:</label>
            <input 
              type="text" 
              id="name" 
              name="name" 
              class="form-control" 
              placeholder="{% trans "Name" %}" 
              value="{{ request.GET.name }}"
            >
          </div>
  
          <!-- Author -->
          <div class="mb-3">
            <label for="author" class="form-label">{% trans "Author" %}:</label>
            <input 
              type="text" 
              id="author" 
              name="author" 
              class="form-control" 
              placeholder="{% trans "Author" %}" 
              value="{{ request.GET.author }}"
            >
          </div>
  
          <!-- Status checkboxes -->
          <div class="mb-3">
            <label class="form-label">{% trans "Select statuses" %}:</label>
            <div class="filter-statuses">
              {% for status_value, status_name in status_choices %}
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    name="status" 
                    value="{{ status_value }}" 
                    id="status-{{ status_value }}"
                    {% if status_value|stringformat:"s" in selected_statuses %}checked{% endif %}
                  >
                  <label class="form-check-label" for="status-{{ status_value }}">
                    {{ status_name }}
                  </label>
                </div>
              {% endfor %}
            </div>
          </div>
  
          <!-- Moderator -->
          <div class="mb-3">
            <label for="moderator" class="form-label">{% trans "Moderator" %}:</label>
            <select name="moderator" id="moderator" class="form-select">
              <option value="">{% trans "All" %}</option>
              <option value="none" {% if request.GET.moderator == 'none' %}selected{% endif %}>
                {% trans "Unassigned" %}
              </option>
              {% for moderator in moderators %}
                <option 
                  value="{{ moderator.id }}"
                  {% if request.GET.moderator == moderator.id|stringformat:"s" %}selected{% endif %}
                >
                  {{ moderator.get_full_name|default:moderator.username }}
                </option>
              {% endfor %}
            </select>
          </div>
  
          <!-- Date range -->
          <div class="mb-3">
            <label for="start_date" class="form-label">{% trans "Start date" %}:</label>
            <input 
              type="date" 
              id="start_date" 
              name="start_date" 
              class="form-control" 
              value="{{ request.GET.start_date }}"
            >
          </div>
          <div class="mb-3">
            <label for="end_date" class="form-label">{% trans "End date" %}:</label>
            <input 
              type="date" 
              id="end_date" 
              name="end_date" 
              class="form-control" 
              value="{{ request.GET.end_date }}"
            >
          </div>
  
          <!-- Cost range -->
          <div class="mb-3">
            <label for="min_price" class="form-label">{% trans "Min cost" %}:</label>
            <input 
              type="number" 
              id="min_price" 
              name="min_price" 
              class="form-control" 
              min="0" 
              step="0.01" 
              value="{{ request.GET.min_price }}"
            >
          </div>
          <div class="mb-3">
            <label for="max_price" class="form-label">{% trans "Max cost" %}:</label>
            <input 
              type="number" 
              id="max_price" 
              name="max_price" 
              class="form-control" 
              min="0" 
              step="0.01" 
              value="{{ request.GET.max_price }}"
            >
          </div>
  
          <!-- Show per page -->
          <div class="mb-3">
            <label for="records_per_page" class="form-label">{% trans "Show" %}:</label>
            <select 
              name="records_per_page" 
              id="records_per_page"
              class="form-select"
            >
              <option value="5"  {% if request.GET.records_per_page == '5'  %}selected{% endif %}>5</option>
              <option value="10" {% if request.GET.records_per_page == '10' %}selected{% endif %}>10</option>
              <option value="20" {% if request.GET.records_per_page == '20' %}selected{% endif %}>20</option>
              <option value="50" {% if request.GET.records_per_page == '50' %}selected{% endif %}>50</option>
              <option value="100"{% if request.GET.records_per_page == '100'%}selected{% endif %}>100</option>
            </select>
            <div class="form-text">{% trans "scenarios per page" %}</div>
          </div>
  
          <!-- Buttons -->
          <div class="d-grid gap-2">
            <button class="btn btn-primary" type="submit">
              {% trans "Apply" %}
            </button>
            <button class="btn btn-secondary" type="button" onclick="clearFilters();">
              {% trans "Clear filters" %}
            </button>
          </div>
  
        </form>
      </div>
    </div>
  </div>
  

        <!-- Scenarios table -->
        <div class="col-md-10">
            <div class="dashboard-element">
                <div class="table-responsive">
                    <table class="table my-custom-table">
                        <thead>
                            <tr>
                                <th>
                                    <a class="sort-link" href="?order_by=name&direction={% if order_by == 'name' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Name" %}
                                    </a>
                                </th>
                                <th>
                                    <a class="sort-link" href="?order_by=scenario_version&direction={% if order_by == 'scenario_version' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Version" %}
                                    </a>
                                </th>
                                <th>
                                    <a class="sort-link" href="?order_by=author&direction={% if order_by == 'author' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Author" %}
                                    </a>
                                </th>
                                <th>
                                    <a class="sort-link" href="?order_by=created_at&direction={% if order_by == 'created_at' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Created" %}
                                    </a>
                                </th>
                                <th>
                                    <a class="sort-link" href="?order_by=price&direction={% if order_by == 'price' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Cost" %}
                                    </a>
                                </th>
                                <th>
                                    <a class="sort-link" href="?order_by=status&direction={% if order_by == 'status' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Status" %}
                                    </a>
                                </th>
                                <th>
                                    <a class="sort-link" href="?order_by=moderator__userprofile__user_name&direction={% if order_by == 'moderator__userprofile__user_name' and order_direction == 'asc' %}desc{% else %}asc{% endif %}">
                                        {% trans "Moderator" %}
                                    </a>
                                </th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for scenario in page_obj %}
                            <tr>
                                <td class="max-width-column"
                                    data-bs-toggle="tooltip"
                                    data-bs-placement="top"
                                    title="{{ scenario.name }}">
                                {{ scenario.name }}
                                </td>
                                <td>{{ scenario.scenario_version }}</td>
                                <td>{{ scenario.author }}</td>
                                <td>{{ scenario.created_at|date:"Y-m-d H:i" }}</td>
                                <td>{{ scenario.price }}</td>
                                <td>
                                    <span class="status-badge {{ scenario.get_status_display|slugify }}">
                                        {{ scenario.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if scenario.moderator %}
                                      {{ scenario.moderator.get_full_name|default:scenario.moderator.username }}
                                    {% else %}
                                      {% trans "unassigned" %}
                                    {% endif %}
                                  </td>
                                <td>
                                    <a href="{% url 'scenario_mod_details' scenario.sid %}" class="btn btn-warning">
                                        <i class="fas fa-eye"></i> {% trans "Details" %}
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="8">{% trans "No scenarios." %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            
                <div class="pagination">
                    <span class="step-links">
                        {% if page_obj.has_previous %}
                            <a href="?page=1&records_per_page={{ request.GET.records_per_page }}&name={{ request.GET.name }}&author={{ request.GET.author }}&start_date={{ request.GET.start_date }}&end_date={{ request.GET.end_date }}&min_price={{ request.GET.min_price }}&max_price={{ request.GET.max_price }}&moderator={{ request.GET.moderator }}&order_by={{ order_by }}&direction={{ order_direction }}">{% trans "First" %}</a>
                            <a href="?page={{ page_obj.previous_page_number }}&records_per_page={{ request.GET.records_per_page }}&name={{ request.GET.name }}&author={{ request.GET.author }}&start_date={{ request.GET.start_date }}&end_date={{ request.GET.end_date }}&min_price={{ request.GET.min_price }}&max_price={{ request.GET.max_price }}&moderator={{ request.GET.moderator }}&order_by={{ order_by }}&direction={{ order_direction }}">{% trans "Previous" %}</a>        
                        {% endif %}

                        <span class="current">
                            {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}.
                        </span>

                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}&records_per_page={{ request.GET.records_per_page }}&name={{ request.GET.name }}&author={{ request.GET.author }}&start_date={{ request.GET.start_date }}&end_date={{ request.GET.end_date }}&min_price={{ request.GET.min_price }}&max_price={{ request.GET.max_price }}&moderator={{ request.GET.moderator }}&order_by={{ order_by }}&direction={{ order_direction }}">{% trans "Next" %}</a>
                            <a href="?page={{ page_obj.paginator.num_pages }}&records_per_page={{ request.GET.records_per_page }}&start_date={{ request.GET.start_date }}&end_date={{ request.GET.end_date }}&min_price={{ request.GET.min_price }}&max_price={{ request.GET.max_price }}&moderator={{ request.GET.moderator }}&order_by={{ order_by }}&direction={{ order_direction }}">{% trans "Last" %}</a>
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearFilters() {
    const url = new URL(window.location.href);
    url.searchParams.delete('name');
    url.searchParams.delete('author');
    url.searchParams.delete('start_date');
    url.searchParams.delete('end_date');
    url.searchParams.delete('min_price');
    url.searchParams.delete('max_price');
    url.searchParams.delete('status');
    url.searchParams.delete('moderator');
    url.searchParams.delete('records_per_page');
    url.searchParams.delete('page');
    url.searchParams.delete('order_by');
    url.searchParams.delete('direction');
    window.location.href = url.toString();
}
</script>
{% endblock %}
