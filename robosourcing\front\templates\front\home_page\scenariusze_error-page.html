{% extends 'main.html' %}
{% load static %}

{% block title %}
    {{ block.super }} - Home
{% endblock title %}

{% block scripts %}
<script>

</script>
{% endblock %}

{% block navbar_buttons %}
    {% if user.is_authenticated %}
    <a class="btn btn-success me-2 px-4 button" href="{% url 'home' %}" role="button">Dashboard</a>
    <form method="post" action="{% url 'account_logout' %}" class=" me-2">
      {% csrf_token %}
      {% if redirect_field_value %}
          <input type="hidden"
                 name="{{ redirect_field_name }}"
                 value="{{ redirect_field_value }}"/>
      {% endif %}
      <button class="btn btn-outline-primary button px-4" type="submit">
          Wyloguj
      </button>
      </form>
    {% else %}
    <a class="btn btn-primary px-4 me-2 button" href="{% url 'account_login' %}" role="button">Zaloguj</a>
    <a class="btn btn-outline-primary px-4 me-2 button" href="{% url 'account_signup' %}" role="button">Zarejestruj</a>
    {% endif %}
{% endblock %}

{% block content %}
    <h1 class="p-5 mt-5">NIE UDAŁO SIĘ POBRAĆ SCENARIUSZY</h1>
{% endblock %}