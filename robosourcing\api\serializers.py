from django.conf import settings
from django.forms import ValidationError
from rest_framework import serializers, validators
from django.core.validators import MinLengthValidator
from rest_framework.fields import empty
from django.core.serializers import serialize
import json

from db.models import App, Category, KeywordsHash, Robot, Hid, Pid, Credit, Scenario_Event, Scenario_Event_Message, Program, Scenario, Scenario_Program, Tag, DownloadPlatform, DownloadItem, DownloadEvent, UserStorage, File, FileCategory, FileKeyword
from profiles.models import UserConnectKey, RobotResetKey, UserProfile
from django.contrib.auth.models import User


class DynamicFieldsModelSerializer(serializers.ModelSerializer):
    """
    A ModelSerializer that takes an additional `fields` argument that
    controls which fields should be displayed.
    """
    def __init__(self, *args, **kwargs):
        # Don't pass the 'fields' arg up to the superclass
        fields = kwargs.pop('fields', None)
        # Instantiate the superclass normally
        super().__init__(*args, **kwargs)
        if fields is not None:
            # Drop any fields that are not specified in the `fields` argument.
            allowed = set(fields)
            existing = set(self.fields)
            for field_name in existing - allowed:
                self.fields.pop(field_name)


class CreateAnonymousRobotRequestSerializer(serializers.Serializer):
    hid         = serializers.ModelField(model_field=Hid()._meta.get_field('hid'), help_text="HID", required=True)
    pid         = serializers.ModelField(model_field=Pid()._meta.get_field('pid'), help_text="PID", required=True)
    
    class Meta:
        fields = ['hid', 'pid']


class CreateRobotSerializer(serializers.ModelSerializer):
    class Meta:
        model = Robot
        fields = ['__all__']

    def validate(self, data):
        hid         = data.get('hid', None)
        local_user  = data.get('local_user', None)
        user        = data.get('user', None)
        if not hid and not user:
            raise serializers.ValidationError("At least one of the fields 'hid' or 'user' should have a value.")
        if (not hid and not local_user) or (not user and not local_user):
            raise serializers.ValidationError("At least one of pair 'hid+local_user' or 'user+local_user' should have a values.")
        return data


class RobotSerializer(DynamicFieldsModelSerializer):
    class Meta:
        model = Robot
        fields = '__all__'


class ConnectKeyResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserConnectKey       
        exclude = ('user_id',)


class RobotConnectSerializer(serializers.Serializer):
    rid          = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="RID", required=True)
    connect_key  = serializers.UUIDField(required=True)
    name         = serializers.ModelField(model_field=Robot()._meta.get_field('name'), help_text="Robot name", required=False, default=None)

    class Meta:
        fields = ['rid', 'connect_key', 'name']
    

class RobotDeleteSerializer(serializers.Serializer):
    rid          = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="RID", required=True)
    

class RobotUpdateSerializer(serializers.Serializer):
    rid          = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="RID", required=True)
    name         = serializers.ModelField(model_field=Robot()._meta.get_field('name'), help_text="Robot name", required=True)


class DetailedResponseSerializer(serializers.Serializer):
    detail = serializers.CharField(help_text='Detailed information of response')

    class Meta:
        fields = ['detail']

    def __init__(self, detail=None):
        super().__init__(self)
        self.detail = detail


class RobotActionAviableSerializer(serializers.Serializer):
    rid = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="Robot ID")
    sid = serializers.ModelField(model_field=Scenario()._meta.get_field('sid'), help_text="Scenario ID")

    class Meta:
        fields = ['rid', 'sid']


class LockCreditsSerializer(serializers.Serializer):
    rid                 = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="Robot ID")
    sid                 = serializers.ModelField(model_field=Scenario()._meta.get_field('sid'), help_text="Scenario ID")
    credits_to_lock     = serializers.IntegerField(help_text="Credits to lock")
    private_scenario    = serializers.BooleanField(default=False, help_text="Private scenario indicator")

    class Meta:
        fields = ['rid', 'sid', 'credits_to_lock', 'private_scenario']


class LockCreditsResponseSerializer(serializers.Serializer):
    detail          = serializers.CharField(help_text='Detailed information of response')
    locked_credits  = serializers.ReadOnlyField(help_text="Locked credits")
    cached_credits  = serializers.ModelField(model_field=Robot()._meta.get_field('cached_credits'), help_text="Robot credits")

    class Meta:
        fields = ['detail', 'locked_credits', 'cached_credits']

    def __init__(self, detail=None, locked_credits=None, cached_credits=None):
        super().__init__(self)
        self.detail = detail
        self.locked_credits = locked_credits
        self.cached_credits = cached_credits


class CreditsPaymentSerializer(serializers.Serializer):
    rid                 = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="Robot ID")
    sid                 = serializers.ModelField(model_field=Scenario()._meta.get_field('sid'), help_text="Scenario ID")
    used_credits        = serializers.IntegerField(initial=0, help_text='Used credits')
    locked_credits      = serializers.IntegerField(required=False, help_text="Locked credits")
    offline_payment     = serializers.BooleanField(default=False, help_text="Offline payment indicator")
    private_scenario    = serializers.BooleanField(default=False, help_text="Private scenario indicator")

    class Meta:
        fields = ['rid', 'sid', 'used_credits', 'offline_payment', 'private_scenario']


class UploadScenarioResponseSerializer(serializers.Serializer):
    detail = serializers.CharField(help_text='Detailed information of response')
    sid = serializers.UUIDField(help_text="Scenario ID")
    
    class Meta:
        fields = ['detail', 'sid']
    
    def __init__(self, detail=None, sid=None):
        super().__init__(self)
        self.detail = detail
        self.sid = sid


class RobotInfoSerializer(serializers.Serializer):
    rid    = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="Robot ID", required=True)

    class Meta:
        fields = ['rid']
    

class RobotInfoResponseSerializer(serializers.Serializer):
    uid             = serializers.ReadOnlyField(help_text="UID")
    user_name       = serializers.ReadOnlyField(help_text="User Name")
    user_role       = serializers.ReadOnlyField(help_text="User Role")
    robot_credits   = serializers.ReadOnlyField(help_text="Robot Credits")
    profile_credits = serializers.ReadOnlyField(help_text="Profile Credits")
    locked_credits  = serializers.ReadOnlyField(help_text="Locked Credits")

    class Meta:
        fields = ['uid', 'user_name', 'user_role', 'robot_credits', 'profile_credits', 'locked_credits']

    def __init__(self, uid=None, user_name=None, user_role=None, robot_credits=None, profile_credits=None, locked_credits=None,):
        super().__init__(self)
        self.uid = uid
        self.user_name = user_name
        self.user_role = user_role
        self.robot_credits = robot_credits
        self.profile_credits = profile_credits
        self.locked_credits = locked_credits


class RidSerializer(serializers.Serializer):
    rid    = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="Robot ID", required=True)

    class Meta:
        fields = ['rid']


class ResetVerificationSerializer(serializers.Serializer):
    reset_key    = serializers.ModelField(model_field=RobotResetKey()._meta.get_field('reset_key'), help_text="Reset Token", required=True)

    class Meta:
        fields = ['reset_key']


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'
    def __init__(self, instance=None, data=..., **kwargs):
        super().__init__(instance, data, **kwargs)
        new_validators = filter(
            lambda validator: not isinstance(validator, validators.UniqueValidator), 
            self.fields["name"].validators
        )
        self.fields["name"].validators = new_validators


class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = '__all__'
    def __init__(self, instance=None, data=..., **kwargs):
        super().__init__(instance, data, **kwargs)
        new_validators = filter(
            lambda validator: not isinstance(validator, validators.UniqueValidator), 
            self.fields["name"].validators
        )
        self.fields["name"].validators = new_validators


class ProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = Program
        fields = '__all__'


class KeywordHashSerializer(serializers.ModelSerializer):
    class Meta:
        model = KeywordsHash
        fields = '__all__'
    def __init__(self, instance=None, data=..., **kwargs):
        super().__init__(instance, data, **kwargs)
        new_validators = filter(
            lambda validator: not isinstance(validator, validators.UniqueValidator), 
            self.fields["keywords_hash"].validators
        )
        self.fields["keywords_hash"].validators = new_validators


class RegisterScenarioEventSerializer(serializers.Serializer):
    event_type_id = serializers.IntegerField(help_text="XXXXX", required=True)
    sid = serializers.UUIDField(help_text="Scenario ID", required=True)
    rid = serializers.IntegerField(help_text="Robot ID", required=False, allow_null=True)
    uid = serializers.IntegerField(help_text="User ID", required=False, allow_null=True)
    name = serializers.CharField(required=False, allow_blank=True)
    description = serializers.CharField(required=False, allow_blank=True)
    execution_cost = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)
    is_fixed_cost = serializers.BooleanField(default=False, required=False)
    tags = serializers.ListSerializer(child=TagSerializer(), required=False)
    categories = serializers.ListSerializer(child=CategorySerializer(), required=False)
    message = serializers.CharField(required=False, allow_blank=True)

    def validate(self, data):
        """
        Check that the start is before the stop.
        """
        if not data.get('rid') and not data.get('uid'):
            raise serializers.ValidationError("At least one of the fields 'rid' or 'uid' should have a value.")
        
        event_type = data.get('event_type_id')
        if event_type == Scenario_Event.EventType.REJECTED and (not data.get('name') or not data.get('description') or data.get('execution_cost') is None or not data.get('message')):
            raise serializers.ValidationError('Incomplete request data for EventType.REJECTED event type')
        return data
    
class DownloadPlatformSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadPlatform
        fields = ['id', 'name']

class DownloadVersionInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadItem
        fields = ['id', 'release_version', 'release_date', 'publish_time', 'end_time', 'is_active', 'filename', 'short_description', 'full_description', 'requirements', 'server_id', 'package_id', 'platforms']

class RegisterDownloadEventSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadEvent
        fields = ['version', 'event_time', 'ip_address', 'region']

class AddDownloadItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadItem
        fields = '__all__'

class UpdateDownloadItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadItem
        fields = '__all__'
        extra_kwargs = {'id': {'read_only': True}}

class ScenarioDetailsSerializer(DynamicFieldsModelSerializer):
    # tags        = TagSerializer(many=True, read_only=True)
    # categories  = CategorySerializer(many=True, read_only=True)
    # apps        = ProgramSerializer(many=True, required=False)
    apps        = serializers.ListSerializer(child=ProgramSerializer())
    # message     = serializers.ModelField(model_field=Scenario_Event_Message()._meta.get_field('message'), help_text="message")
    
    class Meta:
        model = Scenario
        scenario_fields = '__all__'
        # extra_fields = ['tags', 'categories', 'apps', 'message']
    
    def __init__(self, apps=None):
        super().__init__(self)
        self.apps.create(apps)
    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
    #     print('J1')
    #     print(self)
    #     print('J2')
    #     print(self.sid)

    #     self.apps = Scenario_Program.objects.filter(sid=self.sid).values('program')

class DownloadItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadItem
        fields = '__all__'

class MyScenariosSerializer(serializers.Serializer):
    rid = serializers.ModelField(model_field=Robot()._meta.get_field('rid'), help_text="Robot ID")

    class Meta:
        fields = ['rid']

class MyScenariosResponseSerializer(serializers.Serializer):
    sid     = serializers.ModelField(model_field=Scenario()._meta.get_field('sid'), help_text="Scenario ID")
    name    = serializers.ModelField(model_field=Scenario()._meta.get_field('name'), help_text="Scenario name")

    class Meta:
        fields = ['sid', 'name']

class MyScenariosDetailedResponseSerializer(serializers.ModelSerializer):

    is_robot_assigned = serializers.BooleanField()

    class Meta:
        model = Scenario
        fields = ['sid', 'name', 'scenario_version', 'description', 'author', 'created_at', 'updated_at', 'accepted',
                  'owner', 'moderator', 'price', 'status', 'publish_time', 'is_fixed_cost', 'available', 'editable',
                  'updatable', 'is_robot_assigned']


class ScenarioChangelogSerializer(serializers.ModelSerializer):
    class Meta:
        model = Scenario
        fields = ['sid', 'scenario_version', 'version_changelog']


class ScenarioCheckUpdateSerializer(serializers.Serializer):
        sids = serializers.ListField(child=serializers.ModelField(model_field=Scenario()._meta.get_field('sid'), help_text="SID"), help_text="Scenario ID", required=True)


class ScenarioLatestVersionsSerializer(serializers.Serializer):
    origin_sid      = serializers.UUIDField(help_text="Origin Scenario ID")
    latest_version  = serializers.IntegerField(help_text="Latets Scenario version")
    latest_sid      = serializers.UUIDField(help_text="Latest Scenario ID")
    class Meta:
        fields = ['origin_sid', 'latest_version', 'latest_sid']


class FileCategorySerializer(serializers.Serializer):
    name = serializers.ModelField(model_field=FileCategory()._meta.get_field('name'), help_text="Category name")
    class Meta:
        fields = '__all__'


class FileKeywordSerializer(serializers.Serializer):
    keyword = serializers.ModelField(model_field=FileKeyword()._meta.get_field('keyword'), help_text="Keyword")
    class Meta:
        fields = '__all__'


class FileHashSerializer(serializers.Serializer):
    file_hashes = serializers.ListField(child=serializers.ModelField(model_field=File()._meta.get_field('file_hash'), help_text="file_hash"), help_text="File hashes", required=True)


class ListKeywordsHashSerializer(serializers.Serializer):
    keywords_hashes   = serializers.ListField(child=serializers.ModelField(model_field=KeywordsHash()._meta.get_field('keywords_hash')), help_text="Keywords hashes", required=True)


class UploadScenarioAttachmentSerializer(serializers.Serializer):
    file            = serializers.CharField(help_text="Base64 file representation")
    export_name     = serializers.ModelField(model_field=File()._meta.get_field('export_name'), help_text="export_name")
    file_type       = serializers.ModelField(model_field=File()._meta.get_field('file_type'), help_text="file_type")
    file_hash       = serializers.ModelField(model_field=File()._meta.get_field('file_hash'), help_text="file_hash")
    keywords_hash   = serializers.ModelField(model_field=KeywordsHash()._meta.get_field('keywords_hash'), help_text="keywords_hash", required=False)
    scenario        = serializers.ModelField(model_field=Scenario()._meta.get_field('sid'), help_text="Scenario ID")
    keywords        = serializers.ListField(child=serializers.ModelField(model_field=FileKeyword()._meta.get_field('keyword'), help_text="Keywords"), required=False)
    categories      = serializers.ListField(child=serializers.ModelField(model_field=FileCategory()._meta.get_field('name'), help_text="Category name"), required=False)
    class Meta:
        fields = '__all__'
    
    def validate_file(self, value):
        max_size = settings.MAX_BYTES_SIZE
        if (len(value)/4)*3 > max_size:
            raise serializers.ValidationError(f'File cannot be larger than {(max_size/1000000):f} MB')
        return value


class AttachmentSerializer(serializers.ModelSerializer):
    file = serializers.ReadOnlyField(source='file_base64')

    class Meta:
        model = File
        fields = '__all__'


class ListAttachmentSerializer(serializers.ListSerializer):
    child = AttachmentSerializer()


class AttachmentMetadataSerializer(serializers.ModelSerializer):
    class Meta:
        model = File
        fields = '__all__'


class ListAttachmentMetadataSerializer(serializers.ListSerializer):
    child = AttachmentMetadataSerializer()


class UploadScenarioSerializer(serializers.ModelSerializer):
    rid = serializers.UUIDField(write_only=True, help_text="Robot ID")
    tags            = TagSerializer(write_only=True, many=True, required=False)
    categories      = CategorySerializer(many=True, required=False)
    apps            = ProgramSerializer(many=True, required=False)
    keyword_hashes  = KeywordHashSerializer(many=True, required=False)
    upload_mode     = serializers.IntegerField(help_text="Upload mode", required=True, write_only=True)
   

    class Meta:
        model = Scenario
        fields = ['scenario_version', 'version_changelog', 'name', 'description', 'author', 'created_at', 'updated_at', 'available', 'scenario', 'owner', 'price', 'rid', 'tags', 'categories', 'apps', 'keyword_hashes', 'upload_mode', 'editable', 'updatable']
        extra_kwargs = {
            'owner': {'read_only': True}
        }

    def validate_rid(self, value):
        if not Robot.objects.filter(rid=value).exists():
            raise serializers.ValidationError("No robot with given ID exists.")
        return value

    def create(self, validated_data):
        # Usuń 'rid' z przekazanych danych, ponieważ nie jest on używany bezpośrednio do tworzenia scenariusza
        validated_data.pop('rid', None)
        instance = Scenario.objects.create(**validated_data)
        return instance
    
    def update(self, instance, validated_data):
        instance.sid = validated_data.get('sid', instance.sid)
        instance.scenario_version = validated_data.get('scenario_version', instance.scenario_version)
        instance.name = validated_data.get('name', instance.name)
        instance.description = validated_data.get('description', instance.description)
        instance.author = validated_data.get('author', instance.author)
        instance.available = validated_data.get('available', instance.available)
        instance.price = validated_data.get('price', instance.price)
        instance.scenario = validated_data.get('scenario', instance.scenario)
        instance.status = Scenario.ScenarioStatus.TO_MOD
        instance.save()
        return instance


class ChangeDescriptionScenarioSerializer(serializers.ModelSerializer):
    class Meta:
        model = Scenario
        fields = ['name', 'description']