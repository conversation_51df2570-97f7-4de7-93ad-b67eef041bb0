{% extends 'main.html' %}
{% load i18n %}
{% load static %}

{% block title %}
    {{ block.super }} - {% trans "How it works" %}
{% endblock title %}

{% block scripts %}
{% endblock %}

{% block navbar_buttons %}
  {{ block.super }}
{% endblock %}

{% block content %}
<!-- <section id="section-how-it-works" class="section-how-it-works section light-background py-5">
  <div class="container fade-in">
    <div class="content">
      <div class="row">
        <div class="col-lg-10 offset-lg-1 border border-lead px-5 pb-5 mb-4">
          <h2 class="content-title text-center">
            {% trans "How Botie Works" %}
          </h2>
          <p class="lead">
            
          </p>
          <p class="lead">
            
          </p>
          <p class="lead">
            
          </p>

          <h3 class="content-title">
            
          </h3>
          <div class="accordion" id="creditPackages">
            <div class="accordion-item">
              <h2 class="accordion-header" id="headingOne">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#oneTime" aria-expanded="true" aria-controls="oneTime">
                  
                </button>
              </h2>
              <div id="oneTime" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#creditPackages">
                <div class="accordion-body">
                  
                </div>
              </div>
            </div>
            <div class="accordion-item">
              <h2 class="accordion-header" id="headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#renewable" aria-expanded="false" aria-controls="renewable">
                  
                </button>
              </h2>
              <div id="renewable" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#creditPackages">
                <div class="accordion-body">
                  
                </div>
              </div>
            </div>
            <div class="accordion-item">
              <h2 class="accordion-header" id="headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#combined" aria-expanded="false" aria-controls="combined">
                  
                </button>
              </h2>
              <div id="combined" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#creditPackages">
                <div class="accordion-body">
                  
                </div>
              </div>
            </div>
          </div>
        </div>
      </div> 
    </div> 
  </div> 
</section> -->

    <!-- Stats Section -->
    <section id="stats" class="stats section">

      <div class="container">

        <div class="row gy-2 justify-content-center">

          <div class="col-lg-5">
            <div class="images-overlap">
              <img src="{% static 'images/homepage/Asset 3.png' %}" alt="student" class="img-fluid img-1" data-aos="fade-up">
            </div>
          </div>

          <div class="col-lg-6 ps-lg-5">
            <!-- <span class="content-subtitle">Why Us</span> -->
            <h2 class="content-title">{% trans "How Botie Works" %}</h2>
            <p class="lead">
              {% trans "Botie is an application installed on the User's computer, which serves to play scenarios of robotic processes (similar to how a CD player plays CDs), as well as the botie.pl web portal, from which the User can search and download scenarios from various business fields (similar to CDs that require a CD player)." %}
            </p>
            <p class="mb-3">
              {% trans "Downloading the application and scenarios from the botie.pl portal is free. With the first installation and launch of the application, the User receives a free welcome package of 100 credits, which Botie consumes for playing scenarios. The costs of executing individual scenarios vary depending on the number of actions and are known to the User before launching the application." %}
            </p>
            <p class="mb-5">
              {% trans "It is possible to increase the credit package by purchasing them in the Store. After launching, the Botie application synchronizes with the User's account to refresh the number of purchased credits." %}
            </p>

        </div>

      </div>
    </section><!-- /Stats Section -->

    <!-- Faq Section -->
    <section id="faq" class="faq section">
      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>{% trans "Types of Credit Packages:" %}</h2>
      </div><!-- End Section Title -->

      <div class="container" data-aos="fade-up">
        <div class="row">
          <div class="col-12">
            <div class="custom-accordion" id="accordion-faq">
             
              <div class="accordion-item">
                <h2 class="mb-0">
                  <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-faq-1">
                    {% trans "One-Time Packages" %}
                  </button>
                </h2>

                <div id="collapse-faq-1" class="collapse show" aria-labelledby="headingOne" data-parent="#accordion-faq">
                  <div class="accordion-body">
                    {% trans "One-time packages have no time limit and are available until exhausted. Purchasing additional packages adds to the previous ones." %}
                  </div>
                </div>
              </div>
              
              <!-- .accordion-item -->

            <div class="accordion-item">
              <h2 class="mb-0">
                <button class="btn btn-link collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-faq-2">
                  {% trans "Renewable Packages" %}
                </button>
              </h2>

              <div id="collapse-faq-2" class="collapse" aria-labelledby="headingThree" data-parent="#accordion-faq">
                <div class="accordion-body">
                  {% trans "Renewable packages are valid for one calendar month, after which they are replenished to the user-defined limit. Unused credits expire and do not accumulate." %}
                </div>
              </div>
            </div>
            <!-- .accordion-item -->

            <div class="accordion-item">
              <h2 class="mb-0">
                <button class="btn btn-link collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-faq-3">
                  {% trans "Combined Packages" %}
                </button>
              </h2>

              <div id="collapse-faq-3" class="collapse" aria-labelledby="headingThree" data-parent="#accordion-faq">
                <div class="accordion-body">
                  {% trans "It is possible to have a monthly renewable credit package and purchase a one-time package. In this case, the credits from the renewable package are used first, and after they are fully utilized, Botie will consume credits from the one-time package." %}
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
      </div>
    </section><!-- /Faq Section -->

    <!-- Services 2 Section -->
    <section id="services-2" class="services-2 section">

      <div class="container section-title fade-in">
        <p>{% trans "Our Services" %}</p>
      </div>

      <div class="container fade-in p-5">
        <div class="row justify-content-center" data-aos="fade-up">
          <div class="col-md-6 col-lg-4">
            
            <h2 class="content-title">
              {% trans "Portal botie.pl" %}
            </h2>
            <img src="{% static 'images/homepage/Asset 4.png' %}" alt="Botie Face" class="d-flex mb-2">
            <p class="lead">
              
              Na Portalu botie.pl znajdziesz wszystko czego potrzebujesz, aby rozpocząć proces robotyzacji. Pobierzesz oprogramowanie, a także będziesz mógł zarządzać swoimi kredytami, scenariuszami i robotami w Panelu Użytkownika.
            </p>
            
            
            <p class="mt-5">
              <a href="{% url 'account_login' %}" class="btn btn-get-started">{% trans "Sing in" %}</a>
            </p>
          </div>
          <div class="col-md-6 col-lg-8 ps-lg-5">
            <div class="row">
              
              <div class="col-6 col-sm-6 col-md-6 col-lg-6">
                <div class="services-item" data-aos="fade-up" data-aos-delay="0">
                  <div class="services-icon">
                    <i class="bi bi-cart"></i>
                  </div>
                  <div>
                    <h3><a href="{% url 'pricelist' %}" class="link-info">Sklep</a></h3>
                    <p>{% trans "In the Store, it is possible to buy credits, view purchase history, and manage profile data." %}</p>
                  </div>
                </div>
              </div>

              
              
              <div class="col-6 col-sm-6 col-md-6 col-lg-6">
                <div class="services-item" data-aos="fade-up" data-aos-delay="200">
                  <div class="services-icon">
                    <i class="bi bi-info-circle"></i>
                  </div>
                  <div>
                    <h3><a href="#" class="link-info">Centrum Pomocy</a></h3>
                    <p>{% trans "In the Help Center, there is a knowledge base along with a beginner's Botie User academy, inspiring users to explore more robotic and ready-to-use business areas." %}</p>
                  </div>
                </div>
              </div>

              <div class="col-6 col-sm-6 col-md-6 col-lg-6">
                <div class="services-item" data-aos="fade-up" data-aos-delay="400">
                  <div class="services-icon">
                    <i class="bi bi-book"></i>
                  </div>
                  <div>
                    <h3><a href="{% url 'scenarios_page' %}" class="link-info">BotBook</a></h3>
                    <p>{% trans "In BotBook, there is a collection of ready-made robotic process scenarios prepared by our automation specialists, divided into categories and subcategories. Users can freely browse the database, read the descriptions of individual scenarios, add them to their favorites, and download them to their computer to play in the Botie application." %}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      
      <div class="container mt-5 light-background p-5">
        <div class="row justify-content-center" data-aos="fade-up">
          <div class="col-md-6 col-lg-4">
            
            <h2 class="content-title">
              {% trans "The Botie application:" %}
            </h2>
            <p class="lead">
              Botie to zaawansowana aplikcaja pozwalająca na uruchamianie i tworzenie scenariuszy. Dzięki możliwości pracy z przeglądarką, systemem oraz programami firm trzecich jej zastosowanie jest wręcz nieograniczone. Łatwy próg wejścia zapewniają gotowe scenariusze przygotowane przez zespół Botie.
            </p>
            
            
            <p class="mt-5">
              <a href="{% url 'download' %}" class="btn btn-get-started">{% trans "Download" %}</a>
            </p>
          </div>
          <div class="col-md-6 col-lg-8 ps-lg-5">
            <div class="row">
              
              <div class="col-6 col-sm-6 col-md-6 col-lg-6">
                <div class="services-item" data-aos="fade-up" data-aos-delay="0">
                  <div class="services-icon">
                    <i class="bi bi-play-btn"></i>
                  </div>
                  <div>
                    <h3>Uruchamianie Scenariuszy</h3>
                    <p>{% trans "Plays the robotic process scenarios downloaded from BotBook." %}</p>
                  </div>
                </div>
              </div>

              
              
              <div class="col-6 col-sm-6 col-md-6 col-lg-6">
                <div class="services-item" data-aos="fade-up" data-aos-delay="200">
                  <div class="services-icon">
                    <i class="bi bi-plus-circle"></i>
                  </div>
                  <div>
                    <h3>Tworzenie Scenariuszy</h3>
                    <p>{% trans "Allows users to create their own scenarios in the editor." %}</p>
                  </div>
                </div>
              </div>

              <div class="col-6 col-sm-6 col-md-6 col-lg-6">
                <div class="services-item" data-aos="fade-up" data-aos-delay="400">
                  <div class="services-icon">
                    <i class="bi bi-toggles2"></i>
                  </div>
                  <div>
                    <h3>Zarządzanie i synchronizacja</h3>
                    <p>{% trans "Manages user profiles and synchronizes credits with the user's account in the web application botie.pl." %}</p>
                  </div>
                </div>
              </div>

              <div class="col-6 col-sm-6 col-md-6 col-lg-6">
                <div class="services-item" data-aos="fade-up" data-aos-delay="600">
                  <img src="{% static 'images/homepage/Asset <EMAIL>' %}" alt="Botie Face" class="mt-5 mx-auto d-block">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section><!-- /Services 2 Section -->




  <!-- <section id="section-image-text" class="section-image-text section light-background py-5">
    <div class="container fade-in">
      <div class="content">
        <div class="row">
          <div class="col-lg-6 text-center mb-4">
            <img src="{% static 'images/homepage/robo_image_2.webp' %}" class="img-fluid" alt="{% trans 'How Botie Works' %}">
          </div>
          <div class="col-lg-6">
            <h2 class="content-title">
              
            </h2>
            <ul class="list-unstyled list-check">
              <li>
                
              </li>
              <li>
                
              </li>
              <li>
                
              </li>
            </ul>
            <div class="d-grid gap-2 d-md-flex justify-content-md-start mb-4">
              <a href="{% url 'scenarios_page' %}" class="btn-get-started">
                {% trans "BotBook" %}
              </a>
              <a href="{% url 'pricelist' %}" class="btn-get-started">
                {% trans "Store" %}
              </a>
            </div>
            <h2 class="content-title mt-4">
              {% trans "The Botie application:" %}
            </h2>
            <ul class="list-unstyled list-check">
              <li>
                
              </li>
              <li>
                
              </li>
              <li>
                
              </li>
            </ul>
          </div>
        </div> 
    </div> 
  </section> -->
  

<div class="mt-5 mb-5 divider"></div>

{% endblock %}