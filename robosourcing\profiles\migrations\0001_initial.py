# Generated by Django 4.1.7 on 2024-05-06 20:49

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('db', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('street', models.CharField(max_length=50)),
                ('street_number', models.PositiveIntegerField()),
                ('home_number', models.PositiveIntegerField(blank=True, null=True)),
                ('city', models.CharField(max_length=50)),
                ('postal_code', models.CharField(max_length=6, validators=[django.core.validators.RegexValidator('^\\d{2}-\\d{3}$', 'Invalid postal code format. Expected format: XX-XXX')])),
                ('country', models.CharField(default='PL', max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Consent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('additional_description', models.TextField(blank=True, null=True)),
                ('type', models.CharField(choices=[('general', 'Ogólne'), ('user', 'Użytkownik'), ('moderator', 'Moderator')], max_length=10)),
                ('is_displayed', models.BooleanField(default=True, help_text='Czy ten element powinien być wyświetlany?')),
                ('email_required', models.BooleanField(default=True, verbose_name='Wymagana zgoda na e-mail')),
                ('app_required', models.BooleanField(default=True, verbose_name='Wymagana zgoda na aplikację')),
                ('phone_sms_required', models.BooleanField(default=True, verbose_name='Wymagana zgoda na telefon/SMS')),
            ],
        ),
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('area_code', models.CharField(blank=True, max_length=5, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=9, null=True, validators=[django.core.validators.RegexValidator(message='Phone number must be entered with 7 to 9 digits.', regex='^\\d{7,9}$')])),
            ],
        ),
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('tax_id', models.CharField(max_length=25, primary_key=True, serialize=False)),
                ('name_1', models.CharField(max_length=100)),
                ('name_2', models.CharField(blank=True, max_length=100, null=True)),
                ('street', models.CharField(max_length=50)),
                ('street_number', models.PositiveIntegerField()),
                ('home_number', models.PositiveIntegerField(blank=True, null=True)),
                ('city', models.CharField(max_length=50)),
                ('postal_code', models.CharField(max_length=6, validators=[django.core.validators.RegexValidator('^\\d{2}-\\d{3}$', 'Invalid postal code format. Expected format: XX-XXX')])),
                ('country', models.CharField(default='PL', max_length=20)),
            ],
            options={
                'ordering': ['tax_id'],
            },
        ),
        migrations.CreateModel(
            name='UserConnectKey',
            fields=[
                ('connect_key', models.UUIDField(auto_created=True, default=uuid.uuid4, unique=True)),
                ('user_id', models.OneToOneField(editable=False, on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('create_time', models.DateTimeField()),
                ('duration_time', models.DurationField()),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_name', models.CharField(max_length=100)),
                ('first_name', models.CharField(max_length=25)),
                ('last_name', models.CharField(max_length=100)),
                ('role', models.CharField(choices=[('REG', 'Regular User'), ('MOD', 'Moderator'), ('ADM', 'Administrator')], default='REG', max_length=3)),
                ('address', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='profiles.address')),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='profiles.contact')),
                ('organization', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='profiles.organization')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user'],
            },
        ),
        migrations.CreateModel(
            name='UserConsent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_consent', models.BooleanField(default=False)),
                ('app_consent', models.BooleanField(default=False)),
                ('phone_sms_consent', models.BooleanField(default=False)),
                ('consent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_consents', to='profiles.consent')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_consents', to='profiles.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='RobotResetKey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reset_key', models.UUIDField(auto_created=True, default=uuid.uuid4)),
                ('create_time', models.DateTimeField()),
                ('duration_time', models.DurationField()),
                ('rid', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='db.robot')),
            ],
        ),
    ]
