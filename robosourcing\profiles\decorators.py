from django.contrib.auth.decorators import user_passes_test
from django.core.exceptions import PermissionDenied

def user_in_group(user, group_name):
    return user.groups.filter(name=group_name).exists()

def group_required(group_name, login_url=None, raise_exception=False):
    def in_group(u):
        if u.is_authenticated:
            if u.groups.filter(name=group_name).exists() or u.is_superuser:
                return True
        return False

    actual_decorator = user_passes_test(
        in_group,
        login_url=login_url,
    )

    if raise_exception:
        def decorator(view_func):
            def _wrapped_view(request, *args, **kwargs):
                if in_group(request.user):
                    return view_func(request, *args, **kwargs)
                else:
                    raise PermissionDenied
            return _wrapped_view
        return decorator
    return actual_decorator