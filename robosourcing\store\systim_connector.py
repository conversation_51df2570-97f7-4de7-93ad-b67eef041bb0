import json
import requests
import logging
from datetime import datetime
# from requests_toolbelt.utils import dump


class SingletonMeta(type):
    """
    The Singleton metaclass.
    """

    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]


class SystimConnector(metaclass=SingletonMeta):
    """
    A class containing methods for handling contrahents and invoices using Systim API
    """
    _SYSTIM_ACCOUNT_ID = 49275
    _SYSTIM_LOGIN = '<EMAIL>'
    _SYSTIM_API_KEY = ';IkK(_1w,YLHqBcRLRB2SpJs{j-Sr(F'

    _token = None
    _logger = None

    # ********************************************
    def __init__(self):
        # init logger
        self._logger = logging.getLogger('SYSTIM')
        self._logger.setLevel(logging.DEBUG)
        self._file_handler = logging.FileHandler('systim.log')
        self._file_handler.setLevel(logging.INFO)
        self._file_handler.setFormatter(logging.Formatter('%(asctime)s | %(levelname)s | %(message)s'))
        self._logger.addHandler(self._file_handler)

    """""""""""""""""""""""""""""""""""""""""""""
    LOGIN AND FUNCTIONAL PRIVATE METHODS:
    - _print_json: prints out object in user-friendly way
    - _make_request: makes request to Systim API
    - _make_request_with_relogin: checks the response code and re-login in needed
    - login: authorize to Systim to get an access token
    """""""""""""""""""""""""""""""""""""""""""""

    @staticmethod
    # ********************************************
    def _print_json(code):
        """
        Prints out JSON formatted code
        """
        print(json.dumps(code, indent=4))

    # ********************************************
    def _make_request(self, data, force=False):
        resp = requests.post(f'https://{self._SYSTIM_ACCOUNT_ID}.systim.pl/jsonAPI.php', data=data)
        # data_dump = dump.dump_all(resp)
        # print(data_dump.decode('utf-8'))
        obj = resp.json()
        if obj['error']['code'] != 13 or force:  # 13 - token invalid or expired
            return obj
        return None

    # ********************************************
    def _make_request_with_relogin(self, data):
        if not data['token']:
            # login to get a token
            self.login()
            data['token'] = self._token
        # try to make the request
        obj = self._make_request(data)
        if obj:
            # return response as JSON
            return obj
        # re-login to get a new token
        self.login()
        data['token'] = self._token
        # retry the request with new token
        return self._make_request(data, force=True)

    # ********************************************
    def login(self):
        if self._token:
            print('re-login to renew the token...')
        else:
            print('login to get access token...')
        # auth
        data = {'act': 'login', 'login': self._SYSTIM_LOGIN, 'pass': self._SYSTIM_API_KEY}
        obj = self._make_request(data, force=True)
        # self._print_json(obj)
        try:
            self._token = obj["result"]["token"]
        except KeyError:
            pass

        # < Response[200] >
        # {
        #     "error": {
        #         "code": 0,
        #         "message": "OK",
        #         "fields": []
        #     },
        #     "method": "login",
        #     "result": {
        #         "token": "f0758b53af6b23d2f79d66028ee824a0a72fc0"
        #     }
        # }

    """""""""""""""""""""""""""""""""""""""""""""
    CONTRAHENTS HANDLING METHODS:
    - list_companies
    - add_company
    - update_company
    - get_company (not used yet)
    """""""""""""""""""""""""""""""""""""""""""""

    # ********************************************
    def list_companies(self, name=None, nip=None) -> json:
        """
        List companies, all or filtered
        """
        data = {'token': self._token, 'act': 'listCompanies'}
        if name:
            data['name'] = name
        if nip:
            data['nip'] = nip
        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

        # < Response[200] >
        # {
        #     "error": {
        #         "code": 0,
        #         "message": "OK",
        #         "fields": []
        #     },
        #     "method": "listCompanies",
        #     "result": {
        #         "1": {
        #             "nip": "7822885548",
        #             "nazwa_skrot": "ROBOSOURCING",
        #             "nipeu": "*********548",
        #             "id_grupy_cenowej": "1",
        #             "email": "<EMAIL>",
        #             "nazwa": "ROBOSOURCING SP\u00d3\u0141KA Z OGRANICZON\u0104 ODPOWIEDZIALNO\u015aCI\u0104",
        #             "miejscowosc": "Pozna\u0144",
        #             "ulica": "os. Or\u0142a Bia\u0142ego 20/18",
        #             "id_wojewodztwa": "15",
        #             "kod": "61-251",
        #             "panstwo": "POLSKA",
        #             "nazwa_dostawy": null,
        #             "miejscowosc_dostawy": null,
        #             "ulica_dostawy": null,
        #             "id_wojewodztwa_dostawy": null,
        #             "kod_dostawy": null,
        #             "panstwo_dostawy": null
        #         },
        #         "2": {
        #             "nip": "7792230132",
        #             "nazwa_skrot": "VCN S.C. ADAM SZULC, ANDRZEJ HAMRYSZCZAK",
        #             "nipeu": "19",

    # ********************************************
    def add_company(self, name, street, city, post_code, nip=None) -> json:
        """
        Adds company
        """
        data = {
            'token': self._token,
            'act': 'addCompany',
            'nazwa': name,
            'ulica': street,
            'miejscowosc': city,
            'kod': post_code
        }
        if nip:
            data['nip'] = nip
        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

        # < Response[200] >
        # {
        #     "error": {
        #         "code": 0,
        #         "message": "OK",
        #         "fields": []
        #     },
        #     "method": "addCompany",
        #     "result": 38
        # }

    # ********************************************
    def update_company(self, id, name, street, city, post_code, nip=None) -> json:
        """
        Adds company
        """
        data = {
            'token': self._token,
            'act': 'updCompany',
            'id': id,
            'nazwa': name,
            'ulica': street,
            'miejscowosc': city,
            'kod': post_code
        }
        if nip:
            data['nip'] = nip
        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

        # < Response[200] >
        # {
        #     "error": {
        #         "code": 0,
        #         "message": "OK",
        #         "fields": []
        #     },
        #     "method": "updCompany",
        #     "result": "47"
        # }

    # ********************************************
    def get_company(self, id) -> json:
        """
        Returns single company data - more params than from list_companies filtered to single company
        """
        data = {
            'token': self._token,
            'act': 'getCompany',
            'id': id
        }
        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

        # < Response[200] >
        # {
        #     "error": {
        #         "code": 0,
        #         "message": "OK",
        #         "fields": []
        #     },
        #     "method": "getCompany",
        #     "result": {
        #         "id": "38",
        #         "nip": "",
        #         "nazwa_skrot": "Testowa Firma",
        #         "nipeu": "",
        #         "id_grupy_cenowej": "1",
        #         "regon": "",
        #         "uwagi": "",
        #         "email": "",
        #         "www": "",
        #         "telefon": "",
        #         "fax": "",
        #         "id_ratingu": null,
        #         "naliczanie_odsetek": "0",
        #         "termin_platnosci": "0",
        #         "zaplanowany_kontakt": "0000-00-00 00:00:00",
        #         "id_handlowca": "5",
        #         "konto_kontrahenta": "",
        #         "bank_kontrahenta": "",
        #         "symbol_kontrahenta": "",
        #         "nazwa": "Testowa Firma",
        #         "miejscowosc": "Tester Ma\u0142y",
        #         "ulica": "Testowa 1",
        #         "id_wojewodztwa": null,
        #         "kod": "11-001",
        #         "panstwo": "",
        #         "nazwa_dostawy": null,
        #         "miejscowosc_dostawy": null,
        #         "ulica_dostawy": null,
        #         "id_wojewodztwa_dostawy": null,
        #         "kod_dostawy": null,
        #         "panstwo_dostawy": null
        #     }
        # }

    """""""""""""""""""""""""""""""""""""""""""""
    PRODUCTS HANDLING METHODS:
    - list_products (not used yet)
    """""""""""""""""""""""""""""""""""""""""""""

    # ********************************************
    def list_products(self) -> json:
        """
        Lists products, all or filtered by ids
        """
        data = {
            'token': self._token,
            'act': 'listProducts',
            'ids': []
        }
        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

        # < Response[200] >
        # {
        #     "error": {
        #         "code": 0,
        #         "message": "OK",
        #         "fields": []
        #     },
        #     "method": "listProducts",
        #     "result": {
        #         "2": {
        #             "rodzaj": "1",
        #             "nazwa": "Abonament miesi\u0119czny SaaS Botie",
        #             "kod_produktu": "PM500",
        #             "kod_kreskowy": "",
        #             "kategoria": "Us\u0142ugi",
        #             "id_kategorii": "1",
        #             "opis": "",
        #             "zdjecie": "",
        #             "miniaturka": "",
        #             "ikonka": "",
        #             "pkwiu": "",
        #             "jednostka": "szt.",
        #             "cena_netto": "25.0000",
        #             "stawka_vat": "23%",
        #             "cena_brutto": "30.7500",
        #             "ilosc": null,
        #             "ukryty": "0",
        #             "parametry": []
        #         },
        #         "3": {
        #             "rodzaj": "1",
        #             "nazwa": "Abonament miesi\u0119czny SaaS Botie",
        #             "kod_produktu": "PM1000",

    """""""""""""""""""""""""""""""""""""""""""""
    INVOICES HANDLING METHODS:
    - add_invoice 
    - get_invoice (not used yet)
    - update_invoice (not used yet)
    - delete_invoice (not used yet)
    - get_invoice_pdf
    """""""""""""""""""""""""""""""""""""""""""""

    # ********************************************
    def add_invoice(self, id, items, payment_method, amount_paid) -> json:
        """
        Adds new invoice
        """
        data = {
            'token': self._token,
            'act': 'addSellInvoice',
            'id_kontrahenta': id,
            'data_wystawienia': datetime.now().date().strftime("%Y-%m-%d"),
            'data_sprzedazy': datetime.now().date().strftime("%Y-%m-%d"),
            'rodzaj': 0,

            'id_szablonu': 43,  # Faktura VAT (FV)
            'id_numeracji': 1,  # %n/%mm/%r

            'razem': amount_paid,
            'forma_platnosci': 6 if payment_method == 'CARD_TOKEN' else 0,  # 6 - card
            'zaplacono': amount_paid,
            'termin_platnosci': 0,
        }

        for index, item in enumerate(items):
            for key, value in item.items():
                data[f'{key}[{index}]'] = value

        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

        # {"error": {"code": 0, "message": "OK", "fields": []}, "method": "addSellInvoice",
        #  "result": {"id": 11, "numer_faktury": "5\/08\/2024", "result_code": 100,
        #             "ksiegowanie": "Faktura zosta\u0142a zaksi\u0119gowana. Utworzono ksi\u0119gowanie o numerze dowodu FV 5\/08\/2024.",
        #             "fiskalizacja": null}}

    # ********************************************
    def get_invoice(self, id) -> json:
        """
        Gets invoice data
        """
        data = {
            'token': self._token,
            'act': 'getInvoice',
            'id': id,
        }

        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

    # ********************************************
    def update_invoice(self, id) -> json:
        """
        Updates existing invoice
        """
        data = {
            'token': self._token,
            'act': 'updSellInvoice',
            'id': id,
            'id_kontrahenta': 38,  # from invoice (no change)
            'data_wystawienia': datetime.now().date().strftime("%Y-%m-%d"),
            'data_sprzedazy': datetime.now().date().strftime("%Y-%m-%d"),
            'rodzaj': 0,

            'id_szablonu': 43,  # Faktura VAT (FV)
            'id_numeracji': 1,  # %n/%mm/%r

            'numer_faktury': '4/07/2024',  # from invoice (no change)
            'numer_miesieczny': 3,  # from invoice (no change)
            'numer_roczny': 0,  # from invoice (no change)

            # 'razem': 100,
            # 'zaplacono': True,
            # 'termin_platnosci': 0,
        }

        items = [
            {
                'id_produktu': None,
                'ilosc': 2,
                'cena_netto': 25,
                'kwota_netto': 0,  # not accept None but 0 is ok
                'kwota_vat': 0,  # not accept None but 0 is ok
                'kwota_brutto': 0,  # not accept None but 0 is ok
                'jednostka': 'szt.',
                'opis': 'Pakiet 500',
                'stawka_vat': 1,  # 23%
            },
            {
                'id_produktu': None,
                'ilosc': 1,
                'cena_netto': 45,
                'kwota_netto': 0,  # not accept None but 0 is ok
                'kwota_vat': 0,  # not accept None but 0 is ok
                'kwota_brutto': 0,  # not accept None but 0 is ok
                'jednostka': 'szt.',
                'opis': 'Pakiet 1000',
                'stawka_vat': 1,  # 23%
            }
        ]

        for index, item in enumerate(items):
            for key, value in item.items():
                data[f'{key}[{index}]'] = value

        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

    # ********************************************
    def delete_invoice(self, id) -> json:
        """
        Deletes existing invoice
        """
        data = {
            'token': self._token,
            'act': 'delSellInvoice',
            'ids': id,
        }

        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

        # < Response[200] >
        # {
        #     "error": {
        #         "code": 0,
        #         "message": "OK",
        #         "fields": []
        #     },
        #     "method": "delSellInvoice",
        #     "result": null
        # }

    # ********************************************
    def get_invoice_pdf(self, id) -> json:
        """
        Returns invoice number and base64-encoded PDF file content
        """
        data = {
            'token': self._token,
            'act': 'getSellInvoicePDF',
            'id': id
        }
        obj = self._make_request_with_relogin(data)
        # self._print_json(obj)
        return obj

        # < Response[200] >
        # {
        #     "error": {
        #         "code": 0,
        #         "message": "OK",
        #         "fields": []
        #     },
        #     "method": "getSellInvoicePDF",
        #     "result": {
        #         "file": "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",
        #         "name": "FV_6_08_2024.pdf"
        #     }
        # }
