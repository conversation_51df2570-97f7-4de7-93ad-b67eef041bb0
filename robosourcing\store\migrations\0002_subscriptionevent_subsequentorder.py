# Generated by Django 4.1.9 on 2024-05-22 12:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SubsequentOrder',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('order_id', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('card_token', models.CharField(max_length=50, null=True)),
                ('price', models.FloatField(default=0)),
                ('description', models.CharField(max_length=250)),
                ('status', models.CharField(max_length=30, null=True)),
                ('update_time', models.DateTimeField(auto_now=True)),
                ('reference_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.order')),
            ],
        ),
        migrations.CreateModel(
            name='SubscriptionEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_time', models.DateTimeField(auto_now_add=True)),
                ('event_type', models.CharField(choices=[('SUB', 'SUBSCRIBE'), ('CHG', 'CHANGE'), ('UNS', 'UNSUBSCRIBE')], default='SUB', max_length=3)),
                ('change_mode', models.CharField(choices=[('IME', 'IMMEDIATE'), ('END', 'WITH PERIOD END')], default='END', max_length=3)),
                ('amount', models.FloatField(default=0)),
                ('payment_id', models.CharField(max_length=50, null=True)),
                ('status', models.CharField(choices=[('PENDING', 'PENDING'), ('CANCELLED', 'CANCELLED'), ('COMPLETED', 'COMPLETED')], default='PENDING', max_length=9)),
                ('product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='store.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
