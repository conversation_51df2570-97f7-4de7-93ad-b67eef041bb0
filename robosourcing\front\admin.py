from django.contrib import admin
from django.utils import timezone
from django.shortcuts import render, redirect
from django.contrib import admin, messages
from django.urls import path

from db.forms import ConceptScenarioForm, ScenarioForm
from profiles.models import Address, OrganizationUUID, Contact, UserProfile
from .mixins import ResetPermissionsMixin, RidDisplayMixin, UserDisplayMixin, SidDisplayMixin
from .models import (CarouselItem, CategoryCard, CardDescription, NewsletterSubscription,
                     NewsletterSegment, NewsletterTemplate, NewsletterCampaign,
                     NewsletterDelivery, NewsletterOpen, NewsletterClick, ExternalMailingService)
from db.models import DownloadItem, DownloadRequirement, DownloadServer, DownloadPackage, DownloadPlatform, \
    DownloadEvent, Scenario, Scenario_Event, Tag, Program, Category, UserSubscription, Credit, CreditEvent, \
    EmailNotification, StartScenario, MarketingCampaign, Referral, Visit, File, AffiliateLink, CommissionRateHistory, \
    CommissionChangeEvent, AffiliateWithdrawalRequest, WithdrawalCycle, TagGroup
from .forms import CarouselItemForm, DownloadItemForm
from store.models import Product, Order, OrderItem, SubscriptionEvent, RecurringPayment, SubsequentOrder, Invoice
from django.utils.html import format_html
from seo.admin import ObjectMetaInline

"""
PUBLIC WEB FRONT
Download section
"""

@admin.register(DownloadRequirement)
class DownloadRequirementAdmin(admin.ModelAdmin):
    list_display = ['os', 'processor', 'ram', 'disk', 'graphic']

@admin.register(DownloadServer)
class DownloadServerAdmin(admin.ModelAdmin):
    list_display = ['name', 'domain_name_based_url', 'id_based_url', 'repository_path', 'active', 'default']

@admin.register(DownloadPackage)
class DownloadPackageAdmin(admin.ModelAdmin):
    list_display = ['name', 'platform_display', 'active', 'default']

    def platform_display(self, obj):
        return obj.platform_id.name if obj.platform_id else 'N/A'
    platform_display.short_description = 'Platform'

@admin.register(DownloadPlatform)
class DownloadPlatformAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'default']

@admin.register(DownloadItem)
class DownloadItemAdmin(admin.ModelAdmin):
    form = DownloadItemForm
    list_display = ['release_date', 'release_version', 'platform_display', 'package_display', 'requirements_display',
                    'server_display', 'is_active', 'is_default']
    list_per_page = 20

    def requirements_display(self, obj):
        return obj.requirements.os if obj.requirements else 'N/A'
    requirements_display.short_description = 'Requirements'

    def server_display(self, obj):
        return obj.server_id.name if obj.server_id else 'N/A'
    server_display.short_description = 'Server'

    def package_display(self, obj):
        return obj.package_id.name if obj.package_id else 'N/A'
    package_display.short_description = 'Package'

    def platform_display(self, obj):
        return obj.platform_id.name if obj.platform_id else 'N/A'
    platform_display.short_description = 'Platform'

@admin.register(DownloadEvent)
class DownloadEventAdmin(ResetPermissionsMixin, admin.ModelAdmin):
    list_display = ['event_time', 'version_display', 'package_display', 'server_display', 'ip_address', 'region']
    list_per_page = 20

    def version_display(self, obj):
        qs = DownloadItem.objects.filter(id=obj.version_id)
        return qs.first().release_version if qs.exists() else 'Unknown'
    version_display.short_description = 'Downloaded version'

    def package_display(self, obj):
        qs = DownloadItem.objects.filter(id=obj.version_id)
        return qs.first().package_id.name if qs.exists() else 'Unknown'
    package_display.short_description = 'Package name'

    def server_display(self, obj):
        qs = DownloadItem.objects.filter(id=obj.version_id)
        return qs.first().server_id.name if qs.exists() else 'Unknown'
    server_display.short_description = 'Server name'

"""
PUBLIC WEB FRONT
Carousel
"""

@admin.register(CarouselItem)
class CarouselItemAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'layout', 'is_displayed', 'order')
    list_filter = ('layout', 'is_displayed')
    ordering = ('order',)
    fieldsets = (
        (None, {
            'fields': ('layout', 'order', 'is_displayed', 'display_start_date', 'display_end_date')
        }),
        ('Text', {
            'fields': (('title_en', 'title_pl'), ('description_en', 'description_pl'))
        }),
        ('Primary Button', {
            'fields': (('button_description_en', 'button_description_pl', 'button_color'), 'button_url')
        }),
        ('Secondary Button (opcjonalnie)', {
            'fields': (('button2_description_en', 'button2_description_pl'), 'button2_url')
        }),
        ('Background Images (Full Screen - Layout C)', {
            'fields': ('background_image', 'background_image_dark')
        }),
        ('Graphic Image (Layout A/B)', {
            'fields': ('graphic_image',)
        }),
        ('Responsive Images (Light Mode)', {
            'fields': ('background_image_desktop', 'background_image_tablet', 'background_image_mobile')
        }),
        ('Responsive Images (Dark Mode)', {
            'fields': ('background_image_desktop_dark', 'background_image_tablet_dark', 'background_image_mobile_dark')
        }),
    )


class ConceptScenarioAdmin(admin.ModelAdmin):
    form = ConceptScenarioForm

    def save_model(self, request, obj, form, change):
        # Automatyczne przypisanie właściciela, jeśli brak
        if not obj.owner:
            obj.owner = request.user

        # Zapisz główny obiekt
        super().save_model(request, obj, form, change)


@admin.register(Scenario)
class ScenarioAdmin(admin.ModelAdmin):
    search_fields = ['name', 'author']
    list_display = ['created_at', 'name', 'scenario_version_display', 'author', 'owner', 'available',
                    'status', 'apps_count', 'tags_count', 'categories_count', 'updated_at']
    list_per_page = 20
    ordering = ['-created_at']
    form = ScenarioForm

    def scenario_version_display(self, obj):
        return obj.scenario_version

    scenario_version_display.short_description = "Ver."

    def apps_count(self, obj):
        return obj.apps.count()

    apps_count.short_description = "Apps"

    def tags_count(self, obj):
        return obj.tags.count()

    tags_count.short_description = "Tags"

    def categories_count(self, obj):
        return obj.categories.count()

    categories_count.short_description = "Cats."

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path("add_simple/", self.admin_site.admin_view(self.add_concept_view), name="scenario_add_concept"),
        ]
        return custom_urls + urls

    def add_concept_view(self, request):
        if request.method == "POST":
            form = ConceptScenarioForm(request.POST)
            if form.is_valid():
                scenario = form.save(commit=False)
                scenario.owner = request.user  # Automatycznie przypisz właściciela
                scenario.save()
                messages.success(request, "Pomysł na scenariusz został dodany.")
                return redirect("admin:db_scenario_changelist")  # Przekierowanie na listę scenariuszy
        else:
            form = ConceptScenarioForm()

        return render(request, "admin/scenario_add_concept.html", {"form": form, "opts": self.model._meta})

@admin.register(StartScenario)
class StartScenarioAdmin(admin.ModelAdmin, SidDisplayMixin):
    list_display = ['sid_display', 'author_display', 'is_active', 'start_date', 'end_date']
    list_per_page = 20

    def author_display(self, obj):
        return obj.sid.author if obj.sid else 'N/A'
    author_display.short_description = 'Author'

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "sid":
            kwargs["queryset"] = Scenario.objects.filter(available=Scenario.AvailabilityStatus.PUBLIC)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

@admin.register(Scenario_Event)
class Scenario_EventAdmin(ResetPermissionsMixin, admin.ModelAdmin, RidDisplayMixin, SidDisplayMixin, UserDisplayMixin):
    list_display = ['event_time', 'sid_display', 'type_label', 'user_display', 'rid_display']
    list_per_page = 20
    ordering = ['-event_time']

    def type_label(self, obj):
        return Scenario_Event.EventType(obj.type_id).label

    type_label.short_description = "Event Type"

@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ['name', 'group_name']
    ordering = ('name',)
    list_per_page = 20

    def group_name(self, obj):
        return obj.group.name if obj.group else "-"

    group_name.short_description = "Group"

@admin.register(TagGroup)
class TagGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'has_icon', 'icon_preview']
    ordering = ('name',)
    list_per_page = 20

    def has_icon(self, obj):
        return bool(obj.icon)

    has_icon.boolean = True
    has_icon.short_description = "Has icon"

    def icon_preview(self, obj):
        if obj.icon:
            return format_html('<img src="{}" width="32" height="32" style="object-fit: contain;"/>', obj.icon.url)
        return "-"

    icon_preview.short_description = "Preview"

@admin.register(Program)
class ProgramAdmin(admin.ModelAdmin):
    list_display = ['name', 'version_number', 'build_number', 'producer']
    ordering = ('name', 'version_number', 'build_number', 'producer')
    list_per_page = 20

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent']
    ordering = ('name',)
    list_per_page = 20

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'value', 'price', 'type', 'active']
    list_per_page = 20
    inlines = [ObjectMetaInline]

class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ['name_display', 'price_display', 'quantity']

    def name_display(self, obj):
        qs = Product.objects.filter(id=obj.product_id)
        return qs.first().name if qs.exists() else 'Unknown'
    name_display.short_description = 'Product name'

    def price_display(self, obj):
        qs = Product.objects.filter(id=obj.product_id)
        return qs.first().price if qs.exists() else 'Unknown'
    price_display.short_description = 'Price'

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin, UserDisplayMixin):
    list_display = ['create_time', 'order_id', 'description', 'user_display', 'price', 'tax', 'price_incl_tax', 'status', 'update_time']
    list_per_page = 20
    inlines = [OrderItemInline]

@admin.register(SubsequentOrder)
class SubsequentOrderAdmin(admin.ModelAdmin):
    list_display = ['create_time', 'order_id', 'reference_order', 'description', 'price', 'tax', 'price_incl_tax', 'status', 'update_time']
    list_per_page = 20

@admin.register(UserSubscription)
class UserSubscriptionAdmin(admin.ModelAdmin, UserDisplayMixin):
    list_display = ['user_display', 'value', 'renew_date']
    list_per_page = 20

@admin.register(Credit)
class CreditAdmin(admin.ModelAdmin, UserDisplayMixin):
    list_display = ['user_display', 'subscription_credits', 'l_subscription_credits', 'package_credits', 'l_package_credits',
                    'update_time']
    list_per_page = 20

@admin.register(SubscriptionEvent)
class SubscriptionEventAdmin(admin.ModelAdmin, UserDisplayMixin):
    list_display = ['user_display', 'event_time', 'event_type', 'product', 'change_mode', 'amount', 'payment_id', 'status',
                    'update_time']
    list_per_page = 20

@admin.register(RecurringPayment)
class RecurringPaymentAdmin(admin.ModelAdmin, UserDisplayMixin):
    list_display = ['user_display', 'reference_order', 'payment_month_day', 'next_payment_date', 'next_payment_amount',
                    'last_payment_date', 'annual_payment', 'pending_payment']
    list_per_page = 20

@admin.register(CreditEvent)
class CreditEventAdmin(admin.ModelAdmin, UserDisplayMixin, RidDisplayMixin):
    list_display = ['user_display', 'rid_display', 'event_time', 'event_type', 'd_subscription_credits', 'd_l_subscription_credits',
                    'd_package_credits', 'd_l_package_credits', 'subscription_credits', 'l_subscription_credits',
                    'package_credits', 'l_package_credits']
    list_filter = ['user', 'event_type', 'event_time']
    list_per_page = 20

@admin.register(EmailNotification)
class EmailNotificationAdmin(ResetPermissionsMixin, admin.ModelAdmin, UserDisplayMixin):
    list_display = ['user_display', 'sending_time', 'message_id', 'status_id']
    list_per_page = 20

@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ['issue_time', 'number', 'system', 'ext_id', 'downloaded']
    list_per_page = 20

@admin.register(MarketingCampaign)
class MarketingCampaignAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'created_at', 'custom_start_date', 'first_week_visits', 'second_week_visits',
                    'third_week_visits', 'fourth_week_visits', 'fifth_week_visits']
    readonly_fields = ['start_date', 'code']

    def custom_start_date(self, obj):
        return obj.start_date
    custom_start_date.short_description = 'Start date (first use of code)'

    def first_week_visits(self, obj):
        return obj.first_week_visits()
    first_week_visits.short_description = 'Visits - Week 1'

    def second_week_visits(self, obj):
        return obj.second_week_visits()
    second_week_visits.short_description = 'Week 2'

    def third_week_visits(self, obj):
        return obj.third_week_visits()
    third_week_visits.short_description = 'Week 3'

    def fourth_week_visits(self, obj):
        return obj.fourth_week_visits()
    fourth_week_visits.short_description = 'Week 4'

    def fifth_week_visits(self, obj):
        return obj.fifth_week_visits()
    fifth_week_visits.short_description = 'Week 5'

class ReferralInline(admin.TabularInline):
    model = Referral
    extra = 0
    max_num = 0
    can_delete = False
    readonly_fields = ['path', 'visited_at']

@admin.register(Visit)
class VisitAdmin(ResetPermissionsMixin, admin.ModelAdmin):
    list_display = ['campaign', 'referrer_ip', 'started_at']
    inlines = [ReferralInline]
    readonly_fields = ['campaign', 'referrer_ip', 'started_at']

@admin.register(AffiliateLink)
class AffiliateLinkAdmin(admin.ModelAdmin):
    list_display = ('user', 'code', 'current_commission_rate', 'created_at')
    search_fields = ('user__username', 'code')

    def current_commission_rate(self, obj):
        now = timezone.now()
        current_rate = obj.rate_history.filter(start_date__lte=now, end_date__isnull=True).first()
        return f"{current_rate.commission_rate}%" if current_rate else "Brak"
    current_commission_rate.short_description = 'Aktualna stawka prowizji'

@admin.register(CommissionRateHistory)
class CommissionRateHistoryAdmin(admin.ModelAdmin):
    list_display = ('affiliate_link', 'commission_rate', 'start_date', 'end_date')
    
    def save_model(self, request, obj, form, change):
        if change:
            # Edycja istniejącego wpisu w CommissionRateHistory
            old_obj = CommissionRateHistory.objects.get(pk=obj.pk)
            # Sprawdzamy czy faktycznie zmieniła się stawka prowizji
            #if old_obj.commission_rate != obj.commission_rate:
            CommissionChangeEvent.objects.create(
                affiliate_link=obj.affiliate_link,
                old_rate=old_obj.commission_rate,
                new_rate=obj.commission_rate,
                changed_by=request.user
            )
        else:
            # Tworzenie nowego wpisu w CommissionRateHistory
            # Logujemy to zdarzenie także w CommissionChangeEvent
            CommissionChangeEvent.objects.create(
                affiliate_link=obj.affiliate_link,
                old_rate=None,  # brak poprzedniej stawki, bo to nowy wpis
                new_rate=obj.commission_rate,
                changed_by=request.user
            )

        super().save_model(request, obj, form, change)

@admin.register(CommissionChangeEvent)
class CommissionChangeEventAdmin(admin.ModelAdmin):
    list_display = ('affiliate_link', 'old_rate', 'new_rate', 'changed_by', 'changed_at')
    search_fields = ('affiliate_link__user__username', 'changed_by__username')
    list_filter = ('new_rate', 'changed_by', 'changed_at')

@admin.register(AffiliateWithdrawalRequest)
class AffiliateWithdrawalRequestAdmin(admin.ModelAdmin):
    list_display = ('user', 'amount', 'status', 'requested_at', 'processed_at')
    list_filter = ('status', 'requested_at', 'processed_at')
    search_fields = ('user__username',)

    actions = ['mark_as_paid', 'mark_as_canceled']

    def mark_as_paid(self, request, queryset):
        for w in queryset:
            w.mark_as_paid()
        self.message_user(request, "Wybrane żądania wypłat zostały oznaczone jako 'Wypłacono'.")

    def mark_as_canceled(self, request, queryset):
        for w in queryset:
            w.mark_as_canceled()
        self.message_user(request, "Wybrane żądania wypłat zostały anulowane.")

    mark_as_paid.short_description = "Oznacz wybrane jako wypłacone"
    mark_as_canceled.short_description = "Oznacz wybrane jako anulowane"

@admin.register(WithdrawalCycle)
class WithdrawalCycleAdmin(admin.ModelAdmin):
    list_display = ('partner', 'start_date', 'end_date', 'total_withdrawn')
    list_filter = ('partner', 'start_date', 'end_date')
    search_fields = ('partner__username',)

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'get_username', 'get_first_name', 'get_last_name', 'is_partner']
    readonly_fields = ['user']
    search_fields = ('user__username', 'user__first_name', 'user__last_name')
    list_filter = ('user__groups',)
    change_form_template = 'admin/userprofile/change_form.html'

    def get_username(self, obj):
        return obj.user.username
    get_username.short_description = 'Username'

    def get_first_name(self, obj):
        return obj.user.first_name
    get_first_name.short_description = 'First Name'

    def get_last_name(self, obj):
        return obj.user.last_name
    get_last_name.short_description = 'Last Name'

    def change_view(self, request, object_id, form_url='', extra_context=None):
        profile = self.get_object(request, object_id)
        addresses = Address.objects.filter(userprofile=profile)
        contacts = Contact.objects.filter(userprofile=profile)
        organizations_uuid = OrganizationUUID.objects.filter(userprofile=profile)

        extra_context = extra_context or {}
        extra_context['addresses'] = addresses
        extra_context['contacts'] = contacts
        extra_context['organizations_uuid'] = organizations_uuid

        return super().change_view(request, object_id, form_url, extra_context=extra_context)

@admin.register(Address)
class AddressAdmin(admin.ModelAdmin):
    model = Address
    extra = 0
    can_delete = False
    readonly_fields = ['timezone']
    fields = ['street', 'street_number', 'home_number', 'city', 'postal_code', 'country', 'timezone']

@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    model = Contact
    extra = 0
    can_delete = False
    readonly_fields = []
    fields = ['area_code', 'phone_number']

@admin.register(OrganizationUUID)
class OrganizationUUIDAdmin(admin.ModelAdmin):
    model = OrganizationUUID
    extra = 0
    can_delete = True
    readonly_fields = []
    fields = ['tax_id', 'name_1', 'name_2', 'street', 'street_number', 'home_number', 'city', 'postal_code', 'country']

class CardDescriptionInline(admin.StackedInline):
    model = CardDescription
    extra = 1

@admin.register(CategoryCard)
class CategoryCardAdmin(admin.ModelAdmin):
    inlines = [CardDescriptionInline]
    list_display = ('title_en', 'title_pl')

@admin.register(File)
class FileAdmin(admin.ModelAdmin):
    list_display = ['export_name', 'file_type', 'scenario_name', 'keywords_count', 'categories_count']
    list_per_page = 20

    def scenario_name(self, obj):
        return obj.scenario.name

    scenario_name.short_description = "Scenario"

    def keywords_count(self, obj):
        return obj.keywords.count()

    keywords_count.short_description = "Keywords Count"

    def categories_count(self, obj):
        return obj.categories.count()

    categories_count.short_description = "Categories Count"


@admin.register(NewsletterSubscription)
class NewsletterSubscriptionAdmin(admin.ModelAdmin):
    list_display = ('email', 'is_confirmed', 'created_at', 'confirmed_at', 'marketing_consent', 'data_processing_consent')
    list_filter = ('is_confirmed', 'marketing_consent', 'data_processing_consent', 'created_at', 'confirmed_at')
    search_fields = ('email',)
    readonly_fields = ('confirmation_token', 'created_at', 'confirmed_at', 'ip_address', 'user_agent')

    fieldsets = (
        ('Podstawowe informacje', {
            'fields': ('email', 'is_confirmed', 'confirmation_token')
        }),
        ('Zgody', {
            'fields': ('marketing_consent', 'data_processing_consent')
        }),
        ('Daty', {
            'fields': ('created_at', 'confirmed_at')
        }),
        ('Informacje techniczne', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_confirmed', 'export_emails']

    def mark_as_confirmed(self, request, queryset):
        updated = queryset.update(is_confirmed=True, confirmed_at=timezone.now())
        self.message_user(request, f'{updated} subskrypcji zostało oznaczonych jako potwierdzone.')
    mark_as_confirmed.short_description = "Oznacz jako potwierdzone"

    def export_emails(self, request, queryset):
        """Eksportuj adresy e-mail do pliku CSV"""
        import csv
        from django.http import HttpResponse

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="newsletter_emails.csv"'

        writer = csv.writer(response)
        writer.writerow(['Email', 'Potwierdzone', 'Data rejestracji', 'Data potwierdzenia'])

        for subscription in queryset:
            writer.writerow([
                subscription.email,
                'Tak' if subscription.is_confirmed else 'Nie',
                subscription.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                subscription.confirmed_at.strftime('%Y-%m-%d %H:%M:%S') if subscription.confirmed_at else ''
            ])

        return response
    export_emails.short_description = "Eksportuj adresy e-mail do CSV"


@admin.register(NewsletterSegment)
class NewsletterSegmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'confirmed_only', 'created_at', 'subscribers_count')
    list_filter = ('is_active', 'confirmed_only', 'created_at')
    search_fields = ('name', 'description')

    fieldsets = (
        ('Podstawowe informacje', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Kryteria segmentacji', {
            'fields': ('confirmed_only', 'registration_date_from', 'registration_date_to')
        }),
    )

    def subscribers_count(self, obj):
        return obj.get_subscribers().count()
    subscribers_count.short_description = "Liczba subskrybentów"


@admin.register(NewsletterTemplate)
class NewsletterTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'subject', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active', 'created_at', 'updated_at')
    search_fields = ('name', 'subject')

    fieldsets = (
        ('Podstawowe informacje', {
            'fields': ('name', 'subject', 'is_active')
        }),
        ('Treść', {
            'fields': ('content_html', 'content_text')
        }),
        ('Dostępne zmienne', {
            'fields': (),
            'description': 'Dostępne zmienne: {{ subscriber.email }}, {{ unsubscribe_url }}, {{ current_date }}, {{ site_url }}'
        }),
    )


class NewsletterDeliveryInline(admin.TabularInline):
    model = NewsletterDelivery
    extra = 0
    readonly_fields = ('sent_at', 'tracking_id')
    fields = ('subscription', 'delivery_status', 'sent_at', 'tracking_id')


@admin.register(NewsletterCampaign)
class NewsletterCampaignAdmin(admin.ModelAdmin):
    list_display = ('name', 'status', 'template', 'segment', 'scheduled_at', 'sent_at', 'total_recipients', 'emails_sent')
    list_filter = ('status', 'created_at', 'sent_at')
    search_fields = ('name',)

    fieldsets = (
        ('Podstawowe informacje', {
            'fields': ('name', 'template', 'segment', 'status')
        }),
        ('Harmonogram', {
            'fields': ('scheduled_at', 'sent_at')
        }),
        ('Statystyki', {
            'fields': ('total_recipients', 'emails_sent', 'emails_failed'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('sent_at', 'total_recipients', 'emails_sent', 'emails_failed')
    inlines = [NewsletterDeliveryInline]

    actions = ['send_campaign']

    def send_campaign(self, request, queryset):
        # Tutaj będzie logika wysyłania kampanii
        for campaign in queryset:
            if campaign.status == 'draft':
                campaign.status = 'scheduled'
                campaign.save()
        self.message_user(request, f'{queryset.count()} kampanii zostało zaplanowanych.')
    send_campaign.short_description = "Zaplanuj wysyłkę kampanii"


@admin.register(NewsletterDelivery)
class NewsletterDeliveryAdmin(admin.ModelAdmin):
    list_display = ('campaign', 'subscription', 'delivery_status', 'sent_at', 'opens_count', 'clicks_count')
    list_filter = ('delivery_status', 'sent_at', 'campaign')
    search_fields = ('subscription__email', 'campaign__name')
    readonly_fields = ('tracking_id', 'sent_at')

    def opens_count(self, obj):
        return obj.newsletteropen_set.count()
    opens_count.short_description = "Otwarcia"

    def clicks_count(self, obj):
        return obj.newsletterclick_set.count()
    clicks_count.short_description = "Kliknięcia"


@admin.register(NewsletterOpen)
class NewsletterOpenAdmin(admin.ModelAdmin):
    list_display = ('delivery', 'opened_at', 'ip_address')
    list_filter = ('opened_at', 'delivery__campaign')
    search_fields = ('delivery__subscription__email', 'delivery__campaign__name')
    readonly_fields = ('opened_at',)


@admin.register(NewsletterClick)
class NewsletterClickAdmin(admin.ModelAdmin):
    list_display = ('delivery', 'url', 'clicked_at', 'ip_address')
    list_filter = ('clicked_at', 'delivery__campaign')
    search_fields = ('url', 'delivery__subscription__email', 'delivery__campaign__name')
    readonly_fields = ('clicked_at',)


@admin.register(ExternalMailingService)
class ExternalMailingServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'service_type', 'is_active', 'is_default', 'daily_limit', 'monthly_limit')
    list_filter = ('service_type', 'is_active', 'is_default')
    search_fields = ('name',)

    fieldsets = (
        ('Podstawowe informacje', {
            'fields': ('name', 'service_type', 'is_active', 'is_default')
        }),
        ('Konfiguracja API', {
            'fields': ('api_key', 'api_url')
        }),
        ('Limity', {
            'fields': ('daily_limit', 'monthly_limit')
        }),
    )
