{% extends 'main.html' %}
{% load i18n %}
{% load static %}
{% load key %}

{% block title %}
    {{ block.super }} - {% trans "BotBook" %}
{% endblock title %}



{% block content %}
<div id="skill" class="skill section pb-0">
    <div id="messages"></div>
    <div class="container content pt-3">

        <div class="d-flex align-items-center mb-5">
            <div class="section-title pb-1 fade-in flex-grow-1">
                <h2>{{ scenario.name }}</h2>
            </div>
            <a class="btn btn-outline-secondary" href="{% url 'scenarios_page' %}">
                <i class="fa fa-times"></i>
            </a>
        </div>

        
        <p>{{ scenario.description|linebreaksbr }}</p>

        <hr>

        <div class="d-flex align-items-center flex-wrap">
            <strong class="me-2">{% trans "Tags" %}:</strong>
            {% for group in tag_groups %}
                <a href="{% url 'scenarios_page' %}?q=&tags={{ group.name }}&cat=" class="badge bg-secondary text-decoration-none pt-0 pb-0 d-inline-flex align-items-center m-1">
                    {% if group.icon %}
                        <img src="{{ group.icon.url }}" alt="{{ group.name }}" title="{{ group.name }}" class="tag-icon me-1" style="width: 22px; height: 22px;" />
                    {% endif %}
                    <span>#{{ group.name }}</span>
                </a>
            {% endfor %}
        </div>

        <hr>

        {% if programs %}
        <p><strong>{% trans "Programs" %}:</strong>
        {% for program in programs %}
            <span class="badge bg-secondary text-decoration-none">{{ program.name }}</span>
        {% endfor %}
        </p>
        <hr>

        
        {% endif %}


        <p><strong>{% trans "Categories" %}:</strong>
        {% for category in categories %}
            {% if forloop.counter0 %}, {% endif %}
            {% for parent in paths|key:category.name %}
                <a href="{% url 'scenarios_page' %}?q=&tags=&cat={{ parent }}" class="badge bg-secondary text-decoration-none">{{ parent }}</a> &raquo;
            {% endfor %}
            <a href="{% url 'scenarios_page' %}?q=&tags=&cat={{ category.name }}" class="badge bg-secondary text-decoration-none">{{ category.name }}</a>
        {% endfor %}
        </p>
        <br>

        {% if scenario.scenario_version != -1 %}
        <!-- If scenario.version != -1 – show the buttons -->
        <div class="row">
            <div class="col col-auto">
                <div class="btn-group" role="group">
                    <button id="assignBtn" type="button" class="btn btn-primary{% if not user_robots %} disabled{% endif %}" data-bs-toggle="modal" data-bs-target="#assignModal">
                        {% trans "Assign to a robot" %}
                    </button>
                    {% if user.is_authenticated and user_robots %}
                        <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'You can assign skills to Robots. You then gain the ability to download them directly from the application.' %}">
                            <i class="fa fa-info-circle"></i>
                        </button>
                    {% elif user.is_authenticated %}
                        <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'You already have an account on our website. You can link the Botie app to it. Then you get a Robot to which you can assign skills and download them directly from the application.' %}">
                            <i class="fa fa-info-circle"></i>
                        </button>
                    {% else %}
                        <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" title="{% trans 'Hint' %}" data-bs-content="{% trans 'You can create or log in to an account on our website. You can link the Botie app to it. Then you get a Robot to which you can assign skills and download them directly from the application.' %}">
                            <i class="fa fa-info-circle"></i>
                        </button>
                    {% endif %}
                </div>
            </div>
            <div class="col text-end">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-placement="left" title="{% trans 'Hint' %}" data-bs-content="{% trans 'After downloading and saving the skill file on your disk, you can double-click on the file name to open the skill immediately in the Botie application editor.' %}">
                        <i class="fa fa-info-circle"></i>
                    </button>
                    <a href="{% url 'scenario_get' scenario.sid %}" class="btn btn-primary">{% trans "Download" %}</a>
                </div>
            </div>
        </div>
        {% else %}
        <!-- If scenario.version == -1 – show the alert -->
        <div class="">
            <div class="col text-end">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-secondary" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-placement="left" title="{% trans 'Hint' %}" data-bs-content="{% trans 'The skill file has not yet been prepared.' %}">
                        <i class="fa fa-info-circle"></i>
                    </button>
                    <a href="{% url 'contact' %}?scenario_name={{ scenario.name|urlencode }}&scenario_sid={{ scenario.sid }}" class="btn btn-primary">{% trans "Request Skill" %}</a>
                </div>
            </div>

        </div>
        {% endif %}
    </div>
</div>




<div class="mt-5 mb-5 divider"></div>

<script>
document.addEventListener("DOMContentLoaded", function () {
    // Get the modal, 'Przypisz' button, and the scenario ID
    var assignBtn = document.getElementById("assignBtn");
    if (assignBtn) {
        var modal = new bootstrap.Modal(document.getElementById("assignModal"));
        //var scenarioId = assignBtn.getAttribute("data-scenario-id");
        var scenarioId = '{{ scenario.sid }}';
        // Set the scenario ID as the value of the hidden field in the form
        var form = document.getElementById("assignForm");
        /*
        var scenarioField = document.createElement("input");
        scenarioField.type = "hidden";
        scenarioField.name = "scenario";
        scenarioField.value = scenarioId;
        form.appendChild(scenarioField);
        */
        form.scenario.value = scenarioId;

        // Prevent the form from being submitted
        form.addEventListener('submit', function(event) {
            event.preventDefault();
        });

        // Add a click event listener to the 'assign' button
        var submitBtn = form.getElementsByClassName("btn btn-primary");
        console.log(submitBtn);
        if (submitBtn) {
            submitBtn[0].addEventListener("click", function () {
                // Get the form data and submit the form using AJAX
                var formData = new FormData(form);
                // Gather all robot checkboxes and add their values to FormData
                document.querySelectorAll('#assignModal .robot-checkbox').forEach(checkbox => {
                    if (!checkbox.checked) {
                        formData.append('remove[]', checkbox.value);
                    }
                });
                fetch(form.action, {
                    method: form.method,
                    body: formData,
                    headers: {
                        "X-CSRFToken": "{{ csrf_token }}",
                    },
                })
                .then(function (response) {
                    if (response.ok) {
                        // Close the modal if the form submission is successful
                         modal.hide();
                        // Display success message
                        displayMessage('{% trans "Scenario successfully assigned to robot." %}', 'success');
                    } else {
                        // Display form errors or other error messages
                        displayMessage('{% trans "There was an issue assigning the scenario." %}', 'error');
                    }
                })
                .catch(function (error) {
                    console.error("Error:", error);
                    displayMessage('{% trans "An error occurred while submitting data." %}', 'error');
                });

            });
        }
    }

    //init popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
      return new bootstrap.Popover(popoverTriggerEl)
    });

});

function displayMessage(message, type) {
    const messagesContainer = document.getElementById('messages') || createMessagesContainer();
    const messageDiv = document.createElement('div');

    // Add appropriate Bootstrap classes based on message type
    if (type === 'success') {
        messageDiv.className = 'alert alert-success';
    } else if (type === 'error') {
        messageDiv.className = 'alert alert-danger';
    }

    messageDiv.textContent = message;
    messagesContainer.appendChild(messageDiv);

    // Optionally remove message after some time
    setTimeout(() => messageDiv.remove(), 2000);
}
</script>
{% endblock %}