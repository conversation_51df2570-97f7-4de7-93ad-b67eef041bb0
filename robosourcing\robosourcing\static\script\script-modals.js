document.addEventListener("DOMContentLoaded", function() {
    console.log("DOM fully loaded and parsed.");
  
    // Organization Modal Elements
    const countrySelect = document.getElementById('org_country');
    const taxIdInput = document.getElementById('tax_id');
    const taxIdFeedback = document.getElementById('tax-id-feedback');
    const postalCodeInput = document.getElementById('org_postal_code');
    const postalCodeFeedback = document.getElementById('org-postal-code-feedback');
    const organizationForm = document.getElementById('organization-form');
  
    // Address Modal Elements
    const addressCountrySelect = document.getElementById('address_country');
    const addressPostalCodeInput = document.getElementById('address_postal_code');
    const addressPostalCodeFeedback = document.getElementById('address-postal-code-feedback');
    const addressForm = document.getElementById('address-form');

    const formSubmissionFailed = document.getElementById('form-submission-failed');
  
    if (formSubmissionFailed) {
        const orgModal = new bootstrap.Modal(document.getElementById('orgModal'));
        orgModal.show();
    }
  
    // Fetch translations from the JSON script tag in the template
    const validationMessages = JSON.parse(document.getElementById('validationMessages').textContent);
    const postalCodePatterns = JSON.parse(document.getElementById('postalCodePatterns').textContent);
  
    function updatePostalCodeValidation(country, postalCodeInput, postalCodeFeedback) {
        const postalCodeData = postalCodePatterns[country];
  
        if (country === 'OTHER') {
            postalCodeInput.removeAttribute('pattern');
            postalCodeInput.removeAttribute('required');
            postalCodeFeedback.textContent = '';  // No feedback for "Other"
            postalCodeFeedback.style.display = 'none';
        } else if (postalCodeData) {
            postalCodeInput.setAttribute('pattern', postalCodeData.pattern);
            postalCodeInput.setAttribute('required', 'required');
            postalCodeFeedback.textContent = postalCodeData.message;
            postalCodeFeedback.style.display = 'none';  // Hide feedback initially
        }
    }
  
    function validateTaxId() {
        const country = countrySelect.value;
        const taxId = taxIdInput.value;
        const validationData = validationMessages[country];
  
        if (validationData && !new RegExp(validationData.pattern).test(taxId)) {
            taxIdInput.setCustomValidity('Invalid');
            taxIdFeedback.textContent = validationData.message;
            taxIdFeedback.style.display = 'block';
        } else {
            taxIdInput.setCustomValidity('');
            taxIdFeedback.textContent = '';
            taxIdFeedback.style.display = 'none';
        }
    }
  
    function validatePostalCode(countrySelect, postalCodeInput, postalCodeFeedback) {
        const country = countrySelect.value;
        const postalCode = postalCodeInput.value;
        const postalCodeData = postalCodePatterns[country];
  
        if (postalCodeData && country !== 'OTHER' && !new RegExp(postalCodeData.pattern).test(postalCode)) {
            postalCodeInput.setCustomValidity('Invalid');
            postalCodeFeedback.textContent = postalCodeData.message;
            postalCodeFeedback.style.display = 'block';
        } else {
            postalCodeInput.setCustomValidity('');
            postalCodeFeedback.textContent = '';
            postalCodeFeedback.style.display = 'none';
        }
    }
  
    if (countrySelect && taxIdInput && postalCodeInput) {
        countrySelect.addEventListener('change', function() {
            updatePostalCodeValidation(countrySelect.value, postalCodeInput, postalCodeFeedback);
            validateTaxId();
            validatePostalCode(countrySelect, postalCodeInput, postalCodeFeedback);
        });
        taxIdInput.addEventListener('input', validateTaxId);
        postalCodeInput.addEventListener('input', function() {
            validatePostalCode(countrySelect, postalCodeInput, postalCodeFeedback);
        });
    }
  
    if (addressCountrySelect && addressPostalCodeInput) {
        addressCountrySelect.addEventListener('change', function() {
            updatePostalCodeValidation(addressCountrySelect.value, addressPostalCodeInput, addressPostalCodeFeedback);
            validatePostalCode(addressCountrySelect, addressPostalCodeInput, addressPostalCodeFeedback);
        });
        addressPostalCodeInput.addEventListener('input', function() {
            validatePostalCode(addressCountrySelect, addressPostalCodeInput, addressPostalCodeFeedback);
        });
    }
  
    if (organizationForm) {
        organizationForm.addEventListener('submit', function(event) {
            validateTaxId();
            validatePostalCode(countrySelect, postalCodeInput, postalCodeFeedback);
            if (!organizationForm.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            organizationForm.classList.add('was-validated');
        });
    }
  
    if (addressForm) {
        addressForm.addEventListener('submit', function(event) {
            validatePostalCode(addressCountrySelect, addressPostalCodeInput, addressPostalCodeFeedback);
            if (!addressForm.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            addressForm.classList.add('was-validated');
        });
    }
  
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
  
    const selectAllCheckbox = document.getElementById('select_all_robots');
    if (selectAllCheckbox) {
        const robotCheckboxes = document.querySelectorAll('.robot-checkbox');
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = selectAllCheckbox.checked;
            robotCheckboxes.forEach(function(checkbox) {
                checkbox.checked = isChecked;
            });
        });
    }
  
    // Initialize validation on page load
    if (countrySelect) {
        updatePostalCodeValidation(countrySelect.value, postalCodeInput, postalCodeFeedback);
    }
    if (addressCountrySelect) {
        updatePostalCodeValidation(addressCountrySelect.value, addressPostalCodeInput, addressPostalCodeFeedback);
    }
  });