{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Delete Segment" %}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_segments' %}">{% trans "Segments" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{% trans "Delete Segment" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fa fa-trash me-2 text-danger"></i>{% trans "Delete Segment" %}</h2>
                <a href="{% url 'profiles_newsletter_segments' %}" class="newsletter-btn-secondary btn">
                    <i class="fa fa-arrow-left"></i> {% trans "Back to Segments" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="newsletter-card">
                <div class="newsletter-card-body">
                    <div class="text-center mb-4">
                        <i class="fa fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h4>{% trans "Are you sure?" %}</h4>
                        <p class="text-muted">{% trans "This action cannot be undone." %}</p>
                    </div>

                    <div class="newsletter-card mb-4" style="background-color: var(--muted);">
                        <div class="newsletter-card-body">
                            <h5><i class="fa fa-layer-group me-2"></i>{{ segment.name }}</h5>
                            {% if segment.description %}
                                <p class="mb-2">{{ segment.description }}</p>
                            {% endif %}
                            
                            <div class="row text-center mt-3">
                                <div class="col-4">
                                    <div class="newsletter-stat-item">
                                        <div class="newsletter-stat-number">{{ subscriber_count }}</div>
                                        <div class="newsletter-stat-label">{% trans "Subscribers" %}</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="newsletter-stat-item">
                                        <div class="newsletter-stat-number">{{ campaigns_count }}</div>
                                        <div class="newsletter-stat-label">{% trans "Campaigns" %}</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="newsletter-stat-item">
                                        <div class="newsletter-stat-number">{{ segment.created_at|date:"d.m.Y" }}</div>
                                        <div class="newsletter-stat-label">{% trans "Created" %}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if campaigns_count > 0 %}
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle me-2"></i>
                        <strong>{% trans "Warning!" %}</strong>
                        {% blocktrans count counter=campaigns_count %}
                            This segment is used in {{ counter }} campaign. Deleting it may affect campaign functionality.
                        {% plural %}
                            This segment is used in {{ counter }} campaigns. Deleting it may affect campaign functionality.
                        {% endblocktrans %}
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'profiles_newsletter_segments' %}" class="newsletter-btn-secondary btn">
                                <i class="fa fa-times"></i> {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fa fa-trash"></i> {% trans "Delete Segment" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
