from django.contrib.auth.models import User
from rest_framework import serializers

from api.serializers import CategorySerializer, KeywordHashSerializer, ProgramSerializer, TagSerializer, UserStorage, Robot

from db.models import FileKeyword, KeywordsHash, Program, Scenario, Scenario_Event, Scenario_Event_Message, Tag, Category
import logging

LOGGER = logging.getLogger('logger')


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'scenarios']

class ScenarioSerializer(serializers.ModelSerializer):
    rid             = serializers.UUIDField(required=False)
    message         = serializers.SerializerMethodField('get_last_message', required=False)
    apps            = ProgramSerializer(many=True, required=False)
    tags            = TagSerializer(many=True, required=False)
    categories      = CategorySerializer(many=True, required=False)
    keywords_hashes = KeywordHashSerializer(many=True, required=False)

    class Meta:
        model = Scenario
        fields = ['sid', 'scenario_version', 'version_changelog', 'name', 'description', 'author', 'apps', 'tags', 'categories', 'created_at', 'updated_at', 'scenario', 'price', 'available', 'editable', 'updatable', 'status', 'rid', 'keywords_hashes', 'message']
        extra_kwargs = {
            'rid': {'write_only': True},
            'message': {'write_only': True},
            }
        
    def get_last_message(self, obj):
        event_message = Scenario_Event_Message.objects.filter(event_id_id__in=Scenario_Event.objects.filter(sid=obj.sid)).order_by('-id').first()
        if event_message is not None: return event_message.message
        else: return None
