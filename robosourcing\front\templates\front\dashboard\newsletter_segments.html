{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Newsletter Segments" %}{% endblock %}

{% block extra_content_1 %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item site-name"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Segments" %}</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fa fa-layer-group me-2"></i>{% trans "Newsletter Segments" %}</h2>
                <button class="btn btn-primary">
                    <i class="fa fa-plus"></i> {% trans "New Segment" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-body">
                    {% if segments %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Name" %}</th>
                                        <th>{% trans "Description" %}</th>
                                        <th>{% trans "Subscribers" %}</th>
                                        <th>{% trans "Created" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for segment in segments %}
                                    <tr>
                                        <td><strong>{{ segment.name }}</strong></td>
                                        <td>{{ segment.description|truncatechars:80 }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ segment.subscriber_count }}</span>
                                        </td>
                                        <td>{{ segment.created_at|date:"d.m.Y" }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm">
                                                    <i class="fa fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info btn-sm">
                                                    <i class="fa fa-users"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fa fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No segments yet" %}</h5>
                            <p class="text-muted">{% trans "Create segments to organize your subscribers." %}</p>
                            <button class="btn btn-primary">
                                <i class="fa fa-plus"></i> {% trans "Create Segment" %}
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
