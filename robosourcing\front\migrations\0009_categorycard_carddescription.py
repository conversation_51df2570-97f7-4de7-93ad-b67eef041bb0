# Generated by Django 4.2.13 on 2024-11-20 20:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('front', '0008_remove_contactrequest_files_contactfile'),
    ]

    operations = [
        migrations.CreateModel(
            name='CategoryCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_en', models.CharField(max_length=255, verbose_name='Title (English)')),
                ('title_pl', models.CharField(max_length=255, verbose_name='Title (Polish)')),
                ('img', models.ImageField(upload_to='images/category_images/', verbose_name='Image')),
            ],
        ),
        migrations.CreateModel(
            name='CardDescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text_en', models.TextField(verbose_name='Text (English)')),
                ('text_pl', models.TextField(verbose_name='Text (Polish)')),
                ('order', models.PositiveIntegerField(default=0)),
                ('card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='descriptions', to='front.categorycard')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
    ]
