{% extends "front/dashboard/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Template Preview" %} - {{ template.name }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{% static 'styles/newsletter_modern.css' %}">
<style>

.preview-content {
    min-height: 400px;
}
.html-preview {
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

</style>
{% endblock %}

{% block extra_content_1 %}
<div class="container newsletter-container">
    <nav aria-label="breadcrumb" class="newsletter-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'home' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_dashboard' %}">{% trans "Newsletter" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item"><a href="{% url 'profiles_newsletter_templates' %}">{% trans "Templates" %}</a></li>
            <li class="breadcrumb-item newsletter-breadcrumb-item active">{% trans "Preview" %}</li>
        </ol>
    </nav>
</div>
{% endblock %}

{% block extra_content_2 %}
<div class="container newsletter-container">
    <div class="row">
        <div class="col-12">
            <div class="newsletter-card">
                <div class="newsletter-card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fa fa-eye me-2"></i>{% trans "Template Preview" %}: {{ template.name }}
                    </h5>
                    <div>
                        <a href="{% url 'profiles_newsletter_template_edit' template.id %}" class="newsletter-btn-secondary btn btn-sm me-2">
                            <i class="fa fa-edit"></i> {% trans "Edit" %}
                        </a>
                        <a href="{% url 'profiles_newsletter_templates' %}" class="newsletter-btn-secondary btn btn-sm">
                            <i class="fa fa-arrow-left"></i> {% trans "Back" %}
                        </a>
                    </div>
                </div>
                <div class="newsletter-card-body">
                    <!-- Informacje o szablonie -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <p><strong>{% trans "Subject:" %}</strong> {{ template.subject }}</p>
                            <p><strong>{% trans "Created:" %}</strong> {{ template.created_at|date:"d.m.Y H:i" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Status:" %}</strong> 
                                {% if template.is_active %}
                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                {% endif %}
                            </p>
                            <p><strong>{% trans "Last updated:" %}</strong> {{ template.updated_at|date:"d.m.Y H:i" }}</p>
                        </div>
                    </div>

                    <!-- Dane podglądu -->
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle me-2"></i>
                        <strong>{% trans "Preview data:" %}</strong>
                        {% trans "Email" %}: {{ preview_data.subscriber.email }}, 
                        {% trans "Date" %}: {{ preview_data.current_date }}, 
                        {% trans "Site" %}: {{ preview_data.site_url }}
                    </div>

                    <!-- Podgląd HTML -->
                    <div class="preview-content">
                        <div class="html-preview">
                            {{ rendered_html|safe }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Nie potrzebujemy JavaScript dla zakładek, bo mamy tylko HTML preview -->
{% endblock %}
